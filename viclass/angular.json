{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "packages/editors", "projects": {"@viclass/editor.core": {"projectType": "library", "root": "packages/editors/viclass/editor.core", "sourceRoot": "packages/editors/viclass/editor.core/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.core/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.core/tsconfig.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.core/tsconfig.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.core/tsconfig.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.core/src/test.ts", "tsConfig": "packages/editors/viclass/editor.core/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.core/karma.conf.js"}}}}, "@viclass/editor.freedrawing": {"projectType": "library", "root": "packages/editors/viclass/editor.freedrawing", "sourceRoot": "packages/editors/viclass/editor.freedrawing/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.freedrawing/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.freedrawing/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.freedrawing/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.freedrawing/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.freedrawing/src/test.ts", "tsConfig": "packages/editors/viclass/editor.freedrawing/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.freedrawing/karma.conf.js"}}}}, "@viclass/portal.homepage": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "packages/portal/viclass/portal.homepage", "sourceRoot": "packages/portal/viclass/portal.homepage/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/viclass/portal.homepage", "index": "packages/portal/viclass/portal.homepage/src/index.html", "main": "packages/portal/viclass/portal.homepage/src/main.ts", "polyfills": "packages/portal/viclass/portal.homepage/src/polyfills.ts", "tsConfig": "packages/portal/viclass/portal.homepage/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["packages/portal/viclass/portal.homepage/src/favicon.ico", "packages/portal/viclass/portal.homepage/src/assets"], "styles": ["packages/portal/viclass/portal.homepage/src/styles.scss"], "scripts": [], "extraWebpackConfig": "packages/portal/viclass/portal.homepage/webpack.config.js", "commonChunk": false}, "configurations": {"production": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/portal/viclass/portal.homepage/src/environments/environment.ts", "with": "packages/portal/viclass/portal.homepage/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/portal/viclass/portal.homepage/src/environments/environment.ts", "with": "packages/portal/viclass/portal.homepage/src/environments/environment.staging.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "configurations": {"production": {"browserTarget": "@viclass/portal.homepage:build:production"}, "staging": {"browserTarget": "@viclass/portal.homepage:build:staging"}, "development": {"browserTarget": "@viclass/portal.homepage:build:development"}}, "defaultConfiguration": "development"}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/viclass/portal.homepage-server", "main": "packages/portal/viclass/portal.homepage/src/ssr/server.ts", "tsConfig": "packages/portal/viclass/portal.homepage/src/ssr/tsconfig.server.json", "inlineStyleLanguage": "scss"}, "configurations": {"production": {"buildOptimizer": true, "outputHashing": "media", "fileReplacements": [{"replace": "packages/portal/viclass/portal.homepage/src/environments/environment.ts", "with": "packages/portal/viclass/portal.homepage/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "extractLicenses": false, "vendorChunk": true}, "staging": {"buildOptimizer": true, "outputHashing": "media", "fileReplacements": [{"replace": "packages/portal/viclass/portal.homepage/src/environments/environment.ts", "with": "packages/portal/viclass/portal.homepage/src/environments/environment.staging.ts"}], "optimization": true, "sourceMap": false, "extractLicenses": false, "vendorChunk": true}, "development": {"buildOptimizer": false, "optimization": false, "sourceMap": true, "extractLicenses": false, "vendorChunk": true}}, "defaultConfiguration": "production"}, "serve-ssr": {"builder": "@nguniversal/builders:ssr-dev-server", "configurations": {"development": {"browserTarget": "@viclass/portal.homepage:build:development", "serverTarget": "@viclass/portal.homepage:server:development", "publicHost": "https://devlocal.viclass.vn", "port": 4000}, "production": {"browserTarget": "@viclass/portal.homepage:build:production", "serverTarget": "@viclass/portal.homepage:server:production", "publicHost": "https://viclass.vn", "port": 4000}, "staging": {"browserTarget": "@viclass/portal.homepage:build:staging", "serverTarget": "@viclass/portal.homepage:server:staging", "publicHost": "https://staging.viclass.vn", "port": 4000}}, "defaultConfiguration": "development"}, "prerender": {"builder": "@nguniversal/builders:prerender", "options": {"routes": ["/features", "/terms-and-conditions"]}, "configurations": {"production": {"browserTarget": "@viclass/portal.homepage:build:production", "serverTarget": "@viclass/portal.homepage:server:production"}, "staging": {"browserTarget": "@viclass/portal.homepage:build:staging", "serverTarget": "@viclass/portal.homepage:server:staging"}, "development": {"browserTarget": "@viclass/portal.homepage:build:development", "serverTarget": "@viclass/portal.homepage:server:development"}}, "defaultConfiguration": "production"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "@viclass/portal.homepage:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/portal/viclass/portal.homepage/src/test.ts", "polyfills": "packages/portal/viclass/portal.homepage/src/polyfills.ts", "tsConfig": "packages/portal/viclass/portal.homepage/tsconfig.spec.json", "karmaConfig": "packages/portal/viclass/portal.homepage/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["packages/portal/viclass/portal.homepage/src/favicon.ico", "packages/portal/viclass/portal.homepage/src/assets"], "styles": ["packages/portal/viclass/portal.homepage/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["packages/portal/viclass/portal.homepage/**/*.ts", "packages/portal/viclass/portal.homepage/**/*.html"]}}}}, "@viclass/portal.common": {"projectType": "library", "root": "packages/portal/viclass/portal.common", "sourceRoot": "packages/portal/viclass/portal.common/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/portal/viclass/portal.common/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/portal/viclass/portal.common/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/portal/viclass/portal.common/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/portal/viclass/portal.common/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/portal/viclass/portal.common/src/test.ts", "tsConfig": "packages/portal/viclass/portal.common/tsconfig.spec.json", "karmaConfig": "packages/portal/viclass/portal.common/karma.conf.js"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["packages/portal/viclass/portal.common/**/*.ts", "packages/portal/viclass/portal.common/**/*.html"]}}}}, "@viclass/editorui.loader": {"projectType": "library", "root": "packages/editoruis/viclass/editorui.loader", "sourceRoot": "packages/editoruis/viclass/editorui.loader/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/editorui.loader/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/editorui.loader/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/editorui.loader/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/editorui.loader/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/editorui.loader/src/test.ts", "tsConfig": "packages/editoruis/viclass/editorui.loader/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/editorui.loader/karma.conf.js"}}}}, "@viclass/editorui.freedrawing": {"projectType": "library", "root": "packages/editoruis/viclass/editorui.freedrawing", "sourceRoot": "packages/editoruis/viclass/editorui.freedrawing/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/editorui.freedrawing/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/editorui.freedrawing/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/editorui.freedrawing/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/editorui.freedrawing/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/editorui.freedrawing/src/test.ts", "tsConfig": "packages/editoruis/viclass/editorui.freedrawing/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/editorui.freedrawing/karma.conf.js"}}}}, "@viclass/proto": {"projectType": "library", "root": "packages/protobuf/viclass/proto", "sourceRoot": "packages/protobuf/viclass/proto/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/protobuf/viclass/proto/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/protobuf/viclass/proto/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/protobuf/viclass/proto/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/protobuf/viclass/proto/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/protobuf/viclass/proto/src/test.ts", "tsConfig": "packages/protobuf/viclass/proto/tsconfig.spec.json", "karmaConfig": "packages/protobuf/viclass/proto/karma.conf.js"}}}}, "@viclass/mfe": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "sass"}}, "root": "packages/mfe/viclass/mfe", "sourceRoot": "packages/mfe/viclass/mfe/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/viclass/mfe", "index": "packages/mfe/viclass/mfe/src/index.html", "main": "packages/mfe/viclass/mfe/src/main.ts", "polyfills": "packages/mfe/viclass/mfe/src/polyfills.ts", "tsConfig": "packages/mfe/viclass/mfe/tsconfig.app.json", "inlineStyleLanguage": "sass", "aot": true, "assets": [], "styles": [], "scripts": [], "extraWebpackConfig": "packages/mfe/viclass/mfe/webpack.ng.js", "commonChunk": false}, "configurations": {"production": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "400kb", "maximumError": "500kb"}], "fileReplacements": [{"replace": "packages/mfe/viclass/mfe/src/environments/environment.ts", "with": "packages/mfe/viclass/mfe/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "400kb", "maximumError": "500kb"}], "fileReplacements": [{"replace": "packages/mfe/viclass/mfe/src/environments/environment.ts", "with": "packages/mfe/viclass/mfe/src/environments/environment.staging.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "@viclass/mfe:build:production"}, "staging": {"browserTarget": "@viclass/mfe:build:staging"}, "development": {"browserTarget": "@viclass/mfe:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "@viclass/mfe:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/mfe/viclass/mfe/src/test.ts", "polyfills": "packages/mfe/viclass/mfe/src/polyfills.ts", "tsConfig": "packages/mfe/viclass/mfe/tsconfig.spec.json", "karmaConfig": "packages/mfe/viclass/mfe/karma.conf.js", "inlineStyleLanguage": "sass", "assets": ["packages/mfe/viclass/mfe/src/favicon.ico", "packages/mfe/viclass/mfe/src/assets"], "styles": ["packages/mfe/viclass/mfe/src/styles.sass"], "scripts": []}}}}, "@viclass/ww": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "sass"}}, "root": "packages/mfe/viclass/ww", "sourceRoot": "packages/mfe/viclass/ww/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/viclass/ww", "index": "packages/mfe/viclass/ww/src/index.html", "main": "packages/mfe/viclass/ww/src/main.ts", "polyfills": "packages/mfe/viclass/ww/src/polyfills.ts", "tsConfig": "packages/mfe/viclass/ww/tsconfig.app.json", "inlineStyleLanguage": "sass", "aot": true, "assets": ["packages/mfe/viclass/ww/src/favicon.ico", "packages/mfe/viclass/ww/src/assets"], "styles": ["packages/mfe/viclass/ww/src/styles.sass"], "scripts": [], "extraWebpackConfig": "packages/mfe/viclass/ww/webpack.config.js", "commonChunk": false}, "configurations": {"production": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/mfe/viclass/ww/src/environments/environment.ts", "with": "packages/mfe/viclass/ww/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/mfe/viclass/ww/src/environments/environment.ts", "with": "packages/mfe/viclass/ww/src/environments/environment.staging.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "@viclass/mfe:build:production"}, "staging": {"browserTarget": "@viclass/mfe:build:staging"}, "development": {"browserTarget": "@viclass/mfe:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "@viclass/mfe:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/mfe/viclass/ww/src/test.ts", "polyfills": "packages/mfe/viclass/ww/src/polyfills.ts", "tsConfig": "packages/mfe/viclass/ww/tsconfig.spec.json", "karmaConfig": "packages/mfe/viclass/ww/karma.conf.js", "inlineStyleLanguage": "sass", "assets": ["packages/mfe/viclass/ww/src/favicon.ico", "packages/mfe/viclass/ww/src/assets"], "styles": ["packages/mfe/viclass/ww/src/styles.sass"], "scripts": []}}}}, "@viclass/editor.coordinator": {"projectType": "library", "root": "packages/editors/viclass/editor.coordinator", "sourceRoot": "packages/editors/viclass/editor.coordinator/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.coordinator/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.coordinator/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.coordinator/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.coordinator/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.coordinator/src/test.ts", "tsConfig": "packages/editors/viclass/editor.coordinator/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.coordinator/karma.conf.js"}}}}, "@viclass/editor.geo": {"projectType": "library", "root": "packages/editors/viclass/editor.geo", "sourceRoot": "packages/editors/viclass/editor.geo/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.geo/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.geo/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.geo/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.geo/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.geo/src/test.ts", "tsConfig": "packages/editors/viclass/editor.geo/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.geo/karma.conf.js"}}}}, "@viclass/editorui.geo": {"projectType": "library", "root": "packages/editoruis/viclass/editorui.geo", "sourceRoot": "packages/editoruis/viclass/editorui.geo/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/editorui.geo/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/editorui.geo/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/editorui.geo/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/editorui.geo/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/editorui.geo/src/test.ts", "tsConfig": "packages/editoruis/viclass/editorui.geo/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/editorui.geo/karma.conf.js"}}}}, "@viclass/editor.magh": {"projectType": "library", "root": "packages/editors/viclass/editor.magh", "sourceRoot": "packages/editors/viclass/editor.magh/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.magh/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.magh/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.magh/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.magh/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.magh/src/test.ts", "tsConfig": "packages/editors/viclass/editor.magh/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.magh/karma.conf.js"}}}}, "@viclass/editorui.magh": {"projectType": "library", "root": "packages/editoruis/viclass/editorui.magh", "sourceRoot": "packages/editoruis/viclass/editorui.magh/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/editorui.magh/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/editorui.magh/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/editorui.magh/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/editorui.magh/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/editorui.magh/src/test.ts", "tsConfig": "packages/editoruis/viclass/editorui.magh/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/editorui.magh/karma.conf.js"}}}}, "@viclass/editorui.word": {"projectType": "library", "root": "packages/editoruis/viclass/editorui.word", "sourceRoot": "packages/editoruis/viclass/editorui.word/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/editorui.word/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/editorui.word/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/editorui.word/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/editorui.word/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/editorui.word/src/test.ts", "tsConfig": "packages/editoruis/viclass/editorui.word/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/editorui.word/karma.conf.js"}}}}, "@viclass/editorui.composer": {"projectType": "library", "root": "packages/editoruis/viclass/editorui.composer", "sourceRoot": "packages/editoruis/viclass/editorui.composer/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/editorui.composer/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/editorui.composer/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/editorui.composer/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/editorui.composer/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/editorui.composer/src/test.ts", "tsConfig": "packages/editoruis/viclass/editorui.composer/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/editorui.composer/karma.conf.js"}}}}, "@viclass/editor.math": {"projectType": "library", "root": "packages/editors/viclass/editor.math", "sourceRoot": "packages/editors/viclass/editor.math/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.math/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.math/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.math/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.math/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.math/src/test.ts", "tsConfig": "packages/editors/viclass/editor.math/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.math/karma.conf.js"}}}}, "@viclass/editorui.math": {"projectType": "library", "root": "packages/editoruis/viclass/editorui.math", "sourceRoot": "packages/editoruis/viclass/editorui.math/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/editorui.math/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/editorui.math/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/editorui.math/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/editorui.math/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/editorui.math/src/test.ts", "tsConfig": "packages/editoruis/viclass/editorui.math/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/editorui.math/karma.conf.js"}}}}, "@viclass/test.embed": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "sass"}}, "root": "packages/test/viclass/test.embed", "sourceRoot": "packages/test/viclass/test.embed/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/viclass/test.embed", "index": "packages/test/viclass/test.embed/src/index.html", "main": "packages/test/viclass/test.embed/src/main.ts", "polyfills": "packages/test/viclass/test.embed/src/polyfills.ts", "tsConfig": "packages/test/viclass/test.embed/tsconfig.app.json", "inlineStyleLanguage": "sass", "assets": ["packages/test/viclass/test.embed/src/favicon.ico", "packages/test/viclass/test.embed/src/assets"], "styles": ["packages/test/viclass/test.embed/src/styles.sass"], "scripts": []}, "configurations": {"production": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/test/viclass/test.embed/src/environments/environment.ts", "with": "packages/test/viclass/test.embed/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/test/viclass/test.embed/src/environments/environment.ts", "with": "packages/test/viclass/test.embed/src/environments/environment.staging.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "@viclass/test.embed:build:production"}, "staging": {"browserTarget": "@viclass/test.embed:build:staging"}, "development": {"browserTarget": "@viclass/test.embed:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "@viclass/test.embed:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/test/viclass/test.embed/src/test.ts", "polyfills": "packages/test/viclass/test.embed/src/polyfills.ts", "tsConfig": "packages/test/viclass/test.embed/tsconfig.spec.json", "karmaConfig": "packages/test/viclass/test.embed/karma.conf.js", "inlineStyleLanguage": "sass", "assets": ["packages/test/viclass/test.embed/src/favicon.ico", "packages/test/viclass/test.embed/src/assets"], "styles": ["packages/test/viclass/test.embed/src/styles.sass"], "scripts": []}}}}, "@viclass/editorui.commontools": {"projectType": "library", "root": "packages/editoruis/viclass/commontools", "sourceRoot": "packages/editoruis/viclass/commontools/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/commontools/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/commontools/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/commontools/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/commontools/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/commontools/src/test.ts", "tsConfig": "packages/editoruis/viclass/commontools/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/commontools/karma.conf.js"}}}}, "@viclass/editorui.classroomtools": {"projectType": "library", "root": "packages/editoruis/viclass/classroomtools", "sourceRoot": "packages/editoruis/viclass/classroomtools/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editoruis/viclass/classroomtools/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editoruis/viclass/classroomtools/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editoruis/viclass/classroomtools/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editoruis/viclass/classroomtools/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editoruis/viclass/classroomtools/src/test.ts", "tsConfig": "packages/editoruis/viclass/classroomtools/tsconfig.spec.json", "karmaConfig": "packages/editoruis/viclass/classroomtools/karma.conf.js"}}}}, "@viclass/editor.word": {"projectType": "library", "root": "packages/editors/viclass/editor.word", "sourceRoot": "packages/editors/viclass/editor.word/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.word/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.word/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.word/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.word/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.word/src/test.ts", "tsConfig": "packages/editors/viclass/editor.word/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.word/karma.conf.js"}}}}, "@viclass/editor.composer": {"projectType": "library", "root": "packages/editors/viclass/editor.composer", "sourceRoot": "packages/editors/viclass/editor.composer/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.composer/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.composer/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.composer/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.composer/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/editors/viclass/editor.composer/src/test.ts", "tsConfig": "packages/editors/viclass/editor.composer/tsconfig.spec.json", "karmaConfig": "packages/editors/viclass/editor.composer/karma.conf.js"}}}}, "@viclass/portal.classrooms": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "packages/portal/viclass/portal.classrooms", "sourceRoot": "packages/portal/viclass/portal.classrooms/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/viclass/portal.classrooms", "index": "packages/portal/viclass/portal.classrooms/src/index.html", "main": "packages/portal/viclass/portal.classrooms/src/main.ts", "polyfills": "packages/portal/viclass/portal.classrooms/src/polyfills.ts", "tsConfig": "packages/portal/viclass/portal.classrooms/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["packages/portal/viclass/portal.classrooms/src/favicon.ico", "packages/portal/viclass/portal.classrooms/src/assets"], "styles": ["packages/portal/viclass/portal.classrooms/src/styles.scss"], "scripts": ["packages/portal/viclass/portal.classrooms/src/lib-jitsi-meet.min.js"], "extraWebpackConfig": "packages/portal/viclass/portal.classrooms/webpack.config.js", "commonChunk": false}, "configurations": {"production": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/portal/viclass/portal.classrooms/src/environments/environment.ts", "with": "packages/portal/viclass/portal.classrooms/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"buildOptimizer": true, "optimization": true, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "packages/portal/viclass/portal.classrooms/src/environments/environment.ts", "with": "packages/portal/viclass/portal.classrooms/src/environments/environment.staging.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "@viclass/portal.classrooms:build:production"}, "staging": {"browserTarget": "@viclass/portal.classrooms:build:staging"}, "development": {"browserTarget": "@viclass/portal.classrooms:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "@viclass/portal.classrooms:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/portal/viclass/portal.classrooms/src/test.ts", "polyfills": "packages/portal/viclass/portal.classrooms/src/polyfills.ts", "tsConfig": "packages/portal/viclass/portal.classrooms/tsconfig.spec.json", "karmaConfig": "packages/portal/viclass/portal.classrooms/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["packages/portal/viclass/portal.classrooms/src/favicon.ico", "packages/portal/viclass/portal.classrooms/src/assets"], "styles": ["packages/portal/viclass/portal.classrooms/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["packages/portal/viclass/portal.classrooms/**/*.ts", "packages/portal/viclass/portal.classrooms/**/*.html"]}}}}, "@viclass/editor.mcq": {"projectType": "library", "root": "packages/editors/viclass/editor.mcq", "sourceRoot": "packages/editors/viclass/editor.mcq/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "packages/editors/viclass/editor.mcq/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/editors/viclass/editor.mcq/tsconfig.lib.prod.json"}, "staging": {"tsConfig": "packages/editors/viclass/editor.mcq/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/editors/viclass/editor.mcq/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "packages/editors/viclass/editor.mcq/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["packages/editors/viclass/editor.mcq/**/*.ts", "packages/editors/viclass/editor.mcq/**/*.html"], "eslintConfig": "packages/editors/viclass/editor.mcq/eslint.config.mjs"}}}}}, "cli": {"analytics": false, "schematicCollections": ["angular-eslint"]}}
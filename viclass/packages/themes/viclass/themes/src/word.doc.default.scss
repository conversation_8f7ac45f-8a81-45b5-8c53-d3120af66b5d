@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Pacifico&display=swap');

* {
    box-sizing: border-box;
}

.word-encapsulation {
    height: 100%;
    width: 100%;
    margin: 0px;
    padding: 0px;
}

.word-encapsulation .vi-word-doc {
    @apply h-full overflow-auto;
    margin: 0px;
    padding: 15px;
    outline: none;
    position: relative;

    scrollbar-width: thin;
    scrollbar-color: transparent transparent;

    &::-webkit-scrollbar-track {
        display: none;
    }

    &::-webkit-scrollbar {
        width: 5px;
        background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        -webkit-box-shadow: inset 0 0 6px transparent;
        @apply bg-transparent;
    }

    .resizable {
        outline: inherit;
        position: relative;

        &.resizing {
            overflow: hidden !important;
        }

        .node-resizer {
            display: none;
            pointer-events: none;
            width: 7px;
            height: 7px;
            position: absolute;
            background-color: rgb(var(--P1));
            border: 1px solid #fff;

            &.node-resizer-n {
                top: -6px;
                left: calc(50% - 3px);
                cursor: n-resize;
            }

            &.node-resizer-ne {
                top: -6px;
                right: -6px;
                cursor: ne-resize;
            }

            &.node-resizer-e {
                bottom: calc(50% - 3px);
                right: -6px;
                cursor: e-resize;
            }

            &.node-resizer-se {
                bottom: -6px;
                right: -6px;
                cursor: nwse-resize;
            }

            &.node-resizer-s {
                bottom: -6px;
                left: calc(50% - 3px);
                cursor: s-resize;
            }

            &.node-resizer-sw {
                bottom: -6px;
                left: -6px;
                cursor: sw-resize;
            }

            &.node-resizer-w {
                bottom: calc(50% - 3px);
                left: -6px;
                cursor: w-resize;
            }

            &.node-resizer-nw {
                top: -6px;
                left: -6px;
                cursor: nw-resize;
            }
        }

        .resizable-select-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        &.resizable-selected {
            outline: 1px solid rgb(var(--P1));

            .node-resizer {
                display: block;
                pointer-events: all;
            }

            .resizable-select-overlay {
                display: none;
                pointer-events: none;
            }
        }
    }

    .inline-viewport .resizable .node-resizer {
        display: none !important;
    }

    /**
    * Copyright (c) Meta Platforms, Inc. and affiliates.
    *
    * This source code is licensed under the MIT license found in the
    * LICENSE file in the root directory of this source tree.
    *
    *
    */
    .LexicalEditorTheme__ltr {
        text-align: left;
    }
    .LexicalEditorTheme__rtl {
        text-align: right;
    }
    .LexicalEditorTheme__paragraph {
        margin: 0;
        position: relative;
    }
    .LexicalEditorTheme__quote {
        margin: 0;
        margin-left: 20px;
        margin-bottom: 10px;
        font-size: 15px;
        color: rgb(101, 103, 107);
        border-left-color: rgb(206, 208, 212);
        border-left-width: 4px;
        border-left-style: solid;
        padding-left: 16px;
    }
    .LexicalEditorTheme__h1 {
        font-size: 24px;
        color: rgb(5, 5, 5);
        font-weight: 400;
        margin: 0;
    }
    .LexicalEditorTheme__h2 {
        font-size: 15px;
        color: rgb(101, 103, 107);
        font-weight: 700;
        margin: 0;
        text-transform: uppercase;
    }
    .LexicalEditorTheme__h3 {
        font-size: 12px;
        margin: 0;
        text-transform: uppercase;
    }
    .LexicalEditorTheme__indent {
        --lexical-indent-base-value: 40px;
    }
    .LexicalEditorTheme__textBold {
        font-weight: bold;
    }
    .LexicalEditorTheme__paragraph mark {
        background-color: unset;
    }
    .LexicalEditorTheme__textHighlight {
        background: rgba(255, 212, 0, 0.14);
        border-bottom: 2px solid rgba(255, 212, 0, 0.3);
    }
    .LexicalEditorTheme__textItalic {
        font-style: italic;
    }
    .LexicalEditorTheme__textUnderline {
        text-decoration: underline;
    }

    .LexicalEditorTheme__textStrikethrough {
        text-decoration: line-through;
    }

    .LexicalEditorTheme__textUnderlineStrikethrough {
        text-decoration: underline line-through;
    }

    .LexicalEditorTheme__tabNode {
        position: relative;
        text-decoration: none;
    }

    .LexicalEditorTheme__tabNode.LexicalEditorTheme__textUnderline::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0.15em;
        border-bottom: 0.1em solid currentColor;
    }

    .LexicalEditorTheme__tabNode.LexicalEditorTheme__textStrikethrough::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0.69em;
        border-top: 0.1em solid currentColor;
    }

    .LexicalEditorTheme__tabNode.LexicalEditorTheme__textUnderlineStrikethrough::before,
    .LexicalEditorTheme__tabNode.LexicalEditorTheme__textUnderlineStrikethrough::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
    }

    .LexicalEditorTheme__tabNode.LexicalEditorTheme__textUnderlineStrikethrough::before {
        top: 0.69em;
        border-top: 0.1em solid currentColor;
    }

    .LexicalEditorTheme__tabNode.LexicalEditorTheme__textUnderlineStrikethrough::after {
        bottom: 0.05em;
        border-bottom: 0.1em solid currentColor;
    }

    .LexicalEditorTheme__textSubscript {
        font-size: 0.8em;
        vertical-align: sub !important;
    }
    .LexicalEditorTheme__textSuperscript {
        font-size: 0.8em;
        vertical-align: super;
    }
    .LexicalEditorTheme__textCode {
        background-color: rgb(240, 242, 245);
        padding: 1px 0.25rem;
        font-family: Menlo, Consolas, Monaco, monospace;
        font-size: 94%;
    }
    .LexicalEditorTheme__textLowercase {
        text-transform: lowercase;
    }
    .LexicalEditorTheme__textUppercase {
        text-transform: uppercase;
    }
    .LexicalEditorTheme__textCapitalize {
        text-transform: capitalize;
    }
    .LexicalEditorTheme__hashtag {
        background-color: rgba(88, 144, 255, 0.15);
        border-bottom: 1px solid rgba(88, 144, 255, 0.3);
    }
    .LexicalEditorTheme__link {
        color: rgb(33, 111, 219);
        text-decoration: none;
    }
    .LexicalEditorTheme__link:hover {
        text-decoration: underline;
        cursor: pointer;
    }
    .PlaygroundEditorTheme__blockCursor {
        display: block;
        pointer-events: none;
        position: absolute;
    }
    .PlaygroundEditorTheme__blockCursor:after {
        content: '';
        display: block;
        position: absolute;
        top: -2px;
        width: 20px;
        border-top: 1px solid black;
        animation: CursorBlink 1.1s steps(2, start) infinite;
    }
    @keyframes CursorBlink {
        to {
            visibility: hidden;
        }
    }
    .LexicalEditorTheme__code {
        background-color: rgb(240, 242, 245);
        font-family: Menlo, Consolas, Monaco, monospace;
        display: block;
        padding: 8px 8px 8px 52px;
        line-height: 1.53;
        font-size: 13px;
        margin: 0;
        margin-top: 8px;
        margin-bottom: 8px;
        overflow-x: auto;
        position: relative;
        tab-size: 2;
    }
    .LexicalEditorTheme__code:before {
        content: attr(data-gutter);
        position: absolute;
        background-color: #eee;
        left: 0;
        top: 0;
        border-right: 1px solid #ccc;
        padding: 8px;
        color: #777;
        white-space: pre-wrap;
        text-align: right;
        min-width: 25px;
    }
    .LexicalEditorTheme__tableScrollableWrapper {
        overflow-x: auto;
        margin: 0px 25px 30px 0px;
    }
    .LexicalEditorTheme__tableScrollableWrapper > .LexicalEditorTheme__table {
        /* Remove the table's vertical margin and put it on the wrapper */
        margin-top: 0;
        margin-bottom: 0;
    }
    .LexicalEditorTheme__tableAlignmentCenter {
        margin-left: auto;
        margin-right: auto;
    }
    .LexicalEditorTheme__tableAlignmentRight {
        margin-left: auto;
    }
    .LexicalEditorTheme__table {
        border-collapse: collapse;
        border-spacing: 0;
        overflow-y: scroll;
        overflow-x: scroll;
        table-layout: fixed;
        display: inline-block;
        width: fit-content;
        overflow: auto;
    }
    .LexicalEditorTheme__tableFrozenRow {
        /* position:sticky needs overflow:clip or visible
           https://github.com/w3c/csswg-drafts/issues/865#issuecomment-350585274 */
        overflow-x: clip;
    }
    .LexicalEditorTheme__tableFrozenRow tr:nth-of-type(1) > td {
        overflow: clip;
        background-color: #ffffff;
        position: sticky;
        z-index: 2;
        top: 44px;
    }
    .LexicalEditorTheme__tableFrozenRow tr:nth-of-type(1) > th {
        overflow: clip;
        background-color: #f2f3f5;
        position: sticky;
        z-index: 2;
        top: 44px;
    }
    .LexicalEditorTheme__tableFrozenRow tr:nth-of-type(1) > th:after,
    .LexicalEditorTheme__tableFrozenRow tr:nth-of-type(1) > td:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        border-bottom: 1px solid #bbb;
    }
    .LexicalEditorTheme__tableFrozenColumn tr > td:first-child {
        background-color: #ffffff;
        position: sticky;
        z-index: 2;
        left: 0;
    }
    .LexicalEditorTheme__tableFrozenColumn tr > th:first-child {
        background-color: #f2f3f5;
        position: sticky;
        z-index: 2;
        left: 0;
    }
    .LexicalEditorTheme__tableFrozenColumn tr > :first-child::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        height: 100%;
        border-right: 1px solid #bbb;
    }
    .LexicalEditorTheme__tableRowStriping tr:nth-child(even) {
        background-color: #f2f5fb;
    }
    .LexicalEditorTheme__tableSelection *::selection {
        background-color: transparent;
    }
    .LexicalEditorTheme__tableSelected {
        outline: 2px solid rgb(60, 132, 244);
    }
    .LexicalEditorTheme__tableCell {
        border: 1px solid #000000;
        vertical-align: top;
        text-align: start;
        padding: 2px;
        position: relative;
        outline: none;
        overflow: auto;
    }
    /*
        A firefox workaround to allow scrolling of overflowing table cell
        ref: https://bugzilla.mozilla.org/show_bug.cgi?id=1904159
      */
    // TODO: this is added from original lexical repo https://github.com/facebook/lexical/pull/6966
    // but it work fine in firefox now and not cause scroll issue on chrome. Check when any cell overflow issue emerge.
    //.LexicalEditorTheme__tableCell > * {
    //    overflow: inherit;
    //}
    .LexicalEditorTheme__tableCellResizer {
        position: absolute;
        right: -4px;
        height: 100%;
        width: 8px;
        cursor: ew-resize;
        z-index: 10;
        top: 0;
    }
    .LexicalEditorTheme__tableCellHeader {
        background-color: #f2f3f5;
        text-align: start;
    }
    .LexicalEditorTheme__tableCellSelected {
        caret-color: transparent;
    }
    .LexicalEditorTheme__tableCellSelected::after {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        background-color: highlight;
        mix-blend-mode: multiply;
        content: '';
        pointer-events: none;
    }
    .LexicalEditorTheme__tableAddColumns {
        position: absolute;
        background-color: #eee;
        height: 100%;
        animation: table-controls 0.2s ease;
        border: 0;
        cursor: pointer;
    }
    .LexicalEditorTheme__tableAddColumns:after {
        display: block;
        content: ' ';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.4;
    }
    .LexicalEditorTheme__tableAddColumns:hover,
    .LexicalEditorTheme__tableAddRows:hover {
        background-color: #c9dbf0;
    }
    .LexicalEditorTheme__tableAddRows {
        position: absolute;
        width: calc(100% - 25px);
        background-color: #eee;
        animation: table-controls 0.2s ease;
        border: 0;
        cursor: pointer;
    }
    .LexicalEditorTheme__tableAddRows:after {
        display: block;
        content: ' ';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.4;
    }
    @keyframes table-controls {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }
    .LexicalEditorTheme__tableCellResizeRuler {
        display: block;
        position: absolute;
        width: 1px;
        background-color: rgb(60, 132, 244);
        height: 100%;
        top: 0;
    }
    .LexicalEditorTheme__tableCellActionButtonContainer {
        display: block;
        right: 5px;
        top: 6px;
        position: absolute;
        z-index: 4;
        width: 20px;
        height: 20px;
    }
    .LexicalEditorTheme__tableCellActionButton {
        background-color: #eee;
        display: block;
        border: 0;
        border-radius: 20px;
        width: 20px;
        height: 20px;
        color: #222;
        cursor: pointer;
    }
    .LexicalEditorTheme__tableCellActionButton:hover {
        background-color: #ddd;
    }
    .LexicalEditorTheme__characterLimit {
        display: inline;
        background-color: #ffbbbb !important;
    }
    .LexicalEditorTheme__ol1 {
        padding: 0;
        margin: 0;
        list-style-position: outside;
    }
    .LexicalEditorTheme__ol2 {
        padding: 0;
        margin: 0;
        list-style-type: upper-alpha;
        list-style-position: outside;
    }
    .LexicalEditorTheme__ol3 {
        padding: 0;
        margin: 0;
        list-style-type: lower-alpha;
        list-style-position: outside;
    }
    .LexicalEditorTheme__ol4 {
        padding: 0;
        margin: 0;
        list-style-type: upper-roman;
        list-style-position: outside;
    }
    .LexicalEditorTheme__ol5 {
        padding: 0;
        margin: 0;
        list-style-type: lower-roman;
        list-style-position: outside;
    }
    .LexicalEditorTheme__ul {
        padding: 0;
        margin: 0;
        list-style-position: outside;
    }
    .LexicalEditorTheme__listItem {
        margin: 0 32px;
    }
    .LexicalEditorTheme__listItem::marker {
        color: var(--listitem-marker-color);
        background-color: var(--listitem-marker-background-color);
        font-family: var(--listitem-marker-font-family);
        font-size: var(--listitem-marker-font-size);
    }
    .LexicalEditorTheme__listItemChecked,
    .LexicalEditorTheme__listItemUnchecked {
        position: relative;
        margin-left: 8px;
        margin-right: 8px;
        padding-left: 24px;
        padding-right: 24px;
        list-style-type: none;
        outline: none;
    }
    .LexicalEditorTheme__listItemChecked {
        text-decoration: line-through;
    }
    .LexicalEditorTheme__listItemUnchecked:before,
    .LexicalEditorTheme__listItemChecked:before {
        content: '';
        width: 16px;
        height: 16px;
        top: 2px;
        left: 0;
        cursor: pointer;
        display: block;
        background-size: cover;
        position: absolute;
    }
    .LexicalEditorTheme__listItemUnchecked[dir='rtl']:before,
    .LexicalEditorTheme__listItemChecked[dir='rtl']:before {
        left: auto;
        right: 0;
    }
    .LexicalEditorTheme__listItemUnchecked:focus:before,
    .LexicalEditorTheme__listItemChecked:focus:before {
        box-shadow: 0 0 0 2px #a6cdfe;
        border-radius: 2px;
    }
    .LexicalEditorTheme__listItemUnchecked:before {
        border: 1px solid #999;
        border-radius: 2px;
    }
    .LexicalEditorTheme__listItemChecked:before {
        border: 1px solid rgb(61, 135, 245);
        border-radius: 2px;
        background-color: #3d87f5;
        background-repeat: no-repeat;
    }
    .LexicalEditorTheme__listItemChecked:after {
        content: '';
        cursor: pointer;
        border-color: #fff;
        border-style: solid;
        position: absolute;
        display: block;
        top: 6px;
        width: 3px;
        left: 7px;
        right: 7px;
        height: 6px;
        transform: rotate(45deg);
        border-width: 0 2px 2px 0;
    }
    .LexicalEditorTheme__nestedListItem {
        list-style-type: none;
    }
    .LexicalEditorTheme__nestedListItem:before,
    .LexicalEditorTheme__nestedListItem:after {
        display: none;
    }
    .LexicalEditorTheme__tokenComment {
        color: slategray;
    }
    .LexicalEditorTheme__tokenPunctuation {
        color: #999;
    }
    .LexicalEditorTheme__tokenProperty {
        color: #905;
    }
    .LexicalEditorTheme__tokenSelector {
        color: #690;
    }
    .LexicalEditorTheme__tokenOperator {
        color: #9a6e3a;
    }
    .LexicalEditorTheme__tokenAttr {
        color: #07a;
    }
    .LexicalEditorTheme__tokenVariable {
        color: #e90;
    }
    .LexicalEditorTheme__tokenFunction {
        color: #dd4a68;
    }
    .LexicalEditorTheme__mark {
        background: rgba(255, 212, 0, 0.14);
        border-bottom: 2px solid rgba(255, 212, 0, 0.3);
        padding-bottom: 2px;
    }
    .LexicalEditorTheme__markOverlap {
        background: rgba(255, 212, 0, 0.3);
        border-bottom: 2px solid rgba(255, 212, 0, 0.7);
    }
    .LexicalEditorTheme__mark.selected {
        background: rgba(255, 212, 0, 0.5);
        border-bottom: 2px solid rgba(255, 212, 0, 1);
    }
    .LexicalEditorTheme__markOverlap.selected {
        background: rgba(255, 212, 0, 0.7);
        border-bottom: 2px solid rgba(255, 212, 0, 0.7);
    }
    .LexicalEditorTheme__embedBlock {
        user-select: none;
    }
    .LexicalEditorTheme__embedBlockFocus {
        outline: 2px solid rgb(60, 132, 244);
    }
    .LexicalEditorTheme__layoutContainer {
        display: grid;
        gap: 10px;
        margin: 10px 0;

        .LexicalEditorTheme__layoutItem {
            border: 1px dashed transparent;
            padding: 2px;
        }

        &:hover .LexicalEditorTheme__layoutItem,
        &.editing .LexicalEditorTheme__layoutItem {
            border-color: #ddd;
        }
    }

    .LexicalEditorTheme__autocomplete {
        color: #ccc;
    }
    .LexicalEditorTheme__hr {
        padding: 2px 2px;
        border: none;
        margin: 1em 0;
        cursor: pointer;
    }
    .LexicalEditorTheme__hr:after {
        content: '';
        display: block;
        height: 2px;
        background-color: #ccc;
        line-height: 2px;
    }
    .LexicalEditorTheme__hr.LexicalEditorTheme__hrSelected {
        outline: 2px solid rgb(60, 132, 244);
        user-select: none;
    }

    .LexicalEditorTheme__specialText {
        background-color: yellow;
        font-weight: bold;
    }

    .LexicalEditorTheme__list-style-type-point {
        list-style-type: disc;
    }

    .LexicalEditorTheme__list-style-type-pinwheel {
        list-style-type: '✧ ';
    }

    .LexicalEditorTheme__list-style-type-plus {
        list-style-type: '+ ';
    }

    .LexicalEditorTheme__list-style-type-star {
        list-style-type: '⋆ ';
    }

    .LexicalEditorTheme__list-style-type-number {
        list-style-type: decimal;
    }

    .LexicalEditorTheme__list-style-type-roman-number {
        list-style-type: upper-roman;
    }

    .LexicalEditorTheme__list-style-type-upercase {
        list-style-type: upper-alpha;
    }

    .LexicalEditorTheme__list-style-type-lowercase {
        list-style-type: lower-alpha;
    }

    .ImageNode__contentEditable {
        min-height: 20px;
        border: 0px;
        resize: none;
        cursor: text;
        caret-color: rgb(5, 5, 5);
        display: block;
        position: relative;
        outline: 0px;
        padding: 10px;
        user-select: text;
        font-size: 12px;
        width: calc(100% - 20px);
        white-space: pre-wrap;
        word-break: break-word;
    }

    .ImageNode__placeholder {
        font-size: 12px;
        color: #888;
        overflow: hidden;
        position: absolute;
        text-overflow: ellipsis;
        top: 10px;
        left: 10px;
        user-select: none;
        white-space: nowrap;
        display: inline-block;
        pointer-events: none;
    }

    .image-control-wrapper--resizing {
        touch-action: none;
    }

    .loading {
        @apply bg-BW5 animate-pulse;
    }
}

.word-root.selected .word-encapsulation .vi-word-doc {
    scrollbar-color: #000000 #fff;

    &::-webkit-scrollbar {
        background-color: #f5f5f5;
    }

    &::-webkit-scrollbar-thumb {
        -webkit-box-shadow: inset 0 0 6px #0000004d;
        @apply bg-BW3;
    }
}

.TableCellResizer__resizer {
    position: absolute;
    z-index: 1000;
}

.vi-word-doc[contenteditable='false'],
[viewport-mode='InteractiveMode'] {
    .LexicalEditorTheme__layoutItem {
        border: 1px dashed transparent !important;
    }

    .node-resizer {
        display: none !important;
    }
}

.word-root.processing {
    pointer-events: none;
    opacity: 0.8;
}

@media print {
    .word-encapsulation .vi-word-doc {
        padding: 0px !important;

        .LexicalEditorTheme__layoutContainer {
            gap: 0 !important;
        }

        .resizable {
            &.resizable-selected {
                outline-color: transparent !important;
            }

            .node-resizer {
                display: none !important;
            }
        }
    }
}

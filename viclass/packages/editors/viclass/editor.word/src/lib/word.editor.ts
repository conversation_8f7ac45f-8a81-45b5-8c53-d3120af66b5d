import { BoundaryDelegator } from '@viclass/editor.coordinator/classroom';
import {
    AttachmentFeature,
    AwarenessFeature,
    AwarenessId,
    Cmd,
    CmdChannel,
    ContentVisibilityCheckFeature,
    CopyPasteDTO,
    CRDHistoryItem,
    CRUDChangeResult,
    CRUDDelegator,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocumentEditor,
    DocumentId,
    DOMElementLayerCtrl,
    EditorBase,
    EditorConfig,
    EditorCoordinator,
    EditorId,
    EditorType,
    ExportTarget,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FEATURE_ATTACHMENT,
    FEATURE_AWARENESS,
    FEATURE_CONTENT_VISIBILITY,
    FEATURE_COPYPASTE,
    FEATURE_CRD_DOC,
    FEATURE_CRUD,
    FEATURE_HISTORY,
    FEATURE_HTML_EXPORT,
    FEATURE_REMOVE,
    FEATURE_ROB,
    FEATURE_SELECTION,
    HasSelectionFeature,
    HistoryFeature,
    HistoryItem,
    InsertDocCtrlDelegator,
    LoadingContext,
    OperationMode,
    ROBFeature,
    SelectContext,
    SelectDelegator,
    SelectionFeature,
    SupportContentVisibilityCheckFeature,
    SupportCopyPasteFeature,
    SupportFeatureHistory,
    SupportHtmlExportFeature,
    SupportRemoveFeature,
    SupportSelectFeature,
    ThrottleCombinator,
    ToolBar,
    VICLASS_GENERAL_DTO_MIME,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { $isSubViewportNode } from '@viclass/editor.word.transform';
import { WordCmdTypeProto } from '@viclass/proto/editor.word';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import {
    $getNodeByKey,
    $getSelection,
    $isRangeSelection,
    $setSelection,
    COPY_COMMAND,
    DELETE_CHARACTER_COMMAND,
    PASTE_COMMAND,
} from 'lexical';
import { deserializer } from './cmds/word.cmd';
import { WordCmdProcessor } from './cmds/word.cmd.processor';
import { WordEditorConfig, WordEditorCoordinatorConfig } from './config';
import { WordEditorCoordinator } from './coord/word.coordinator';
import {
    DELETE_SUBVIEWPORTS_COMMAND,
    isSubViewportCopyPasteDTO,
    PASTE_SUBVIEWPORT_COMMAND,
    SubViewportCopyPasteDTO,
} from './doceditor/plugins';
import { WordDocCtrl } from './docs/word.doc.ctrl';
import { WordHistoryItem, WordSubViewportHistoryItem } from './history';
import { FetchDocResponse, WordDoc, WordDocRestorable, WordLayer } from './model';
import {
    BlockTypeTool,
    DocInsertTool,
    FormatTextTool,
    HeadingWrapperTool,
    LayoutTool,
    ListTool,
    SubEditorManagerTool,
    TextStylingTool,
    WordAlignmentTool,
    WordContextTool,
    WordCreateDocumentTool,
    WordSettingsTool,
    WordTableTool,
    WordToolBar,
    WordToolType,
} from './tools';
import { ContentTool } from './tools/word.content.tool';
import { WordGateway } from './word.gateway';
import { WordDocInitData } from './word.models';
import { destroyRestorable } from './word.utils';

/**
 * Implementation of the viclass document editor (wysiwyg)
 */
export class WordEditor
    extends EditorBase<WordDocCtrl>
    implements
        DocumentEditor,
        SupportContentVisibilityCheckFeature,
        SupportCopyPasteFeature,
        SupportRemoveFeature,
        SupportFeatureHistory,
        HasSelectionFeature,
        SupportHtmlExportFeature
{
    static readonly awarenessPastingId: AwarenessId = 'awareness-word-pasting';
    static readonly awarenessCopyingId: AwarenessId = 'awareness-word-copying';

    readonly id: EditorId;
    readonly editorType: EditorType;

    private readonly _cmdChannel: CmdChannel;
    private readonly _cmdProcessor: WordCmdProcessor;
    private readonly _wordGateway: WordGateway;

    /**
     * Map of restorables for documents that have been removed, so we can restore history
     * from the previous state
     */
    private readonly _restorables: Map<string, WordDocRestorable> = new Map();

    toolbars: Map<ViewportId, WordToolBar> = new Map();

    /**
     * Coordinator related stuff
     * The word editor itself contains an internal coordinator which it uses to manage the editors
     * it needs to create the inserted documents of other editors
     **/
    wcoord: WordEditorCoordinator;
    selectionFeature: SelectionFeature;
    historyFeature: HistoryFeature;
    robFeature: ROBFeature;
    crdFeature: DocCRDFeature;
    awarenessFeature: AwarenessFeature;
    crudDelegator: CRUDDelegator;
    contentVisibilityFeature: ContentVisibilityCheckFeature;
    attachmentFeature: AttachmentFeature;

    readonly selectDelegator = new SelectDelegator<WordDocCtrl>(this, {
        onSelect: this.onSelectDocCtrl.bind(this),
    });

    readonly insertDocDelegator = new InsertDocCtrlDelegator<WordDocCtrl, WordDoc>(
        this,
        (vp, state) => new WordDocCtrl(this, state, vp)
    );

    // TODO: here
    private readonly _operationMode: OperationMode;

    get operationMode(): OperationMode {
        return this._operationMode;
    }

    static readonly cmdChannelThrottle = 300;

    readonly boundaryDelegator = new BoundaryDelegator(this);

    constructor(
        public conf: EditorConfig,
        private _coordinator: EditorCoordinator
    ) {
        super(conf);
        this.id = conf.id;
        this.editorType = conf.editorType;
        this._operationMode = conf.operationMode || OperationMode.CLOUD;

        this._wordGateway = new WordGateway(conf.apiUri);

        this._cmdProcessor = new WordCmdProcessor(this);
        this._cmdChannel = _coordinator.registerCmdChannel(this.id);
        this._cmdChannel.registerCmdListener(this._cmdProcessor);
        this._cmdChannel.registerCombinator(
            FCCmdTypeProto.PREVIEW_BOUNDARY,
            new ThrottleCombinator(WordEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(
            FCCmdTypeProto.UPDATE_BOUNDARY,
            new ThrottleCombinator(WordEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(
            WordCmdTypeProto.SYNC_LEXICAL_AWARENESS,
            new ThrottleCombinator(WordEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(
            WordCmdTypeProto.SYNC_WORD_DOC_SCROLL,
            new ThrottleCombinator(WordEditor.cmdChannelThrottle)
        );

        this._cmdChannel.registerDeserializer(deserializer);

        const coordConf: WordEditorCoordinatorConfig = (conf as WordEditorConfig).wcoordConf;

        this.wcoord = new WordEditorCoordinator(coordConf, this, _coordinator);

        this.crudDelegator = new CRUDDelegator(
            this,
            this.cmdChannel,
            this.regMan,
            { doc: this.docReg, layer: this.layerReg },
            this.generateInitDocData.bind(this)
        );
    }

    // Word doc is not support local content yet
    getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined {
        return undefined;
    }

    /**
     * Get the restorable for a document
     */
    getRestorable(vpId: ViewportId, localId: DocLocalId): WordDocRestorable | undefined {
        return this._restorables.get(`${vpId}_${localId}`) ?? undefined;
    }

    /**
     * Remove the restorable for a document
     */
    removeRestorable(vpId: ViewportId, localId: DocLocalId) {
        this._restorables.delete(`${vpId}_${localId}`);
    }

    /**
     * Called when a document is selected to change the editable state
     */
    onSelectDocCtrl(docCtrl: WordDocCtrl): void {
        const viewPortId = docCtrl.viewport.id;

        const contentTool = this.toolbars.get(viewPortId).getTool('ContentEditorTool') as ContentTool;
        contentTool.checkFocusedDocEditable(viewPortId, docCtrl, this.selectionFeature);

        (docCtrl as WordDocCtrl).select();
    }

    /**
     * Checks if sub-editor functionality is currently active for a given document controller
     */
    isSubEditorActive(docCtr: WordDocCtrl): boolean {
        const toolbar = this.toolbars.get(docCtr.viewport.id);
        if (!toolbar) return false;
        return !toolbar.isToolDisable('SubEditorManagerTool');
    }

    /**
     * Supporter of the ContentVisibilityCheckFeature so we can show a border when the document look empty
     */
    isContentVisible(docCtrl: WordDocCtrl): boolean {
        return !docCtrl.wordLib.isContentEmpty();
    }

    /**
     * @inheritdoc
     */
    async initialize(): Promise<void> {
        await this.wcoord.initialize();
    }

    /**
     * @inheritdoc
     */
    async duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>> {
        return await this.wordGateway.duplicateDoc(docGlobalIds);
    }

    /**
     * Supporter of the HistoryFeature to perform undo action
     */
    async undo(item: WordHistoryItem) {
        switch (item.type) {
            case 'word-editing': {
                const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
                const docCtrl = docRegistry.getEntity(item.docId);
                docCtrl.wordLib.undoManager.undo();
                break;
            }
            case 'subviewport-editing': {
                const subVpHistoryItem = (item as WordSubViewportHistoryItem).originalItem;
                await subVpHistoryItem.supporter.undo(subVpHistoryItem);
                break;
            }
            default:
                break;
        }
    }

    /**
     * Supporter of the HistoryFeature to perform redo action
     */
    async redo(item: WordHistoryItem) {
        switch (item.type) {
            case 'word-editing': {
                const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
                const docCtrl = docRegistry.getEntity(item.docId);
                docCtrl.wordLib.undoManager.redo();
                break;
            }
            case 'subviewport-editing': {
                const subVpHistoryItem = (item as WordSubViewportHistoryItem).originalItem;
                await subVpHistoryItem.supporter.redo(subVpHistoryItem);
                break;
            }
            default:
                break;
        }
    }

    async clearHistory(viewportId: ViewportId): Promise<void> {
        this.historyFeature?.clear(this, viewportId);
    }

    /**
     * Supporter of the HistoryFeature to perform necessary cleanup when destroying a history item.
     * Here we need to clean up the restorable data associated with the CRUD history item
     */
    async destroyHistoryItem(item: HistoryItem): Promise<void> {
        const crdItem = item as CRDHistoryItem;
        const type = crdItem?.type;
        if (['create-doc', 'remove-doc', 'insert-doc'].includes(type)) {
            const vmId = crdItem.vm.id;
            const crdChanges = crdItem.crudChanges.filter(c => c.editor.editorType === 'WordEditor');
            for (const crdChange of crdChanges) {
                for (const change of crdChange.expectedChanges) {
                    const restorable = this.getRestorable(vmId, change.localId);
                    if (restorable) {
                        destroyRestorable(restorable);
                        this.removeRestorable(vmId, change.localId);
                    }
                }
            }
        }
    }

    /**
     * Supporter for the custom HTML export feature.
     * @returns The selectors for elements to perform custom conversion before export to HTML.
     */
    getCustomHtmlExportSelectors(target: ExportTarget): string[] {
        const selectors = new Set<string>();
        this.wcoord.editors.forEach(editor => {
            if (editor.isSupportFeature(FEATURE_HTML_EXPORT)) {
                const htmlExportFeature = editor.featureSupporter<SupportHtmlExportFeature>(FEATURE_HTML_EXPORT);
                if (htmlExportFeature) {
                    htmlExportFeature.getCustomHtmlExportSelectors(target).forEach(selector => selectors.add(selector));
                }
            }
        });
        return [...selectors];
    }

    /**
     * Supporter for the custom HTML export feature. Word itself don't need any custom conversion,
     * but it need to gather the custom conversion functions from child editors in word coordinator.
     */
    async customHtmlExportConverter(node: HTMLElement, target: ExportTarget): Promise<HTMLElement | null> {
        const convertPromises: Promise<HTMLElement | null>[] = [];
        this.wcoord.editors.forEach(editor => {
            if (editor.isSupportFeature(FEATURE_HTML_EXPORT)) {
                const htmlExportFeature = editor.featureSupporter<SupportHtmlExportFeature>(FEATURE_HTML_EXPORT);
                if (
                    htmlExportFeature &&
                    typeof htmlExportFeature.customHtmlExportConverter === 'function' &&
                    htmlExportFeature.getCustomHtmlExportSelectors(target).some(selector => node.matches(selector))
                )
                    convertPromises.push(htmlExportFeature.customHtmlExportConverter(node, target));
            }
        });

        return (await Promise.all(convertPromises)).find(Boolean) ?? null;
    }

    /**
     * @inheritdoc
     *
     * Supporter of the RemoveFeature to allow custom removal on the word document content
     */
    async remove(vpId: ViewportId, isCutting?: boolean): Promise<boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(vpId);
        if (currDocs.length !== 1) return false;

        const docCtr = currDocs[0];
        docCtr.wordLib.lexical.focus();

        return docCtr.wordLib.lexical.getEditorState().read(() => {
            const selectionCtx = docCtr.wordLib.contextManager.selectionContext$.value;

            // if not selected element, then return false (meaning remove tool take care for document)
            if (!selectionCtx) return false;

            const nodeKeys = selectionCtx.elementKeys;

            // if not selected element inside lexical then return true (meaning lexical take care itself)
            if (!nodeKeys || nodeKeys.length !== 1) return false;

            const node = $getNodeByKey(nodeKeys[0]);
            if ($isSubViewportNode(node)) {
                const nodeEditor = this.wcoord.editorByType(node.editorType);
                const nodeVmId = node.viewportId;

                let removePromise: Promise<boolean> | undefined = undefined;
                if (nodeEditor.isSupportFeature(FEATURE_REMOVE)) {
                    const removeFeature = nodeEditor.featureSupporter<SupportRemoveFeature>(FEATURE_REMOVE);
                    if (removeFeature) {
                        removePromise = removeFeature.remove(nodeVmId, isCutting);
                    }
                }

                (removePromise ?? Promise.resolve(false)).then(handled => {
                    if (!handled) docCtr.wordLib.dispatchCommand(DELETE_SUBVIEWPORTS_COMMAND, [nodeVmId]);
                });
            } else {
                const selection = $getSelection();
                if ($isRangeSelection(selection) && selection.isCollapsed()) return false;

                docCtr.wordLib.dispatchCommand(DELETE_CHARACTER_COMMAND, true);
            }

            return true;
        });
    }

    /**
     * @inheritdoc
     *
     * Supporter of the CopyFeature to allow custom copy on the word document content
     */
    async copy(sourceVpId: ViewportId): Promise<CopyPasteDTO | boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(sourceVpId);
        if (currDocs.length !== 1) return undefined;

        const docCtr = currDocs[0];

        docCtr.wordLib.lexical.focus();
        return docCtr.wordLib.lexical.getEditorState().read(() => {
            const selection = $getSelection();

            // let copy-paste tool handle when selection is empty range
            if ($isRangeSelection(selection) && selection.isCollapsed()) return false;

            docCtr.wordLib.dispatchCommand(COPY_COMMAND, null);

            return true; // `true` to indicate that lexical will handle itself
        });
    }

    /**
     * @inheritdoc
     *
     * Supporter of the PasteFeature to allow custom paste by API on the word document content
     */
    async paste(targetVpId: ViewportId, copiedDTO?: CopyPasteDTO): Promise<CopyPasteDTO | boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(targetVpId);
        if (currDocs.length !== 1) return false;
        const docCtr = currDocs[0];
        const selection = docCtr.wordLib.contextManager.selectionContext$.value;

        // if not selected element, then return false (meaning copy paste tool take care for document)
        if (!selection) return false;

        // if focus on doc. let lexical handle itself
        const lexicalInst = docCtr.wordLib.lexical;
        if (!!copiedDTO && isSubViewportCopyPasteDTO(copiedDTO)) {
            lexicalInst.dispatchCommand(PASTE_SUBVIEWPORT_COMMAND, copiedDTO as SubViewportCopyPasteDTO);
        } else {
            // no DTO -> transfer the items of Clipboard Async API into a fake ClipboardEvent
            const items = await navigator.clipboard.read();
            if (!items?.length || !items[0].types.length) return false;

            const data = new DataTransfer();
            const item = items[0];
            for (const type of item.types) {
                // ignore any left-over DTO mime as it should be invalid (ex: DTO of Word doc which can not be handled here)
                if (type === VICLASS_GENERAL_DTO_MIME) continue;
                const dataString = await (await item.getType(type)).text(); // fast + small size -> no need Promise.all
                data.setData(type, dataString);
            }

            if (data.types.length === 0) return false;

            const prevSelection = lexicalInst.read(() => $getSelection());
            lexicalInst.focus(() => {
                lexicalInst.update(() => {
                    // try restore prev selection as it will jump to the end of doc after focus
                    if (prevSelection) $setSelection(prevSelection.clone());

                    lexicalInst.dispatchCommand(PASTE_COMMAND, new ClipboardEvent('paste', { clipboardData: data }));
                });
            });
        }

        return true;
    }

    /**
     * remove document internally, meaning just remove doc without sync cmd
     *
     * @param vpId
     * @param docId
     */
    async internalRemoveDoc(vpId: ViewportId, docId: DocLocalId) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vpId);
        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vpId, docId);
        const docCtrl = docRegistry.getEntity(docId);
        if (docCtrl) {
            // because the deselect method doesn't wait for blur, cannot use it directly
            await this.selectDelegator.doBlurDocCtrl(docCtrl);
            const context: SelectContext = {
                doc: docCtrl,
                supporter: this.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION),
                selectDetails: {},
            };

            this.selectionFeature.onDeselect(context);
            docRegistry.removeEntity(docId);
            layerRegistry.removeEntity(docCtrl.layer.state.id);
            const restorable = docCtrl.onRemove();

            // store the restorable on the presenter side only as it's needed for undo/redo
            const vm = this.coordinator.getViewportManager(vpId);
            if (vm.mode === 'EditMode') {
                this._restorables.set(`${vpId}_${docId}`, restorable);
            } else {
                destroyRestorable(restorable);
            }
        }
    }

    get coordinator(): EditorCoordinator {
        return this._coordinator;
    }

    get cmdChannel(): CmdChannel {
        return this._cmdChannel;
    }

    get wordGateway(): WordGateway {
        return this._wordGateway;
    }

    async sendCommand(cmd: Cmd<any>): Promise<void> {
        await this._cmdChannel.receive(cmd);
    }

    /**
     * @inheritdoc
     */
    async loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByGlobalId(
            this._cmdChannel.channelCode,
            globalId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext);
    }

    /**
     * @inheritdoc
     */
    async loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByLocalId(
            this._cmdChannel.channelCode,
            localId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext, localId);
    }

    /**
     * @inheritdoc
     */
    async reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        await this.loadDocumentByLocalId(localId, loadingContext);
    }

    /**
     * @inheritdoc
     */
    getDocumentContentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext): Promise<FetchDocResponse> {
        return loadingContext.connector.loadDocumentByGlobalId(this._cmdChannel.channelCode, globalId, 'json');
    }

    /**
     * Create a new document controller from the load doc response data
     */
    private async createDocumentCtrlFromResponseData(
        response: FetchDocResponse,
        loadingContext: LoadingContext,
        localId?: DocLocalId
    ): Promise<WordDocCtrl> {
        const docRegistry = this.regDelegator.getOrCreateDocReg(loadingContext.vm.id);

        // in scenarios where documents are not loaded by localId but only
        // global id is provided, we generate a local id for usage; it doesn't matter anyway
        if (!localId) localId = docRegistry.getAndIncrementId();

        const doc = new WordDoc(localId, response.id, response.viewportElClass, response.content, response.version);

        const layerId = 1; // only one layer per document, so layer id is always 1

        const state = new WordLayer(layerId, localId); // initially, the layer state doesn't have a boundary

        /**
         * Request the layer used by the document controller from the viewport
         * Provide what we know about the layer. The actual positioning of the layer
         * on the board is determined by the viewport.
         *
         * In the case of the classroom, the viewport manager will get the position from the
         * doc mapping, in another case, the layer will be created in a default position with
         * the provided width / height
         */
        state.zindex = loadingContext.zIndexes[0];
        const layerCtrl = loadingContext.vm.requestLayer(DOMElementLayerCtrl, true, {
            docLocalId: localId,
            docGlobalId: response.id,
            viewport: loadingContext.vm,
            editor: this,
            state: state,
            domElType: 'div',
        }) as DOMElementLayerCtrl;

        state.boundary = layerCtrl.boundary;

        // let's start creating the doc controller
        const docCtrl = new WordDocCtrl(this, doc, loadingContext.vm, loadingContext);

        docRegistry.addEntity(doc.id, docCtrl);

        // link the layer with the doc controller
        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);

        docCtrl.setContent(response.content, response.settingJSON, response.headingOverridesJSON);

        const layerRegistry = this.regDelegator.getOrCreateLayerReg(loadingContext.vm.id, localId);
        layerRegistry.addEntity(1, layerCtrl);
        loadingContext.vm.addLayer(layerCtrl);

        return docCtrl;
    }

    /**
     * @inheritdoc
     */
    async start() {
        await this._cmdProcessor.start();
        await this._cmdChannel.start();
        await this.wcoord.start();
    }

    /**
     * @inheritdoc
     */
    featureSupporter<T>(featureKey: string): T {
        if (
            [
                FEATURE_HISTORY,
                FEATURE_COPYPASTE,
                FEATURE_REMOVE,
                FEATURE_CONTENT_VISIBILITY,
                FEATURE_AWARENESS,
                FEATURE_ATTACHMENT, // no need to actually implement supporter on this yet
                FEATURE_HTML_EXPORT,
            ].includes(featureKey)
        )
            return this as unknown as T;

        if (featureKey == FEATURE_ROB) return this.robFeature as T;
        if (featureKey == FEATURE_CRUD) return this.crudDelegator as T;
        if (featureKey == FEATURE_SELECTION) return this.selectDelegator as T;

        throw new Error(`Word Editor doesn't support feature ${featureKey}`);
    }

    /**
     * @inheritdoc
     */
    isSupportFeature(featureKey: string): boolean {
        return [
            FEATURE_SELECTION,
            FEATURE_ROB,
            FEATURE_HISTORY,
            FEATURE_CRD_DOC,
            FEATURE_CRUD,
            FEATURE_COPYPASTE,
            FEATURE_REMOVE,
            FEATURE_CONTENT_VISIBILITY,
            FEATURE_AWARENESS,
            FEATURE_ATTACHMENT,
            FEATURE_HTML_EXPORT,
        ].includes(featureKey);
    }

    /**
     * @inheritdoc
     */
    onFeatureInitialization(featureKey: string, feature: any): Promise<void> {
        switch (featureKey) {
            case FEATURE_SELECTION: {
                this.selectionFeature = feature as SelectionFeature;
                this.boundaryDelegator.setSelectionFeature(this.selectionFeature);
                break;
            }
            case FEATURE_HISTORY: {
                this.historyFeature = feature as HistoryFeature;
                break;
            }
            case FEATURE_ROB: {
                this.robFeature = feature as ROBFeature;
                break;
            }
            case FEATURE_CRD_DOC: {
                this.crdFeature = feature as DocCRDFeature;
                this.crdFeature.registerEditor(this, this.cmdChannel);
                break;
            }
            case FEATURE_CONTENT_VISIBILITY: {
                this.contentVisibilityFeature = feature as ContentVisibilityCheckFeature;
                break;
            }
            case FEATURE_AWARENESS: {
                this.awarenessFeature = feature as AwarenessFeature;
                break;
            }
            case FEATURE_ATTACHMENT: {
                this.attachmentFeature = feature as AttachmentFeature;
                break;
            }
            default:
                break;
        }
        return Promise.resolve();
    }

    /**
     * Create a delegator to be used in the create doc tool on attach viewport
     */
    initCreateDocTool(viewport: ViewportManager) {
        const createToolType: WordToolType = 'CreateWordDocumentTool';
        return this.crdFeature.generateCRDToolForViewport(
            viewport.id,
            createToolType,
            this,
            this.cmdChannel,
            this.robFeature,
            this.selectionFeature
        );
    }

    /**
     * @inheritdoc
     */
    createToolbar(): ToolBar<any, any> {
        const tb = new WordToolBar(this);

        // floating ui is only available for a bounded document
        if (this.conf.docViewMode == 'bounded') tb.addTool('CreateWordDocumentTool', new WordCreateDocumentTool(this));
        tb.addTool('InsertionEditorTool', new DocInsertTool(this));
        tb.addTool('SubEditorManagerTool', new SubEditorManagerTool(this, this.wcoord));
        tb.addTool('ContentEditorTool', new ContentTool(this));
        tb.addTool('WordContextTool', new WordContextTool(this));
        tb.addTool('ListTool', new ListTool(this));
        tb.addTool('FormatTextTool', new FormatTextTool(this));
        tb.addTool('WordAlignmentTool', new WordAlignmentTool(this));
        tb.addTool('TextStylingTool', new TextStylingTool(this));
        tb.addTool('WordTableTool', new WordTableTool(this));
        tb.addTool('BlockTypeTool', new BlockTypeTool(this));
        tb.addTool('LayoutTool', new LayoutTool(this));
        tb.addTool('WordSettingsTool', new WordSettingsTool(this));
        tb.addTool('HeadingWrapperTool', new HeadingWrapperTool(this));

        return tb;
    }

    /**
     * To be called from the CRD delegator to init the document
     */
    async generateInitDocData(curChanges: CRUDChangeResult, insertDoc: FCInsertDocCmd, insertLayer: FCInsertLayerCmd) {
        const response = await this.wordGateway.createDoc();
        const initData: WordDocInitData = {
            viewportElClass: response.viewportElClass,
            content: response.content,
        };

        const encoder = new TextEncoder();
        const bytes = encoder.encode(JSON.stringify(initData));
        insertDoc.state.setInitdata(bytes);
        insertDoc.state.setGlobalId(response.id);
    }

    /**
     * Util function to add history item to the history manager
     */
    addHistoryItem(item: WordHistoryItem) {
        if (!this.historyFeature) return; // if the history feature is not initialized, we simply ignore
        const manager = this.historyFeature.getHistoryManager(item.viewportId);

        manager.push(item);
    }

    /**
     * Notify the content visibility feature about a content visibility possible change
     */
    notifyContentVisibilityChange(docCtrl: WordDocCtrl) {
        this.contentVisibilityFeature?.onContentVisibilityChange(docCtrl);
    }
}

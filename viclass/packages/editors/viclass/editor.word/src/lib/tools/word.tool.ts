import {
    DefaultVDocObjCtrl,
    DocLocalId,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventListener,
    MouseHandlingItem,
    NativeEventTarget,
    PointerHandlingItem,
    Tool,
    ToolBar,
    ToolState,
    UserInputHandlerType,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { WordDocCtrl } from '../docs/word.doc.ctrl';
import { WordKeyboardEvent } from '../model';
import { WordEditor } from '../word.editor';
import { wordDocReg } from '../word.utils';
import { WordToolType } from './models';
import { WordToolBar } from './word.toolbar';

export abstract class WordTool<TState extends ToolState, OCtrl extends DefaultVDocObjCtrl<any, any>> implements Tool {
    readonly type: UserInputHandlerType = 'Tool';
    childToolbar?: ToolBar<any, Tool>;
    abstract readonly toolType: WordToolType;
    abstract readonly toolState: TState;
    readonly mouseHandling: MouseHandlingItem[] = [];
    readonly keyboardHandling: KeyboardHandlingItem[] = [];
    readonly pointerHandling: PointerHandlingItem[] = [];

    toolbar: WordToolBar;

    constructor(public editor: WordEditor) {}

    mouseHandler: MouseEventListener<NativeEventTarget<any>>;
    readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public tool: WordTool<ToolState, any>) {}

        onEvent(event: WordKeyboardEvent): WordKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleKeyboardEvent(event);
        }
    })(this);

    protected getWordDoc(vm: ViewportManager, localId: DocLocalId): WordDocCtrl {
        return this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id))?.getEntity(localId);
    }

    registerMouseHandling(...handling: MouseHandlingItem[]) {
        this.mouseHandling.push(...handling);
    }

    registerKeyboardHandling(...handling: KeyboardHandlingItem[]) {
        this.keyboardHandling.push(...handling);
    }

    registerPointerHandling(...handling: PointerHandlingItem[]) {
        this.pointerHandling.push(...handling);
    }

    registerToolbar(toolbar: WordToolBar) {
        this.toolbar = toolbar;
    }

    onBlur() {}
    onFocus() {
        // refocus on the lexical doc to continue typing
        const focusedDocs = this.getFocusedWordDocCtrls();
        if (focusedDocs.length === 1) {
            focusedDocs[0].wordLib.lexical.focus();
        }
    }

    onDisable() {}
    onEnable() {}

    onAttachViewport() {}

    onDetachViewport() {}

    focusAble(vpId: ViewportId): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;

        return this.editor.selectDelegator.getFocusedDocs(vpId)?.length == 1 && true;
    }

    protected getFocusedWordDocCtrls(): WordDocCtrl[] {
        const vm = this.toolbar.viewport;

        if (!vm) {
            throw new Error(
                'The toolbar of the word editor has not been attached to any viewport. Cannot insert document!'
            );
        }

        return this.editor.selectDelegator.getFocusedDocs(vm.id);
    }

    protected executeInFocusedDocCtrl(cb: (doc: WordDocCtrl) => any) {
        const docCtrls = this.getFocusedWordDocCtrls();
        if (!docCtrls?.length) return;

        cb(docCtrls[0]);
        docCtrls[0].wordLib.lexical.focus();
    }

    handleKeyboardEvent(event: WordKeyboardEvent): WordKeyboardEvent {
        return event;
    }
}

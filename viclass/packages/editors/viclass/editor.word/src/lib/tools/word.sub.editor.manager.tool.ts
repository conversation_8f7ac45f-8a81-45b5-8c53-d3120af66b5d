import {
    <PERSON><PERSON><PERSON><PERSON>,
    CoordinatorE<PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    EditorAddedCES,
    EditorType,
    FEATURE_SELECTION,
    SelectContext,
    SelectTool,
    SupportSelectFeature,
    ToolBar,
    ToolEventData,
    ToolEventListener,
    ToolState,
    VEventListener,
} from '@viclass/editor.core';
import { WordEditorCoordinator } from '../coord/word.coordinator';
import { HandlerAttachmentManager } from '../handler.attachment.manager';
import { WordTool, WordToolType } from '../tools';
import { WordEditor } from '../word.editor';

export class SubEditorManagerToolState implements ToolState {
    private subToolbars = new Map<EditorType, DefaultToolBar<any, any>>();
    currentEditor: EditorType;

    hasEditor(edType: EditorType) {
        return this.subToolbars.has(edType);
    }

    remove(edType: EditorType) {
        this.subToolbars.delete(edType);
    }

    existingEditors(): IterableIterator<EditorType> {
        return this.subToolbars.keys();
    }

    setEditorToolbar(edType: EditorType, tb: DefaultToolBar<any, any>) {
        this.subToolbars.set(edType, tb);
    }

    getEditorToolbar(edType: EditorType): DefaultToolBar<any, any> {
        return this.subToolbars.get(edType);
    }
}

/**
 * This tool initializes all the toolbar of the editors used by the word editor.
 * The word editor tool UI will base on this toolbars to create the necessary UI for the sub editor and connect to them
 */
export class SubEditorManagerTool extends WordTool<ToolState, any> {
    override readonly toolType: WordToolType = 'SubEditorManagerTool';
    override toolState = new SubEditorManagerToolState();

    private readonly subCoordEventListener: VEventListener<CoordinatorEvent>;
    public readonly commonToolbar: CommonToolbar;

    // for each internal viewport, if its internal document is selected, we store the select context here
    private curSelectContext = new Map<string, SelectContext>();

    readonly ham: HandlerAttachmentManager;

    constructor(
        override editor: WordEditor,
        private wcoord: WordEditorCoordinator
    ) {
        super(editor);
        this.subCoordEventListener = new this.SubCoordEventListener(this);
        wcoord.registerCoordEventListener(this.subCoordEventListener);
        this.refreshToolbarList();
        this.commonToolbar = wcoord.createToolbar() as CommonToolbar;
        this.ham = new HandlerAttachmentManager(this.wcoord, this, this.commonToolbar);
    }

    private SubCoordEventListener = class implements VEventListener<CoordinatorEvent> {
        constructor(private _p: SubEditorManagerTool) {}

        async onEvent(eventData: CoordinatorEvent): Promise<CoordinatorEvent> {
            if (eventData.eventType === 'editor-added') {
                this._p.refreshToolbarList();
                const editor = (eventData.state as EditorAddedCES).editor;
                await editor.start(); // if the sub editor was added after the word editor has started, we have to start it here
                return eventData;
            }

            if (eventData.eventType === 'editor-removed') {
                this._p.refreshToolbarList();
                return eventData;
            }

            if (this._p.toolbar.isDisabled() || this._p.toolbar.isToolDisable(this._p.toolType)) return eventData;

            switch (eventData.eventType as CoordinatorEventType) {
                case 'viewport-focusin': {
                    const containingDoc = this._p.wcoord.containingDoc.get(eventData.state.vmId);
                    // we not process this event if the sub viewport being focus is not inside a parent viewport of the tool
                    if (!containingDoc || this._p.toolbar.viewport != containingDoc.viewport) return eventData;

                    const vm = this._p.wcoord.getViewportManager(eventData.state.vmId);

                    const el = vm.rootEl;
                    const edType = el.dataset['edType'] as EditorType;
                    el.focus(); // focus on the editor

                    const edToolbar = this._p.toolState.getEditorToolbar(edType as EditorType);
                    edToolbar.attachViewport(vm);
                    edToolbar.registerToolListener(this._p.toolListener);
                    this._p.commonToolbar.attachViewport(vm);

                    this._p.wcoord.focusEditor(edType as EditorType, vm);
                    const switchModePromise = this._p.wcoord.switchViewportMode(vm.id, containingDoc.viewport.mode);

                    // wait for the viewport mode to be switch before select doc
                    // so that the toolbar is ready to handle the incoming event from selection (tb enable/disable based on vp mode)
                    // ! Must use `then` instead of `await` because the `viewport-focusin` event's promise need to be resolved first
                    switchModePromise.then(() => {
                        const editor = this._p.wcoord.editorByType(edType);
                        if (editor.isSupportFeature(FEATURE_SELECTION)) {
                            // add select tool into selection feature for the viewport first
                            // so that the unselection can be done later
                            this._p.wcoord.selectionFeature.addSelectTool(
                                vm.id,
                                this._p.commonToolbar.getTool('select') as SelectTool
                            );
                            const supporter = editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION);
                            const docCtrl = editor.findDocumentByGlobalId(vm.id, el.dataset['globalId']);
                            this._p.curSelectContext.set(vm.id, supporter.selectDoc(docCtrl));
                        }

                        this._p.showEditorToolBar(edType as EditorType);
                    });
                    break;
                }
                case 'viewport-focusout': {
                    const containingDoc = this._p.wcoord.containingDoc.get(eventData.state.vmId);
                    // we not process this event if the sub viewport being focus is not inside a parent viewport of the tool
                    if (!containingDoc || this._p.toolbar.viewport != containingDoc.viewport) return eventData;

                    const vm = this._p.wcoord.getViewportManager(eventData.state.vmId);

                    const el = vm.rootEl;
                    const edType = el.dataset['edType'] as EditorType;
                    const editor = this._p.wcoord.editorByType(edType);

                    this._p.hideAllEditorToolBar();

                    if (editor.isSupportFeature(FEATURE_SELECTION)) {
                        const supporter = editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION);
                        const selectContext = this._p.curSelectContext.get(vm.id);

                        if (selectContext) {
                            supporter.deselectDoc(selectContext.doc);
                            this._p.curSelectContext.delete(vm.id);
                        }
                        // clean up
                        this._p.wcoord.selectionFeature.removeSelectTool(vm.id);
                    }

                    const edToolbar = this._p.toolState.getEditorToolbar(edType as EditorType);
                    edToolbar.detachViewport(vm);
                    edToolbar.unregisterToolListener(this._p.toolListener);
                    this._p.commonToolbar.detachViewport(vm);

                    this._p.wcoord.blurEditor(edType as EditorType, vm);
                    this._p.wcoord.switchViewportMode(vm.id, 'Disabled');
                    break;
                }
                case 'viewport-removed': {
                    this._p.hideAllEditorToolBar();
                    break;
                }
            }
            return eventData;
        }
    };

    /**
     * Show the toolbar of the sub editor
     * @param edType the editor type of the sub toolbar to show
     */
    showEditorToolBar(edType: EditorType) {
        this.toolState.currentEditor = edType;
        this.toolbar.update(this.toolType, this.toolState);
    }

    /**
     * Hide all the sub editor toolbar so we can switch to the word toolbar
     */
    hideAllEditorToolBar() {
        delete this.toolState.currentEditor;
        this.toolbar.update(this.toolType, this.toolState);
    }

    override onDisable(): void {
        super.onDisable();
        this.ham.setActive(false);
    }

    override onEnable(): void {
        super.onEnable();
        this.ham.setActive(true);
    }

    /**
     * Override the onBlur method to clear the focus on all tools of the sub-editor toolbar
     */
    override onBlur(): void {
        super.onBlur();
        if (this.toolState.currentEditor) {
            this.toolState.getEditorToolbar(this.toolState.currentEditor)?.clearAllFocus();
        }
    }

    // create / remove toolbars for all the sub editor inside the word coordinator
    private refreshToolbarList() {
        let change = false;

        const subEditors = this.wcoord.editors;

        for (const et of subEditors.keys()) {
            if (!this.toolState.hasEditor(et)) {
                change = true;
                const ed = subEditors.get(et)!!;
                const etb = ed.createToolbar();
                this.toolState.setEditorToolbar(et, etb as DefaultToolBar<any, any>);
            }
        }

        for (const et of this.toolState.existingEditors()) {
            if (subEditors.has(et)) continue;
            else {
                change = true;
                this.toolState.remove(et);
            }
        }

        // this method could be called at the construction time when toolbar has not yet been set
        // need to check if the toolbar is there before update state
        if (change && this.toolbar) {
            this.toolbar.update(this.toolType, this.toolState);
        }
    }

    /**
     * Listen to tool event from the child editor toolbar.
     * Currently use to focus on the SubEditorManagerTool when any child editor tool is focused.
     * So the other tool of the root coord (i.g. select, pan tool) can be blurred
     */
    private readonly toolListener = new (class implements ToolEventListener<ToolBar<any, any>, any> {
        constructor(private m: SubEditorManagerTool) {}

        onEvent(eventData: ToolEventData<ToolBar<any, any>, any>): ToolEventData<ToolBar<any, any>, any> {
            if (this.m.toolbar.isDisabled() || this.m.toolbar.isToolDisable(this.m.toolType)) return eventData;

            if (eventData.eventType === 'focus') {
                this.m.toolbar.focus(this.m.toolType);
            }

            return eventData;
        }
    })(this);
}

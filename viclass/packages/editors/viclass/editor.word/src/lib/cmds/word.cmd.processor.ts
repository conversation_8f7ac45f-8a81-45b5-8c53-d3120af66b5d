import {
    AbstractCommand,
    CmdMeta,
    CmdOriginType,
    CmdProcessor,
    DOMElementLayerCtrl,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    ViewportManager,
} from '@viclass/editor.core';
import { WordCmdTypeProto } from '@viclass/proto/editor.word';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { WordDocCtrl } from '../docs/word.doc.ctrl';
import { WordDoc, WordDocSettings, WordLayer } from '../model';
import { WordEditor } from '../word.editor';
import { WordDocInitData } from '../word.models';
import { AutoExpiringCache, reconnectUint8Array, wordDocReg, wordLayerReg } from '../word.utils';
import {
    ResetHeadingOverridesCmd,
    SyncLexicalAwarenessUpdateCmd,
    SyncLexicalUpdateCmd,
    SyncWordDocScrollCmd,
    UpdateDocSettingsCmd,
    UpdateHeadingOverridesCmd,
    WrappingSubEditorCmd,
} from './word.cmd';
import { WordWrappingCmdGateway } from './word.wrapping.cmd.gateway';

/**
 * Buffer TTL in miliseconds, will reset once another chunk come in.
 * We will attempt to reload the Y.Doc from server when a update buffer expired
 */
const BUFFER_TTL_MS = 30 * 1000; // 30s

type BufferChunk = {
    buffer: Uint8Array;
    chunkIndex: number;
};

type UpdateBuffer = {
    chunks: BufferChunk[];
    totalChunks: number;
    docCtrl: WordDocCtrl;
};

export class WordCmdProcessor extends CmdProcessor {
    private updateCache = new AutoExpiringCache<string, UpdateBuffer>(this.onUpdateBufferExpired.bind(this));

    constructor(private editor: WordEditor) {
        super();
    }

    async processCmd(
        cmd: AbstractCommand<WordCmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<WordCmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case FCCmdTypeProto.INSERT_DOC: {
                return this.processInsertDocCmd(cmd as FCInsertDocCmd);
            }
            case FCCmdTypeProto.PREVIEW_BOUNDARY: {
                this.processNewDocBoundaryCmd(cmd as FCPreviewBoundaryCmd);
                break;
            }
            case FCCmdTypeProto.INSERT_LAYER: {
                this.processInsertLayerCmd(cmd as FCInsertLayerCmd);
                break;
            }
            case FCCmdTypeProto.REMOVE_DOC: {
                await this.processRemoveDocCmd(cmd as FCRemoveDocCmd);
                break;
            }
            case FCCmdTypeProto.RELOAD_DOC: {
                return this.processReloadCmd(cmd as FCReloadDocCmd);
            }
            case FCCmdTypeProto.UPDATE_BOUNDARY: {
                return this.processUpdateBoundaryCmd(cmd as FCUpdateDocCmd);
            }

            case WordCmdTypeProto.WRAPPING_SUB_EDITOR_CMD: {
                this.processWrappingCmd(cmd as WrappingSubEditorCmd);
                break;
            }
            case WordCmdTypeProto.SYNC_LEXICAL_UPDATE: {
                this.processSyncLexicalUpdate(cmd as SyncLexicalUpdateCmd);
                break;
            }
            case WordCmdTypeProto.SYNC_LEXICAL_AWARENESS: {
                this.processSyncLexicalAwareness(cmd as SyncLexicalAwarenessUpdateCmd);
                break;
            }
            case WordCmdTypeProto.SYNC_WORD_DOC_SCROLL: {
                this.processSyncWordDocScroll(cmd as SyncWordDocScrollCmd);
                break;
            }
            case WordCmdTypeProto.UPDATE_DOC_SETTINGS: {
                this.processUpdateDocSettings(cmd as UpdateDocSettingsCmd);
                break;
            }
            case WordCmdTypeProto.UPDATE_HEADING_OVERRIDES: {
                this.processUpdateHeadingOverrides(cmd as UpdateHeadingOverridesCmd);
                break;
            }
            case WordCmdTypeProto.RESET_HEADING_OVERRIDES: {
                this.processResetHeadingOverrides(cmd as ResetHeadingOverridesCmd);
                break;
            }
            default:
                break;
        }
        return cmd;
    }

    /**
     * Process sync lexical update include modify the text content and CRUD on the child nodes.
     * The updates are binary encoded by yjs
     */
    processSyncLexicalUpdate(cmd: SyncLexicalUpdateCmd) {
        try {
            const vm = cmd.meta.viewport;
            const docRegistry = this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id));
            const docCtrl = docRegistry.getEntity(cmd.meta.versionable);
            const docEditor = docCtrl.wordLib;

            let update = cmd.state.getUpdate_asU8();
            if (cmd.isPartial()) {
                // store the chunk into cache
                const key = `${vm.id}_${docCtrl.state.id}_${cmd.getPartialId()}`;
                const buffer: UpdateBuffer = this.updateCache.has(key)
                    ? this.updateCache.get(key)
                    : {
                          chunks: [],
                          totalChunks: cmd.state.getTotalChunks(),
                          docCtrl: docCtrl,
                      };
                buffer.chunks.push({
                    buffer: update,
                    chunkIndex: cmd.state.getChunkIndex(),
                });
                this.updateCache.set(key, buffer, BUFFER_TTL_MS);

                // process completed chunks
                if (buffer.chunks.length !== buffer.totalChunks) return;
                const sortedChunks = buffer.chunks
                    .sort((a, b) => a.chunkIndex - b.chunkIndex)
                    .map(chunk => chunk.buffer);

                update = reconnectUint8Array(sortedChunks);
                this.updateCache.delete(key);
            }

            if (cmd.meta.origin !== CmdOriginType.local) {
                if (cmd.state.hasUpdate()) {
                    docEditor.readUpdate(cmd.state.getYClientId(), update);
                }

                if (cmd.state.getForceUpdate()) {
                    docEditor.forceLexicalUpdate();
                    docEditor.loadPendingSubViewportNodes();
                }
            }

            if (cmd.state.hasAwarenessId()) {
                const awarenessId = cmd.state.getAwarenessId();
                this.editor.awarenessFeature?.getAwarenessTool(vm.id)?.clearAwarenessState(awarenessId, false);
            }

            this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        } catch (error) {
            console.error(error);
            console.log('sync cmd', cmd);
        }
    }

    /**
     * Process sync lexical awareness (cursor position, selection).
     * The updates are comming from @lexical/yjs package
     */
    processSyncLexicalAwareness(cmd: SyncLexicalAwarenessUpdateCmd) {
        if (cmd.meta.origin === CmdOriginType.local) return;

        const vm = cmd.meta.viewport;
        const docRegistry = this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id));

        // awareness is emitted from lexical, when the doc is deleted this might come later
        // than the entity deletion -> ignore as it's not needed
        if (!docRegistry.hasEntityId(cmd.meta.versionable)) return;
        const docCtrl = docRegistry.getEntity(cmd.meta.versionable);
        docCtrl.wordLib.readUpdate(cmd.state.getYClientId(), cmd.state.getUpdate_asU8());

        const cursorsContainer = docCtrl.wordLib.binding.cursorsContainer;
        if (cursorsContainer) {
            cursorsContainer.style.top = '0px';
            cursorsContainer.setAttribute('scrollAnchor', '1');
        }
    }

    /**
     * Sync scroll state of the document
     */
    processSyncWordDocScroll(cmd: SyncWordDocScrollCmd) {
        if (cmd.meta.origin === CmdOriginType.local) return;

        const vm = cmd.meta.viewport;
        const docRegistry = this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(cmd.meta.versionable);
        docCtrl.wordLib.rootElement.scrollTo({
            top: cmd.state.getScrollTop(),
            behavior: 'smooth',
        });
    }

    /**
     * Process update word document settings
     */
    processUpdateDocSettings(cmd: UpdateDocSettingsCmd) {
        const vm = cmd.meta.viewport;
        const docRegistry = this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id));

        cmd.state.getSettingsList().forEach(settingObj => {
            const localId = settingObj.getDocLocalId();
            const docCtrl = docRegistry.getEntity(localId);

            const settingJSON = settingObj.getSettingJson();
            const settings = JSON.parse(settingJSON) as WordDocSettings;

            docCtrl.applySettings(settings);
        });

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Process update word document heading overrides for the heading wrapper tool
     */
    processUpdateHeadingOverrides(cmd: UpdateHeadingOverridesCmd) {
        const vm = cmd.meta.viewport;
        const docRegistry = this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(cmd.meta.versionable);

        const overrideJson = cmd.state.getHeadingOverrideJson();
        docCtrl.replaceHeadingStyleFromJSON(overrideJson);

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Process reset word document heading overrides
     */
    processResetHeadingOverrides(cmd: ResetHeadingOverridesCmd) {
        const vm = cmd.meta.viewport;
        const docRegistry = this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(cmd.meta.versionable);

        docCtrl.resetHeadingStyles();

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Process the wrapped command from the sub editor
     */
    processWrappingCmd(cmd: WrappingSubEditorCmd) {
        // only processing remote wrapping command, because local one
        // is not supposed to be processed since it is produced by local editor
        // whose processor already process the underlying command
        if (cmd.meta.origin === CmdOriginType.remote) {
            const underlying = cmd.state.getUnderlying_asU8();
            (this.editor.wcoord.cmdGateway as WordWrappingCmdGateway).receive(underlying);
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private processNewDocBoundaryCmd(cmd: FCPreviewBoundaryCmd): FCPreviewBoundaryCmd {
        const result = this.editor.crdFeature.processDocCreationPreviewCmd(cmd, this.editor);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return result;
    }

    private async processInsertDocCmd(cmd: FCInsertDocCmd) {
        const initData = cmd.state.getInitdata() as Uint8Array;
        const decoder = new TextDecoder();
        const initObj: WordDocInitData = JSON.parse(decoder.decode(initData));

        if (cmd.meta.origin === CmdOriginType.remote && (!cmd.state.getGlobalId() || !initObj.viewportElClass))
            throw new Error('State data for new word document is not complete!');

        const doc = new WordDoc(cmd.meta.targetId, cmd.state.getGlobalId(), initObj.viewportElClass, initObj.content);

        this.editor.insertDocDelegator.insertDocCtrl(cmd.meta.viewport, doc);

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);

        return cmd;
    }

    private processInsertLayerCmd(cmd: FCInsertLayerCmd) {
        const layerId = cmd.meta.targetId;
        const docId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;
        const zindex = cmd.state.getZIndex();

        const docRegistry = this.editor.regMan.registry<WordDocCtrl>(wordDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(docId);

        const layerState = new WordLayer(layerId, docId, fcConvertProtoToBoundary(cmd.state.getBoundary()));

        const layerCtrl = docCtrl.viewport.requestLayer(DOMElementLayerCtrl, true, {
            boundary: layerState.boundary,
            docLocalId: docId,
            docGlobalId: docCtrl.state.globalId,
            viewport: docCtrl.viewport,
            editor: this.editor,
            state: layerState,
            domElType: 'div',
        }) as DOMElementLayerCtrl;

        layerCtrl.doc = docCtrl;
        layerCtrl.zindex = zindex;
        docCtrl.addLayer(layerCtrl);
        docCtrl.initializeLayerContent();

        const layerRegistry = this.editor.regMan.registry<DOMElementLayerCtrl>(
            wordLayerReg(cmd.meta.viewport.id, docId)
        );
        layerRegistry.addEntity(layerId, layerCtrl);

        this.editor.insertDocDelegator.notifyDocCreation(vm, docId);

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private async processRemoveDocCmd(cmd: FCRemoveDocCmd) {
        const l = cmd.state.getIdsList();
        for (const id of l) {
            await this.editor.internalRemoveDoc(cmd.meta.viewport.id, id.getLocalId());
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    async processReloadCmd(cmd: FCReloadDocCmd): Promise<FCReloadDocCmd> {
        const promises = cmd.state
            .getLocalidList()
            .map(localId => this.editor.crdFeature.reloadLocalDoc(this.editor, cmd.meta.viewport.id, localId));
        await Promise.all(promises);

        this.editor.insertDocDelegator.notifyDocCreation(cmd.meta.viewport, ...cmd.state.getLocalidList());
        return cmd;
    }

    private async processUpdateBoundaryCmd(cmd: FCUpdateDocCmd) {
        this.editor.boundaryDelegator.processUpdateBoundaryCmd(cmd, WordEditor.cmdChannelThrottle);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return cmd;
    }

    isLocalCmd(cmd: AbstractCommand<any>) {
        return cmd.meta.origin === CmdOriginType.local;
    }

    private onUpdateBufferExpired(key: string, buffer: UpdateBuffer) {
        console.log('Should reload', key, buffer);
        const docEditor = buffer.docCtrl?.wordLib;
        if (!docEditor) return;

        docEditor.onProviderDocReload(docEditor.yDoc);
    }

    private clearCurrentViewportHistoryIfRemoteCmd(meta: CmdMeta) {
        if (meta.origin === CmdOriginType.remote) this.editor.clearHistory(meta.viewport.id);
    }
}

import { REMOVE_LIST_COMMAND } from '@lexical/list';
import { $createHeadingNode, HeadingTagType } from '@lexical/rich-text';
import { $patchStyleText, $setBlocksType } from '@lexical/selection';
import {
    Bo<PERSON>ryRectangle,
    DefaultVDocCtrl,
    DocLocalContent,
    DocLocalId,
    DocumentId,
    DocumentViewMode,
    DOMElementLayerCtrl,
    EditorType,
    HasBoundaryCtrl,
    isCtrlOrMeta,
    KeyboardEventData,
    KeyboardEventListener,
    LoadingContext,
    MouseEventData,
    PanEventData,
    SelectHitContext,
    VDocLayerCtrl,
    VEventListener,
    ViewportId,
    ViewportManager,
    ZoomEventData,
} from '@viclass/editor.core';
import { HEADING_CLASS, HEADING_PREFIX, HeadingWrapperType } from '@viclass/editor.word.transform';
import { $createParagraphNode, $getSelection, FORMAT_ELEMENT_COMMAND, FORMAT_TEXT_COMMAND } from 'lexical';
import { BehaviorSubject, Subject, Subscription, tap, throttleTime } from 'rxjs';
import * as Y from 'yjs';
import { WordEditorConfig } from '../config';
import {
    INSERT_CUSTOM_ORDERED_LIST_COMMAND,
    INSERT_CUSTOM_UNORDERED_LIST_COMMAND,
    INSERT_LAYOUT_COMMAND,
    RELOAD_HEADINGS_COMMAND,
    UPDATE_SUBVIEWPORT_LOCAL_CONTENT_COMMAND,
    UPDATE_VIEWPORT_VIEW_STATE,
    WRAP_HEADING_COMMAND,
} from '../doceditor/plugins';
import { WordLib } from '../doceditor/word.lib';
import {
    AlignmentType,
    DEFAULT_SETTINGS,
    FetchDocResponse,
    FormatType,
    WordDoc,
    WordDocRestorable,
    WordDocSettings,
    WordLayer,
} from '../model';
import { HeadingWrapperStyle } from '../tools';
import { WordEditor } from '../word.editor';
import { loadCSS } from '../word.utils';

// prettier-ignore
const lexicalNativeHotkeys = new Set(['KeyI', 'KeyB', 'KeyU', 'KeyC', 'KeyV', 'KeyX', 'KeyZ', 'KeyY', 'Enter', 'Backspace', 'Delete']);
// prettier-ignore
const lexicalNotHandleKeys = new Set(['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12', 'Escape', 'PrintScreen']);

function isWordHandled(event: KeyboardEvent): boolean {
    const isCtrl = isCtrlOrMeta(event);
    if (isCtrl && lexicalNativeHotkeys.has(event.code)) return true;
    if (!isCtrl && !event.altKey) {
        if (event.code.startsWith('Key') || event.code.startsWith('Digit')) return true;
        if (!lexicalNotHandleKeys.has(event.code)) return true;
    }

    return false;
}

export class WordDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    // keyboard listener that listens for all keyboard events originating from the Word document body
    keyboardListener: KeyboardEventListener<any> = this.createKeyboardEventListener(this);

    layer: DOMElementLayerCtrl;

    root: HTMLDivElement;

    wordLib: WordLib;
    docMap: Map<string, Y.Doc>;

    settings: WordDocSettings;

    headingOverrides: Map<HeadingWrapperType, HeadingWrapperStyle> = new Map();

    readonly processingCount$ = new BehaviorSubject(0);

    private focusHandler = this.processFocus.bind(this);

    private docViewMode: DocumentViewMode;

    // --------------------

    constructor(
        override editor: WordEditor,
        public override state: WordDoc,
        viewport: ViewportManager,
        private loadingContext?: LoadingContext
    ) {
        super(state, editor, viewport);
        this.docViewMode = editor.conf.docViewMode;
    }

    /**
     * Add a layer to the document control. In word, we only use 1 layer
     */
    override addLayer(layer: VDocLayerCtrl): void {
        if (!(layer instanceof DOMElementLayerCtrl)) return;

        super.addLayer(layer);

        this.layer = layer;
        this.state.addLayer(layer.state as WordLayer);
    }

    /**
     * Apply settings of the word document. To be called after the document is created
     * or the setting changes
     */
    applySettings(settings: WordDocSettings) {
        this.settings = { ...settings };

        const lexicalRoot = this.wordLib.lexical.getRootElement();
        lexicalRoot.style.paddingLeft = `${settings.padding}px`;
        lexicalRoot.style.paddingRight = `${settings.padding}px`;
    }

    /**
     * Clean up the word document on remove
     */
    onRemove(): WordDocRestorable {
        if (this.headingOverrides.size > 0) {
            this.headingOverrides.clear();
            this.applyHeadingStyles();
        }

        this.deregisterInputEvent();
        const restorable = this.wordLib?.destroy();

        this.root.remove();
        delete this.root;

        this.state.layer = undefined;
        this.removeLayer(this.layer);
        this.viewport.removeLayer(this.layer);

        return restorable;
    }

    /**
     * Check if the mouse event hits the word document for the selection feature
     */
    checkHit(event: MouseEventData<any>, l: DOMElementLayerCtrl): SelectHitContext {
        const rect = l.domEl.getBoundingClientRect();
        const posX = event.nativeEvent.clientX;
        const posY = event.nativeEvent.clientY;

        if (rect.left < posX && posX < rect.right && rect.top < posY && posY < rect.bottom) {
            return {
                doc: this,
                hitDetails: undefined,
            };
        }
        return undefined;
    }

    /**
     * Disable the word document edit mode
     */
    disableEditMode() {
        const root = this.wordLib?.rootElement;
        // might not ready as the rootNode is created on iframe load event
        if (!root) return;

        this.wordLib.setEditable(false);
    }

    /**
     * Enable the word document edit mode
     */
    enableEditMode() {
        const root = this.wordLib?.rootElement;
        // might not ready as the rootNode is created on iframe load event
        if (!root) return;

        this.wordLib.setEditable(true);
    }

    /**
     * Register the input event for the word document.
     * Currently use to handle focus and blur events on the root element
     */
    registerInputEvent() {
        this.wordLib.rootElement.addEventListener('focus', this.focusHandler);
        this.wordLib.rootElement.addEventListener('blur', this.focusHandler);
    }

    /**
     * Deregister the input event for the word document on doc removal
     */
    deregisterInputEvent() {
        this.wordLib.rootElement.removeEventListener('focus', this.focusHandler);
        this.wordLib.rootElement.removeEventListener('blur', this.focusHandler);
    }

    /**
     * Process focus and blur events on the root element.
     * We will clear the internal viewport focus so when user focus into the word doc again,
     * the word toolbar will show up instead of the previous sub-editor toolbar
     */
    processFocus(event: Event) {
        switch (event.type) {
            case 'focus': {
                this.editor.wcoord.clearInternalViewportFocus(this);
                break;
            }
            case 'blur': {
                this.editor.wcoord.clearInternalViewportFocus(this);
                break;
            }
        }
    }

    /**
     * Trigger this method to insert a new embed viewport at the current selection position
     */
    processInsertViewportEvent(edType: EditorType) {
        const viewportId = this.viewportId(this.state.globalId, edType as EditorType);
        this.wordLib.insertViewport(viewportId, edType);
    }

    /**
     * Generate viewport id for the sub-viewport
     * @param parentId id of the parent word doc
     * @param edType editor type of sub-viewport
     * @returns the randomly generated viewport id
     */
    viewportId(parentId: DocumentId, edType: EditorType) {
        return `${parentId}_${edType}_${new Date().getTime()}${Math.floor(Math.random() * 10000)}`;
    }

    /**
     * Insert a new viewport into the provided DOM element,
     * which is the DOM generated by the SubViewportNode
     *
     * @param element the root element of the viewport
     * @param edType the editor type of the sub-viewport
     * @returns the viewport manager for the new viewport
     */
    insertViewport(element: HTMLElement, edType: EditorType): ViewportManager {
        const coord = this.editor.wcoord;
        return coord.createViewport(this, element, edType);
    }

    /**
     * Insert a new ordered list at selection
     *
     * @param listStyleType the type of the ordered list to insert
     */
    insertOrderedList(listStyleType: string) {
        this.wordLib.dispatchCommand(INSERT_CUSTOM_ORDERED_LIST_COMMAND, listStyleType);
    }

    /**
     * Insert a new unordered list at selection
     *
     * @param listStyleType the type of the unordered list to insert
     */
    insertUnorderedList(listStyleType: string) {
        this.wordLib.dispatchCommand(INSERT_CUSTOM_UNORDERED_LIST_COMMAND, listStyleType);
    }

    /**
     * Remove the list (ordered or unordered of different types) at selection
     */
    removeList() {
        this.wordLib.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }

    /**
     * Insert a new layout at selection (similar to column feature in MS Word)
     *
     * @param template the template of the layout to insert
     */
    insertLayout(template: string) {
        this.wordLib.dispatchCommand(INSERT_LAYOUT_COMMAND, template);
    }

    /**
     * Format the selected text with different types like bold, italic...
     */
    formatText(formatType: FormatType) {
        this.wordLib.dispatchCommand(FORMAT_TEXT_COMMAND, formatType);
    }

    /**
     * Style the selected text with CSS properties. Can be used to change font family, size, color...
     */
    textStyling(styles: Record<string, string>, skipHistoryStack?: boolean) {
        this.wordLib.lexical.update(
            () => {
                const selection = $getSelection();
                if (selection !== null) {
                    $patchStyleText(selection, styles);
                }
            },
            skipHistoryStack ? { tag: 'historic' } : {}
        );
    }

    /**
     * Align the selected text content (left, center, right...)
     */
    align(alignType: AlignmentType) {
        this.wordLib.dispatchCommand(FORMAT_ELEMENT_COMMAND, alignType);
    }

    /**
     * Wrap the selected text into a heading.
     * Heading is a predefined styling that can be overriden by the user.
     * All text node with the same heading type will share the same style
     */
    wrapHeading(wrapType: HeadingWrapperType) {
        this.wordLib.dispatchCommand(WRAP_HEADING_COMMAND, wrapType);
    }

    /**
     * Get the content of the word document from server.
     * The content format is yjs binary encoded in base64
     */
    async getWordDocContent(): Promise<FetchDocResponse | null> {
        if (!this.loadingContext) return null;

        return await this.editor.getDocumentContentByGlobalId(this.state.globalId, this.loadingContext);
    }

    /**
     * Listen to keyboard event to block some key events. Because word editor will handle all keys
     * so we block them from polute the parent viewport (ex: classroom)
     */
    private createKeyboardEventListener(_p: WordDocCtrl): KeyboardEventListener<any> {
        return new (class implements KeyboardEventListener<any> {
            onEvent(eventData: KeyboardEventData<any>): KeyboardEventData<any> {
                if (isWordHandled(eventData.nativeEvent)) {
                    eventData.continue = false;
                }

                return eventData;
            }
        })();
    }

    /**
     * Listen to zoom and pan events of sub-viewports to update the viewport state
     */
    createZoomPanListener(): VEventListener<ZoomEventData | PanEventData> {
        return new (class implements VEventListener<ZoomEventData | PanEventData> {
            throttler$ = new Subject<ZoomEventData | PanEventData>();
            subscription: Subscription;

            constructor(docCtrl: WordDocCtrl) {
                this.subscription = this.throttler$
                    .pipe(
                        throttleTime(100),
                        tap(data => {
                            if (docCtrl.viewport.mode === 'EditMode') {
                                const internalVmId = data.source.id;
                                const center = data.source.currentLookAt;
                                const zoom = data.source.zoomLevel;

                                docCtrl.wordLib.dispatchCommand(UPDATE_VIEWPORT_VIEW_STATE, {
                                    viewportId: internalVmId,
                                    lookAt: { ...center },
                                    zoom: zoom,
                                });
                            }
                        })
                    )
                    .subscribe();
            }

            onEvent(eventData: ZoomEventData | PanEventData): any {
                // only trigger if this is has uiEventSource (event comming from ZoomTool or PanTool).
                // otherwise it will be an infinite loop
                if (eventData.uiEventSource) {
                    this.throttler$.next(eventData);
                }
            }

            onUnregister? = () => {
                if (!this.subscription.closed) this.subscription.unsubscribe();
            };
        })(this);
    }

    /**
     * Handle unselect the word document, we will clear selection and disable editing here
     */
    unselect() {
        this.root.classList.remove('selected');
        this.wordLib.clearNodeSelection();
        this.disableEditMode();
        if (this.layer) this.viewport.sink(this.layer);
    }

    /**
     * Handle select the word document
     */
    select() {
        this.root.classList.add('selected');
        if (this.layer) this.viewport.float(this.layer);
    }

    /**
     * To be called form the cmd processor to update boundary of the doc ctrl
     */
    updateBoundary(boundary: BoundaryRectangle) {
        if (this.docViewMode == 'bounded') {
            this.state.layer.boundary = boundary;

            if (this.layer) this.layer.updateBoundary(boundary);
        }
    }

    /**
     * Init the word document content, optionally with an initial state
     */
    private initWordDocContent(initialState: Uint8Array) {
        const wordTheme = (this.editor.conf as WordEditorConfig).wordTheme;
        loadCSS(wordTheme, this.root.ownerDocument);

        const encapsulation = document.createElement('div');
        encapsulation.classList.add('word-encapsulation');
        encapsulation.dataset['globalId'] = this.state.globalId;

        this.root.appendChild(encapsulation);

        const container = this.createWordContainer();
        encapsulation.appendChild(container);

        const restorable = this.editor.getRestorable(this.viewport.id, this.state.id);
        this.wordLib = new WordLib(container, this.editor, this, restorable);
        this.wordLib.init(initialState);

        if (restorable) {
            this.editor.removeRestorable(this.viewport.id, this.state.id);
        }
    }

    /**
     * Create the container for the word document that will be use for lexical
     */
    private createWordContainer(): HTMLDivElement {
        const container = document.createElement('div');
        container.classList.add('vi-word-doc', 'vi-theme');
        container.contentEditable = 'true';

        // Restore scroll position on layout shift
        let scrollVal: number | undefined = undefined;
        // Restore focus on the container as it will lose focus on layout shift
        let refocus = false;
        this.layer.beforeLayoutShift = () => {
            scrollVal = container.scrollTop;
            refocus = document.activeElement === container;
        };

        this.layer.afterLayoutShift = () => {
            if (Number.isFinite(scrollVal)) {
                container.scrollTop = scrollVal;
            }
            scrollVal = undefined;

            if (refocus) {
                container.focus({ preventScroll: true });
            }
            refocus = false;
        };

        return container;
    }

    /**
     * Update doc mapping of the sub-viewports
     */
    updateInternalDocMapping(vmId: string, globalId: DocumentId, localId: number) {
        this.wordLib.updateSubViewportMapping(vmId, globalId, localId);
    }

    /**
     * Initialize the word document content
     */
    initializeLayerContent() {
        this.setContent(this.state.content);
    }

    /**
     * Replace the content of the word document with the given content
     * @param content word content as binary encoded in base64
     * @param settingJSON settings of the word document
     * @param headingOverridesJSON heading style overrides
     */
    setContent(content: string, settingJSON?: string, headingOverridesJSON?: string) {
        let wordContent: Uint8Array;
        if (content) {
            try {
                wordContent = Uint8Array.from(window.atob(content), char => char.charCodeAt(0));
            } catch (e) {
                console.warn('Failed to base64 decode content', e);
            }
        }

        if (!this.wordLib) {
            this.createRootElement();
            this.layer.domEl.appendChild(this.root);

            this.initWordDocContent(wordContent);
        } else if (!!wordContent) {
            this.wordLib.setContent(wordContent);
        }

        let settings = { ...DEFAULT_SETTINGS };
        if (settingJSON) {
            settings = { ...settings, ...JSON.parse(settingJSON) };
        }
        this.wordLib.loadPendingSubViewportNodes();
        this.applySettings(settings);
        this.replaceHeadingStyleFromJSON(headingOverridesJSON);
    }

    /**
     * Update local content of the sub-viewport in LOCAL mode (i.e. Math viewport)
     */
    updateSubVpLocalContent(vmId: ViewportId, localId: DocLocalId, content: DocLocalContent) {
        this.wordLib.dispatchCommand(UPDATE_SUBVIEWPORT_LOCAL_CONTENT_COMMAND, {
            viewportId: vmId,
            localId: localId,
            localContent: content,
        });
    }

    /**
     * Create the root element for the word document
     */
    private createRootElement() {
        this.root = document.createElement('div') as HTMLDivElement;
        this.root.classList.add('word-root');
        Object.assign(this.root.style, {
            padding: '0',
            margin: '0',
            width: '100%',
            height: '100%',
        });

        /**
         * We want the click & mousedown event to only limited inside the word doc
         * and not be processed by the parent viewport. Otherwise the SelectTool of parent viewport
         * will be triggered and accidentally de-select the word doc.
         */
        const handleRootElementEvent = (e: MouseEvent) => {
            e.stopPropagation();
            if (e.button === 1) {
                e.preventDefault(); // prevent middle mouse browser cursor
            }
        };
        this.root.addEventListener('click', handleRootElementEvent);
        this.root.addEventListener('mousedown', handleRootElementEvent);

        this.layer.domEl.appendChild(this.root);
    }

    get viewportElClass(): string {
        return this.state.viewportElClass;
    }

    /**
     * Set the block type of the current selection (paragraph or h1, h2...h6)
     *
     * @deprecated currently using the Heading feature instead
     */
    setBlockType(tagName: HeadingTagType | 'paragraph', skipHistoryStack = false) {
        this.wordLib.lexical.update(
            () => {
                const selection = $getSelection();
                if (selection) {
                    if (tagName == 'paragraph') $setBlocksType(selection, () => $createParagraphNode());
                    // If you want to set a heading block type instead, use:
                    else $setBlocksType(selection, () => $createHeadingNode(tagName));
                }
            },
            skipHistoryStack ? { tag: 'historic' } : {}
        );
    }

    /**
     * Replace the style of the given heading type. The style will be saved into database for this specific document
     */
    replaceHeadingStyle(type: HeadingWrapperType, style: HeadingWrapperStyle) {
        this.headingOverrides.set(type, style);

        this.applyHeadingStyles();
        this.wordLib.dispatchCommand(RELOAD_HEADINGS_COMMAND, type); // reload this heading type
    }

    /**
     *  !NOTE: compare to `replaceHeadingStyle()` this function only reload the style on the toolbar,
     *  the style in the content will be reload through yjs
     */
    replaceHeadingStyleFromJSON(overrideJson: string) {
        if (overrideJson) {
            const overrides = JSON.parse(overrideJson) as Record<HeadingWrapperType, HeadingWrapperStyle>;

            Object.keys(overrides).forEach(type => {
                this.headingOverrides.set(type as HeadingWrapperType, overrides[type]);
            });
        }

        this.applyHeadingStyles();
    }

    /**
     * Reset all heading styles to default
     */
    resetHeadingStyles() {
        this.headingOverrides.clear();
        this.applyHeadingStyles();
        this.wordLib.dispatchCommand(RELOAD_HEADINGS_COMMAND, null); // reload all headings
    }

    /**
     * Apply the heading styles to the toolbar by replacing the document global CSS
     */
    applyHeadingStyles() {
        const headingsCss: string[] = [];

        for (const [type, style] of this.headingOverrides) {
            const selector = `.${HEADING_CLASS}.${HEADING_PREFIX}${type}`;
            const css = [`.v-toolbar.heading-wrapper-tb__${this.state.globalId} ${selector} {`];

            css.push(`\tbackground-color: ${style.backgroundColor ? style.backgroundColor : 'transparent'};`);
            css.push(`\tfont-family: "${style.fontFamily}";`);
            css.push(`\tfont-size: ${style.fontSize};`);
            css.push(`\tcolor: ${style.fontColor};`);
            css.push(`\tfont-weight: ${style.isBold ? '700' : '400'};`);
            css.push(`\tfont-style: ${style.isItalic ? 'italic' : 'normal'};`);
            const textDecoration =
                `${style.isUnderline ? 'underline' : ''} ${style.isStrikethrough ? 'line-through' : ''}`.trim();
            css.push(`\ttext-decoration: ${textDecoration ? textDecoration : 'none'};`);

            css.push('}');

            headingsCss.push(css.join('\n'));
        }

        const styleId = 'word-heading-override-' + this.state.globalId;
        const docToAppy = this.root.ownerDocument;
        if (headingsCss.length) {
            const existingStyle = docToAppy.getElementById(styleId);
            if (existingStyle) {
                existingStyle.textContent = headingsCss.join('\n\n');
            } else {
                const styleElem = docToAppy.createElement('style');
                styleElem.id = styleId;
                styleElem.textContent = headingsCss.join('\n\n');

                docToAppy.head.appendChild(styleElem);
            }
        } else {
            const existingStyle = docToAppy.getElementById(styleId);
            existingStyle?.remove();
        }
    }

    /**
     * Start a processing on the document that require blocking the user input.
     * Ex: inserting a document into sub-viewport
     */
    startProcessing() {
        this.processingCount$.next(this.processingCount$.value + 1);
    }

    /**
     * Finishes a processing on the document that require blocking the user input.
     */
    stopProcessing() {
        this.processingCount$.next(this.processingCount$.value - 1);
    }

    getBoundary(): BoundaryRectangle {
        return this.state.layer?.boundary;
    }
}

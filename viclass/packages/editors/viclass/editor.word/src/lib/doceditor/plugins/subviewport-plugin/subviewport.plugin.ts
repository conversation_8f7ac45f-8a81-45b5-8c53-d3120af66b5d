import { mergeRegister } from '@lexical/utils';
import {
    BaseBoardViewportManager,
    createDummyLocalDocId,
    DocLocalContent,
    EditorType,
    FEATURE_SELECTION,
    FocusDocEvent,
    isLocalContentDocId,
    SelectDelegator,
    SupportSelectFeature,
    VEventListener,
    ViewportManager,
} from '@viclass/editor.core';
import {
    $createEmptyTextToken,
    $createSubViewportNode,
    $getNodeBySubViewportId,
    $getParentSubViewportNodeFromDOM,
    $insertSubViewportNode,
    $isSubViewportNode,
    isEmptyTextToken,
    isInlineSubViewport,
    SUBVIEWPORT_CLASSNAME,
    SUBVIEWPORT_RESIZABLE_CLASSNAME,
    SubViewportNode,
} from '@viclass/editor.word.transform';
import {
    $getNodeByKey,
    $getSelection,
    $isNodeSelection,
    $nodesOfType,
    CLICK_COMMAND,
    COMMAND_PRIORITY_EDITOR,
    COMMAND_PRIORITY_LOW,
    KEY_ARROW_DOWN_COMMAND,
    KEY_ARROW_LEFT_COMMAND,
    KEY_ARROW_RIGHT_COMMAND,
    KEY_ARROW_UP_COMMAND,
    KEY_TAB_COMMAND,
    NodeKey,
    SELECTION_CHANGE_COMMAND,
    TextNode,
} from 'lexical';
import { WordEditorCoordinator } from '../../../coord/word.coordinator';
import { WordDocCtrl } from '../../../docs/word.doc.ctrl';
import { createAlignableNode, createResizableNode, NodeSelectionUtils } from '../../utils';
import { WordPlugin } from '../word.plugin';
import {
    DELETE_SUBVIEWPORTS_COMMAND,
    INSERT_SUBVIEWPORT_COMMAND,
    isImportedSubViewport,
    isNewSubViewport,
    LOAD_SUBVIEWPORTS_COMMAND,
    onEscape,
    SubViewportData,
    UPDATE_SUBVIEWPORT_LOCAL_CONTENT_COMMAND,
    UPDATE_VIEWPORT_VIEW_STATE,
    UpdateSubViewportLocalContentPayload,
} from './subviewport.utils';

/**
 * Plugin to insert Editor sub viewports into the lexical editor
 */
export class SubViewportPlugin extends WordPlugin {
    readonly nodeVmMap: Map<string, SubViewportData> = new Map();
    readonly newlyInsertedKeys = new Set<string>();

    isFirstLoad: boolean = true;

    private get wcoord(): WordEditorCoordinator {
        return this.wordEditor.wcoord;
    }

    /**
     * ctor
     * @param docCtrl the document controller
     */
    constructor(docCtrl: WordDocCtrl) {
        super(docCtrl);
    }

    private readonly canSelectViewport = (): boolean => {
        return this.wordEditor.isSubEditorActive(this.docCtrl);
    };

    /**
     * Check if the sub viewport is already loaded into the node
     */
    private isSubViewportLoaded(nodeKey: string): boolean {
        return this.nodeVmMap.has(nodeKey);
    }

    /**
     * Initialize various event handlers for the sub viewports.
     * Also handle the SubViewportNode mutation for create/update/delete nodes.
     *
     * @inheritdoc
     */
    init(): void {
        this.handleEscapeSubViewport();

        this.handleInsertSubViewport();

        this.addUnsubscribe(
            this.lexical.registerCommand(
                LOAD_SUBVIEWPORTS_COMMAND,
                () => {
                    this.handleLoadExistingSubViewports();
                    return true;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );

        this.addUnsubscribe(
            this.lexical.registerCommand(
                UPDATE_SUBVIEWPORT_LOCAL_CONTENT_COMMAND,
                payload => {
                    this.handleUpdateLocalContent(payload);
                    return true;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );

        this.addUnsubscribe(
            this.lexical.registerCommand(
                DELETE_SUBVIEWPORTS_COMMAND,
                vmIds => {
                    $nodesOfType(SubViewportNode)
                        .filter(node => vmIds.includes(node.viewportId))
                        .forEach(node => node.remove());
                    return false;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );

        this.addUnsubscribe(
            this.lexical.registerMutationListener(SubViewportNode, async mutatedNodes => {
                const destroyed: [NodeKey, HTMLElement][] = [];
                const created: [NodeKey, HTMLElement][] = [];
                const updated: [NodeKey, HTMLElement][] = [];

                for (const [nodeKey, mutation] of mutatedNodes) {
                    const elem = this.lexical.getElementByKey(nodeKey) as HTMLElement;

                    switch (mutation) {
                        case 'created':
                            created.push([nodeKey, elem]);
                            break;
                        case 'updated':
                            updated.push([nodeKey, elem]);
                            break;
                        case 'destroyed':
                            destroyed.push([nodeKey, elem]);
                            break;
                    }
                }

                // handle in the order of destroyed -> created -> updated to avoid multiple node handle the same viewport
                // example: on undo a viewport line breaker reconcilation -> old node is destroyed and new node is created
                // at the same time from yjs undo manager -> need to destroy the old node first so we can recreate the new one after
                const promises: Promise<any>[] = [];
                for (const [nodeKey, elem] of destroyed) {
                    promises.push(this.handleSubViewportNodeDestroyed(nodeKey, elem));
                }
                await Promise.all(promises);
                promises.length = 0;

                for (const [nodeKey, elem] of created) {
                    // we wait and flush update here so order is necessary
                    await this.handleSubViewportNodeCreated(nodeKey, elem);
                }

                for (const [nodeKey, elem] of updated) {
                    this.handleSubViewportNodeUpdated(nodeKey, elem);
                }

                if (created.length > 0) {
                    this.$autoInsertEmptyTextToken(created.map(([key]) => key));
                }
            })
        );

        this.handleSubViewportSelection();

        this.addUnsubscribe(
            this.lexical.registerCommand(
                UPDATE_VIEWPORT_VIEW_STATE,
                payload => {
                    const node = $getNodeBySubViewportId(payload.viewportId);
                    if (!node) return false;

                    node.lookAt = payload.lookAt;
                    node.zoom = payload.zoom;
                    return true;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );

        // auto insert an empty text token after inline-VP node to allow cursor selection
        // when VP is at the end of line or the next one is also an inline viewport
        this.addUnsubscribe(
            this.lexical.registerMutationListener(TextNode, nodeMutations => {
                for (const [, mutation] of nodeMutations) {
                    if (mutation === 'destroyed') {
                        return this.$autoInsertEmptyTextToken();
                    }
                }
            })
        );
    }

    /**
     * Set up the viewport focus listener. We have to use a combine of SELECTION_CHANGE_COMMAND
     * and CLICK_COMMAND as lexical always stealing the focus on the root node
     */
    private handleSubViewportSelection() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                SELECTION_CHANGE_COMMAND,
                this.handleViewportSelectionChange,
                COMMAND_PRIORITY_LOW
            )
        );

        this.addUnsubscribe(
            this.lexical.registerCommand(CLICK_COMMAND, this.handleViewportClick, COMMAND_PRIORITY_LOW)
        );
    }

    /**
     * Handle the command to insert new sub-viewport node into word doc
     */
    private handleInsertSubViewport() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                INSERT_SUBVIEWPORT_COMMAND,
                payload => {
                    const vpNode = $createSubViewportNode(payload.viewportId, payload.editorType);
                    this.newlyInsertedKeys.add(vpNode.getKey());
                    $insertSubViewportNode(vpNode);
                    return true;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );
    }

    /**
     * Handle escape from the subviewport context into the parent word doc context.
     * For example: pressing arrow keys at the start/end of math sub-viewport will escape the cursor to the next content in word
     */
    private handleEscapeSubViewport() {
        this.addUnsubscribe(
            mergeRegister(
                // When subviewport is the last child pressing down/right arrow will insert paragraph
                // below it to allow adding more content.
                this.lexical.registerCommand(KEY_ARROW_DOWN_COMMAND, ev => onEscape(ev, false), COMMAND_PRIORITY_LOW),
                this.lexical.registerCommand(KEY_ARROW_RIGHT_COMMAND, ev => onEscape(ev, false), COMMAND_PRIORITY_LOW),
                // When subviewport is the first child pressing up/left arrow will insert paragraph
                // above it to allow adding more content.
                this.lexical.registerCommand(KEY_ARROW_UP_COMMAND, ev => onEscape(ev, true), COMMAND_PRIORITY_LOW),
                this.lexical.registerCommand(KEY_ARROW_LEFT_COMMAND, ev => onEscape(ev, true), COMMAND_PRIORITY_LOW),
                this.lexical.registerCommand(KEY_TAB_COMMAND, ev => onEscape(ev, true), COMMAND_PRIORITY_LOW)
            )
        );

        const docListener = new this.EscapeDocEventListener(this);
        [...this.wcoord.editors.values()].forEach(ed => {
            if (ed.isSupportFeature(FEATURE_SELECTION)) {
                const supporter = ed.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION);
                // a little cheat but now all editors select feature is handled by the delegators
                if (supporter && supporter instanceof SelectDelegator) {
                    supporter.registerDocEventListener(docListener);
                    this.addUnsubscribe(() => {
                        supporter.unregisterDocEventListener(docListener);
                    });
                }
            }
        });
    }

    /**
     * Listen to the escape event from the document.
     * Example: press arrow key at the end of math sub viewport
     */
    private readonly EscapeDocEventListener = class implements VEventListener<FocusDocEvent<any>> {
        constructor(private p: SubViewportPlugin) {}

        onEvent(eventData: FocusDocEvent<any>): FocusDocEvent<any> | Promise<FocusDocEvent<any>> {
            if (eventData.eventType === 'doc-escaped') {
                const direction = eventData.state?.direction || 'forward';
                this.p.lexical.update(() => {
                    const node = $getNodeBySubViewportId(eventData.state.vm.id);
                    if (!node) return;

                    /**
                     * After escape, select the previous/next node from the current context.
                     * If we hit an empty text token in the way: [inline-vp][empty-token][inline-vp]
                     * then we need to convert the empty token to normal text, otherwise the cursor will
                     * jump directly into the next inline-vp as empty token will not contains any cursor
                     */
                    if (direction === 'backward' || direction === 'upward') {
                        const prevNode = node.getPreviousSibling();
                        if (isEmptyTextToken(prevNode) && prevNode.getPreviousSibling()) {
                            prevNode.setMode('normal').setTextContent(' ');
                        }
                        node.selectPrevious();
                    } else {
                        const nextNode = node.getNextSibling();
                        if (isEmptyTextToken(nextNode) && nextNode.getNextSibling()) {
                            nextNode.setMode('normal').setTextContent(' ');
                        }
                        node.selectNext(0, 0);
                    }

                    this.p.handleViewportSelectionChange();
                });
            }

            return eventData;
        }
    };

    private $autoInsertEmptyTextToken(subVpNodeKeys?: NodeKey[]) {
        if (!this.lexical.isEditable()) return;

        this.lexical.update(() => {
            const subVpNodes = subVpNodeKeys
                ? subVpNodeKeys.map(key => $getNodeByKey<SubViewportNode>(key)).filter(Boolean)
                : $nodesOfType(SubViewportNode);

            subVpNodes.forEach(node => {
                if (isInlineSubViewport(node.__editorType)) {
                    const nextNode = node.getNextSibling();
                    if (!nextNode || $isSubViewportNode(nextNode)) {
                        node.insertAfter($createEmptyTextToken());
                    }
                }
            });
        });
    }

    /**
     * Handle the creation mutation of a SubViewportNode.
     * The node can be created by:
     * - created by the insert command or pasting -> insert viewport + insert/create the document
     * - the node is sync from other users -> insert viewport + load document
     *
     * @param nodeKey the lexical node key of the SubViewportNode
     * @param elem root HTML element of SubViewportNode
     * @returns
     */
    async handleSubViewportNodeCreated(nodeKey: string, elem: HTMLElement) {
        const vmData = this.nodeVmMap.get(nodeKey);
        const vm = vmData?.viewportManager;
        if (vm) {
            // node is re-created by lexical DOM reconcilation -> try reconnect the ViewportManager
            this.reconnectViewportRoot(nodeKey, elem, vmData);
            return;
        }

        let isImported = false;
        let isNewNode = false;
        let isCreation = false;

        this.lexical.read(() => {
            const node = $getNodeByKey<SubViewportNode>(nodeKey);

            isImported = isImportedSubViewport(node);
            isNewNode = isNewSubViewport(node);
            isCreation = isImported || isNewNode;
        });

        if (isCreation) {
            await this.$processSubViewportCreation(elem, nodeKey);
        } else if (!this.isFirstLoad) {
            // not a new or import node + not first load -> this is a re-created node from undo/redo
            this.lexical.read(() => this.$loadSubViewports([$getNodeByKey<SubViewportNode>(nodeKey)], true));
        }
    }

    /**
     * Handle the update mutation of SubViewportNode. Include auto load the uninitiated SubViewportNode,
     * reconnect the ViewportManager if the SubViewportNode is re-created by lexical DOM reconcilation,
     * and handle lookAt, zoom change of the SubViewportNode.
     *
     * @param nodeKey the lexical node key of the SubViewportNode
     * @param elem root HTML element of SubViewportNode
     */
    handleSubViewportNodeUpdated(nodeKey: string, elem: HTMLElement) {
        const vmData = this.nodeVmMap.get(nodeKey);
        const vm = vmData?.viewportManager;
        if (!vm) {
            this.lexical.read(() => {
                const node = $getNodeByKey<SubViewportNode>(nodeKey);
                if (!node) return;

                const isCreation = isImportedSubViewport(node) || isNewSubViewport(node);
                if (!isCreation) this.$loadSubViewports([node]);
            });
        } else {
            // node can be re-created by lexical DOM reconcilation -> try reconnect the ViewportManager
            this.reconnectViewportRoot(nodeKey, elem, vmData);

            this.lexical.read(() => {
                const node = $getNodeByKey<SubViewportNode>(nodeKey);
                if (vm instanceof BaseBoardViewportManager) {
                    vm.centerAt(node.lookAt);
                    vm.zoom(node.zoom);
                }

                if (isLocalContentDocId(node.globalId)) {
                    this.wcoord.compareAndUpdateLocalContent(node.editorType, vm, node.localId, node.localContent);
                }
            });
        }
    }

    /**
     * Clean up the ViewportManager and other registered hooks (i.e. resize, align,... handlers)
     * when the SubViewportNode is removed
     *
     * @param nodeKey the lexical node key of the SubViewportNode
     * @param _elem root HTML element of SubViewportNode
     */
    async handleSubViewportNodeDestroyed(nodeKey: string, _elem: HTMLElement) {
        const vmData = this.nodeVmMap.get(nodeKey);
        if (vmData) {
            vmData.teardowns.forEach(fn => fn());
            this.nodeVmMap.delete(nodeKey);

            await this.wcoord.removeViewport(vmData.viewportManager.id);
        }
    }

    /**
     * On lexical DOM reconciliation, the SubViewportNode can be recreated with a new HTML element.
     * Reconnect the existing ViewportManager with the new HTML element of the SubViewportNode.
     * @param nodeKey the key of the re-created sub-viewport node
     * @param nodeRoot root HTML element of SubViewportNode
     * @param vmData saved sub-viewport data
     */
    private reconnectViewportRoot(nodeKey: string, nodeRoot: HTMLElement, vmData: SubViewportData) {
        const vm = vmData.viewportManager;
        if (!nodeRoot || !vm?.rootEl) return;

        const vpElement = nodeRoot.querySelector(`.${SUBVIEWPORT_CLASSNAME}`) as HTMLElement;
        if (vpElement && !vpElement.contains(vm.rootEl)) {
            vpElement.appendChild(vm.rootEl);

            vmData.teardowns.forEach(teardown => teardown());
            this.lexical.read(() => {
                const node = $getNodeByKey<SubViewportNode>(nodeKey);
                vmData.teardowns = this.setupViewportResizeAlign(nodeRoot, node, vmData.selection);
            });
        }
    }

    /**
     * Handle initialize the viewport inside the SubViewportNode.
     * Only working on the presenter side, where the insert cmd is dispatched.
     * The viewport on other peers is created by the command processor of the sub-editor.
     *
     * @param elem the HTML element of the SubViewportNode
     * @param nodeKey the lexical node key of the SubViewportNode
     * @returns
     */
    private async $processSubViewportCreation(elem: HTMLElement, nodeKey: NodeKey) {
        if (this.docCtrl.viewport.mode !== 'EditMode') return;

        let subVpId: string;
        let docGlobalId: string;
        let docLocalId: number;
        let edType: EditorType;
        let isImported = false;
        let vm: ViewportManager;
        let localContent: DocLocalContent | undefined;

        this.lexical.read(() => {
            const node = $getNodeByKey<SubViewportNode>(nodeKey);
            // save all states first before do the async work to prevent losing the active editorState
            subVpId = node.viewportId;
            docGlobalId = node.globalId;
            docLocalId = node.localId;
            edType = node.editorType;
            localContent = node.localContent;
            isImported = isImportedSubViewport(node);

            const selectionUtils = new NodeSelectionUtils(this.lexical, nodeKey, this.canSelectViewport);
            const teardowns = this.setupViewportResizeAlign(elem, node, selectionUtils);

            if (isImported) {
                // if this is an imported viewport -> create new viewportId, so it won't be duplicated
                subVpId = this.docCtrl.viewportId(this.docCtrl.state.globalId, node.editorType);
            }

            const vpElement = elem.querySelector(`.${SUBVIEWPORT_CLASSNAME}`) as HTMLElement;
            vpElement.id = subVpId; // do manually as we need it immediately for `insertViewport()`

            vm = this.docCtrl.insertViewport(vpElement, node.editorType);
            this.nodeVmMap.set(nodeKey, {
                selection: selectionUtils,
                viewportManager: vm,
                teardowns,
            });
        });

        // update viewport ID flush updates before creating sub-viewport
        if (isImported) {
            const editor = this.wcoord.editorByType(edType);
            if (editor.operationMode === 'LOCAL' && !isLocalContentDocId(docGlobalId)) {
                // import from remote doc -> we need to load the local content first
                const loadCtx = this.wcoord.loadingCtxGenerator(editor, vm.id);
                localContent = await editor.getLocalContentFromGlobalId(docGlobalId, loadCtx);
                docGlobalId = createDummyLocalDocId(vm.id, editor.id, docLocalId);
            }

            this.lexical.update(() => {
                const node = $getNodeByKey<SubViewportNode>(nodeKey);
                node.viewportId = subVpId;
                node.globalId = docGlobalId;
                node.localContent = localContent;
            });
        }
        await this.wordLib.flushUpdates();

        elem.classList.add('loading');
        this.runAsyncWithInputBlocker(
            async () => {
                if (isImported) {
                    // pasted sub-viewport -> duplicate and load it
                    const result = await this.wcoord.insertDocumentInViewport(
                        docGlobalId,
                        docLocalId,
                        edType,
                        vm,
                        localContent
                    );
                    docGlobalId = result[0].expectedChanges[0].globalId;
                } else {
                    const result = await this.wcoord.createDocumentInViewport(subVpId, edType);
                    docGlobalId = result.expectedChanges[0].globalId;
                }

                this.lexical.update(
                    () => {
                        const node = $getNodeByKey<SubViewportNode>(nodeKey);
                        node.globalId = docGlobalId;
                    },
                    { discrete: true }
                );

                this.trySelectNewlyInsertedDoc(nodeKey);
            },
            () => elem.classList.remove('loading')
        );
    }

    /**
     * Try to select the newly inserted document to allow immediate editing.
     *
     * @param nodeKey lexical node key
     */
    private trySelectNewlyInsertedDoc(nodeKey: string) {
        if (this.newlyInsertedKeys.has(nodeKey)) {
            const data = this.nodeVmMap.get(nodeKey);
            if (data) {
                data.selection.setSelected(true);
            }
            this.newlyInsertedKeys.delete(nodeKey);
        }
    }

    /**
     * Setup the resizer and alignable for SubViewportNode
     */
    private setupViewportResizeAlign(
        elem: HTMLElement,
        node: SubViewportNode,
        selectionUtils: NodeSelectionUtils
    ): Array<() => void> {
        const nodeKey = node.getKey();

        const resizableElement = elem.querySelector(`.${SUBVIEWPORT_RESIZABLE_CLASSNAME}`) as HTMLElement;
        // create alignable
        const teardownAlignable = createAlignableNode(
            elem,
            this.lexical,
            nodeKey,
            selectionUtils,
            node.format || 'center'
        );

        // create resizer
        const handleResizing = (width: number, height: number) => {
            resizableElement.style.width = width + 'px';
            resizableElement.style.height = height + 'px';
            this.lexical.update(
                () => {
                    const node = $getNodeByKey<SubViewportNode>(nodeKey);
                    node.size = { width, height };
                },
                { tag: 'historic' } // don't add to history
            );
        };

        const handleResizeFinished = (width: number, height: number) => {
            this.lexical.update(() => {
                const node = $getNodeByKey<SubViewportNode>(nodeKey);
                node.size = { width, height };
            });
        };

        const teardownResizable = createResizableNode(
            resizableElement,
            this.lexical,
            nodeKey,
            selectionUtils,
            this.docCtrl,
            {
                selectOverlay: true,
                maxHeight: 1000,
                checkResizable: () => !node.isInline() && this.lexical.isEditable(),
                onResizing: handleResizing,
                onResizeFinished: handleResizeFinished,
            }
        );
        // Start observing the resizable node to optimize rendering when the element is overflowed
        this.wordLib.overflowManager.observe(elem);

        return [teardownResizable, teardownAlignable, () => this.wordLib.overflowManager.unobserve(elem)];
    }

    /**
     * Load all sub-viewports available in the editor.
     * To be used on the first load or after a force update command
     */
    private handleLoadExistingSubViewports() {
        this.lexical.read(() => {
            const nodes = $nodesOfType(SubViewportNode);
            this.$loadSubViewports(nodes);
            this.isFirstLoad = false;
        });
    }

    /**
     * Load a list of sub-viewport nodes.
     *
     * - For REMOTE editor nodes, the viewport is inserted into the node.
     * And we only load doc into viewport when the word doc is first loaded or the node is recreated by undo/redo.
     * Because it will be also loaded by the command processor by sub-editor normal flow.
     *
     * - For LOCAL editor nodes, the viewport is inserted into the node, and the local content will always be loaded
     *
     * @param nodes the nodes to be loaded
     * @param isRecreation is a node recreated from undo/redo
     */
    private $loadSubViewports(nodes: SubViewportNode[], isRecreation = false) {
        for (const node of nodes) {
            const nodeKey = node.getKey();
            if (this.isSubViewportLoaded(nodeKey) || isImportedSubViewport(node)) continue;

            const elem = this.lexical.getElementByKey(nodeKey);
            if (!elem) {
                console.warn('No element found for sub-viewport', node);
                continue;
            }

            const selectionUtils = new NodeSelectionUtils(this.lexical, nodeKey, this.canSelectViewport);
            const teardowns = this.setupViewportResizeAlign(elem, node, selectionUtils);

            const vpElement = elem.querySelector(`.${SUBVIEWPORT_CLASSNAME}`) as HTMLElement;
            vpElement.id = node.viewportId; // do manually as we need it immediately for `insertViewport()`

            const vm = this.docCtrl.insertViewport(vpElement, node.editorType);
            this.nodeVmMap.set(nodeKey, {
                selection: selectionUtils,
                viewportManager: vm,
                teardowns,
            });

            const globalId = node.globalId;
            const localId = node.localId;
            const edType = node.editorType;
            const localContent = node.localContent;
            const isLocalContentDoc = isLocalContentDocId(globalId);

            if (this.isFirstLoad || isRecreation) {
                // first load or recreated from undo/redo -> need to load doc after insert viewport
                elem.classList.add('loading');
                this.runAsyncWithInputBlocker(
                    async () => {
                        if (isLocalContentDoc) {
                            await this.wcoord.loadDocumentFromLocalContent(vm, localId, edType, localContent);
                        } else {
                            await this.wcoord.loadDocumentInViewport(globalId, localId, edType, vm);
                        }
                    },
                    () => elem.classList.remove('loading')
                );
            } else if (isLocalContentDoc) {
                this.wcoord.compareAndUpdateLocalContent(edType, vm, localId, localContent);
            }
        }
    }

    /**
     * Destroy and clean up the plugin
     */
    override destroy(): void {
        super.destroy();
        this.nodeVmMap.forEach(vm => {
            this.wcoord.removeViewport(vm.viewportManager.id);
            vm.selection.destroy();
            vm.teardowns?.forEach(fn => fn());
        });
        this.nodeVmMap.clear();
    }

    /**
     * Handle the update local content command, when the local content doc is updated,
     * we need to update its local content props inside the sub-viewport so it can be sync to other peers
     *
     * @param payload
     * @returns
     */
    private handleUpdateLocalContent(payload: UpdateSubViewportLocalContentPayload) {
        if (this.docCtrl.viewport.mode !== 'EditMode') return;

        // update the local content in a single transaction
        this.lexical.update(
            () => {
                const nodes = $nodesOfType(SubViewportNode);
                const node = nodes.find(n => n.viewportId === payload.viewportId && n.localId === payload.localId);
                if (node) {
                    node.localContent = payload.localContent;
                }
            },
            { discrete: true }
        );
    }

    /**
     * Handle focus sub viewport on the selection is changed
     */
    private handleViewportSelectionChange = () => {
        const selection = $getSelection();
        const selectedNodes = selection?.getNodes() || [];

        if (selectedNodes.length > 0 && $isNodeSelection(selection)) {
            // try focus the last selected sub viewport first
            for (let i = selectedNodes.length - 1; i >= 0; i--) {
                const node = selectedNodes[i];
                if (!$isSubViewportNode(node)) continue;

                const vmData = this.nodeVmMap.get(node.getKey());
                return this.focusSubViewport(vmData.viewportManager);
            }
        }

        // no sub viewport -> return focus to the word editor
        this.wcoord.clearInternalViewportFocus(this.docCtrl);

        return false;
    };

    /**
     * Handle select viewport on viewport click
     */
    private handleViewportClick = (e: MouseEvent) => {
        const node = $getParentSubViewportNodeFromDOM(e.target as Node);
        if (!node) return false;

        const vmData = this.nodeVmMap.get(node.getKey());
        if (!vmData.selection.isSelected) {
            vmData.selection.setSelected(true, true);
        }

        return true;
    };

    /**
     * Focus on the sub viewport
     */
    private focusSubViewport(vm: ViewportManager): boolean {
        if (this.wcoord.getCurrentFocusedViewport(this.docCtrl) === vm) return false;

        this.wcoord.focusViewport(this.docCtrl, vm);
        return true;
    }
}

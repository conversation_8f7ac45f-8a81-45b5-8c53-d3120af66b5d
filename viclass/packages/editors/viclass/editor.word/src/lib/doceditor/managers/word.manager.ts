import { LexicalEditor } from 'lexical';
import { WordDocCtrl } from '../../docs/word.doc.ctrl';
import { WordEditor } from '../../word.editor';
import { WordLib } from '../word.lib';

export default abstract class WordManager {
    private readonly unsubscribeFns: Array<() => void> = [];

    protected get lexical(): LexicalEditor {
        return this.docEditor.lexical;
    }

    protected get wordLib(): WordEditor {
        return this.docEditor.editor;
    }

    protected get docCtrl(): WordDocCtrl {
        return this.docEditor.docCtrl;
    }

    constructor(protected docEditor: WordLib) {}

    abstract init();

    protected addUnsubscribe(fn: () => void): void {
        this.unsubscribeFns.push(fn);
    }

    destroy(): void {
        this.unsubscribeFns.forEach(fn => fn());
    }
}

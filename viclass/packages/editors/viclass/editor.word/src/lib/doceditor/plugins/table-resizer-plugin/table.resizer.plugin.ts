import {
    $computeTableMapSkipCellCheck,
    $getTableNodeFromLexicalNodeOrThrow,
    $getTableRowIndexFromTableCellNode,
    $isTableCellNode,
    $isTableRowNode,
    $isTableSelection,
    TableCellNode,
    TableDOMCell,
    TableMapType,
    TableNode,
    TableRowNode,
    getDOMCellFromTarget,
} from '@lexical/table';
import { calculateZoomLevel } from '@lexical/utils';
import { $isCustomTableCellNode } from '@viclass/editor.word.transform';
import { $getNearestNodeFromDOMNode, $getSelection, COMMAND_PRIORITY_EDITOR, createCommand } from 'lexical';
import { getSelectionBounds } from '../../managers/table-manager/table.utils';
import { WordPlugin } from '../word.plugin';

export const DISTRIBUTE_COLUMNS_EVENLY = createCommand<void>('DISTRIBUTE_COLUMNS_EVENLY');
export const DISTRIBUTE_ROWS_EVENLY = createCommand<void>('DISTRIBUTE_ROWS_EVENLY');

type MousePosition = {
    x: number;
    y: number;
};

type MouseDraggingDirection = 'left' | 'right' | 'top' | 'bottom';

export const MIN_ROW_HEIGHT = 33;
export const MIN_COLUMN_WIDTH = 10;
const DEFAULT_COLUMN_WIDTH = 92;

const isMouseDownOnEvent = (event: MouseEvent) => {
    return (event.buttons & 1) === 1;
};

export class TableResizerPlugin extends WordPlugin {
    private resizerContainer: HTMLElement;
    private topResizer: HTMLElement;
    private bottomResizer: HTMLElement;
    private leftResizer: HTMLElement;
    private rightResizer: HTMLElement;

    private targetRef: HTMLElement | null = null;
    private activeCell: TableDOMCell | null = null;
    private isMouseDown = false;
    private draggingDirection: MouseDraggingDirection | null = null;
    private mouseStartPos: MousePosition = { x: 0, y: 0 };
    private mouseCurrentPos: MousePosition = { x: 0, y: 0 };
    private tableRect: ClientRect | null = null;

    get rootDocument() {
        return this.lexical.getRootElement().ownerDocument;
    }

    resetState = () => {
        this.activeCell = null;
        this.targetRef = null;
        this.draggingDirection = null;
        this.mouseStartPos = null;
        this.tableRect = null;

        this.updateResizerStyles();
    };

    isMouseDownOnEvent = (event: MouseEvent) => {
        return (event.buttons & 1) === 1;
    };

    init() {
        this.setupResizer();
        this.setupTableNodeTransform();
        this.setupResizeWithMouseEvents();

        this.handleDistributeColumnsEvently();
        this.handleDistributeRowsEvently();
    }
    /**
     * Distributes the widths of selected columns evenly.
     * Takes the total width of selected columns and divides it equally among them.
     */
    private handleDistributeColumnsEvently() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                DISTRIBUTE_COLUMNS_EVENLY,
                () => {
                    const selection = $getSelection();
                    if (!$isTableSelection(selection)) {
                        return false;
                    }

                    const tableNode = $getTableNodeFromLexicalNodeOrThrow(selection.anchor.getNode());
                    const [gridMap] = $computeTableMapSkipCellCheck(tableNode, null, null);
                    const selectedCells = selection.getNodes().filter($isCustomTableCellNode);
                    const selectionBounds = getSelectionBounds(selectedCells, gridMap);
                    if (!selectionBounds) return false;

                    const { minCol, maxCol } = selectionBounds;
                    const colWidths = tableNode.getColWidths();
                    if (!colWidths) return false;

                    const selectedColWidths = colWidths.slice(minCol, maxCol + 1);
                    const totalWidth = selectedColWidths.reduce((acc, width) => acc + width, 0);
                    const averageWidth = totalWidth / selectedColWidths.length;

                    const newColWidths = [...colWidths];
                    for (let i = minCol; i <= maxCol; i++) {
                        newColWidths[i] = averageWidth;
                    }

                    tableNode.setColWidths(newColWidths);

                    return true;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );
    }

    /**
     * Distributes the heights of selected rows evenly.
     * Takes the total height of selected rows and divides it equally among them.
     */
    private handleDistributeRowsEvently() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                DISTRIBUTE_ROWS_EVENLY,
                () => {
                    const selection = $getSelection();
                    if (!$isTableSelection(selection)) {
                        return false;
                    }

                    const tableNode = $getTableNodeFromLexicalNodeOrThrow(selection.anchor.getNode());
                    const [gridMap] = $computeTableMapSkipCellCheck(tableNode, null, null);
                    const selectedCells = selection.getNodes().filter($isCustomTableCellNode);
                    const selectionBounds = getSelectionBounds(selectedCells, gridMap);
                    if (!selectionBounds) return false;

                    const { minRow, maxRow } = selectionBounds;
                    const tableRows = tableNode.getChildren();

                    let totalHeight = 0;
                    for (let i = minRow; i <= maxRow; i++) {
                        const row = tableRows[i] as TableRowNode;
                        totalHeight += row.getHeight() || 0;
                    }

                    const averageHeight = totalHeight / (maxRow - minRow + 1);

                    for (let i = minRow; i <= maxRow; i++) {
                        const row = tableRows[i] as TableRowNode;
                        row.setHeight(averageHeight);
                    }

                    return true;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );
    }
    setupTableNodeTransform() {
        // Ensure tables have proper column widths initialized
        this.addUnsubscribe(
            this.lexical.registerNodeTransform(TableNode, tableNode => {
                if (tableNode.getColWidths()) {
                    return tableNode;
                }

                const numColumns = tableNode.getColumnCount();
                tableNode.setColWidths(Array(numColumns).fill(DEFAULT_COLUMN_WIDTH));
                return tableNode;
            })
        );
    }

    setupResizeWithMouseEvents() {
        const rootDoc = this.rootDocument;

        const onMouseMove = (event: MouseEvent) => {
            setTimeout(() => {
                const target = event.target;

                if (this.draggingDirection) {
                    this.mouseCurrentPos = {
                        x: event.clientX,
                        y: event.clientY,
                    };
                    this.updateResizerStyles();
                    return;
                }
                this.isMouseDown = isMouseDownOnEvent(event);
                if (this.resizerContainer && this.resizerContainer.contains(target as Node)) {
                    return;
                }

                if (this.targetRef !== target) {
                    this.targetRef = target as HTMLElement;
                    const cell = getDOMCellFromTarget(target as HTMLElement);

                    if (cell && this.activeCell !== cell) {
                        this.lexical.update(() => {
                            const tableCellNode = $getNearestNodeFromDOMNode(cell.elem);
                            if (!tableCellNode) {
                                console.warn('TableCellResizer: Table cell node not found.');
                                return;
                            }

                            const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);
                            const tableElement = this.lexical.getElementByKey(tableNode.getKey());

                            if (!tableElement) {
                                console.warn('TableCellResizer: Table element not found.');
                                return;
                            }

                            this.targetRef = target as HTMLElement;
                            this.tableRect = tableElement.getBoundingClientRect();
                            this.activeCell = cell;

                            this.updateResizerStyles();
                        });
                    } else if (cell == null) {
                        this.resetState();
                    }
                }
            }, 0);
        };

        const onMouseDown = (_event: MouseEvent) => {
            setTimeout(() => {
                this.isMouseDown = true;
            }, 0);
        };

        const onMouseUp = (_event: MouseEvent) => {
            setTimeout(() => {
                if (this.isMouseDown) {
                    this.isMouseDown = false;
                    this.resetState();
                }
            }, 100);
        };

        rootDoc.addEventListener('mousemove', onMouseMove);
        rootDoc.addEventListener('mousedown', onMouseDown);
        rootDoc.addEventListener('mouseup', onMouseUp);

        this.addUnsubscribe(() => {
            rootDoc.removeEventListener('mousemove', onMouseMove);
            rootDoc.removeEventListener('mousedown', onMouseDown);
            rootDoc.removeEventListener('mouseup', onMouseUp);
        });
    }

    isHeightChanging = (direction: MouseDraggingDirection) => {
        return direction === 'bottom' || direction === 'top';
    };

    getCellNodeHeight = (cell: TableCellNode): number | undefined => {
        const domCellNode = this.lexical.getElementByKey(cell.getKey());
        return domCellNode?.clientHeight;
    };

    updateRowHeight = (heightChange: number) => {
        if (!this.activeCell) {
            throw new Error('TableCellResizer: Expected active cell.');
        }

        this.lexical.update(
            () => {
                const tableCellNode = $getNearestNodeFromDOMNode(this.activeCell.elem);
                if (!$isTableCellNode(tableCellNode)) {
                    throw new Error('TableCellResizer: Table cell node not found.');
                }

                const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);
                const baseRowIndex = $getTableRowIndexFromTableCellNode(tableCellNode);
                const tableRows = tableNode.getChildren();

                // Determine if this is a full row merge by checking colspan
                const isFullRowMerge = tableCellNode.getColSpan() === tableNode.getColumnCount();

                // For full row merges, apply to first row. For partial merges, apply to last row
                const tableRowIndex = isFullRowMerge ? baseRowIndex : baseRowIndex + tableCellNode.getRowSpan() - 1;

                if (tableRowIndex >= tableRows.length || tableRowIndex < 0) {
                    throw new Error('Expected table cell to be inside of table row.');
                }

                const tableRow = tableRows[tableRowIndex];

                if (!$isTableRowNode(tableRow)) {
                    throw new Error('Expected table row');
                }

                let height = tableRow.getHeight();
                if (height === undefined) {
                    const rowCells = tableRow.getChildren<TableCellNode>();
                    height = Math.min(...rowCells.map(cell => this.getCellNodeHeight(cell) ?? Infinity));
                }

                const newHeight = Math.max(height + heightChange, MIN_ROW_HEIGHT);
                tableRow.setHeight(newHeight);
            },
            { tag: 'skip-scroll-into-view' }
        );
    };

    getCellColumnIndex = (tableCellNode: TableCellNode, tableMap: TableMapType) => {
        for (let row = 0; row < tableMap.length; row++) {
            for (let column = 0; column < tableMap[row].length; column++) {
                if (tableMap[row][column].cell === tableCellNode) {
                    return column;
                }
            }
        }
        return undefined;
    };

    updateColumnWidth = (widthChange: number, direction: 'left' | 'right') => {
        if (!this.activeCell) {
            throw new Error('TableCellResizer: Expected active cell.');
        }
        this.lexical.update(
            () => {
                const tableCellNode = $getNearestNodeFromDOMNode(this.activeCell.elem);
                if (!$isTableCellNode(tableCellNode)) {
                    throw new Error('TableCellResizer: Table cell node not found.');
                }

                const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);
                const [tableMap] = $computeTableMapSkipCellCheck(tableNode, null, null);
                let columnIndex = this.getCellColumnIndex(tableCellNode, tableMap);
                if (columnIndex === undefined) {
                    throw new Error('TableCellResizer: Table column not found.');
                }

                if (direction === 'right') {
                    // if dragging the right edge, we need to adjust the column index to the last column of the merged cell
                    columnIndex += tableCellNode.getColSpan() - 1;
                    columnIndex = Math.min(columnIndex, tableNode.getColumnCount() - 1);
                }

                const colWidths = tableNode.getColWidths();
                if (!colWidths) {
                    return;
                }
                const newColWidths = [...colWidths];
                const isFirstColumn = columnIndex === 0;
                const isLastColumn = columnIndex === colWidths.length - 1;

                // Handle edge cases for first and last columns, where we only resize one column.
                if ((direction === 'left' && isFirstColumn) || (direction === 'right' && isLastColumn)) {
                    const width = colWidths[columnIndex];
                    const newWidth = Math.max(width + widthChange, MIN_COLUMN_WIDTH);
                    newColWidths[columnIndex] = newWidth;
                    tableNode.setColWidths(newColWidths);
                    return;
                }

                // Handle resizing of two adjacent columns.
                if (direction === 'right') {
                    const leftColumnIndex = columnIndex;
                    const rightColumnIndex = columnIndex + 1;

                    const leftColWidth = colWidths[leftColumnIndex];
                    const rightColWidth = colWidths[rightColumnIndex];

                    // Prevent resizing beyond the minimum width.
                    const maxAllowedChange = rightColWidth - MIN_COLUMN_WIDTH;
                    const minAllowedChange = -(leftColWidth - MIN_COLUMN_WIDTH);
                    const adjustedWidthChange = Math.max(minAllowedChange, Math.min(widthChange, maxAllowedChange));

                    newColWidths[leftColumnIndex] = leftColWidth + adjustedWidthChange;
                    newColWidths[rightColumnIndex] = rightColWidth - adjustedWidthChange;
                } else {
                    // direction === 'left'
                    const leftColumnIndex = columnIndex - 1;
                    const rightColumnIndex = columnIndex;

                    const leftColWidth = colWidths[leftColumnIndex];
                    const rightColWidth = colWidths[rightColumnIndex];

                    // For left resize, a positive widthChange means dragging right, which should shrink the left column.
                    const maxAllowedChange = leftColWidth - MIN_COLUMN_WIDTH;
                    const minAllowedChange = -(rightColWidth - MIN_COLUMN_WIDTH);
                    const adjustedWidthChange = Math.max(minAllowedChange, Math.min(widthChange, maxAllowedChange));

                    newColWidths[leftColumnIndex] = leftColWidth - adjustedWidthChange;
                    newColWidths[rightColumnIndex] = rightColWidth + adjustedWidthChange;
                }

                tableNode.setColWidths(newColWidths);
            },
            { tag: 'skip-scroll-into-view' }
        );
    };

    mouseUpHandler = (direction: MouseDraggingDirection) => {
        const handler = (event: MouseEvent) => {
            event.preventDefault();
            event.stopPropagation();

            if (!this.activeCell) {
                console.warn('TableCellResizer: Expected active cell.');
                return;
            }

            if (this.mouseStartPos) {
                const { x, y } = this.mouseStartPos;

                if (this.activeCell === null) {
                    return;
                }
                const zoom = calculateZoomLevel(event.target as Element);

                if (this.isHeightChanging(direction)) {
                    const heightChange = Math.abs(event.clientY - y) / zoom;
                    const isShrinking =
                        (direction === 'bottom' && y > event.clientY) || (direction === 'top' && y < event.clientY);

                    this.updateRowHeight(heightChange * (isShrinking ? -1 : 1));
                } else {
                    const widthChange = Math.abs(event.clientX - x) / zoom;
                    const isShrinking =
                        (direction === 'right' && x > event.clientX) || (direction === 'left' && x < event.clientX);

                    this.updateColumnWidth(widthChange * (isShrinking ? -1 : 1), direction as any);
                }

                this.resetState();
                this.rootDocument.removeEventListener('mouseup', handler);
            }
        };
        return handler;
    };

    getResizerStyle = () => {
        if (!this.activeCell) {
            return {
                bottom: null,
                left: null,
                right: null,
                top: null,
            };
        }

        const { height, width, top, left } = this.activeCell.elem.getBoundingClientRect();
        const zoom = calculateZoomLevel(this.activeCell.elem);

        const offsetX = window.pageXOffset;
        const offsetY = window.pageYOffset;

        const resizerWidth = 5;
        const styles = {
            top: {
                backgroundColor: 'transparent',
                cursor: 'row-resize',
                height: `${resizerWidth}px`,
                left: `${offsetX + left}px`,
                top: `${offsetY + top - resizerWidth / 2}px`,
                width: `${width}px`,
            },
            bottom: {
                backgroundColor: 'transparent',
                cursor: 'row-resize',
                height: `${resizerWidth}px`,
                left: `${offsetX + left}px`,
                top: `${offsetY + top + height - resizerWidth / 2}px`,
                width: `${width}px`,
            },
            left: {
                backgroundColor: 'transparent',
                cursor: 'col-resize',
                height: `${height}px`,
                left: `${offsetX + left - resizerWidth / 2}px`,
                top: `${offsetY + top}px`,
                width: `${resizerWidth}px`,
            },
            right: {
                backgroundColor: 'transparent',
                cursor: 'col-resize',
                height: `${height}px`,
                left: `${offsetX + left + width - resizerWidth / 2}px`,
                top: `${offsetY + top}px`,
                width: `${resizerWidth}px`,
            },
        };

        const tableRect = this.tableRect;
        const direction = this.draggingDirection;

        if (direction && this.mouseCurrentPos && tableRect) {
            if (this.isHeightChanging(direction)) {
                styles[direction].left = `${offsetX + tableRect.left}px`;
                styles[direction].top = `${offsetY + this.mouseCurrentPos.y / zoom}px`;
                styles[direction].width = `${tableRect.width}px`;
            } else {
                styles[direction].left = `${offsetX + this.mouseCurrentPos.x / zoom}px`;
                styles[direction].top = `${offsetY + tableRect.top}px`;
                styles[direction].height = `${tableRect.height}px`;
            }

            styles[direction].backgroundColor = '#adf';
        }

        return styles;
    };

    updateResizerStyles = () => {
        const resizerStyles = this.getResizerStyle();

        if (resizerStyles.top && this.activeCell?.y === 0) {
            this.topResizer.style.display = 'block';
            Object.assign(this.topResizer.style, resizerStyles.top);
        } else {
            this.topResizer.style.display = 'none';
        }

        if (resizerStyles.bottom) {
            this.bottomResizer.style.display = 'block';
            Object.assign(this.bottomResizer.style, resizerStyles.bottom);
        } else {
            this.bottomResizer.style.display = 'none';
        }

        if (resizerStyles.left && this.activeCell?.x === 0) {
            this.leftResizer.style.display = 'block';
            Object.assign(this.leftResizer.style, resizerStyles.left);
        } else {
            this.leftResizer.style.display = 'none';
        }

        if (resizerStyles.right) {
            this.rightResizer.style.display = 'block';
            Object.assign(this.rightResizer.style, resizerStyles.right);
        } else {
            this.rightResizer.style.display = 'none';
        }
    };

    toggleResize = (direction: MouseDraggingDirection): ((e: MouseEvent) => void) => {
        const handler = event => {
            event.preventDefault();
            event.stopPropagation();

            if (!this.activeCell) {
                throw new Error('TableCellResizer: Expected active cell.');
            }

            this.mouseStartPos = {
                x: event.clientX,
                y: event.clientY,
            };
            this.draggingDirection = direction;

            this.rootDocument.addEventListener('mouseup', this.mouseUpHandler(direction));
        };

        return handler;
    };

    private setupResizer(): void {
        const resizerContainer = document.createElement('div');
        resizerContainer.className = 'TableCellResizer';

        const topResizer = document.createElement('div');
        topResizer.className = 'TableCellResizer__resizer TableCellResizer__ui';
        resizerContainer.appendChild(topResizer);

        const bottomResizer = document.createElement('div');
        bottomResizer.className = 'TableCellResizer__resizer TableCellResizer__ui';
        resizerContainer.appendChild(bottomResizer);

        const leftResizer = document.createElement('div');
        leftResizer.className = 'TableCellResizer__resizer TableCellResizer__ui';
        resizerContainer.appendChild(leftResizer);

        const rightResizer = document.createElement('div');
        rightResizer.className = 'TableCellResizer__resizer TableCellResizer__ui';
        resizerContainer.appendChild(rightResizer);

        this.resizerContainer = resizerContainer;
        this.topResizer = topResizer;
        this.bottomResizer = bottomResizer;
        this.leftResizer = leftResizer;
        this.rightResizer = rightResizer;

        const startTopResize = this.toggleResize('top');
        const startBottomResize = this.toggleResize('bottom');
        const startLeftResize = this.toggleResize('left');
        const startRightResize = this.toggleResize('right');

        topResizer.addEventListener('mousedown', startTopResize);
        bottomResizer.addEventListener('mousedown', startBottomResize);
        leftResizer.addEventListener('mousedown', startLeftResize);
        rightResizer.addEventListener('mousedown', startRightResize);

        this.addUnsubscribe(() => {
            topResizer?.removeEventListener('mousedown', startTopResize);
            bottomResizer?.removeEventListener('mousedown', startBottomResize);
            leftResizer?.removeEventListener('mousedown', startLeftResize);
            rightResizer?.removeEventListener('mousedown', startRightResize);
        });

        this.rootDocument.body.appendChild(this.resizerContainer);
    }
}

import {
    $computeTableMapSkipCellCheck,
    $deleteTableColumn__EXPERIMENTAL,
    $deleteTableRow__EXPERIMENTAL,
    $getNodeTriplet,
    $getTableCellNodeFromLexicalNode,
    $getTableColumnIndexFromTableCellNode,
    $getTableNodeFromLexicalNodeOrThrow,
    $getTableRowIndexFromTableCellNode,
    $insertTableColumn__EXPERIMENTAL,
    $insertTableRow__EXPERIMENTAL,
    $isTableCellNode,
    $isTableRowNode,
    $isTableSelection,
    $unmergeCell,
    getTableObserverFromTableElement,
    HTMLTableElementWithWithTableSelectionState,
    TableCellHeaderStates,
    TableCellNode,
    TableRowNode,
    TableSelection,
} from '@lexical/table';
import {
    $isCustomTableCellNode,
    BorderSideStyle,
    BorderStyles,
    CustomTableCellNode,
} from '@viclass/editor.word.transform';
import {
    $createParagraphNode,
    $getRoot,
    $getSelection,
    $isParagraphNode,
    $isRangeSelection,
    COMMAND_PRIORITY_CRITICAL,
    SELECTION_CHANGE_COMMAND,
} from 'lexical';
import { BehaviorSubject } from 'rxjs';
import { BorderStyleApplyMode, BorderStyleData } from '../../../tools';
import { DISTRIBUTE_COLUMNS_EVENLY, DISTRIBUTE_ROWS_EVENLY, INSERT_NEW_TABLE_COMMAND } from '../../plugins';
import WordManager from '../word.manager';
import {
    $canUnmerge,
    $cellContainsEmptyParagraph,
    $createTableCellSelection,
    $selectLastDescendant,
    computeSelectionCount,
    getSelectionBounds,
    isTableSelectionRectangular,
    SelectionBounds,
} from './table.utils';

/**
 * Convert BorderStyleData to BorderSideStyle
 */
function borderStyleDataToBorderSideStyle(data: BorderStyleData): BorderSideStyle {
    return {
        width: `${data.width}px`,
        style: data.type,
        color: data.color,
    };
}

export type CellVerticalAlign = 'top' | 'middle' | 'bottom';

export type TableSelectionCount = {
    columns: number;
    rows: number;
};

export type TableContext = {
    isTableSelection: boolean;
    canMerge: boolean;
    canUnmerge: boolean;
    selectionCounts: TableSelectionCount;
};

export type TableCellContext = {
    cell: TableCellNode | null;
    cellBgColor: string;
    verticalAlign?: CellVerticalAlign;
    borderStyle?: string; // e.g. "1px solid red"
};

export const DEFAULT_TABLE_CONTEXT: TableContext = {
    isTableSelection: false,
    canMerge: false,
    canUnmerge: false,
    selectionCounts: {
        columns: 0,
        rows: 0,
    },
};

export const DEFAULT_TABLE_CELL_NODE_CTX: TableCellContext = {
    cell: null,
    cellBgColor: 'transparent',
    verticalAlign: undefined,
    borderStyle: undefined,
};

/**
 * Responsible for managing the state and behavior of table-related interactions within a word document,
 * include the current state of table selections and cell contexts.
 * Provides methods to interact with and manipulate table structures,
 * such as merging or unmerging cells, and updating the selection context when changes occur.
 */
export class TableManager extends WordManager {
    /**
     * The table selection context (when cells are highlighted)
     */
    readonly tableContext$ = new BehaviorSubject<TableContext>({ ...DEFAULT_TABLE_CONTEXT });

    /**
     * The currently editing table cell
     */
    readonly tableCellContext$ = new BehaviorSubject<TableCellContext>({ ...DEFAULT_TABLE_CELL_NODE_CTX });

    /**
     * Distribute columns evenly
     */
    distributeColumnsEvenly = () => {
        this.lexical.dispatchCommand(DISTRIBUTE_COLUMNS_EVENLY, undefined);
    };

    /**
     * Distribute rows evenly
     */
    distributeRowsEvenly = () => {
        this.lexical.dispatchCommand(DISTRIBUTE_ROWS_EVENLY, undefined);
    };

    get currentCellNode(): TableCellNode | null {
        return this.tableCellContext$.value.cell;
    }

    init() {
        this.tableContext$.next({ ...DEFAULT_TABLE_CONTEXT });

        this.addUnsubscribe(
            this.lexical.registerCommand(
                SELECTION_CHANGE_COMMAND,
                () => {
                    this.updateTableCellNode();
                    this.updateTableContext();
                    return false;
                },
                COMMAND_PRIORITY_CRITICAL
            )
        );

        this.addUnsubscribe(
            this.lexical.registerMutationListener(TableCellNode, nodeMutations => {
                if (!this.currentCellNode) return;
                const nodeUpdated = nodeMutations.get(this.currentCellNode.getKey()) === 'updated';

                if (nodeUpdated) {
                    this.lexical.getEditorState().read(() => {
                        this.updateTableCellContext(this.currentCellNode.getLatest());
                    });
                }
            })
        );
    }

    /**
     * Updates the table wide context based on the current selection.
     * E.g. number of selected cells, if cells can be merged/unmerged
     */
    private updateTableContext = () => {
        const context = { ...DEFAULT_TABLE_CONTEXT };
        const selection = $getSelection();

        // when 1 or many cells was selected (highlighted in the UI)
        if ($isTableSelection(selection)) {
            context.isTableSelection = true;
            const currentSelectionCounts = computeSelectionCount(selection);
            context.selectionCounts = currentSelectionCounts;
            // Merge cells
            context.canMerge =
                isTableSelectionRectangular(selection) &&
                (currentSelectionCounts.columns > 1 || currentSelectionCounts.rows > 1);
        }

        // Unmerge cell
        context.canUnmerge = $canUnmerge(this.currentCellNode);

        this.tableContext$.next(context);
    };

    /**
     * Update the context of the currently selected table cell
     * E.g. background color, vertical alignment
     */
    private updateTableCellContext = (cell: TableCellNode) => {
        if (!cell) {
            this.tableCellContext$.next({ ...DEFAULT_TABLE_CELL_NODE_CTX });
        } else {
            this.lexical.getEditorState().read(() => {
                const cellBgColor = cell.getBackgroundColor() || 'transparent';
                const verticalAlign = cell.getVerticalAlign() as CellVerticalAlign;
                let borderStyle = undefined;
                if ($isCustomTableCellNode(cell)) {
                    borderStyle = cell.getBorderStyle();
                }
                this.tableCellContext$.next({ cell, cellBgColor, verticalAlign, borderStyle });
            });
        }
    };

    /**
     * Analyze the current selection to find the current table cell, then update its context
     */
    private updateTableCellNode = () => {
        const selection = $getSelection();

        if ($isRangeSelection(selection)) {
            const tableCellNodeFromSelection = $getTableCellNodeFromLexicalNode(selection.anchor.getNode());

            if (tableCellNodeFromSelection == null) {
                this.updateTableCellContext(null);
                return;
            }

            const tableCellParentNodeDOM = this.lexical.getElementByKey(tableCellNodeFromSelection.getKey());

            if (tableCellParentNodeDOM == null) {
                this.updateTableCellContext(null);
                return;
            }

            this.updateTableCellContext(tableCellNodeFromSelection);
        } else if (!document.activeElement) {
            this.updateTableCellContext(null);
        }
    };

    /**
     * Insert new table into the document.
     * To be called from the tools
     */
    insertTable(cols: number, rows: number, includeHeader = false) {
        this.docEditor.dispatchCommand(INSERT_NEW_TABLE_COMMAND, {
            columns: String(cols),
            rows: String(rows),
            includeHeaders: includeHeader,
        });
    }

    /**
     * Clear the current table selection. Use after some operations like delete table for cleanup
     */
    clearTableSelection = () => {
        this.lexical.update(() => {
            if (!this.currentCellNode) return;
            if (this.currentCellNode.isAttached()) {
                const tableNode = $getTableNodeFromLexicalNodeOrThrow(this.currentCellNode);
                const tableElement = this.lexical.getElementByKey(
                    tableNode.getKey()
                ) as HTMLTableElementWithWithTableSelectionState;

                if (!tableElement) {
                    console.warn('Expected to find tableElement in DOM');
                    return;
                }

                const tableSelection = getTableObserverFromTableElement(tableElement);
                if (tableSelection !== null) {
                    tableSelection.$clearHighlight();
                }

                tableNode.markDirty();
                this.updateTableCellContext(this.currentCellNode.getLatest());
            }

            const rootNode = $getRoot();
            rootNode.selectStart();
        });
    };

    /**
     * Apply border style to table selection based on mode
     * @param selection - Table selection
     * @param borderStyle - Border style to apply
     * @param mode - Application mode
     */
    private applyBorderStyleToTableSelection(
        selection: TableSelection,
        borderStyle: BorderSideStyle,
        mode: BorderStyleApplyMode
    ) {
        const tableNode = $getTableNodeFromLexicalNodeOrThrow(selection.anchor.getNode());
        const [gridMap] = $computeTableMapSkipCellCheck(tableNode, null, null);
        const selectedCells = selection.getNodes().filter($isCustomTableCellNode);

        // Get selection bounds
        const selectionBounds = getSelectionBounds(selectedCells, gridMap);
        if (!selectionBounds) return;

        const { minRow, maxRow, minCol, maxCol } = selectionBounds;

        // Apply border styles based on mode
        for (let row = minRow; row <= maxRow; row++) {
            for (let col = minCol; col <= maxCol; col++) {
                const cellMap = gridMap[row]?.[col];
                if (!cellMap || !$isCustomTableCellNode(cellMap.cell)) continue;

                this.applyBorderStyleToCell(cellMap.cell, borderStyle, mode, selectionBounds, row, col);
            }
        }

        // Handle adjacent cells for outer borders to prevent collision
        if (
            mode === BorderStyleApplyMode.OUTER ||
            mode === BorderStyleApplyMode.ALL ||
            mode === BorderStyleApplyMode.TOP ||
            mode === BorderStyleApplyMode.BOTTOM ||
            mode === BorderStyleApplyMode.LEFT ||
            mode === BorderStyleApplyMode.RIGHT
        ) {
            this.updateAdjacentCellsForBorderCollision(gridMap, selectionBounds, borderStyle, mode);
        }
    }

    /**
     * Apply border style to a single cell based on mode and position
     */
    private applyBorderStyleToCell(
        cell: CustomTableCellNode,
        borderStyle: BorderSideStyle,
        mode: BorderStyleApplyMode,
        selectionBounds: SelectionBounds | null,
        row: number,
        col: number
    ) {
        const currentBorders = cell.getBorderStyles() || {};
        const newBorders: BorderStyles = { ...currentBorders };
        const { minRow, maxRow, minCol, maxCol } = selectionBounds;

        switch (mode) {
            case BorderStyleApplyMode.ALL:
                newBorders.top = borderStyle;
                newBorders.bottom = borderStyle;
                newBorders.left = borderStyle;
                newBorders.right = borderStyle;
                break;

            case BorderStyleApplyMode.TOP:
                if (row === minRow) newBorders.top = borderStyle;
                break;

            case BorderStyleApplyMode.BOTTOM:
                if (row === maxRow) newBorders.bottom = borderStyle;
                break;

            case BorderStyleApplyMode.LEFT:
                if (col === minCol) newBorders.left = borderStyle;
                break;

            case BorderStyleApplyMode.RIGHT:
                if (col === maxCol) newBorders.right = borderStyle;
                break;

            case BorderStyleApplyMode.INNER:
                if (selectionBounds) {
                    // Apply inner borders (not on the perimeter)
                    if (row > minRow) newBorders.top = borderStyle;
                    if (row < maxRow) newBorders.bottom = borderStyle;
                    if (col > minCol) newBorders.left = borderStyle;
                    if (col < maxCol) newBorders.right = borderStyle;
                }
                break;

            case BorderStyleApplyMode.OUTER:
                if (selectionBounds) {
                    // Apply outer borders (only on the perimeter)
                    if (row === minRow) newBorders.top = borderStyle;
                    if (row === maxRow) newBorders.bottom = borderStyle;
                    if (col === minCol) newBorders.left = borderStyle;
                    if (col === maxCol) newBorders.right = borderStyle;
                }
                break;
        }

        cell.setBorderStyles(newBorders);
    }

    /**
     * Update adjacent cells to prevent border collision when border collapse occurs
     */
    private updateAdjacentCellsForBorderCollision(
        gridMap: any[][],
        selectionBounds: SelectionBounds,
        borderStyle: BorderSideStyle,
        mode: BorderStyleApplyMode
    ) {
        const { minRow, maxRow, minCol, maxCol } = selectionBounds;

        // Update top adjacent cells
        if (
            (mode === BorderStyleApplyMode.TOP ||
                mode === BorderStyleApplyMode.OUTER ||
                mode === BorderStyleApplyMode.ALL) &&
            minRow > 0
        ) {
            for (let col = minCol; col <= maxCol; col++) {
                const adjacentCell = gridMap[minRow - 1]?.[col]?.cell;
                if ($isCustomTableCellNode(adjacentCell)) {
                    const borders = adjacentCell.getBorderStyles() || {};
                    adjacentCell.setBorderStyles({ ...borders, bottom: borderStyle });
                }
            }
        }

        // Update bottom adjacent cells
        if (
            (mode === BorderStyleApplyMode.BOTTOM ||
                mode === BorderStyleApplyMode.OUTER ||
                mode === BorderStyleApplyMode.ALL) &&
            maxRow < gridMap.length - 1
        ) {
            for (let col = minCol; col <= maxCol; col++) {
                const adjacentCell = gridMap[maxRow + 1]?.[col]?.cell;
                if ($isCustomTableCellNode(adjacentCell)) {
                    const borders = adjacentCell.getBorderStyles() || {};
                    adjacentCell.setBorderStyles({ ...borders, top: borderStyle });
                }
            }
        }

        // Update left adjacent cells
        if (
            (mode === BorderStyleApplyMode.LEFT ||
                mode === BorderStyleApplyMode.OUTER ||
                mode === BorderStyleApplyMode.ALL) &&
            minCol > 0
        ) {
            for (let row = minRow; row <= maxRow; row++) {
                const adjacentCell = gridMap[row]?.[minCol - 1]?.cell;
                if ($isCustomTableCellNode(adjacentCell)) {
                    const borders = adjacentCell.getBorderStyles() || {};
                    adjacentCell.setBorderStyles({ ...borders, right: borderStyle });
                }
            }
        }

        // Update right adjacent cells
        if (
            (mode === BorderStyleApplyMode.RIGHT ||
                mode === BorderStyleApplyMode.OUTER ||
                mode === BorderStyleApplyMode.ALL) &&
            maxCol < gridMap[0].length - 1
        ) {
            for (let row = minRow; row <= maxRow; row++) {
                const adjacentCell = gridMap[row]?.[maxCol + 1]?.cell;
                if ($isCustomTableCellNode(adjacentCell)) {
                    const borders = adjacentCell.getBorderStyles() || {};
                    adjacentCell.setBorderStyles({ ...borders, left: borderStyle });
                }
            }
        }
    }

    /**
     * Merge the currently selected table cells.
     * To be called from the tools
     */
    mergeTableCellsAtSelection = () => {
        this.lexical.update(() => {
            const selection = $getSelection();
            if ($isTableSelection(selection)) {
                const { columns, rows } = computeSelectionCount(selection);
                const nodes = selection.getNodes();
                let firstCell: null | TableCellNode = null;
                for (let i = 0; i < nodes.length; i++) {
                    const node = nodes[i];
                    if ($isTableCellNode(node)) {
                        if (firstCell === null) {
                            node.setColSpan(columns).setRowSpan(rows);
                            firstCell = node;
                            const isEmpty = $cellContainsEmptyParagraph(node);
                            let firstChild;
                            if (isEmpty && $isParagraphNode((firstChild = node.getFirstChild()))) {
                                firstChild.remove();
                            }
                        } else if ($isTableCellNode(firstCell)) {
                            const isEmpty = $cellContainsEmptyParagraph(node);
                            if (!isEmpty) {
                                firstCell.append(...node.getChildren());
                            }
                            node.remove();
                        }
                    }
                }
                if (firstCell !== null) {
                    if (firstCell.getChildrenSize() === 0) {
                        firstCell.append($createParagraphNode());
                    }
                    $selectLastDescendant(firstCell);
                }
            }
        });
    };

    /**
     * Unmerge the currently selected table cells. To be called from the tools
     */
    unmergeTableCellsAtSelection = () => {
        this.lexical.update(() => {
            if (this.currentCellNode) {
                const selection = $getSelection();
                if (!$isTableSelection(selection) && !$isRangeSelection(selection)) {
                    $createTableCellSelection(this.currentCellNode);
                }
            }
            $unmergeCell();
        });
    };

    /**
     * Insert new row into the table. To be called from the tools
     */
    insertTableRowAtSelection = (shouldInsertAfter: boolean) => {
        this.lexical.update(() => {
            $insertTableRow__EXPERIMENTAL(shouldInsertAfter);
        });
    };

    /**
     * Insert new column into the table. To be called from the tools
     */
    insertTableColumnAtSelection = (shouldInsertAfter: boolean) => {
        this.lexical.update(async () => {
            $insertTableColumn__EXPERIMENTAL(shouldInsertAfter);
        });
    };

    /**
     * Delete the currently selected table row. To be called from the tools
     */
    deleteTableRowAtSelection = () => {
        this.lexical.update(() => {
            $deleteTableRow__EXPERIMENTAL();
        });
    };

    /**
     * Delete the currently selected table. To be called from the tools
     */
    deleteTableAtSelection = () => {
        this.lexical.update(() => {
            if (!this.currentCellNode) return;
            const tableNode = $getTableNodeFromLexicalNodeOrThrow(this.currentCellNode);
            tableNode.remove();

            this.clearTableSelection();
        });
    };

    /**
     * Delete the currently selected table column. To be called from the tools
     */
    deleteTableColumnAtSelection = () => {
        this.lexical.update(() => {
            $deleteTableColumn__EXPERIMENTAL();
        });
    };

    /**
     * Toggle the currently selected table row as header. Which will have a special style.
     * ! Currently not in use
     */
    toggleTableRowIsHeader = () => {
        this.lexical.update(() => {
            if (!this.currentCellNode) return;
            const tableNode = $getTableNodeFromLexicalNodeOrThrow(this.currentCellNode);

            const tableRowIndex = $getTableRowIndexFromTableCellNode(this.currentCellNode);

            const tableRows = tableNode.getChildren();

            if (tableRowIndex >= tableRows.length || tableRowIndex < 0) {
                throw new Error('Expected table cell to be inside of table row.');
            }

            const tableRow = tableRows[tableRowIndex];

            if (!$isTableRowNode(tableRow)) {
                throw new Error('Expected table row');
            }

            tableRow.getChildren().forEach(tableCell => {
                if (!$isTableCellNode(tableCell)) {
                    throw new Error('Expected table cell');
                }

                tableCell.toggleHeaderStyle(TableCellHeaderStates.ROW);
            });

            this.clearTableSelection();
        });
    };

    /**
     * Toggle the currently selected table column as header. Which will have a special style.
     * ! Currently not in use
     */
    toggleTableColumnIsHeader = () => {
        this.lexical.update(() => {
            if (!this.currentCellNode) return;
            const tableNode = $getTableNodeFromLexicalNodeOrThrow(this.currentCellNode);

            const tableColumnIndex = $getTableColumnIndexFromTableCellNode(this.currentCellNode);

            const tableRows = tableNode.getChildren<TableRowNode>();
            const maxRowsLength = Math.max(...tableRows.map(row => row.getChildren().length));

            if (tableColumnIndex >= maxRowsLength || tableColumnIndex < 0) {
                throw new Error('Expected table cell to be inside of table row.');
            }

            for (let r = 0; r < tableRows.length; r++) {
                const tableRow = tableRows[r];

                if (!$isTableRowNode(tableRow)) {
                    throw new Error('Expected table row');
                }

                const tableCells = tableRow.getChildren();
                if (tableColumnIndex >= tableCells.length) {
                    // if cell is outside of bounds for the current row (for example various merge cell cases) we shouldn't highlight it
                    continue;
                }

                const tableCell = tableCells[tableColumnIndex];

                if (!$isTableCellNode(tableCell)) {
                    throw new Error('Expected table cell');
                }

                tableCell.toggleHeaderStyle(TableCellHeaderStates.COLUMN);
            }

            this.clearTableSelection();
        });
    };

    /**
     * Change the background color of the currently selected cells. To be called from the tools
     */
    handleCellBackgroundColor = (value: string) => {
        this.lexical.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection) || $isTableSelection(selection)) {
                const [cell] = $getNodeTriplet(selection.anchor);
                if ($isTableCellNode(cell)) {
                    cell.setBackgroundColor(value);
                }

                if ($isTableSelection(selection)) {
                    const nodes = selection.getNodes();

                    for (let i = 0; i < nodes.length; i++) {
                        const node = nodes[i];
                        if ($isTableCellNode(node)) {
                            node.setBackgroundColor(value);
                        }
                    }
                }
            }
        });
    };

    /**
     * Change the vertical alignment of the currently selected cells. To be called from the tools
     */
    formatVerticalAlign = (value: string) => {
        this.lexical.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection) || $isTableSelection(selection)) {
                const [cell] = $getNodeTriplet(selection.anchor);
                if ($isTableCellNode(cell)) {
                    cell.setVerticalAlign(value);
                }

                if ($isTableSelection(selection)) {
                    const nodes = selection.getNodes();

                    for (let i = 0; i < nodes.length; i++) {
                        const node = nodes[i];
                        if ($isTableCellNode(node)) {
                            node.setVerticalAlign(value);
                        }
                    }
                }
            }
        });
    };

    /**
     * Change the border style of the currently selected cells. To be called from the tools
     */
    handleCellBorderStyle = (value: string) => {
        this.lexical.update(() => {
            const selection = $getSelection();

            // gather all table cells in selection
            if ($isRangeSelection(selection) || $isTableSelection(selection)) {
                const [cell] = $getNodeTriplet(selection.anchor);
                if ($isCustomTableCellNode(cell)) {
                    cell.setBorderStyle(value);
                }

                if ($isTableSelection(selection)) {
                    selection.getTextContent();
                    const nodes = selection.getNodes();

                    for (let i = 0; i < nodes.length; i++) {
                        const node = nodes[i];
                        if ($isCustomTableCellNode(node)) {
                            node.setBorderStyle(value);
                        }
                    }
                }
            }
        });
    };

    /**
     * Handle advanced border style application with per-side control and application modes
     * @param borderData - The border style data to apply
     * @param mode - How to apply the border (all, top, bottom, left, right, inner, outer)
     */
    handleCellBorderStyleAdvanced = (borderData: BorderStyleData, mode: BorderStyleApplyMode) => {
        this.lexical.update(() => {
            const selection = $getSelection();

            if (!($isRangeSelection(selection) || $isTableSelection(selection))) {
                return;
            }

            const borderSideStyle = borderStyleDataToBorderSideStyle(borderData);

            if ($isTableSelection(selection)) {
                this.applyBorderStyleToTableSelection(selection, borderSideStyle, mode);
            } else {
                // Single cell selection
                const [cell] = $getNodeTriplet(selection.anchor);
                if ($isCustomTableCellNode(cell)) {
                    this.applyBorderStyleToCell(cell, borderSideStyle, mode, null, 0, 0);
                }
            }
        });
    };
}

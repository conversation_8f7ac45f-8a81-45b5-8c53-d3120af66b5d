import { syncCursorPositions } from '@lexical/yjs';
import { BaseBoardViewportManager, VEventListener, ZoomEventData } from '@viclass/editor.core';
import { getCursorColor, useYjsFocusTracking } from '../../yjs/yjs.utils';
import { WordPlugin } from '../word.plugin';

export class YAwarenessPlugin extends WordPlugin {
    override init(): void {
        const lib = this.wordLib;

        const root = lib.rootElement;

        const cursorRoot = root.ownerDocument.createElement('div');
        Object.assign(cursorRoot.style, {
            position: 'absolute',
            top: '0px',
            left: '0px',
        });
        cursorRoot.className = 'word-cursor-root';
        root.parentElement.appendChild(cursorRoot); // appent to word doc ctrl root elem
        lib.binding.cursorsContainer = cursorRoot;
        this.initZoomHandler(cursorRoot);

        const username = this.getUserNameFromContext();
        const teardownFocusTracking = useYjsFocusTracking(
            this.lexical,
            lib.provider,
            username,
            getCursorColor(username)
        );
        this.addUnsubscribe(teardownFocusTracking);

        lib.provider.awareness.on('update', this.onAwarenessUpdate);
    }

    private initZoomHandler(root: HTMLDivElement) {
        const vm = this.docCtrl.viewport;
        if (vm instanceof BaseBoardViewportManager) {
            // @ts-ignore zoom was added later to CSSStyleDeclaration
            root.style.zoom = vm.zoomLevel.toString();

            const zoomListener = YAwarenessPlugin.ZoomListener(root);
            vm.zoomEventEmitter().registerListener(zoomListener);

            this.addUnsubscribe(() => vm?.zoomEventEmitter()?.unregisterListener(zoomListener));
        }
    }

    private onAwarenessUpdate = () => {
        syncCursorPositions(this.wordLib.binding, this.wordLib.provider);
    };

    private getUserNameFromContext() {
        let username = '';
        if (this.wordEditor.conf.userCtxGetter) {
            username = this.wordEditor.conf.userCtxGetter()?.username || '';
        }
        return username;
    }

    private static ZoomListener(_cursorRoot: HTMLDivElement): VEventListener<ZoomEventData> {
        return new (class implements VEventListener<ZoomEventData> {
            onEvent(eventData: ZoomEventData): ZoomEventData {
                // @ts-ignore zoom was added later to CSSStyleDeclaration
                _cursorRoot.style.zoom = `${eventData.state.zoomLevel}`;

                return eventData;
            }
        })();
    }
}

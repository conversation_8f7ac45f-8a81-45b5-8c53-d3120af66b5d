import { EditorConfig, LexicalEditor } from 'lexical';
import { WordDocCtrl } from '../../docs/word.doc.ctrl';
import { WordEditor } from '../../word.editor';
import { WordLib } from '../word.lib';

export abstract class WordPlugin {
    private readonly unsubscribeFns: Array<() => void> = [];

    protected get wordEditor(): WordEditor {
        return this.docCtrl.editor;
    }

    protected get wordLib(): WordLib {
        return this.docCtrl.wordLib;
    }

    protected get lexical(): LexicalEditor {
        return this.docCtrl.wordLib.lexical;
    }

    protected get lexicalConfig(): EditorConfig {
        return this.lexical._config;
    }

    constructor(protected docCtrl: WordDocCtrl) {}

    /**
     * Initialize the plugin
     */
    abstract init(): void;

    /**
     * Add an unsubscribe function to the list of functions to be called when the plugin is destroyed
     */
    protected addUnsubscribe(fn: () => void): void {
        this.unsubscribeFns.push(fn);
    }

    /**
     * Clean up the plugin on destroy
     */
    destroy(): void {
        this.unsubscribeFns.forEach(fn => fn());
    }

    /**
     * Run a callback function and ensure the editor is not editable while the callback is running.
     * This is to prevent the editor from triggering unexpected events or updates while the callback is running.
     *
     * @param callback - The callback function to run. If this is a Promise, it will be awaited.
     * @param onSuccess - An optional callback function to run when the callback is successfully resolved.
     * @param onError - An optional callback function to run when the callback is rejected. Defaults to `console.error()`.
     */
    async runAsyncWithInputBlocker(
        callback: () => void | Promise<void>,
        onSuccess?: () => void,
        onError: (err: any) => void = console.error
    ): Promise<void> {
        if (!callback) return;
        this.docCtrl.startProcessing();

        try {
            await callback();
            onSuccess?.();
        } catch (err) {
            onError?.(err);
        } finally {
            this.docCtrl.stopProcessing();
        }
    }
}

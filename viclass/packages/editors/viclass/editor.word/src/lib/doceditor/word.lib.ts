import {
    Binding,
    CONNECTED_COMMAND,
    createBinding,
    createUndoManager,
    syncLexicalUpdateToYjs,
    syncYjsChangesToLexical,
} from '@lexical/yjs';
import { DocLocalContent, DocumentId, EditorType } from '@viclass/editor.core';
import { $getNodeBySubViewportId, SubViewportNode, WordNodes } from '@viclass/editor.word.transform';
import {
    $createParagraphNode,
    $getRoot,
    $getSelection,
    $isNodeSelection,
    $nodesOfType,
    CommandPayloadType,
    createEditor,
    EditorState,
    LexicalCommand,
    LexicalEditor,
    SELECTION_CHANGE_COMMAND,
} from 'lexical';
import * as Y from 'yjs';
import { WordDocCtrl } from '../docs/word.doc.ctrl';
import { WordDocRestorable } from '../word.api';
import { WordEditor } from '../word.editor';
import lexicalEditorTheme from './lexical.editor.theme';
import { WordContextManager } from './managers';
import { TableManager } from './managers/table-manager/table.manager';
import {
    AutoLinkPlugin,
    CodeHighlightPlugin,
    CopyPastePlugin,
    DebugPlugin,
    DragDropPastePlugin,
    HeadingWrapperPlugin,
    ImagePlugin,
    InputBlockerPlugin,
    INSERT_SUBVIEWPORT_COMMAND,
    LayoutPlugin,
    LinkPlugin,
    ListPlugin,
    LOAD_SUBVIEWPORTS_COMMAND,
    RichTextPlugin,
    ScrollPlugin,
    SubViewportPlugin,
    TabIndentPlugin,
    TablePlugin,
    TableResizerPlugin,
    WordPlugin,
    YAwarenessPlugin,
    YHistoryPlugin,
} from './plugins';
import { OverflowManager } from './utils/overflow-manager';
import WordYjsProvider from './yjs/word.yjs.provider';

export type InitialEditorStateType = null | string | EditorState | ((editor: LexicalEditor) => void);

const DEBUG_MODE = false;

/**
 * Internal editor of each WordDocCtrl.
 * Basically, it's a wrapper of LexicalEditor and Yjs for each word doc.
 */
export class WordLib {
    lexical: LexicalEditor;
    yDoc: Y.Doc;

    provider: WordYjsProvider;
    binding: Binding;
    undoManager: Y.UndoManager;
    contextManager: WordContextManager = new WordContextManager(this);
    tableManager: TableManager = new TableManager(this);

    unsubscribes: Array<() => void> = [];

    isReloadingDoc: boolean = false;

    pluginMap: Map<string, WordPlugin> = new Map();
    overflowManager: OverflowManager;

    get docGlobalId(): string {
        return this.docCtrl.state.globalId;
    }

    get rootElement(): HTMLElement {
        return this.lexical.getRootElement();
    }

    constructor(
        private root: HTMLElement,
        public editor: WordEditor,
        public docCtrl: WordDocCtrl,
        private restoreFrom?: WordDocRestorable
    ) {
        if (restoreFrom) {
            this.lexical = restoreFrom.lexical;
            this.yDoc = restoreFrom.yDoc;
            this.provider = restoreFrom.provider;
            this.binding = restoreFrom.binding;
            this.undoManager = restoreFrom.undoManager;

            this.lexical.setRootElement(this.root);
            this.provider.docCtrl = this.docCtrl;
        } else {
            this.lexical = createEditor({
                onError: console.error,
                editable: false,
                nodes: [...WordNodes],
                theme: lexicalEditorTheme,
            });
            this.root.ariaReadOnly = 'true';
            this.root.contentEditable = 'false';

            this.lexical.setRootElement(root);

            this.yDoc = new Y.Doc();
            this.provider = new WordYjsProvider(this.yDoc, this.editor, this.docCtrl);

            const docMap = new Map<string, Y.Doc>();
            docMap.set(this.docGlobalId, this.yDoc);
            this.binding = createBinding(this.lexical, this.provider, this.docGlobalId, this.yDoc, docMap);
            this.undoManager = createUndoManager(this.binding, this.binding.root.getSharedType());
        }

        const namespace = this.lexical._config.namespace; // unique for each LexicalEditor instance
        this.root.id = `lexical-root_${namespace}`; // require to captureAllKeyboardEvent in WordToolBar
        this.enableBrowserSpellChecker(false);

        this.binding.root.getSharedType().observeDeep(this.onYjsTreeChanges);
        this.provider.on('reload', this.onProviderDocReload);
        this.provider.on('status', this.onStatus);
    }

    init(initialState?: Uint8Array) {
        this.overflowManager = new OverflowManager(this.lexical.getRootElement());
        this.initPlugins();
        this.initManagers();

        // apply the init state to yDoc before setup binding, so the init state won't be saved another time
        if (!this.restoreFrom && initialState)
            this.lexical.update(() => Y.applyUpdateV2(this.yDoc, initialState, this), { discrete: true });

        this.unsubscribes.push(
            this.lexical.registerUpdateListener(
                ({ prevEditorState, editorState, dirtyLeaves, dirtyElements, normalizedNodes, tags }) => {
                    if (tags.has('skip-collab') === false) {
                        syncLexicalUpdateToYjs(
                            this.binding,
                            this.provider,
                            prevEditorState,
                            editorState,
                            dirtyElements,
                            dirtyLeaves,
                            normalizedNodes,
                            tags
                        );
                    }
                }
            )
        );

        const contentVisibleSubscription = this.contextManager.isContentEmptyChanges$.subscribe(isEmpty => {
            this.editor.notifyContentVisibilityChange(this.docCtrl);
        });
        this.unsubscribes.push(() => contentVisibleSubscription.unsubscribe());

        this.provider.connect();
        this.yDoc.load();

        // call set content with no update to init for empty doc
        this.setContent();
    }

    initPlugins() {
        this.pluginMap.set('richtext', new RichTextPlugin(this.docCtrl));
        this.pluginMap.set('subviewport', new SubViewportPlugin(this.docCtrl));
        this.pluginMap.set('history', new YHistoryPlugin(this.docCtrl));
        this.pluginMap.set('list', new ListPlugin(this.docCtrl));
        this.pluginMap.set('table', new TablePlugin(this.docCtrl));
        this.pluginMap.set('table-resize', new TableResizerPlugin(this.docCtrl));
        this.pluginMap.set('layout', new LayoutPlugin(this.docCtrl));
        this.pluginMap.set('tab-indent', new TabIndentPlugin(this.docCtrl));
        this.pluginMap.set('link', new LinkPlugin(this.docCtrl));
        this.pluginMap.set('auto-link', new AutoLinkPlugin(this.docCtrl));
        this.pluginMap.set('image', new ImagePlugin(this.docCtrl));
        this.pluginMap.set('code-highlight', new CodeHighlightPlugin(this.docCtrl));
        this.pluginMap.set('drag-drop-paste', new DragDropPastePlugin(this.docCtrl));
        this.pluginMap.set('heading-wrapper', new HeadingWrapperPlugin(this.docCtrl));
        this.pluginMap.set('input-blocker', new InputBlockerPlugin(this.docCtrl));
        this.pluginMap.set('copypaste', new CopyPastePlugin(this.docCtrl));
        this.pluginMap.set('scroll', new ScrollPlugin(this.docCtrl));
        this.pluginMap.set('awareness', new YAwarenessPlugin(this.docCtrl));

        // debug plugin should be registered after all other plugins
        if (DEBUG_MODE) this.pluginMap.set('debug', new DebugPlugin(this.docCtrl));

        this.pluginMap.forEach(plugin => plugin.init());
    }

    initManagers() {
        this.contextManager.init();
        this.tableManager.init();
    }

    isContentEmpty(): boolean {
        return this.contextManager.isContentEmpty;
    }

    setContent(state?: Uint8Array) {
        // force lexical to update immediately
        if (state) {
            this.lexical.update(() => Y.applyUpdateV2(this.yDoc, state, this), { discrete: true });
        }

        this.ensureRootNodeInit();
    }

    destroy(): WordDocRestorable {
        this.setEditable(false);

        this.binding.root.getSharedType().unobserveDeep(this.onYjsTreeChanges);

        this.provider.off('reload', this.onProviderDocReload);
        this.provider.off('status', this.onStatus);
        this.provider.disconnect();

        this.pluginMap.forEach(plugin => plugin.destroy());
        [this.contextManager, this.tableManager].forEach(manager => manager.destroy());
        this.overflowManager?.destroy();

        this.unsubscribes.forEach(fn => fn());

        return {
            lexical: this.lexical,
            yDoc: this.yDoc,
            provider: this.provider,
            binding: this.binding,
            undoManager: this.undoManager,
        };
    }

    setEditable(editable: boolean) {
        if (!editable) this.clearNodeSelection(true);

        this.lexical.setEditable(editable);
        this.root.ariaReadOnly = editable ? 'false' : 'true';
        this.root.contentEditable = editable ? 'true' : 'false';

        if (editable) this.lexical.getRootElement().focus({ preventScroll: true });
        this.ensureRootNodeInit();
    }

    isEditable() {
        return this.lexical.isEditable();
    }

    enableBrowserSpellChecker(enable: boolean) {
        this.root.spellcheck = enable;
    }

    clearNodeSelection(sync: boolean = true) {
        this.lexical.update(
            () => {
                const selection = $getSelection();
                if ($isNodeSelection(selection)) {
                    selection.clear();
                    // as we clear selection manually, we must dispatch the cmd so the subviewports can focus-out properly
                    this.lexical.dispatchCommand(SELECTION_CHANGE_COMMAND, undefined);
                }
            },
            { discrete: sync ? true : undefined }
        );
    }

    dispatchCommand<TCommand extends LexicalCommand<unknown>>(
        type: TCommand,
        payload: CommandPayloadType<TCommand>
    ): boolean {
        return this.lexical.dispatchCommand(type, payload);
    }

    insertViewport(vpId: string, edType: EditorType) {
        this.lexical.dispatchCommand(INSERT_SUBVIEWPORT_COMMAND, {
            editorType: edType,
            viewportId: vpId,
        });
    }

    /**
     * Load all subviewports that are not yet loaded
     */
    loadPendingSubViewportNodes() {
        this.dispatchCommand(LOAD_SUBVIEWPORTS_COMMAND, undefined);
    }

    updateSubViewportMapping(vmId: string, globalId: DocumentId, localId: number) {
        this.lexical.update(
            () => {
                const node = $getNodeBySubViewportId(vmId);

                if (!node) {
                    throw new Error('No node for SubViewport ' + vmId);
                }

                node.globalId = globalId;
                node.localId = localId;
            },
            { discrete: true }
        );
    }

    readUpdate(yClientId: number, update: Uint8Array) {
        if (yClientId === this.yDoc.clientID) return; // don't read our own updates

        this.provider.readMessage(update, true);
    }

    /**
     * Flush all Lexical updates to yjs and all accumulated yjs updates to sync cmd
     */
    async flushUpdates() {
        this.forceLexicalUpdate();
        await this.provider.forceFlush();
    }

    /**
     * Force Lexical to apply all updates
     */
    forceLexicalUpdate() {
        this.lexical.update(() => {}, { discrete: true });
    }

    getSubViewportLocalContent(vmId: string): DocLocalContent | undefined {
        return this.lexical.read(() => {
            const nodes = $nodesOfType(SubViewportNode);
            for (const node of nodes) {
                if (node.viewportId === vmId) {
                    return node.localContent;
                }
            }

            return undefined;
        });
    }

    private ensureRootNodeInit() {
        if (this.isEditable()) {
            const historyPlugin = this.pluginMap.get('history') as YHistoryPlugin;
            historyPlugin.initDocFromEmptyState();

            this.lexical.update(
                () => {
                    const rootNode = $getRoot();
                    if (!rootNode.getFirstChild()) rootNode.append($createParagraphNode());
                },
                { discrete: true }
            );
        }
    }

    private onYjsTreeChanges = (events: Array<Y.YEvent<any>>, transaction: Y.Transaction) => {
        const origin = transaction.origin;
        if (origin !== this.binding) {
            const isFromUndoManger = origin instanceof Y.UndoManager;
            syncYjsChangesToLexical(this.binding, this.provider, events, isFromUndoManger);
        }
    };

    onProviderDocReload = async (ydoc: Y.Doc) => {
        console.log('RELOAD', ydoc);
        const res = await this.docCtrl.getWordDocContent();
        if (!res) {
            console.warn('Reload word doc failed: No content found');
            return;
        }

        const { content } = res;
        if (!content) {
            console.warn('Reload word doc failed: Empty content');
            return;
        }

        this.docCtrl.setContent(content);
    };

    private onStatus = ({ status }: { status: string }) => {
        this.lexical.dispatchCommand(CONNECTED_COMMAND, status === 'connected');
    };
}

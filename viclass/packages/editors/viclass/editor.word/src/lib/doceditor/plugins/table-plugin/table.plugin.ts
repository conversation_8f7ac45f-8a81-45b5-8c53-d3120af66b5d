import {
    $computeTableMapSkip<PERSON>ell<PERSON>heck,
    $createTableCellNode,
    $createTableNodeWithDimensions,
    $findTableNode,
    $getTableAndElementByKey,
    $isTableCellNode,
    $isTableNode,
    $isTableRowNode,
    applyTableHandlers,
    getTableElement,
    HTMLTableElementWithWithTableSelectionState,
    InsertTableCommandPayload,
    TableCellNode,
    TableNode,
    TableObserver,
    TableRowNode,
} from '@lexical/table';
import { $insertFirst, $unwrapAndFilterDescendants, $wrapNodeInElement, mergeRegister } from '@lexical/utils';
import { NonCriticalDocumentAPIErr } from '@viclass/editor.core';
import {
    $createEmptyTextToken,
    $isCustomTableCellNode,
    $unwrapNode,
    CustomTableCellNode,
} from '@viclass/editor.word.transform';
import {
    $createParagraphNode,
    $getNodeByKey,
    $getSelection,
    $insertNodes,
    $isRangeSelection,
    $isRootOrShadowRoot,
    $isTextNode,
    $nodesOfType,
    COMMAND_PRIORITY_EDITOR,
    createCommand,
    LexicalNode,
    NodeKey,
    ParagraphNode,
    SELECTION_INSERT_CLIPBOARD_NODES_COMMAND,
    TextNode,
} from 'lexical';
import { WordPlugin } from '../word.plugin';

export const INSERT_NEW_TABLE_COMMAND = createCommand<InsertTableCommandPayload>('INSERT_NEW_TABLE_COMMAND');

/**
 * Plugin to handle table features.
 * Listen and handle some table commands like table creation, selection, deletion
 */
export class TablePlugin extends WordPlugin {
    /**
     * Start listen to insert table commands
     *
     * @inheritdoc
     */
    init() {
        this.handleTableInsertCommand();

        this.preventNestedTableInsertionByPaste();

        this.handleTransformTableNode();

        this.handleTableSurroundingText();

        this.initTableSelection();

        this.makeSureTableCellContainsParagraph();

        this.addUnsubscribe(
            mergeRegister(
                this.lexical.registerNodeTransform(TableNode, $tableTransform),
                this.lexical.registerNodeTransform(TableRowNode, $tableRowTransform),
                this.lexical.registerNodeTransform(TableCellNode, $tableCellTransform)
            )
        );
    }

    /**
     * Make sure we can edit the text around the table.
     * For leading text, handle unwrap the unnecessary paragraph when user type before the table
     * For trailing text, handle auto insert an empty text node after the table
     */
    private handleTableSurroundingText() {
        /**
         * Auto insert an empty text node after the table node when it is destroyed.
         * This make sure we can move the cursor after the table
         */
        this.addUnsubscribe(
            this.lexical.registerMutationListener(TextNode, (nodeMutations, { prevEditorState }) => {
                for (const [nodeKey, mutation] of nodeMutations) {
                    if (mutation === 'destroyed') {
                        prevEditorState.read(() => {
                            const node = $getNodeByKey(nodeKey);

                            const prevNode = node.getPreviousSibling();
                            const nextNode = node.getNextSibling();

                            // for delete the text node after table
                            if (prevNode && $isTableNode(prevNode) && (!nextNode || !$isTextNode(nextNode))) {
                                const tableKey = prevNode.getKey();
                                this.lexical.update(() => {
                                    const tableNode = $getNodeByKey(tableKey);
                                    if (tableNode && !tableNode.getNextSibling()) {
                                        tableNode?.insertAfter($createEmptyTextToken());
                                    }
                                });
                            }
                        });
                    }
                }
            })
        );

        /**
         * When table is at the start of paragraph and we editing from the start position,
         * the new text will be wrapped around another paragraph node which is redundant.
         * We need to unwrap the paragraph node so the text and the table is on the same line
         */
        this.addUnsubscribe(
            this.lexical.registerMutationListener(ParagraphNode, nodeMutations => {
                for (const [nodeKey, mutation] of nodeMutations) {
                    if (mutation === 'created') {
                        this.lexical.update(() => {
                            const node = $getNodeByKey(nodeKey) as ParagraphNode;
                            const nextNode = node.getNextSibling();
                            if (!node.isEmpty() && nextNode && $isTableNode(nextNode)) {
                                $unwrapNode(node);
                            }
                        });
                    }
                }
            })
        );
    }

    /**
     * Originally we only insert the root TableNode, this method will transform the TableNode
     * to include the rest of the cells/rows nodes
     */
    private handleTransformTableNode() {
        this.addUnsubscribe(
            this.lexical.registerNodeTransform(TableNode, node => {
                const [gridMap] = $computeTableMapSkipCellCheck(node, null, null);
                const maxRowLength = gridMap.reduce((curLength, row) => {
                    return Math.max(curLength, row.length);
                }, 0);
                for (let i = 0; i < gridMap.length; ++i) {
                    const rowLength = gridMap[i].length;
                    if (rowLength === maxRowLength) {
                        continue;
                    }
                    const lastCellMap = gridMap[i][rowLength - 1];
                    const lastRowCell = lastCellMap.cell;
                    for (let j = rowLength; j < maxRowLength; ++j) {
                        // TODO: inherit header state from another header or body
                        const newCell = $createTableCellNode(0);
                        newCell.append($createParagraphNode());
                        if (lastRowCell !== null) {
                            lastRowCell.insertAfter(newCell);
                        } else {
                            $insertFirst(lastRowCell, newCell);
                        }
                    }
                }
            })
        );
    }

    /**
     * Handle insert table command
     *
     * ! Nested table leads to unintended selection behavior where selecting cells in inner tables
     * also selects outer table cells so we won't allow it (https://github.com/facebook/lexical/pull/7192)
     */
    private handleTableInsertCommand() {
        this.addUnsubscribe(
            this.lexical.registerCommand<InsertTableCommandPayload>(
                INSERT_NEW_TABLE_COMMAND,
                ({ columns, rows, includeHeaders }) => {
                    const selection = $getSelection();
                    if (selection) {
                        if (!$isRangeSelection(selection)) return false;

                        // Prevent nested tables by checking if we're already inside a table
                        if ($findTableNode(selection.anchor.getNode())) {
                            return false;
                        }
                    }

                    const tableNode = $createTableNodeWithDimensions(Number(rows), Number(columns), includeHeaders);
                    tableNode.isInline = () => true;
                    $insertNodes([tableNode]);
                    if ($isRootOrShadowRoot(tableNode.getParent())) {
                        $wrapNodeInElement(tableNode, $createParagraphNode).select();
                    }
                    const nextNode = tableNode.getNextSibling();
                    if (!nextNode || !$isTextNode(nextNode)) {
                        tableNode.insertAfter($createEmptyTextToken());
                    }

                    const firstDescendant = tableNode.getFirstDescendant();
                    if ($isTextNode(firstDescendant)) {
                        firstDescendant.select();
                    }
                    return true;
                },
                COMMAND_PRIORITY_EDITOR
            )
        );
    }

    /**
     * Prevent nested tables when pasting by checking if we're already inside a table.
     */
    private preventNestedTableInsertionByPaste() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                SELECTION_INSERT_CLIPBOARD_NODES_COMMAND,
                ({ nodes, selection }) => {
                    if (!$isRangeSelection(selection)) {
                        return false;
                    }
                    const isInsideTableCell = $findTableNode(selection.anchor.getNode()) !== null;
                    return isInsideTableCell && nodes.some($isTableNode);
                },
                COMMAND_PRIORITY_EDITOR
            )
        );
    }

    /**
     * ! Extracted from TablePlugin of @lexical/react for table selection.
     * Every table node has a corresponding TableObserver and HTMLTableElementWithWithTableSelectionState to handle the selection
     */
    initTableSelection(): void {
        const tableSelections = new Map<NodeKey, [TableObserver, HTMLTableElementWithWithTableSelectionState]>();

        const initializeTableNode = (tableNode: TableNode, nodeKey: NodeKey, dom: HTMLElement) => {
            const tableElement = getTableElement(tableNode, dom);
            const tableSelection = applyTableHandlers(tableNode, tableElement, this.lexical, true);
            tableSelections.set(nodeKey, [tableSelection, tableElement]);
        };

        const unregisterMutationListener = this.lexical.registerMutationListener(
            TableNode,
            nodeMutations => {
                this.lexical.getEditorState().read(
                    () => {
                        for (const [nodeKey, mutation] of nodeMutations) {
                            const tableSelection = tableSelections.get(nodeKey);
                            if (mutation === 'created' || mutation === 'updated') {
                                const { tableNode, tableElement } = $getTableAndElementByKey(nodeKey);
                                if (tableSelection === undefined) {
                                    initializeTableNode(tableNode, nodeKey, tableElement);
                                } else if (tableElement !== tableSelection[1]) {
                                    // The update created a new DOM node, destroy the existing TableObserver
                                    tableSelection[0].removeListeners();
                                    tableSelections.delete(nodeKey);
                                    initializeTableNode(tableNode, nodeKey, tableElement);
                                }
                            } else if (mutation === 'destroyed') {
                                if (tableSelection !== undefined) {
                                    tableSelection[0].removeListeners();
                                    tableSelections.delete(nodeKey);
                                }
                            }
                        }
                    },
                    { editor: this.lexical }
                );
            },
            { skipInitialization: false }
        );

        this.addUnsubscribe(() => {
            unregisterMutationListener();
            // Hook might be called multiple times so cleaning up tables listeners as well,
            // as it'll be reinitialized during recurring call
            for (const [, [tableSelection]] of tableSelections) {
                tableSelection.removeListeners();
            }
        });
    }

    private makeSureTableCellContainsParagraph() {
        this.addUnsubscribe(
            this.lexical.registerMutationListener(ParagraphNode, (nodeMutations, { prevEditorState }) => {
                for (const [nodeKey, mutation] of nodeMutations) {
                    if (mutation === 'destroyed') {
                        prevEditorState.read(() => {
                            const node = $getNodeByKey<LexicalNode>(nodeKey);
                            const parent = node?.getParent();
                            if (parent && $isCustomTableCellNode(parent) && parent.getChildren().length === 1) {
                                // If paragraph is removed from cell, we need to add it back
                                // to ensure that cell always has at least one paragraph
                                const parentKey = parent.getKey();
                                this.lexical.update(() => {
                                    $getNodeByKey<CustomTableCellNode>(parentKey)?.append($createParagraphNode());
                                });
                            }
                        });
                    }
                }
            })
        );
    }
}

/**
 * Handle cell transform to init child paragraph and destroy when no longer in valid context
 */
function $tableCellTransform(node: TableCellNode) {
    if (!$isTableRowNode(node.getParent())) {
        // TableCellNode must be a child of TableRowNode.
        node.remove();
    } else if (node.isEmpty()) {
        // TableCellNode should never be empty
        node.append($createParagraphNode());
    }
}

/**
 * Handle transformation on row node to auto destroy when it no longer in table
 * and make sure it contains only table cell node
 */
function $tableRowTransform(node: TableRowNode) {
    if (!$isTableNode(node.getParent())) {
        // TableRowNode must be a child of TableNode.
        // TODO: Future support of tbody/thead/tfoot may change this
        node.remove();
    } else {
        $unwrapAndFilterDescendants(node, $isTableCellNode);
    }
}

/**
 * Handle transformation on table node to make sure it contains only table row node and the cells node are properly init
 */
function $tableTransform(node: TableNode) {
    // TableRowNode is the only valid child for TableNode
    // TODO: Future support of tbody/thead/tfoot/caption may change this
    $unwrapAndFilterDescendants(node, $isTableRowNode);

    const [gridMap] = $computeTableMapSkipCellCheck(node, null, null);
    const maxRowLength = gridMap.reduce((curLength, row) => {
        return Math.max(curLength, row.length);
    }, 0);
    const rowNodes = node.getChildren();
    for (let i = 0; i < gridMap.length; ++i) {
        const rowNode = rowNodes[i];
        if (!rowNode) {
            continue;
        }
        if (!$isTableRowNode(rowNode)) {
            throw new NonCriticalDocumentAPIErr(
                `TablePlugin: Expecting all children of TableNode to be TableRowNode, found ${rowNode.constructor.name} (type ${rowNode.getType()})`
            );
        }
        const rowLength = gridMap[i].reduce((acc, cell) => (cell ? 1 + acc : acc), 0);
        if (rowLength === maxRowLength) {
            continue;
        }
        for (let j = rowLength; j < maxRowLength; ++j) {
            // TODO: inherit header state from another header or body
            const newCell = $createTableCellNode();
            newCell.append($createParagraphNode());
            rowNode.append(newCell);
        }
    }
}

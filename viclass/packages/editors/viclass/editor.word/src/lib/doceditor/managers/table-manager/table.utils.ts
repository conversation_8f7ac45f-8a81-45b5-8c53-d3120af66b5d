import {
    $createTableSelection,
    $getNodeTriplet,
    $isTableCellNode,
    $isTableRowNode,
    $isTableSelection,
    TableCellNode,
    TableSelection,
} from '@lexical/table';
import { CustomTableCellNode } from '@viclass/editor.word.transform';
import {
    $getSelection,
    $isElementNode,
    $isParagraphNode,
    $isRangeSelection,
    $isTextNode,
    $setSelection,
    ElementNode,
    LexicalEditor,
} from 'lexical';

export function computeSelectionCount(selection: TableSelection): {
    columns: number;
    rows: number;
} {
    const selectionShape = selection.getShape();
    return {
        columns: selectionShape.toX - selectionShape.fromX + 1,
        rows: selectionShape.toY - selectionShape.fromY + 1,
    };
}

// This is important when merging cells as there is no good way to re-merge weird shapes (a result
// of selecting merged cells and non-merged)
export function isTableSelectionRectangular(selection: TableSelection): boolean {
    const nodes = selection.getNodes();
    const currentRows: Array<number> = [];
    let currentRow = null;
    let expectedColumns = null;
    let currentColumns = 0;
    for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        if ($isTableCellNode(node)) {
            const row = node.getParentOrThrow();
            if (!$isTableRowNode(row)) {
                throw 'Expected CellNode to have a RowNode parent';
            }
            if (currentRow !== row) {
                if (expectedColumns !== null && currentColumns !== expectedColumns) {
                    return false;
                }
                if (currentRow !== null) {
                    expectedColumns = currentColumns;
                }
                currentRow = row;
                currentColumns = 0;
            }
            const colSpan = node.__colSpan;
            for (let j = 0; j < colSpan; j++) {
                if (currentRows[currentColumns + j] === undefined) {
                    currentRows[currentColumns + j] = 0;
                }
                currentRows[currentColumns + j] += node.__rowSpan;
            }
            currentColumns += colSpan;
        }
    }
    return (
        (expectedColumns === null || currentColumns === expectedColumns) && currentRows.every(v => v === currentRows[0])
    );
}

export function $canUnmerge(editingCell: TableCellNode | null): boolean {
    if (editingCell) {
        return editingCell.__colSpan > 1 || editingCell.__rowSpan > 1;
    }

    try {
        const selection = $getSelection();
        if (
            ($isRangeSelection(selection) && !selection.isCollapsed()) ||
            ($isTableSelection(selection) && !selection.anchor.is(selection.focus)) ||
            (!$isRangeSelection(selection) && !$isTableSelection(selection))
        ) {
            return false;
        }
        const [cell] = $getNodeTriplet(selection.anchor);
        return cell.__colSpan > 1 || cell.__rowSpan > 1;
    } catch (e) {
        return false;
    }
}

export function $cellContainsEmptyParagraph(cell: TableCellNode): boolean {
    if (cell.getChildrenSize() !== 1) {
        return false;
    }
    const firstChild = cell.getFirstChildOrThrow();
    if (!$isParagraphNode(firstChild) || !firstChild.isEmpty()) {
        return false;
    }
    return true;
}

export function $selectLastDescendant(node: ElementNode): void {
    const lastDescendant = node.getLastDescendant();
    if ($isTextNode(lastDescendant)) {
        lastDescendant.select();
    } else if ($isElementNode(lastDescendant)) {
        lastDescendant.selectEnd();
    } else if (lastDescendant !== null) {
        lastDescendant.selectNext();
    }
}

export function currentCellBackgroundColor(editor: LexicalEditor): null | string {
    return editor.getEditorState().read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection) || $isTableSelection(selection)) {
            const [cell] = $getNodeTriplet(selection.anchor);
            if ($isTableCellNode(cell)) {
                return cell.getBackgroundColor();
            }
        }
        return null;
    });
}

export function $createTableCellSelection(cell: TableCellNode): TableSelection {
    const selection = $createTableSelection();
    selection.set(cell.getKey(), cell.getKey(), cell.getKey());

    $setSelection(selection);
    return selection;
}

/**
 * Selection bounds for table operations
 */
export type SelectionBounds = {
    minRow: number;
    maxRow: number;
    minCol: number;
    maxCol: number;
};
/**
 * Get selection bounds from selected cells
 */
export function getSelectionBounds(selectedCells: CustomTableCellNode[], gridMap: any[][]): SelectionBounds | null {
    if (selectedCells.length === 0) return null;

    let minRow = Infinity,
        maxRow = -1,
        minCol = Infinity,
        maxCol = -1;

    for (const cell of selectedCells) {
        for (let row = 0; row < gridMap.length; row++) {
            for (let col = 0; col < gridMap[row].length; col++) {
                if (gridMap[row][col]?.cell === cell) {
                    minRow = Math.min(minRow, row);
                    maxRow = Math.max(maxRow, row);
                    minCol = Math.min(minCol, col);
                    maxCol = Math.max(maxCol, col);
                }
            }
        }
    }

    return minRow !== Infinity ? { minRow, maxRow, minCol, maxCol } : null;
}

import {
    AwarenessCmdOption,
    AwarenessId,
    buildDocumentAwarenessCmdOption,
    copyDTOToClipboard,
    CopyPasteDocDTO,
    CopyPasteDTO,
    CopyPasteType,
    DocLocalContent,
    DocLocalId,
    DocumentId,
    EditorType,
    Position,
    RectSize,
    sendNotiMessage,
    SupportCopyPasteFeature,
    SupportRemoveFeature,
    ViewportId,
} from '@viclass/editor.core';
import {
    $createSubViewportNode,
    $insertSubViewportNode,
    $isImageNode,
    $isSubViewportNode,
    ImageNode,
    IMPORT_SUBVIEWPORT_ID,
    SubViewportNode,
} from '@viclass/editor.word.transform';
import {
    $getSelection,
    $isNodeSelection,
    COMMAND_PRIORITY_LOW,
    COPY_COMMAND,
    createCommand,
    CUT_COMMAND,
    type ElementFormatType,
    LexicalCommand,
    NodeKey,
    PASTE_COMMAND,
} from 'lexical';
import { WordDocCtrl } from '../../../docs/word.doc.ctrl';
import { WordEditor } from '../../../word.api';
import { DELETE_SUBVIEWPORTS_COMMAND } from '../subviewport-plugin/subviewport.utils';
import { WordPlugin } from '../word.plugin';
import { handleCopyImage } from './image.copy.handler';
import {
    DtoPasteHandler,
    getCopyPasteSupporter,
    getRemoveSupporter,
    ImagePasteHandler,
    PasteHandler,
} from './paste-handlers';

const IGNORE_ASYNC_PASTE = 'viclass/ignore-async-paste';

export const PASTE_SUBVIEWPORT_COMMAND: LexicalCommand<SubViewportCopyPasteDTO> =
    createCommand('PASTE_SUBVIEWPORT_COMMAND');

export class SubViewportCopyPasteDocDTO implements CopyPasteDocDTO {
    editorType: EditorType;
    docLocalId: DocLocalId;
    docGlobalId: DocumentId;

    lookAt: Position;
    zoom: number;
    format: ElementFormatType;
    key: NodeKey;
    size: RectSize;

    localContent?: DocLocalContent;
}

export class SubViewportCopyPasteDTO implements CopyPasteDTO {
    type: CopyPasteType = 'documents';
    vpId: ViewportId;
    documents: SubViewportCopyPasteDocDTO[];
}

export function isSubViewportCopyPasteDTO(obj: any): boolean {
    if (!obj) return false;
    if (!obj.hasOwnProperty('type')) return false;
    if (!obj.hasOwnProperty('vpId')) return false;
    if (!obj.hasOwnProperty('documents')) return false;
    if (obj['type'] !== 'documents') return false;
    if (!Array.isArray(obj['documents'])) return false;
    if (obj['documents'].some(o => !isSubViewportCopyPasteDocDTO(o))) return false;
    return true;
}

export function isSubViewportCopyPasteDocDTO(obj: any): boolean {
    if (!obj.hasOwnProperty('editorType')) return false;
    if (!obj.hasOwnProperty('docGlobalId')) return false;
    if (obj['editorType'] === 'WordEditor') return false;
    return true;
}

/**
 * Handle Copy Paste actions in Word. Override lexical default behavior to allow handle paste action in asynchronous way.
 *
 * The flow of pasting is:
 * - Catch the PASTE_COMMAND and mark it as handled + save the previous DataTransfer object
 * and handle the paste event in an async way using the PasteHandlers.
 * - If the event is not handled then dispatch a new PASTE_COMMAND with a ignore flag + the restored DataTransfer object
 * - Catch the newly dispatched PASTE_COMMAND to handle synchronously, and leave lexical to automatically handle the rest
 */
export class CopyPastePlugin extends WordPlugin {
    private readonly pasteHandlers: PasteHandler[];

    constructor(docCtrl: WordDocCtrl) {
        super(docCtrl);

        this.pasteHandlers = [
            new DtoPasteHandler(docCtrl, this.sendPasteAwareness),
            new ImagePasteHandler(docCtrl, this.sendPasteAwareness),
        ];
    }

    /**
     * @inheritdoc
     */
    override init(): void {
        this.handleCopyCommand();
        this.handleCutCommand();
        this.handlePasteCommand();
        this.handlePasteSubViewportCommand();
    }

    /**
     * Handle COPY_COMMAND to copy the selected sub-viewport node into clipboard.
     * The DTO will be generated by the feature supporter of the sub-viewport node then copy into clipboard.
     */
    private handleCopyCommand() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                COPY_COMMAND,
                event => {
                    if (event) event.stopPropagation();

                    const selection = $getSelection();
                    const selectedNodes = selection?.getNodes() || [];

                    if (selectedNodes.length === 1 && $isNodeSelection(selection)) {
                        const node = selectedNodes[0];
                        if ($isSubViewportNode(node)) {
                            const supporter = getCopyPasteSupporter(this.wordEditor.wcoord, node);
                            const dto = this.extractedSubVpDto(node);

                            this.handleCopyDto(supporter, dto, node.viewportId);

                            return true;
                        } else if ($isImageNode(node)) {
                            this.handleCopyImageNode(node);
                            return true;
                        }
                    }

                    return false;
                },
                COMMAND_PRIORITY_LOW
            )
        );
    }

    /**
     * Handle COPY_COMMAND to copy the selected sub-viewport node into clipboard.
     * The DTO will be generated by the feature supporter of the sub-viewport node then copy into clipboard.
     */
    private handleCutCommand() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                CUT_COMMAND,
                event => {
                    if (event) event.stopPropagation();

                    const selection = $getSelection();
                    const selectedNodes = selection?.getNodes() || [];

                    if (selectedNodes.length === 1 && $isNodeSelection(selection)) {
                        const node = selectedNodes[0];
                        if (!$isSubViewportNode(node)) return false;

                        const copySupporter = getCopyPasteSupporter(this.wordEditor.wcoord, node);
                        const removeSupporter = getRemoveSupporter(this.wordEditor.wcoord, node);
                        const dto = this.extractedSubVpDto(node);

                        this.handleCutDto(copySupporter, removeSupporter, dto, node.viewportId);

                        return true;
                    }

                    return false;
                },
                COMMAND_PRIORITY_LOW
            )
        );
    }

    /**
     * Our custom clipboard coming from the new `navigator.clipboard` Async API, which is not accessible from the
     * DataTransfer obj of ClipboardEvent. Because the event handling of lexical is required to be Sync, so:
     *
     * The PASTE_COMMAND handling includes 2 steps with 2 commands dispatched:
     * - original CMD: mark CMD as handled + get DTO from `navigator.clipboard` API (async):
     *      + Has DTO: dispatch PASTE_SUBVIEWPORT_COMMAND -> to the insert sub-vp flow
     *      + No DTO: dispatch a new PASTE_COMMAND with the same DataTransfer obj having a checked flag
     * - the 2nd PASTE_COMMAND: check the flag of no DTO -> send awareness and let lexical handle the rest
     */
    private handlePasteCommand() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                PASTE_COMMAND,
                (e: ClipboardEvent) => {
                    const dataTransfer = e.clipboardData;
                    if (!dataTransfer) return false;

                    const isDtoChecked = dataTransfer.getData(IGNORE_ASYNC_PASTE) === 'true';
                    if (isDtoChecked) {
                        const lexicalClipboard = dataTransfer.getData('application/x-lexical-editor');
                        if (
                            (lexicalClipboard && lexicalClipboard.length > 0) ||
                            dataTransfer.getData('text/plain') ||
                            dataTransfer.getData('text/html') ||
                            dataTransfer.getData('text/uri-list')
                        ) {
                            this.sendPasteAwareness();
                        }

                        return false;
                    }

                    e.preventDefault();
                    this.handlePasteAsync(dataTransfer);

                    return true;
                },
                COMMAND_PRIORITY_LOW
            )
        );
    }

    /**
     * Handle the PASTE_SUBVIEWPORT_COMMAND that dispatched in the async paste flow.
     * It use the dto from command to create and insert new sub-viewport node
     */
    private handlePasteSubViewportCommand() {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                PASTE_SUBVIEWPORT_COMMAND,
                (dto: SubViewportCopyPasteDTO) => {
                    try {
                        for (const _dto of dto.documents) {
                            // insert sub viewport node in to document
                            const subVpNode = $createSubViewportNode(
                                IMPORT_SUBVIEWPORT_ID,
                                _dto.editorType,
                                _dto.docGlobalId,
                                1, // subviewport only contain 1 document
                                _dto.lookAt,
                                _dto.zoom,
                                _dto.format
                            );
                            subVpNode.__localContent = _dto.localContent;

                            $insertSubViewportNode(subVpNode);
                        }

                        this.showToast('Dán tài liệu thành công');
                    } catch (error) {
                        console.error('paste subviewport command failed', dto, error);
                    }

                    return true;
                },
                COMMAND_PRIORITY_LOW
            )
        );
    }

    private async handleCopyImageNode(node: ImageNode) {
        const imageProxyPrefix = (this.wordEditor.coordinator as any).conf.imageProxyPrefix || '';
        try {
            await handleCopyImage(node, imageProxyPrefix);
            this.showToast('Sao chép hình ảnh thành công');
        } catch (e) {
            console.error('Failed to copy image:', e);
            this.showToast('Sao chép hình ảnh thất bại', 'error');
        }
    }

    /**
     * Extracted sub-viewport DTO from the node. This DTO will be used to copy the sub-viewport node into clipboard.
     * @param node
     */
    private extractedSubVpDto(node: SubViewportNode): SubViewportCopyPasteDTO {
        return {
            type: 'documents',
            vpId: node.viewportId,
            documents: [
                {
                    editorType: node.editorType,
                    docLocalId: node.localId,
                    docGlobalId: node.globalId,
                    format: node.format,
                    key: node.getKey(),
                    lookAt: node.lookAt,
                    zoom: node.zoom,
                    size: node.size,
                    localContent: node.localContent,
                },
            ],
        };
    }

    /**
     * Copy the viewport DTO to the clipboard
     */
    private async handleCopyDto(supporter: SupportCopyPasteFeature, dto: CopyPasteDTO, vpId: string) {
        if (supporter) {
            const result = await supporter.copy(vpId, false);
            if (result === true) return; // supporter handled the copy (ex: copy a content that not a document)
            if (!!result) {
                dto = result; // supporter generated a custom copy DTO
            }
        }
        // chose between dto generated by word or generated by sub-viewport
        await copyDTOToClipboard(dto);
        this.showToast('Sao chép tài liệu thành công');
    }

    /**
     * Cut the viewport DTO to the clipboard
     */
    private async handleCutDto(
        copySupporter: SupportCopyPasteFeature,
        removeSupporter: SupportRemoveFeature,
        dto: CopyPasteDTO,
        vpId: string
    ) {
        let supporterCopied = false;
        if (copySupporter) {
            const result = await copySupporter.copy(vpId, true);
            if (result === true) {
                supporterCopied = true; // supporter handled the copy (ex: copy a content that not a document)
            } else if (!!result) {
                dto = result; // supporter generated a custom copy DTO
            }
        }

        let supporterRemoved = false;
        if (removeSupporter) {
            supporterRemoved = await removeSupporter.remove(vpId, true);
        }

        if (!supporterRemoved) this.lexical.dispatchCommand(DELETE_SUBVIEWPORTS_COMMAND, [vpId]);
        if (!supporterCopied) {
            await copyDTOToClipboard(dto);
            this.showToast('Cắt tài liệu thành công');
        }
    }

    /**
     * Handle the paste event in an async way. If the event is not handled then dispatch a new PASTE_COMMAND
     * with the same DataTransfer obj and having a checked flag to ignore this async paste
     */
    private async handlePasteAsync(prevData: DataTransfer) {
        const data = new DataTransfer();
        prevData.types.forEach(dType => data.setData(dType, prevData.getData(dType)));

        for (const handler of this.pasteHandlers) {
            if (await handler.handlePaste()) {
                return;
            }
        }

        data.setData(IGNORE_ASYNC_PASTE, 'true');
        this.lexical.dispatchCommand(PASTE_COMMAND, new ClipboardEvent('paste', { clipboardData: data }));
    }

    /**
     * Send awareness cmd to show a loading indicator when pasting
     */
    private sendPasteAwareness = async (): Promise<AwarenessId> => {
        const options: AwarenessCmdOption = {
            ...buildDocumentAwarenessCmdOption(WordEditor.awarenessPastingId, this.docCtrl),
            useScheduler: false,
            startAfterSeconds: 0,
            // use a longer auto-expire period because we will clear it manually on receive next update
            expireAfterSeconds: 10,
        };

        // mark next update with awareness then pause sending updates to make sure the next word update will clear the awareness
        this.wordLib.provider.nextUpdateAwareness = WordEditor.awarenessPastingId;
        return await this.wordLib.provider.pauseSendingWhile(() =>
            this.docCtrl.editor.awarenessFeature?.sendAwarenessCommand(this.docCtrl.viewport.id, 'Đang dán', options)
        );
    };

    private showToast(message: string, msgType: 'success' | 'error' | 'info' = 'success') {
        const awarenessFeature = this.docCtrl.editor.awarenessFeature;
        if (awarenessFeature) {
            sendNotiMessage(awarenessFeature, this.docCtrl.viewport.id, message, msgType, {
                isNotSendToRemoteReceiver: true,
            });
        }
    }
}

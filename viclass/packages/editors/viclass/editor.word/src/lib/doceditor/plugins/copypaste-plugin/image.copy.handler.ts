import { ImageNode } from '@viclass/editor.word.transform';

async function prepareImageBlob(node: ImageNode, imageProxyPrefix: string): Promise<{ imageBlob: Blob; type: string }> {
    const src = node.getSrc();

    let imageBlob: Blob;
    let type = 'image/png';

    if (src.startsWith('data:')) {
        const matches = src.match(/^data:(.+);base64,(.*)$/);
        if (matches) {
            type = matches[1];
            const binary = atob(matches[2]);
            const array = new Uint8Array(binary.length);
            for (let i = 0; i < binary.length; i++) {
                array[i] = binary.charCodeAt(i);
            }
            imageBlob = new Blob([array], { type });
        } else {
            throw new Error('Invalid base64 image');
        }
    } else {
        const url = src.startsWith(imageProxyPrefix) ? src : imageProxyPrefix + src;
        try {
            const res = await fetch(url);
            if (!res.ok) throw new Error('Fetch failed');
            imageBlob = await res.blob();
            type = imageBlob.type;
        } catch (e) {
            throw new Error('Failed to fetch image: ' + e);
        }
    }

    return { imageBlob, type };
}

export async function handleCopyImage(node: ImageNode, imageProxyPrefix: string): Promise<void> {
    const { element } = node.exportDOM();
    const html = (element as HTMLImageElement).outerHTML;
    const htmlBlob = new Blob([html], { type: 'text/html' });

    const { imageBlob, type } = await prepareImageBlob(node, imageProxyPrefix);

    const permission = await navigator.permissions.query({ name: 'clipboard-write' } as any);
    if (permission.state !== 'granted' && permission.state !== 'prompt') {
        throw new Error('No clipboard write permission');
    }

    await navigator.clipboard.write([
        new ClipboardItem({
            'text/html': htmlBlob,
            [type]: imageBlob,
        }),
    ]);
}

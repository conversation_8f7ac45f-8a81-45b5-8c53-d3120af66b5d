/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type { ElementNode, LexicalCommand, LexicalEditor, LexicalNode, NodeKey } from 'lexical';

import { $findMatchingParent, $insertNodeToNearestRoot, mergeRegister } from '@lexical/utils';
import {
    $createParagraphNode,
    $getNodeByKey,
    $getSelection,
    $isRangeSelection,
    COMMAND_PRIORITY_EDITOR,
    COMMAND_PRIORITY_HIGH,
    COMMAND_PRIORITY_LOW,
    createCommand,
    KEY_ARROW_DOWN_COMMAND,
    KEY_ARROW_LEFT_COMMAND,
    KEY_ARROW_RIGHT_COMMAND,
    KEY_ARROW_UP_COMMAND,
    ParagraphNode,
    SELECTION_CHANGE_COMMAND,
} from 'lexical';

import {
    $createLayoutContainerNode,
    $createLayoutItemNode,
    $isLayoutContainerNode,
    $isLayoutItemNode,
    LayoutContainerNode,
    LayoutItemNode,
} from '@viclass/editor.word.transform';
import { WordPlugin } from '../word.plugin';

export const INSERT_LAYOUT_COMMAND: LexicalCommand<string> = createCommand<string>();

export const UPDATE_LAYOUT_COMMAND: LexicalCommand<{
    template: string;
    nodeKey: NodeKey;
}> = createCommand<{ template: string; nodeKey: NodeKey }>();

export class LayoutPlugin extends WordPlugin {
    private editingLayoutNodeKey: NodeKey | null = null;

    override init(): void {
        const onEscape = (before: boolean) => {
            const selection = $getSelection();
            if ($isRangeSelection(selection) && selection.isCollapsed() && selection.anchor.offset === 0) {
                const container = $findMatchingParent(selection.anchor.getNode(), $isLayoutContainerNode);

                if ($isLayoutContainerNode(container)) {
                    const parent = container.getParent<ElementNode>();
                    const child =
                        parent && (before ? parent.getFirstChild<LexicalNode>() : parent?.getLastChild<LexicalNode>());
                    const descendant = before
                        ? container.getFirstDescendant<LexicalNode>()?.getKey()
                        : container.getLastDescendant<LexicalNode>()?.getKey();

                    if (parent !== null && child === container && selection.anchor.key === descendant) {
                        if (before) {
                            container.insertBefore($createParagraphNode());
                        } else {
                            container.insertAfter($createParagraphNode());
                        }
                    }
                }
            }

            return false;
        };

        this.addUnsubscribe(
            mergeRegister(
                // When layout is the last child pressing down/right arrow will insert paragraph
                // below it to allow adding more content. It's similar what $insertBlockNode
                // (mainly for decorators), except it'll always be possible to continue adding
                // new content even if trailing paragraph is accidentally deleted
                this.lexical.registerCommand(KEY_ARROW_DOWN_COMMAND, () => onEscape(false), COMMAND_PRIORITY_LOW),
                this.lexical.registerCommand(KEY_ARROW_RIGHT_COMMAND, () => onEscape(false), COMMAND_PRIORITY_LOW),
                // When layout is the first child pressing up/left arrow will insert paragraph
                // above it to allow adding more content. It's similar what $insertBlockNode
                // (mainly for decorators), except it'll always be possible to continue adding
                // new content even if leading paragraph is accidentally deleted
                this.lexical.registerCommand(KEY_ARROW_UP_COMMAND, () => onEscape(true), COMMAND_PRIORITY_LOW),
                this.lexical.registerCommand(KEY_ARROW_LEFT_COMMAND, () => onEscape(true), COMMAND_PRIORITY_LOW),
                this.lexical.registerCommand(
                    INSERT_LAYOUT_COMMAND,
                    template => {
                        this.lexical.update(() => {
                            const container = $createLayoutContainerNode(template);
                            const itemsCount = getItemsCountFromTemplate(template);

                            for (let i = 0; i < itemsCount; i++) {
                                container.append($createLayoutItemNode().append($createParagraphNode()));
                            }

                            $insertNodeToNearestRoot(container);
                            container.selectStart();
                        });

                        return true;
                    },
                    COMMAND_PRIORITY_EDITOR
                ),
                this.lexical.registerCommand(
                    UPDATE_LAYOUT_COMMAND,
                    ({ template, nodeKey }) => {
                        this.lexical.update(() => {
                            const container = $getNodeByKey<LexicalNode>(nodeKey);

                            if (!$isLayoutContainerNode(container)) {
                                return;
                            }

                            const itemsCount = getItemsCountFromTemplate(template);
                            const prevItemsCount = getItemsCountFromTemplate(container.getTemplateColumns());

                            // Add or remove extra columns if new template does not match existing one
                            if (itemsCount > prevItemsCount) {
                                for (let i = prevItemsCount; i < itemsCount; i++) {
                                    container.append($createLayoutItemNode().append($createParagraphNode()));
                                }
                            } else if (itemsCount < prevItemsCount) {
                                for (let i = prevItemsCount - 1; i >= itemsCount; i--) {
                                    const layoutItem = container.getChildAtIndex<LexicalNode>(i);

                                    if ($isLayoutItemNode(layoutItem)) {
                                        layoutItem.remove();
                                    }
                                }
                            }

                            container.setTemplateColumns(template);
                        });

                        return true;
                    },
                    COMMAND_PRIORITY_EDITOR
                ),
                this.lexical.registerCommand(
                    SELECTION_CHANGE_COMMAND,
                    () => {
                        const selection = $getSelection();

                        this.lexical.getEditorState().read(() => {
                            let inLayout = false;

                            if (selection && $isRangeSelection(selection)) {
                                const parentLayout = $findMatchingParent(
                                    selection.anchor.getNode(),
                                    $isLayoutContainerNode
                                );

                                if (parentLayout) {
                                    inLayout = true;

                                    // remove editing for the previous layout
                                    if (this.editingLayoutNodeKey) {
                                        toggleLayoutEditing(this.lexical, this.editingLayoutNodeKey, false);
                                    }

                                    this.editingLayoutNodeKey = parentLayout.getKey();
                                    toggleLayoutEditing(this.lexical, this.editingLayoutNodeKey, true);
                                }
                            }

                            if (!inLayout && this.editingLayoutNodeKey) {
                                toggleLayoutEditing(this.lexical, this.editingLayoutNodeKey, false);
                                this.editingLayoutNodeKey = null;
                            }
                        });

                        return false;
                    },
                    COMMAND_PRIORITY_HIGH
                ),
                // Structure enforcing transformers for each node type. In case nesting structure is not
                // "Container > Item" it'll unwrap nodes and convert it back
                // to regular content.
                this.lexical.registerNodeTransform(LayoutItemNode, node => {
                    const parent = node.getParent<ElementNode>();
                    if (!$isLayoutContainerNode(parent)) {
                        const children = node.getChildren<LexicalNode>();
                        for (const child of children) {
                            node.insertBefore(child);
                        }
                        node.remove();
                    }
                }),
                this.lexical.registerNodeTransform(LayoutContainerNode, node => {
                    const children = node.getChildren<LexicalNode>();
                    if (!children.every($isLayoutItemNode)) {
                        for (const child of children) {
                            node.insertBefore(child);
                        }
                        node.remove();
                    }
                })
            )
        );

        this.makeSureLayoutItemContainsParagraph();
    }

    private makeSureLayoutItemContainsParagraph() {
        this.addUnsubscribe(
            this.lexical.registerMutationListener(ParagraphNode, (nodeMutations, { prevEditorState }) => {
                for (const [nodeKey, mutation] of nodeMutations) {
                    if (mutation === 'destroyed') {
                        prevEditorState.read(() => {
                            const node = $getNodeByKey<LexicalNode>(nodeKey);
                            const parent = node?.getParent();
                            if (parent && $isLayoutItemNode(parent) && parent.getChildren().length === 1) {
                                // If paragraph is removed from layout item, we need to add it back
                                // to ensure that layout item always has at least one paragraph
                                const parentKey = parent.getKey();
                                this.lexical.update(() =>
                                    $getNodeByKey<LayoutItemNode>(parentKey)?.append($createParagraphNode())
                                );
                            }
                        });
                    }
                }
            })
        );
    }
}

function getItemsCountFromTemplate(template: string): number {
    return template.trim().split(/\s+/).length;
}

function toggleLayoutEditing(lexical: LexicalEditor, key: NodeKey, add: boolean) {
    const elem = lexical.getElementByKey(key);
    if (!elem) return;

    if (add) {
        elem.classList.add('editing');
    } else {
        elem.classList.remove('editing');
    }
}

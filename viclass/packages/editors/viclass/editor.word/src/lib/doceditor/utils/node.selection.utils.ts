import {
    $createNodeSelection,
    $getNodeByKey,
    $getSelection,
    $isNodeSelection,
    $setSelection,
    LexicalEditor,
    NodeKey,
} from 'lexical';
import { BehaviorSubject } from 'rxjs';

function isNodeSelected(editor: <PERSON>calEdit<PERSON>, key: Node<PERSON><PERSON>): boolean {
    return editor.getEditorState().read(() => {
        const node = $getNodeByKey(key);

        if (node === null) {
            return false;
        }

        return node.isSelected();
    });
}

export class NodeSelectionUtils {
    readonly isSelected$ = new BehaviorSubject<boolean>(false);

    private unsubscribes: (() => void)[] = [];

    constructor(
        private lexical: LexicalEditor,
        private nodeKey: NodeKey,
        private allowSelect?: () => boolean
    ) {
        this.unsubscribes.push(
            lexical.registerUpdateListener(() => {
                if (typeof this.allowSelect === 'function' && !this.allowSelect()) return;
                this.isSelected$.next(isNodeSelected(lexical, nodeKey));
            })
        );
    }

    get isSelected(): boolean {
        return this.isSelected$.value;
    }

    destroy() {
        this.unsubscribes.forEach(fn => fn());
        this.isSelected$.complete();
    }

    setSelected = (selected: boolean, clearOthers: boolean = false) => {
        this.lexical.update(() => {
            let selection = $getSelection();

            if (!$isNodeSelection(selection)) {
                selection = $createNodeSelection();
                $setSelection(selection);
            } else if (clearOthers) {
                selection.clear();
            }

            if ($isNodeSelection(selection)) {
                if (selected) {
                    selection.add(this.nodeKey);
                    this.isSelected$.next(true);
                } else {
                    selection.delete(this.nodeKey);
                    this.isSelected$.next(false);
                }
            }
        });
    };

    clearSelection = () => {
        this.lexical.update(() => {
            const selection = $getSelection();
            if ($isNodeSelection(selection)) {
                selection.delete(this.nodeKey);
            }
        });
    };
}

import { AwarenessId } from '@viclass/editor.core';
import { LexicalEditor } from 'lexical';
import { WordDocCtrl } from '../../../../docs/word.doc.ctrl';
import { WordEditor } from '../../../../word.editor';
import { WordLib } from '../../../word.lib';

/**
 * Abstract class for asynchronously handling paste events
 */
export abstract class PasteHandler {
    protected get wordEditor(): WordEditor {
        return this.docCtrl.editor;
    }

    protected get docEditor(): WordLib {
        return this.docCtrl.wordLib;
    }

    protected get lexical(): LexicalEditor {
        return this.docCtrl.wordLib.lexical;
    }

    constructor(
        protected docCtrl: WordDocCtrl,
        protected sendAwareness: () => Promise<AwarenessId>
    ) {}

    /***
     * Asynchronously handle the paste event
     * @readonly true if the paste event was handled and false otherwise
     */
    abstract handlePaste(): Promise<boolean>;
}

import {
    Bo<PERSON>ryRectangle,
    DocLocalId,
    HistoryItem,
    KeyboardEventData,
    LayerId,
    MouseEventData,
    NativeEventTarget,
    Position,
    Rectangle,
    SupportFeatureHistory,
    ToolEventData,
    ToolState,
    VDoc,
    VDocLayer,
    VDocObj,
    ViewportId,
} from '@viclass/editor.core';

import { FreedrawingToolBar } from './freedrawing.toolbar';

/**
 *
 * <AUTHOR>
 */

export type FreedrawingMouseEvent = MouseEventData<NativeEventTarget<any>>;
export type FreedrawingKeyboardEvent = KeyboardEventData<NativeEventTarget<any>>;
export type FreedrawingToolEventData = ToolEventData<FreedrawingToolBar, FreedrawingToolType>;
export type DocumentId = string;

export type FreedrawingToolType =
    | 'PencilTool'
    | 'LineTool'
    | 'LineV2Tool'
    | 'RectangleTool'
    | 'OvalTool'
    | 'TriangleTool'
    | 'HexagonTool'
    | 'EraserTool'
    | 'CommonPropertiesTool'
    | 'EraserPropertiesTool'
    | 'SelectionTool'
    | 'ArrowTool'
    | 'FloatingUITool';

export class FreedrawingDoc implements VDoc {
    layers: FreedrawingLayer[];
    version = 0;

    constructor(
        public globalId: DocumentId,
        public id: DocLocalId
    ) {
        this.layers = [];
    }

    getLayers(): VDocLayer[] {
        return this.layers;
    }
    setLayers(layers: VDocLayer[]) {
        this.layers = layers as FreedrawingLayer[];
    }

    addLayer(layer: VDocLayer) {
        this.layers.push(layer as FreedrawingLayer);
    }
}

export class FreedrawingLayer implements VDocLayer {
    constructor(
        public id: LayerId,
        public boundary: BoundaryRectangle,
        public zindex: number
    ) {}
    getBoundary(): BoundaryRectangle {
        return this.boundary;
    }
    getZindex() {
        return this.zindex;
    }
}

export abstract class FreedrawingObj<T extends ToolState> implements VDocObj {
    id: number;
    toolState: T;
    toolType: FreedrawingToolType;
    boundary?: Rectangle;
}

export class PenObj implements FreedrawingObj<CommonToolState> {
    toolType: FreedrawingToolType = 'PencilTool';
    toolState: CommonToolState;
    points: Position[] = [];

    constructor(public id: number) {}
}

export class ShapeObj implements FreedrawingObj<CommonToolState> {
    toolType: FreedrawingToolType;
    toolState: CommonToolState;
    boundary: Rectangle; // raw boundary

    constructor(public id: number) {}
}

export class EraserObj implements FreedrawingObj<EraserToolState> {
    toolType: FreedrawingToolType = 'EraserTool';
    toolState: EraserToolState;
    points: Position[] = [];

    constructor(public id: number) {}
}

export class LineObjV2 implements FreedrawingObj<LineV2ToolState> {
    toolType: FreedrawingToolType = 'LineV2Tool';
    toolState: LineV2ToolState;
    boundary: Rectangle;

    constructor(public id: number) {
        // Initialize with default tool state
        this.toolState = {
            lineWidth: 2,
            stroke: '#000000',
            startArrow: false,
            endArrow: false,
        };

        // Initialize with default boundary
        this.boundary = {
            start: { x: 0, y: 0 },
            end: { x: 0, y: 0 },
        };
    }
}

export class CommonToolState implements ToolState {
    constructor(
        public stroke: string,
        public fill: string,
        public lineWidth: number
    ) {}
}

export class EraserToolState implements ToolState {
    constructor(public size: number) {}
}

export interface LineV2ToolState {
    stroke: string;
    lineWidth: number;
    startArrow?: boolean;
    endArrow?: boolean;
}

export class FloatingUIToolState implements ToolState {
    position: Position;
}

export class FreedrawingHistoryItem implements HistoryItem {
    constructor(supporter: SupportFeatureHistory) {
        this.supporter = supporter;
    }
    supporter: SupportFeatureHistory;
    viewportId: ViewportId;
    docId: DocLocalId;
    layerId: LayerId;
    objId: number;

    toolType: FreedrawingToolType;
    state: FreedrawingObj<any>;
    action: FreedrawingHistoryItemType;

    firstObj?: boolean;
    newPreview?: boolean;
    lastPartial?: boolean;
    undoAlready?: boolean;

    docState?: FreedrawingDoc;
}

export type FreedrawingHistoryItemType =
    | 'UPDATE_CONTENT'
    | 'UPDATE_PROPERTY'
    | 'UPDATE_BOUNDARY'
    | 'INSERT_PARTIAL'
    | 'INSERT';

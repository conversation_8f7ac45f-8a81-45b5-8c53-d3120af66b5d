import {
    BoundaryRectangle,
    Cmd,
    CmdChannel,
    cmdMeta,
    combineBoardBoundaries,
    CRUDChangeResult,
    CRUDDelegator,
    DefaultVDocCtrl,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocumentEditor,
    DocumentId,
    EditorBase,
    EditorConfig,
    EditorCoordinator,
    EditorId,
    EditorType,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FEATURE_CONTENT_BOUNDARY,
    FEATURE_CONTEXTMENU,
    FEATURE_CRD_DOC,
    FEATURE_CRUD,
    FEATURE_HISTORY,
    FEATURE_ROB,
    FEATURE_SELECTION,
    HasSelectionFeature,
    HistoryFeature,
    InternalPasteDocResult,
    LayerOptions,
    LoadingContext,
    OperationMode,
    reliableSaveCmdMeta,
    ROBFeature,
    SelectDelegator,
    SelectionFeature,
    SupportContentBoundaryFeature,
    SupportFeatureHistory,
    ThrottleCombinator,
    ToolBar,
    UnboundedGraphicLayerCtrl,
    ViewportDisableCES,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { CmdTypeProto, DocumentProto } from '@viclass/proto/editor.freedrawing';

import {
    deserializer,
    EndPreviewCmd,
    InsertPartialEraserObjCmd,
    InsertPartialPencilObjCmd,
    InsertShapeObjCmd,
    RemoveFreedrawingObjCmd,
    RemoveLayerCmd,
    StartPreviewCmd,
    UpdatePreviewCmd,
} from './cmd/freedrawing.cmd';
import { FreedrawingCmdProcessor } from './cmd/freedrawing.cmd.processor';
import { FreedrawingGateway } from './freedrawing.gateway';
import { FreedrawingLayerRenderer } from './freedrawing.layer.renderer';
import {
    EraserObj,
    FreedrawingDoc,
    FreedrawingHistoryItem,
    FreedrawingLayer,
    FreedrawingToolType,
    PenObj,
    ShapeObj,
} from './freedrawing.models';
import { FreedrawingToolBar } from './freedrawing.toolbar';
import {
    convertProtoToEraserObjCtrl,
    convertProtoToLineObjV2Ctrl,
    convertProtoToPencilObjCtrl,
    convertProtoToShapeObjCtrl,
    freedrawingObjectReg,
} from './freedrawing.util';
import { CoordinatorListener } from './listener';
import { FreedrawingDocCtrl } from './objects/freedrawing.document.ctrl';
import { FreedrawingObjCtrl } from './objects/freedrawing.obj.ctrl';
import { EraserTool } from './tools/freedrawing.eraser.tool';
import { FloatingUITool } from './tools/freedrawing.floatingui.tool';
import { LineV2Tool } from './tools/freedrawing.line.v2.tool';
import { PenTool } from './tools/freedrawing.pen.tool';
import { HexagonTool, LineTool, OvalTool, RectTool, TriangleTool } from './tools/freedrawing.shapes.tool';

/**
 *
 * <AUTHOR>
 */
export class FreedrawingEditor
    extends EditorBase<FreedrawingDocCtrl>
    implements DocumentEditor, SupportFeatureHistory, HasSelectionFeature, SupportContentBoundaryFeature
{
    private readonly _id: EditorId;
    private readonly _editorType: EditorType;
    private readonly _cmdChannel: CmdChannel;
    private readonly _cmdProcessor: FreedrawingCmdProcessor;
    private _frdGateway: FreedrawingGateway;

    private _activated: boolean = false;

    // because freedrawing support one document per viewport
    // we keep a map to know which local id is on which viewport
    readonly docByViewport: Map<string, number>;

    // the previewing objs will be rendered in separate layers. Each viewport will have a preview layer.
    // preview layer is created when the start preview command is received
    // or when first clicking on the viewport
    previewLayers: Map<string, FreedrawingLayerRenderer>;

    private historyFeature: HistoryFeature;
    selectionFeature: SelectionFeature;
    robFeature: ROBFeature;

    readonly historyItems: Map<string, FreedrawingHistoryItem[]> = new Map();
    crdFeature: DocCRDFeature;
    crudDelegator: CRUDDelegator;

    readonly selectDelegator = new SelectDelegator<FreedrawingDocCtrl>(this);

    // TODO:  here
    private readonly _operationMode: OperationMode;

    get operationMode(): OperationMode {
        return this._operationMode;
    }

    getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined {
        return undefined;
    }

    getContentBoundary(vpId: ViewportId, localId: DocLocalId): BoundaryRectangle | null {
        const docCtrl = this.findDocumentByLocalId(vpId, localId);
        if (!docCtrl) {
            console.warn(`No document found for viewport ${vpId} and local id ${localId}`);
            return null;
        }

        const rectBoundary = docCtrl.rendererCtrl.objects.map(obj => obj.boundary).filter(Boolean);
        return combineBoardBoundaries(rectBoundary);
    }

    constructor(
        private conf: EditorConfig,
        private _coordinator: EditorCoordinator
    ) {
        super(conf);

        this._id = conf.id;
        this._editorType = conf.editorType;
        this._operationMode = conf.operationMode || OperationMode.CLOUD;
        this._frdGateway = new FreedrawingGateway(conf.apiUri);

        this._cmdProcessor = new FreedrawingCmdProcessor(this);
        this._cmdChannel = _coordinator.registerCmdChannel(this._id);
        this.cmdChannel.registerCmdListener(this._cmdProcessor);

        this.cmdChannel.registerDeserializer(deserializer);
        this.cmdChannel.registerCombinator(CmdTypeProto.UPDATE_BOUNDARY_PREVIEW, new ThrottleCombinator());
        this.cmdChannel.registerCombinator(CmdTypeProto.UPDATE_POSITION_PREVIEW, new ThrottleCombinator());

        // register coordinator event listener
        this.coordinator.registerCoordEventListener(new CoordinatorListener(this));

        this.docByViewport = new Map();
        this.previewLayers = new Map();

        this.crudDelegator = new CRUDDelegator(
            this,
            this.cmdChannel,
            this.regMan,
            { doc: this.docReg, layer: this.layerReg },
            this.generateInitDocData.bind(this)
        );
    }

    async initialize(): Promise<void> {
        // nothing to do
    }

    toolBar: ToolBar<any, any>;

    featureSupporter<T>(featureKey: string): T {
        switch (featureKey) {
            case FEATURE_HISTORY:
            case FEATURE_CONTENT_BOUNDARY:
                return this as unknown as T;
            case FEATURE_CRUD:
                return this.crudDelegator as T;
        }

        return undefined;
    }

    isSupportFeature(featureKey: string): boolean {
        if (
            [
                FEATURE_HISTORY,
                FEATURE_ROB,
                FEATURE_CONTEXTMENU,
                FEATURE_CRUD,
                FEATURE_CRD_DOC,
                FEATURE_CONTENT_BOUNDARY,
            ].includes(featureKey)
        ) {
            return true;
        }

        return false;
    }

    createToolbar(): ToolBar<any, any> {
        const toolBar = new FreedrawingToolBar(this.coordinator, this);

        toolBar.addTool('LineV2Tool', new LineV2Tool(this, toolBar));
        toolBar.addTool('LineTool', new LineTool(this, toolBar));
        toolBar.addTool('RectangleTool', new RectTool(this, toolBar));
        toolBar.addTool('OvalTool', new OvalTool(this, toolBar));
        toolBar.addTool('TriangleTool', new TriangleTool(this, toolBar));
        toolBar.addTool('HexagonTool', new HexagonTool(this, toolBar));
        toolBar.addTool('PencilTool', new PenTool(this, toolBar));
        toolBar.addTool('EraserTool', new EraserTool(this, toolBar));
        toolBar.addTool('FloatingUITool', new FloatingUITool(this, toolBar));

        return toolBar;
    }

    async pasteDoc(
        vpIdSource: ViewportId,
        vpIdTarget: ViewportId,
        docGlobalId: DocumentId,
        docName?: string
    ): Promise<InternalPasteDocResult> {
        throw new Error('Have not implement');
    }

    async duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>> {
        return await this._frdGateway.duplicateDoc(docGlobalIds);
    }

    onFeatureInitialization(featureKey: string, feature: any): Promise<void> {
        switch (featureKey) {
            case FEATURE_SELECTION: {
                this.selectionFeature = feature as SelectionFeature;
                break;
            }

            case FEATURE_HISTORY: {
                this.historyFeature = feature as HistoryFeature;
                break;
            }
            case FEATURE_ROB: {
                this.robFeature = feature as ROBFeature;
                break;
            }
            case FEATURE_CRD_DOC: {
                this.crdFeature = feature as DocCRDFeature;
                this.crdFeature.registerEditor(this, this.cmdChannel);
                break;
            }
        }

        return Promise.resolve(null);
    }

    async start() {
        await this._cmdProcessor.start();
        await this.cmdChannel.start();
    }

    async onViewportDisabled(eventState: ViewportDisableCES) {
        if (eventState.flushCmd) {
            await this.cmdChannel.flush(eventState.vmId);
        }
    }

    /**
     * Create a preview layer on a viewport
     * @param vm the viewport manager to create the preview on
     */
    createPreviewLayer(vm: ViewportManager): FreedrawingLayerRenderer {
        const option: LayerOptions = {
            editor: this,
            state: { id: -1 },
            viewport: vm,
        };
        const curDoc = this.docByViewport.get(vm.id);
        const previewLayer = vm.requestLayer(UnboundedGraphicLayerCtrl, true, option);
        const tt = new FreedrawingLayerRenderer(previewLayer as UnboundedGraphicLayerCtrl, true);
        const docRegistry = this.regDelegator.getDocReg(vm.id);
        const docCtrl = docRegistry.getEntity(curDoc);
        tt.layer.doc = docCtrl;
        this.previewLayers.set(vm.id, tt);

        return tt;
    }

    /**
     * Remove the freedrawing preview layer on a viewport
     * @param vm the viewport to remove the layer
     */
    removePreviewLayer(vm: ViewportManager) {
        const pl = this.previewLayers.get(vm.id);
        console.log('Remove preview layer', pl);
        if (pl) {
            vm.removeLayer(pl.layer);
            this.previewLayers.delete(vm.id);
        }
    }

    get isSelected(): boolean {
        return this._activated;
    }

    get isActivated(): boolean {
        return this._activated;
    }

    get id(): EditorId {
        return this._id;
    }

    get editorType(): EditorType {
        return this._editorType;
    }

    get coordinator(): EditorCoordinator {
        return this._coordinator;
    }

    get cmdChannel(): CmdChannel {
        return this._cmdChannel;
    }

    onEditorActivated() {
        this._activated = true;
    }

    onEditorDeactivated() {
        this._activated = false;
    }

    async undo(item: FreedrawingHistoryItem) {
        const actualItem = item as unknown as FreedrawingHistoryItem;
        actualItem.undoAlready = true;

        const cmds: Cmd<any>[] = [];

        const vm = this.coordinator.getViewportManager(actualItem.viewportId);
        const docCtrl = this.regDelegator.getDocReg(vm.id)?.getEntity(actualItem.docId);

        switch (actualItem.action) {
            case 'INSERT':
            case 'INSERT_PARTIAL': {
                // remove obj
                const meta = reliableSaveCmdMeta(
                    vm,
                    docCtrl.state,
                    actualItem.docId,
                    actualItem.layerId,
                    CmdTypeProto.REMOVE_OBJ
                );
                const cmd = new RemoveFreedrawingObjCmd(meta);
                cmd.setState(actualItem.layerId, actualItem.objId);
                cmds.push(cmd);

                if (actualItem.firstObj == true) {
                    // remove layer
                    const cmd = new RemoveLayerCmd(
                        reliableSaveCmdMeta(
                            vm,
                            docCtrl.state,
                            actualItem.docId,
                            actualItem.layerId,
                            CmdTypeProto.REMOVE_LAYER
                        )
                    );
                    cmds.push(cmd);
                }

                // send commands
                for (const cmd of cmds) {
                    this.cmdChannel.receive(cmd);
                }
                return;
            }
            default:
                break;
        }

        // find previous item
        const items = this.historyItems.get(actualItem.viewportId);
        if (!items) return;

        const index = items.indexOf(actualItem);
        if (index == -1) return;

        if (index == 0) {
            switch (actualItem.action) {
                case 'UPDATE_CONTENT':
                case 'UPDATE_PROPERTY':
                case 'UPDATE_BOUNDARY': {
                    // end preview
                    const cmd = new EndPreviewCmd(cmdMeta(vm, actualItem.objId, CmdTypeProto.END_PREVIEW));
                    cmds.push(cmd);
                    break;
                }
                default:
                    break;
            }

            if (actualItem.firstObj == true) {
                // remove layer
                const cmd = new RemoveLayerCmd(
                    reliableSaveCmdMeta(
                        vm,
                        docCtrl.state,
                        actualItem.docId,
                        actualItem.layerId,
                        CmdTypeProto.REMOVE_LAYER
                    )
                );
                cmds.push(cmd);
            }

            // send commands
            for (const cmd of cmds) {
                this.cmdChannel.receive(cmd);
            }

            return;
        }

        const preItem = items[index - 1];

        switch (actualItem.action) {
            case 'UPDATE_BOUNDARY': {
                // update preview
                const cmd = new UpdatePreviewCmd(cmdMeta(vm, preItem.objId, CmdTypeProto.UPDATE_BOUNDARY_PREVIEW));
                cmd.updateBoundary(preItem.toolType, preItem.state.boundary, true);
                cmds.push(cmd);
                break;
            }
            case 'UPDATE_PROPERTY': {
                // update preview
                const cmd = new UpdatePreviewCmd(cmdMeta(vm, preItem.objId, CmdTypeProto.UPDATE_TOOL_STATE_PREVIEW));
                cmd.updateToolState(preItem.toolType, preItem.state.toolState);
                cmds.push(cmd);
                break;
            }
            default:
                break;
        }

        // send commands
        for (const cmd of cmds) {
            this.cmdChannel.receive(cmd);
        }
    }

    async clearHistory(viewportId: ViewportId): Promise<void> {
        this.historyFeature?.clear(this, viewportId);
    }

    async internalRemoveDoc(vpId: ViewportId, docId: DocLocalId) {
        const docRegistry = this.regDelegator.getDocReg(vpId);
        const layerRegistry = this.regDelegator.getLayerReg(vpId, docId);
        const objectRegistry = this.regMan.registry<FreedrawingObjCtrl<any>>(freedrawingObjectReg(vpId, docId));
        const docCtrl = docRegistry.getEntity(docId);

        if (docCtrl) {
            docRegistry.removeEntity(docId);
            objectRegistry.removeEntity(docId);

            docCtrl.layers.forEach(layer => {
                const layerRenderer = layerRegistry.getEntity(layer.state.id);
                layerRegistry.removeEntity(layer.state.id);
                docCtrl.removeLayer(layerRenderer.layer);
                docCtrl.viewport.removeLayer(layerRenderer.layer);
                layerRenderer.onRemoved();
            });
        }

        this.regDelegator.deleteDocReg(vpId);
        this.regDelegator.deleteLayerReg(vpId, docId);
        this.regMan.delete(freedrawingObjectReg(vpId, docId));

        this.docByViewport.delete(vpId);
    }

    async redo(item: FreedrawingHistoryItem) {
        const actualItem = item as unknown as FreedrawingHistoryItem;
        if (actualItem.undoAlready == true) {
            actualItem.undoAlready = false;
        }

        const vm = this.coordinator.getViewportManager(actualItem.viewportId);
        const docCtrl = this.regDelegator.getDocReg(vm.id)?.getEntity(actualItem.docId);

        const cmds: Cmd<any>[] = [];

        if (actualItem.firstObj == true) {
            // TODO: HISTORY HAS NOT BEEN DONE! History of create doc should be separated from history of pen!
            // let layerId = actualItem.layerId;
            // let cmd = new InsertLayerCmd(
            //     reliableSaveCmdMeta(vm, docCtrl.state, actualItem.docId, layerId, CmdTypeProto.INSERT_LAYER)
            // );
            // cmds.push(cmd);
        }

        if (
            actualItem.action == 'UPDATE_BOUNDARY' ||
            actualItem.action == 'UPDATE_PROPERTY' ||
            actualItem.action == 'UPDATE_CONTENT'
        ) {
            // find previous item
            const items = this.historyItems.get(actualItem.viewportId);
            if (items && items.indexOf(actualItem) == 0) {
                const meta = cmdMeta(vm, actualItem.objId, CmdTypeProto.START_PREVIEW);
                meta.versionable = docCtrl.state.id;

                const startCmd = new StartPreviewCmd(meta);
                startCmd.setState(actualItem.toolType, actualItem.state);
                cmds.push(startCmd);
            }
        }

        switch (actualItem.action) {
            case 'INSERT': {
                // insert obj
                const cmd = this.generateInsertCmd(actualItem, vm, docCtrl);
                cmds.push(cmd);
                break;
            }
            case 'INSERT_PARTIAL': {
                // insert partial obj
                let cmd = null;
                if (actualItem.toolType == 'PencilTool') {
                    const meta = reliableSaveCmdMeta(
                        vm,
                        docCtrl.state,
                        docCtrl.state.id,
                        actualItem.objId,
                        CmdTypeProto.INSERT_PARTIAL_PENCIL_OBJ
                    );
                    cmd = new InsertPartialPencilObjCmd(meta);
                    cmd.setState(actualItem.state as PenObj, actualItem.layerId);
                } else if (actualItem.toolType == 'EraserTool') {
                    const meta = reliableSaveCmdMeta(
                        vm,
                        docCtrl.state,
                        docCtrl.state.id,
                        actualItem.objId,
                        CmdTypeProto.INSERT_PARTIAL_ERASER_OBJ
                    );
                    cmd = new InsertPartialEraserObjCmd(meta);
                    cmd.setState(actualItem.state as EraserObj, actualItem.layerId);
                }

                if (cmd) {
                    cmds.push(cmd);
                }
                break;
            }
            case 'UPDATE_BOUNDARY': {
                const cmd = new UpdatePreviewCmd(cmdMeta(vm, actualItem.objId, CmdTypeProto.UPDATE_BOUNDARY_PREVIEW));
                cmd.updateBoundary(actualItem.toolType, actualItem.state.boundary, true);
                cmds.push(cmd);
                break;
            }
            case 'UPDATE_PROPERTY': {
                const cmd = new UpdatePreviewCmd(cmdMeta(vm, actualItem.objId, CmdTypeProto.UPDATE_TOOL_STATE_PREVIEW));
                cmd.updateToolState(actualItem.toolType, actualItem.state.toolState);
                cmds.push(cmd);
                break;
            }
            default:
                break;
        }

        // send commands
        for (const cmd of cmds) {
            this.cmdChannel.receive(cmd);
        }
    }

    private generateInsertCmd(
        actualItem: FreedrawingHistoryItem,
        vm: ViewportManager,
        docCtrl: DefaultVDocCtrl
    ): Cmd<any> {
        switch (actualItem.toolType as FreedrawingToolType) {
            case 'LineTool':
            case 'OvalTool':
            case 'RectangleTool':
            case 'TriangleTool':
            case 'HexagonTool': {
                const meta = reliableSaveCmdMeta(
                    vm,
                    docCtrl.state,
                    docCtrl.state.id,
                    actualItem.objId,
                    CmdTypeProto.INSERT_SHAPE_OBJ
                );
                const insertCmd = new InsertShapeObjCmd(meta);
                insertCmd.setState(actualItem.state as ShapeObj, actualItem.layerId);
                return insertCmd;
            }
            default:
                break;
        }

        return null;
    }

    addHistoryItem(item: FreedrawingHistoryItem) {
        if (!this.historyFeature) return; // if history feature is not initialize, we simply ignore

        const manager = this.historyFeature.getHistoryManager(item.viewportId);

        let items = this.historyItems.get(item.viewportId);

        if (items && items.length > 0) {
            if (item.newPreview == true) {
                // remove items from HistoryManagement
                manager.clear(items);
                this.historyItems.delete(item.viewportId);
            } else {
                const lastItem = items[items.length - 1];
                if (lastItem.toolType != item.toolType) {
                    // remove items from HistoryManagement
                    manager.clear(items);
                    this.historyItems.delete(item.viewportId);
                }
            }

            // remove all undo already items
            items = items.filter(i => i.undoAlready == undefined || i.undoAlready == false);
            this.historyItems.set(item.viewportId, items);
        }

        switch (item.action) {
            case 'INSERT_PARTIAL': {
                if (!this.historyItems.has(item.viewportId)) {
                    this.historyItems.set(item.viewportId, []);
                }

                const items = this.historyItems.get(item.viewportId);

                if (!item.lastPartial) {
                    items.push(item);
                }

                if (item.lastPartial == true) {
                    // combine all partial obj
                    if (items.length > 0) {
                        const combined = items.reduce((preValue, curValue) => {
                            if (curValue) {
                                const preObj = preValue.state as any;
                                const curObj = curValue.state as any;
                                preObj.points = [...preObj.points, ...curObj.points];
                                preValue.state = preObj;
                            }

                            return preValue;
                        });

                        const combinedObj = combined.state as any;
                        const itemObj = item.state as any;

                        itemObj.points = [...combinedObj.points, ...itemObj.points];
                    }

                    // remove items from HistoryManagement
                    manager.clear(items);
                    this.historyItems.delete(item.viewportId);

                    // push item to HistoryManagement
                    manager.push(item);
                }
                break;
            }
            case 'INSERT': {
                const items = this.historyItems.get(item.viewportId);

                // remove items from HistoryManagement
                manager.clear(items);
                this.historyItems.delete(item.viewportId);

                // push item to HistoryManagement
                manager.push(item);
                break;
            }
            case 'UPDATE_CONTENT':
            case 'UPDATE_BOUNDARY':
            case 'UPDATE_PROPERTY': {
                const items = this.historyItems.get(item.viewportId);
                if (items) {
                    items.push(item);
                } else {
                    this.historyItems.set(item.viewportId, [item]);
                }

                // item to HistoryManagement
                manager.push(item);
                break;
            }
            default:
                break;
        }
    }

    /**
     *  Cleanup the history items when we cancel the new preview object
     */
    cancelPreviewHistoryItem(viewportId: string) {
        if (!this.historyFeature) return;

        const items = this.historyItems.get(viewportId);
        if (!items) return;

        const previewItems = items.filter(item => item.newPreview);
        if (previewItems.length > 0) {
            const manager = this.historyFeature.getHistoryManager(viewportId);

            // remove items from HistoryManagement
            manager.clear(previewItems);
            this.historyItems.delete(viewportId);
        }
    }

    private async generateInitDocData(
        curChanges: CRUDChangeResult,
        insertCmd: FCInsertDocCmd,
        insertLayer: FCInsertLayerCmd
    ) {
        // for freedrawing, we want to attach a presync hook that create the document
        // on the server and then set the insert cmd with the global id
        insertCmd.meta.preSyncHooks = [
            async (cmd: FCInsertDocCmd) => {
                const globalId = await this._frdGateway.createDoc();

                // this hook is run after the local processing, so we can be sure the doc ctrl already set
                const docRegistry = this.regDelegator.getOrCreateDocReg(cmd.meta.viewport.id);
                const docCtr = docRegistry.getEntity(cmd.meta.versionable);
                docCtr.setGlobalId(globalId);
                cmd.state.setGlobalId(globalId);
            },
        ];
    }

    async reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        await this.loadDocumentByLocalId(localId, loadingContext);
    }

    async loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext) {
        const arrBuf = await loadingContext.connector.loadDocumentByGlobalId(
            this.cmdChannel.channelCode,
            globalId,
            'arraybuffer'
        );
        const docProto = DocumentProto.deserializeBinary(arrBuf);

        const docRegistry = this.regDelegator.getOrCreateDocReg(loadingContext.vm.id);
        const localId = docRegistry.getAndIncrementId();

        this.createDocumentCtrlFromProto(globalId, localId, docProto, loadingContext);

        this.docByViewport.set(loadingContext.vm.id, localId);
    }

    async loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        const arrBuf = await loadingContext.connector.loadDocumentByLocalId(
            this.cmdChannel.channelCode,
            localId,
            'arraybuffer'
        );
        const docProto = DocumentProto.deserializeBinary(arrBuf);
        const globalId = docProto.getGlobalId();
        this.createDocumentCtrlFromProto(globalId, localId, docProto, loadingContext);

        this.docByViewport.set(loadingContext.vm.id, localId);
    }

    private createDocumentCtrlFromProto(
        globalId: string,
        localId: DocLocalId,
        proto: DocumentProto,
        loadingContext: LoadingContext
    ): DefaultVDocCtrl {
        const doc = new FreedrawingDoc(globalId, localId);
        const docCtrl = new FreedrawingDocCtrl(this, doc, loadingContext.vm);
        const zIndexes = loadingContext.zIndexes;
        docCtrl.viewport = loadingContext.vm;

        const docRegistry = this.regDelegator.getOrCreateDocReg(loadingContext.vm.id);
        const layerRegistry = this.regDelegator.getOrCreateLayerReg(loadingContext.vm.id, localId);
        const objectRegistry = this.regMan.register<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(loadingContext.vm.id, localId)
        );

        docRegistry.addEntity(localId, docCtrl);

        if (proto.getLayersList().length == 0) throw new Error('Number of layer of existing document is zero');
        const layers = proto.getLayersList();

        for (const layerProto of layers) {
            const layerId = layerProto.getLocalId();
            const layer = new FreedrawingLayer(layerId, null, zIndexes[layerId]);
            // don't add layer immediately because we need to attach renderer
            const layerCtrl = loadingContext.vm.requestLayer(UnboundedGraphicLayerCtrl, false, {
                state: layer,
                editor: this,
                viewport: loadingContext.vm,
            }) as UnboundedGraphicLayerCtrl;
            docCtrl.rendererCtrl = new FreedrawingLayerRenderer(layerCtrl);

            doc.addLayer(layer as FreedrawingLayer);
            docCtrl.addLayer(layerCtrl);
            layerRegistry.addEntity(layerProto.getLocalId(), docCtrl.rendererCtrl);

            for (const objectProto of layerProto.getObjectsList()) {
                let objCtrl: any;

                if (objectProto.hasShape()) {
                    objCtrl = convertProtoToShapeObjCtrl(objectProto.getShape(), this, docCtrl);
                }
                if (objectProto.hasPencil()) {
                    objCtrl = convertProtoToPencilObjCtrl(objectProto.getPencil(), this, docCtrl);
                }
                if (objectProto.hasEraser()) {
                    objCtrl = convertProtoToEraserObjCtrl(objectProto.getEraser(), this, docCtrl);
                }
                if (objectProto.hasLineV2()) {
                    objCtrl = convertProtoToLineObjV2Ctrl(objectProto.getLineV2(), this, docCtrl);
                }

                if (objCtrl) {
                    objectRegistry.addEntity(objCtrl.id, objCtrl);
                    docCtrl.rendererCtrl.addObjCtrl(objCtrl);
                }
            }

            layerCtrl.zindex = layer.getZindex();
            loadingContext.vm.addLayer(layerCtrl);
        }

        return docCtrl;
    }

    async selectDocByLocalId(vpId: ViewportId, docLocalId: DocLocalId): Promise<void> {
        throw new Error('have not implement');
    }

    async selectDocByGlobalId(vpId: ViewportId, docGlobalId: DocumentId): Promise<void> {
        throw new Error('have not implement');
    }

    async deselectDocByLocalId(vpId: ViewportId, docLocalId: DocLocalId): Promise<void> {
        throw new Error('have not implement');
    }

    async deselectDocByGlobalId(vpId: ViewportId, docGlobalId: DocumentId): Promise<void> {
        throw new Error('have not implement');
    }
}

import {
    BoundaryChangeEventData,
    Position,
    Rectangle,
    RectangleObjectBoundary,
    VDocCtrl,
    VEventListener,
} from '@viclass/editor.core';
import { FreedrawingEditor } from '../freedrawing.editor';
import { FreedrawingToolType, LineObjV2 } from '../freedrawing.models';
import { FreedrawingObjCtrl } from './freedrawing.obj.ctrl';

export class LineObjV2Ctrl extends FreedrawingObjCtrl<LineObjV2> {
    protected boundaryCtrl?: RectangleObjectBoundary;
    toolType: FreedrawingToolType = 'LineV2Tool';

    private _boundaryChangeListener?: VEventListener<BoundaryChangeEventData>;

    constructor(
        state: LineObjV2,
        public override editor: FreedrawingEditor,
        protected doc: VDocCtrl,
        private isPreview: boolean
    ) {
        super(state, editor, doc);

        if (this.isPreview) {
            this.boundaryCtrl = this.editor.robFeature.requestROB(this.doc.viewport.id, this.boundary);
        }
    }

    public set boundaryChangeListener(listener: VEventListener<BoundaryChangeEventData>) {
        if (this.isPreview) {
            this._boundaryChangeListener = listener;
            this.boundaryCtrl.boundaryChange.registerListener(listener);
        }
    }

    override get boundary(): Rectangle {
        const start: Position = {
            x: Math.min(this._state.boundary.start.x, this._state.boundary.end.x),
            y: Math.max(this._state.boundary.start.y, this._state.boundary.end.y),
        };
        const end: Position = {
            x: Math.max(this._state.boundary.start.x, this._state.boundary.end.x),
            y: Math.min(this._state.boundary.start.y, this._state.boundary.end.y),
        };

        return { start, end };
    }

    override afterRender() {
        this.boundaryCtrl?.enablePointerEvent();
    }

    override render(ctx: CanvasRenderingContext2D, preview?: boolean) {
        this.doRender(ctx);

        if (this.isPreview) this.showBoundary();
    }

    protected doRender(ctx: CanvasRenderingContext2D): void {
        // convert from whiteboard to layer local coord
        const line: Rectangle = {
            start: this.layer.canvasPos(this.state.boundary.start),
            end: this.layer.canvasPos(this.state.boundary.end),
        };

        // Set line style
        ctx.strokeStyle = this.state.toolState.stroke || '#000000';
        ctx.lineWidth = this.state.toolState.lineWidth;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        ctx.beginPath();
        ctx.moveTo(line.start.x, line.start.y);
        ctx.lineTo(line.end.x, line.end.y);
        ctx.stroke();

        // Draw arrows if enabled
        if (this.state.toolState.startArrow) {
            this.drawArrow(ctx, line.end, line.start);
        }

        if (this.state.toolState.endArrow) {
            this.drawArrow(ctx, line.start, line.end);
        }

        // Reset dash pattern
        ctx.setLineDash([]);
    }

    private drawArrow(ctx: CanvasRenderingContext2D, from: Position, to: Position): void {
        const dx = to.x - from.x;
        const dy = to.y - from.y;
        const angle = Math.atan2(dy, dx);

        const arrowLength = Math.min(20, this.state.toolState.lineWidth * 8);
        const arrowAngle = Math.PI / 6; // 30 degrees

        // Calculate arrow head points
        const x1 = to.x - arrowLength * Math.cos(angle - arrowAngle);
        const y1 = to.y - arrowLength * Math.sin(angle - arrowAngle);
        const x2 = to.x - arrowLength * Math.cos(angle + arrowAngle);
        const y2 = to.y - arrowLength * Math.sin(angle + arrowAngle);

        // Draw arrow head
        ctx.beginPath();
        ctx.moveTo(to.x, to.y);
        ctx.lineTo(x1, y1);
        ctx.moveTo(to.x, to.y);
        ctx.lineTo(x2, y2);
        ctx.stroke();
    }

    override validate(): boolean {
        const b = this.state.boundary;

        return Math.abs(b.start.x - b.end.x) >= 10 || Math.abs(b.start.y - b.end.y) >= 10;
    }

    showBoundary() {
        this.boundaryCtrl?.show();
    }

    hideBoundary() {
        this.boundaryCtrl?.hide();
    }

    disableBoundaryEvent() {
        this.boundaryCtrl?.disablePointerEvent();
    }

    enableBoundaryEvent() {
        this.boundaryCtrl?.enablePointerEvent();
    }

    override destroy() {
        if (this._boundaryChangeListener) {
            this.boundaryCtrl.boundaryChange.unregisterListener(this._boundaryChangeListener);
        }

        this.editor.robFeature.releaseROB(this.doc.viewport.id, this.boundaryCtrl);
    }

    override onStateChange() {
        this.prepare();
        // update the boundary controller to reflect the boundary change
        this.boundaryCtrl?.updateBoundary(this.boundary);
    }

    private hitTest(pos: Position): boolean {
        const localPos = this.layer.canvasPos(pos);

        const line: Rectangle = {
            start: this.layer.canvasPos(this.state.boundary.start),
            end: this.layer.canvasPos(this.state.boundary.end),
        };

        // Calculate distance from point to line
        const distance = this.distanceToLine(localPos, line.start, line.end);
        const threshold = Math.max(this.state.toolState.lineWidth / 2 + 5, 10);

        return distance <= threshold;
    }

    private distanceToLine(point: Position, lineStart: Position, lineEnd: Position): number {
        const dx = lineEnd.x - lineStart.x;
        const dy = lineEnd.y - lineStart.y;
        const length = Math.sqrt(dx * dx + dy * dy);

        if (length === 0) {
            // Line is a point
            const pdx = point.x - lineStart.x;
            const pdy = point.y - lineStart.y;
            return Math.sqrt(pdx * pdx + pdy * pdy);
        }

        // Calculate the parameter t for the closest point on the line
        const t = Math.max(
            0,
            Math.min(1, ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / (length * length))
        );

        // Calculate the closest point on the line
        const closestX = lineStart.x + t * dx;
        const closestY = lineStart.y + t * dy;

        // Calculate distance from point to closest point on line
        const distX = point.x - closestX;
        const distY = point.y - closestY;

        return Math.sqrt(distX * distX + distY * distY);
    }
}

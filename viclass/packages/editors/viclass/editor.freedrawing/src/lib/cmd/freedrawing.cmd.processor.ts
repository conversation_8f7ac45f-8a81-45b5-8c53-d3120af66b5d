import {
    AbstractCommand,
    Cmd,
    CmdOriginType,
    CmdProcessor,
    DefaultVDocCtrl,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    LayerOptions,
    UnboundedGraphicLayerCtrl,
    VDocCtrl,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.freedrawing';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { FreedrawingEditor } from '../freedrawing.editor';
import { FreedrawingLayerRenderer } from '../freedrawing.layer.renderer';
import {
    EraserObj,
    FreedrawingDoc,
    FreedrawingLayer,
    FreedrawingObj,
    FreedrawingToolType,
    LineObjV2,
    PenObj,
    ShapeObj,
} from '../freedrawing.models';
import {
    convertProtoToBoundary,
    convertProtoToCommonToolState,
    convertProtoToEraserObjCtrl,
    convertProtoToEraserToolState,
    convertProtoToLineObjV2Ctrl,
    convertProtoToLineObjV2ToolState,
    convertProtoToPencilObjCtrl,
    convertProtoToPosition,
    freedrawingDocReg,
    freedrawingLayerReg,
    freedrawingObjectReg,
} from '../freedrawing.util';
import { FreedrawingDocCtrl } from '../objects/freedrawing.document.ctrl';
import { EraserObjCtrl } from '../objects/freedrawing.eraser.obj.ctrl';
import { LineObjV2Ctrl } from '../objects/freedrawing.line.v2.obj.ctrl';
import { FreedrawingObjCtrl } from '../objects/freedrawing.obj.ctrl';
import { PenObjCtrl } from '../objects/freedrawing.pen.obj.ctrl';
import {
    HexagonObjCtrl,
    LineObjCtrl,
    OvalObjCtrl,
    RectangleObjCtrl,
    ShapeObjCtrl,
    TriangleObjCtrl,
} from '../objects/freedrawing.shape.obj.ctrl';
import { LINE_V2_PREVIEW_OBJ_ID } from '../tools/freedrawing.line.v2.tool';
import { SHAPE_PREVIEW_OBJ_ID } from '../tools/freedrawing.shapes.tool';
import {
    InsertLineObjV2Cmd,
    InsertPartialEraserObjCmd,
    InsertPartialPencilObjCmd,
    InsertShapeObjCmd,
    RemoveFreedrawingObjCmd,
    RemoveLayerCmd,
    StartPreviewCmd,
    UpdatePreviewCmd,
} from './freedrawing.cmd';

/**
 *
 * <AUTHOR>
 */

export class FreedrawingCmdProcessor extends CmdProcessor {
    requestedFrame: boolean = false;

    constructor(private editor: FreedrawingEditor) {
        super();
    }

    async processCmd(
        cmd: AbstractCommand<CmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<CmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case FCCmdTypeProto.INSERT_DOC: {
                this.processInsertDocCmd(cmd as FCInsertDocCmd);
                break;
            }
            case FCCmdTypeProto.INSERT_LAYER: {
                this.processInsertLayerCmd(cmd as FCInsertLayerCmd);
                break;
            }
            case CmdTypeProto.INSERT_SHAPE_OBJ: {
                this.processInsertShapeObjCmd(cmd as InsertShapeObjCmd);
                break;
            }
            case CmdTypeProto.INSERT_LINE_OBJ_V2: {
                this.processInsertLineObjV2Cmd(cmd as InsertLineObjV2Cmd);
                break;
            }
            case CmdTypeProto.START_PREVIEW: {
                this.processStartPreviewCmd(cmd as StartPreviewCmd);
                break;
            }
            case CmdTypeProto.UPDATE_TOOL_STATE_PREVIEW: {
                this.processUpdateToolStatePreviewCmd(cmd as UpdatePreviewCmd);
                break;
            }
            case CmdTypeProto.UPDATE_BOUNDARY_PREVIEW: {
                this.processUpdateBoundaryPreviewCmd(cmd as UpdatePreviewCmd);
                break;
            }
            case CmdTypeProto.UPDATE_POSITION_PREVIEW: {
                this.processUpdatePositionPreviewCmd(cmd as UpdatePreviewCmd);
                break;
            }
            case CmdTypeProto.INSERT_PARTIAL_PENCIL_OBJ: {
                this.processInsertPartialPencilObjCmd(cmd as InsertPartialPencilObjCmd);
                break;
            }
            case CmdTypeProto.INSERT_PARTIAL_ERASER_OBJ: {
                this.processInsertPartialEraserObjCmd(cmd as InsertPartialEraserObjCmd);
                break;
            }
            case CmdTypeProto.END_PREVIEW: {
                this.processEndPreview(cmd as AbstractCommand<CmdTypeProto>);
                break;
            }
            case CmdTypeProto.REMOVE_OBJ: {
                this.processRemoveObj(cmd as RemoveFreedrawingObjCmd);
                break;
            }
            case CmdTypeProto.REMOVE_LAYER: {
                this.processRemoveLayer(cmd as RemoveLayerCmd);
                break;
            }
            case FCCmdTypeProto.REMOVE_DOC: {
                this.processRemoveDoc(cmd as FCRemoveDocCmd);
                break;
            }
            case FCCmdTypeProto.RELOAD_DOC: {
                return this.processReloadCmd(cmd as FCReloadDocCmd);
            }
            default:
                break;
        }

        return cmd;
    }

    private async processRemoveDoc(cmd: FCRemoveDocCmd) {
        const l = cmd.state.getIdsList();
        for (const id of l) {
            await this.editor.internalRemoveDoc(cmd.meta.viewport.id, id.getLocalId());
        }
    }

    async processReloadCmd(cmd: FCReloadDocCmd): Promise<FCReloadDocCmd> {
        const promises = cmd.state
            .getLocalidList()
            .map(localId => this.editor.crdFeature.reloadLocalDoc(this.editor, cmd.meta.viewport.id, localId));
        await Promise.all(promises);

        this.editor.crdFeature.docCreationsCheck$.next(
            cmd.state.getLocalidList().map(localId => ({
                editor: this.editor,
                vm: cmd.meta.viewport,
                localId,
            }))
        );

        return cmd;
    }

    private processRemoveLayer(cmd: RemoveLayerCmd) {
        const docRegistry = this.editor.regMan.registry<DefaultVDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));

        const docId = cmd.meta.versionable;
        const docCtrl = docRegistry.getEntity(docId);

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, docId)
        );
        const layerRenderer = layerRegistry.getEntity(cmd.meta.targetId);

        layerRegistry.removeEntity(cmd.meta.targetId);
        docCtrl.removeLayer(layerRenderer.layer);
        cmd.meta.viewport.removeLayer(layerRenderer.layer);

        layerRenderer.onRemoved();
    }

    private processRemoveObj(cmd: RemoveFreedrawingObjCmd) {
        const docId = cmd.meta.versionable;

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, docId)
        );
        const objectRegistry = this.editor.regMan.registry<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(cmd.meta.viewport.id, docId)
        );

        const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());
        layerRenderer.removeObjCtrl(cmd.state.getObjId());
        objectRegistry.removeEntity(cmd.state.getObjId());

        layerRenderer.scheduleRender();
    }

    private getPreviewLayerRenderer(cmd: Cmd<any>): FreedrawingLayerRenderer {
        return this.editor.previewLayers.get(cmd.meta.viewport.id);
    }

    private processInsertPartialPencilObjCmd(cmd: InsertPartialPencilObjCmd) {
        const docRegistry = this.editor.regMan.registry<DefaultVDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));
        const docCtrl = docRegistry.getEntity(cmd.meta.versionable);

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, cmd.meta.versionable)
        );
        const objectRegistry = this.editor.regMan.registry<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(cmd.meta.viewport.id, cmd.meta.versionable)
        );

        const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());

        // if (!layerRenderer.hasObjectId(cmd.meta.targetId)) {
        //     throw new Error("The object to add pencil command doesn't exist");
        // }

        let objCtrl = layerRenderer.getObjCtrl(cmd.meta.targetId) as PenObjCtrl;
        if (!objCtrl) {
            objCtrl = convertProtoToPencilObjCtrl(cmd.state.getPencilObj(), this.editor, docCtrl);
            layerRenderer.addObjCtrl(objCtrl);
        } else {
            const objProto = cmd.state.getPencilObj();
            const partialObj: PenObj = {
                id: cmd.meta.targetId,
                toolType: 'PencilTool',
                toolState: convertProtoToCommonToolState(objProto.getToolState()),
                points: objProto.getPointsList().map(p => convertProtoToPosition(p)),
            };

            objCtrl.updatePartial(partialObj);
        }

        if (!objectRegistry.hasEntityId(objCtrl.id)) {
            objectRegistry.addEntity(objCtrl.id, objCtrl);
        }

        if (cmd.meta.origin == CmdOriginType.local) {
            return; // we don't re-render this locally because the update position already up to date
        }
        const previewLayerRenderer = this.getPreviewLayerRenderer(cmd);
        if (previewLayerRenderer && previewLayerRenderer.hasObjectId(objCtrl.id)) {
            previewLayerRenderer.scheduleRender();
        } else layerRenderer.scheduleRender();
    }

    private processInsertPartialEraserObjCmd(cmd: InsertPartialEraserObjCmd) {
        const docRegistry = this.editor.regMan.registry<FreedrawingDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));
        const docCtrl = docRegistry.getEntity(cmd.meta.versionable);

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, cmd.meta.versionable)
        );
        const objectRegistry = this.editor.regMan.registry<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(cmd.meta.viewport.id, cmd.meta.versionable)
        );

        const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());

        // if (!layerCtrl.hasObjectId(cmd.meta.targetId)) {
        //     throw new Error("The object to add eraser command doesn't exist");
        // }

        let objCtrl = layerRenderer.getObjCtrl(cmd.meta.targetId) as EraserObjCtrl;
        if (!objCtrl) {
            objCtrl = convertProtoToEraserObjCtrl(cmd.state.getEraserObj(), this.editor, docCtrl);
            layerRenderer.addObjCtrl(objCtrl);
        } else {
            const objProto = cmd.state.getEraserObj();
            const partialObj: EraserObj = {
                id: cmd.meta.targetId,
                toolType: 'EraserTool',
                toolState: convertProtoToEraserToolState(objProto.getToolState()),
                points: objProto.getPointsList().map(p => convertProtoToPosition(p)),
            };

            objCtrl.updatePartial(partialObj);
        }

        if (!objectRegistry.hasEntityId(objCtrl.id)) {
            objectRegistry.addEntity(objCtrl.id, objCtrl);
        }

        layerRenderer.scheduleRender();
    }

    private processUpdatePositionPreviewCmd(cmd: UpdatePreviewCmd) {
        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, cmd.meta.versionable)
        );
        const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());

        switch (cmd.state.getToolType() as FreedrawingToolType) {
            case 'PencilTool': {
                const pencil = layerRenderer.getObjCtrl(cmd.meta.targetId) as PenObjCtrl;
                if (pencil) {
                    pencil.updatePreview(convertProtoToPosition(cmd.state.getPoint()));
                    const previewLayerRenderer = this.getPreviewLayerRenderer(cmd);
                    if (previewLayerRenderer) previewLayerRenderer.scheduleRender();
                    else layerRenderer.scheduleRender(); // if preview layer is not yet created, fallback to the actual layer
                }
                break;
            }
            case 'EraserTool': {
                const eraser = layerRenderer.getObjCtrl(cmd.meta.targetId) as EraserObjCtrl;
                if (eraser) {
                    eraser.updatePreview(convertProtoToPosition(cmd.state.getPoint()));
                    layerRenderer.scheduleRender();
                }
                break;
            }
            default:
                break;
        }
    }

    private processUpdateBoundaryPreviewCmd(cmd: UpdatePreviewCmd) {
        const layerRenderer = this.getPreviewLayerRenderer(cmd);

        // if the preview layer or preview object is not available, it means that this peer properly
        // joined in the middle of a preview sequence. Simply discard this end preview command
        // and wait for the next sequence
        if (!layerRenderer || !layerRenderer.hasObjects()) return;

        const previewingObjCtrl = layerRenderer.getObjCtrl(cmd.meta.targetId);

        switch (cmd.state.getToolType() as FreedrawingToolType) {
            case 'LineTool':
            case 'HexagonTool':
            case 'OvalTool':
            case 'RectangleTool':
            case 'TriangleTool': {
                previewingObjCtrl.state = {
                    id: cmd.meta.targetId,
                    toolType: cmd.state.getToolType(),
                    toolState: previewingObjCtrl.state.toolState,
                    boundary: convertProtoToBoundary(cmd.state.getBoundary()),
                };
                break;
            }
            case 'LineV2Tool': {
                previewingObjCtrl.state = {
                    id: cmd.meta.targetId,
                    toolType: cmd.state.getToolType(),
                    toolState: previewingObjCtrl.state.toolState,
                    boundary: convertProtoToBoundary(cmd.state.getBoundary()),
                };
                break;
            }
            default:
                break;
        }

        layerRenderer.scheduleRender();

        if (cmd.state.getEndPoint() && cmd.meta.origin == CmdOriginType.local) {
            previewingObjCtrl.afterRender();
        }
    }

    private processEndPreview(cmd: AbstractCommand<CmdTypeProto>) {
        const layerRenderer = this.getPreviewLayerRenderer(cmd);

        // if the preview layer is not available, it means that this peer properly
        // joined in the middle of a preview sequence. Simply discard this end preview command
        // and wait for the next sequence
        if (!layerRenderer) return;

        const objCtrl = layerRenderer.getObjCtrl(cmd.meta.targetId);

        if (objCtrl) {
            // only destroy the preview object if it is not a pencil or an eraser
            // because the preview layer render the actual pencil object of the actual layer
            // not the preview object

            if (objCtrl instanceof PenObjCtrl) {
                layerRenderer.removeObjCtrl(cmd.meta.targetId, false);
                layerRenderer.resetDrawing();

                const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
                    freedrawingLayerReg(cmd.meta.viewport.id, layerRenderer.layer.doc.state.id)
                );

                const actualLayerRenderer = layerRegistry.getEntity(objCtrl.layer.state.id);
                actualLayerRenderer.scheduleRender(objCtrl); // we schedule with the objCtrl to avoid full redrawal of the layer
            } else if (objCtrl instanceof EraserObjCtrl) {
            } else if (objCtrl instanceof ShapeObjCtrl) {
                layerRenderer.removeObjCtrl(cmd.meta.targetId, true);
                layerRenderer.removeObjCtrl(SHAPE_PREVIEW_OBJ_ID, true);
                layerRenderer.scheduleRender();
            } else if (objCtrl instanceof LineObjV2Ctrl) {
                layerRenderer.removeObjCtrl(cmd.meta.targetId, true);
                layerRenderer.removeObjCtrl(LINE_V2_PREVIEW_OBJ_ID, true);
                layerRenderer.scheduleRender();
            }
        } else {
            console.warn('No preview object found with id ', cmd.meta.targetId);
        }

        if (!layerRenderer.hasObjects()) {
            //this.editor.removePreviewLayer(layerRenderer.layer.viewport);
        }
    }

    private processStartPreviewCmd(cmd: StartPreviewCmd) {
        let previewLayerRenderer: FreedrawingLayerRenderer = null;
        if (cmd.state.getToolType() != 'EraserTool') {
            previewLayerRenderer = this.getPreviewLayerRenderer(cmd);
            if (!previewLayerRenderer) {
                previewLayerRenderer = this.editor.createPreviewLayer(cmd.meta.viewport);
            }
        }

        const docRegistry = this.editor.regMan.registry<FreedrawingDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));

        const docCtrl = docRegistry.getEntity(cmd.meta.versionable);

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, docCtrl.state.id)
        );

        let objCtrl: FreedrawingObjCtrl<any>;

        // init preview obj
        switch (cmd.state.getToolType() as FreedrawingToolType) {
            case 'LineTool':
            case 'HexagonTool':
            case 'OvalTool':
            case 'RectangleTool':
            case 'TriangleTool': {
                const shapeProto = cmd.state.getShape();

                const obj: ShapeObj = {
                    id: cmd.meta.targetId,
                    toolType: cmd.state.getToolType() as FreedrawingToolType,
                    toolState: convertProtoToCommonToolState(shapeProto.getToolState()),
                    boundary: convertProtoToBoundary(shapeProto.getBoundary()),
                };

                objCtrl = this.createShapePreviewObjCtrl(obj, docCtrl);
                if (cmd.meta.origin == CmdOriginType.local && cmd.onCreatePreviewObj) {
                    cmd.onCreatePreviewObj(objCtrl);
                }

                if (cmd.meta.origin == CmdOriginType.remote) (objCtrl as ShapeObjCtrl).disableBoundaryEvent();

                previewLayerRenderer.addObjCtrl(objCtrl);
                break;
            }
            case 'PencilTool': {
                const pencilProto = cmd.state.getPencil();

                const obj: PenObj = {
                    id: cmd.meta.targetId,
                    toolType: cmd.state.getToolType() as FreedrawingToolType,
                    toolState: convertProtoToCommonToolState(pencilProto.getToolState()),
                    points: pencilProto.getPointsList().map(p => convertProtoToPosition(p)),
                };

                const objCtrl = new PenObjCtrl(obj, this.editor, docCtrl);

                const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());
                layerRenderer.addObjCtrl(objCtrl);
                previewLayerRenderer.addObjCtrl(objCtrl, true);

                if (previewLayerRenderer) previewLayerRenderer.scheduleRender();
                else layerRenderer.scheduleRender();

                return;
            }
            case 'EraserTool': {
                const eraserProto = cmd.state.getEraser();

                const obj: EraserObj = {
                    id: cmd.meta.targetId,
                    toolType: cmd.state.getToolType() as FreedrawingToolType,
                    toolState: convertProtoToEraserToolState(eraserProto.getToolState()),
                    points: eraserProto.getPointsList().map(p => convertProtoToPosition(p)),
                };

                const objCtrl = new EraserObjCtrl(obj, this.editor, docCtrl);

                // eraser preview obj have to been added to specific layer instead of preview layer
                const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());
                layerRenderer.addObjCtrl(objCtrl);
                layerRenderer.scheduleRender();

                return;
            }
            case 'LineV2Tool': {
                const lineProto = cmd.state.getLineV2();

                const obj: LineObjV2 = new LineObjV2(cmd.meta.targetId);
                Object.assign(obj, {
                    toolType: cmd.state.getToolType() as FreedrawingToolType,
                    toolState: convertProtoToLineObjV2ToolState(lineProto.getToolState()),
                    boundary: convertProtoToBoundary(lineProto.getBoundary()),
                });

                objCtrl = new LineObjV2Ctrl(obj, this.editor, docCtrl, true);
                if (cmd.meta.origin == CmdOriginType.local && cmd.onCreatePreviewObj) {
                    cmd.onCreatePreviewObj(objCtrl);
                }

                if (cmd.meta.origin == CmdOriginType.remote) (objCtrl as LineObjV2Ctrl).disableBoundaryEvent();

                previewLayerRenderer.addObjCtrl(objCtrl);
                break;
            }
            default:
                break;
        }

        if (cmd.meta.targetId == -1) {
            previewLayerRenderer?.scheduleRender();
        }
    }

    private processUpdateToolStatePreviewCmd(cmd: UpdatePreviewCmd) {
        const freedrawingLayerRenderer = this.getPreviewLayerRenderer(cmd);

        // if the preview layer or preview object is not available, it means that this peer properly
        // joined in the middle of a preview sequence. Simply discard this end preview command
        // and wait for the next sequence
        if (!freedrawingLayerRenderer || !freedrawingLayerRenderer.hasObjects()) return;

        switch (cmd.state.getToolType() as FreedrawingToolType) {
            case 'LineTool':
            case 'HexagonTool':
            case 'OvalTool':
            case 'RectangleTool':
            case 'TriangleTool':
            case 'PencilTool': {
                const previewing = freedrawingLayerRenderer.getObjCtrl(cmd.meta.targetId);
                const curState = previewing.state;

                curState.toolState = convertProtoToCommonToolState(cmd.state.getCommonToolState());
                previewing.state = curState;
                break;
            }
            case 'LineV2Tool': {
                const previewing = freedrawingLayerRenderer.getObjCtrl(cmd.meta.targetId);
                const curState = previewing.state;
                curState.toolState = convertProtoToLineObjV2ToolState(cmd.state.getLineV2ToolState());
                previewing.state = curState;
                break;
            }
            case 'EraserTool': {
                // not happen
                break;
            }
            default:
                break;
        }

        freedrawingLayerRenderer.scheduleRender();
    }

    private createShapePreviewObjCtrl(obj: FreedrawingObj<any>, docCtrl: VDocCtrl): ShapeObjCtrl {
        const shape = obj as ShapeObj;

        switch (obj.toolType) {
            case 'LineTool': {
                return new LineObjCtrl(shape, this.editor, docCtrl, true);
            }
            case 'HexagonTool': {
                return new HexagonObjCtrl(shape, this.editor, docCtrl, true);
            }
            case 'OvalTool': {
                return new OvalObjCtrl(shape, this.editor, docCtrl, true);
            }
            case 'RectangleTool': {
                return new RectangleObjCtrl(shape, this.editor, docCtrl, true);
            }
            case 'TriangleTool': {
                return new TriangleObjCtrl(shape, this.editor, docCtrl, true);
            }
        }

        return null;
    }

    private createShapeObjectCtrl(obj: FreedrawingObj<any>, docCtrl: DefaultVDocCtrl): ShapeObjCtrl {
        const shape = obj as ShapeObj;

        switch (obj.toolType) {
            case 'LineTool':
                return new LineObjCtrl(shape, this.editor, docCtrl, false);
            case 'HexagonTool':
                return new HexagonObjCtrl(shape, this.editor, docCtrl, false);
            case 'OvalTool':
                return new OvalObjCtrl(shape, this.editor, docCtrl, false);
            case 'RectangleTool':
                return new RectangleObjCtrl(shape, this.editor, docCtrl, false);
            case 'TriangleTool':
                return new TriangleObjCtrl(shape, this.editor, docCtrl, false);
        }

        return null;
    }

    private processInsertShapeObjCmd(cmd: InsertShapeObjCmd) {
        // end preview first
        this.processEndPreview(cmd);

        const docRegistry = this.editor.regMan.registry<DefaultVDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));

        const docId = cmd.meta.versionable;
        const docCtrl = docRegistry.getEntity(docId);

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, docId)
        );
        const objectRegistry = this.editor.regMan.registry<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(cmd.meta.viewport.id, docId)
        );

        const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());

        const obj: ShapeObj = {
            id: cmd.state.getShapeObj().getLocalId(),
            toolType: cmd.state.getShapeObj().getToolType() as FreedrawingToolType,
            toolState: convertProtoToCommonToolState(cmd.state.getShapeObj().getToolState()),
            boundary: convertProtoToBoundary(cmd.state.getShapeObj().getBoundary()),
        };

        const objCtrl = this.createShapeObjectCtrl(obj, docCtrl);
        layerRenderer.addObjCtrl(objCtrl);

        layerRenderer.scheduleRender();

        objectRegistry.addEntity(objCtrl.id, objCtrl);
    }

    private processInsertLineObjV2Cmd(cmd: InsertLineObjV2Cmd) {
        // end preview first
        this.processEndPreview(cmd);

        const docRegistry = this.editor.regMan.registry<DefaultVDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));

        const docId = cmd.meta.versionable;
        const docCtrl = docRegistry.getEntity(docId);

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, docId)
        );
        const objectRegistry = this.editor.regMan.registry<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(cmd.meta.viewport.id, docId)
        );

        const layerRenderer = layerRegistry.getEntity(cmd.state.getLayerId());

        const objCtrl = convertProtoToLineObjV2Ctrl(cmd.state.getLineObj(), this.editor, docCtrl);
        layerRenderer.addObjCtrl(objCtrl);

        layerRenderer.scheduleRender();

        objectRegistry.addEntity(objCtrl.id, objCtrl);
    }

    private processInsertLayerCmd(cmd: FCInsertLayerCmd) {
        const layerId = cmd.meta.targetId;
        const docId = cmd.meta.versionable;
        const zindex = cmd.state.getZIndex();

        const docRegistry = this.editor.regMan.registry<DefaultVDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));
        const docCtrl = docRegistry.getEntity(docId) as FreedrawingDocCtrl;

        const layer: FreedrawingLayer = new FreedrawingLayer(
            layerId,
            fcConvertProtoToBoundary(cmd.state.getBoundary()),
            zindex
        );

        const option: LayerOptions = {
            editor: this.editor,
            state: layer,
            viewport: cmd.meta.viewport,
        };
        const layerCtrl = cmd.meta.viewport.requestLayer(
            UnboundedGraphicLayerCtrl,
            true,
            option
        ) as UnboundedGraphicLayerCtrl;
        const layerRenderer = new FreedrawingLayerRenderer(layerCtrl as UnboundedGraphicLayerCtrl);
        docCtrl.rendererCtrl = layerRenderer;
        docCtrl.addLayer(layerCtrl);

        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(cmd.meta.viewport.id, docId)
        );
        layerRegistry.addEntity(layerId, layerRenderer);

        this.editor.crdFeature.docCreationsCheck$.next([
            {
                editor: this.editor,
                vm: cmd.meta.viewport,
                localId: docId,
            },
        ]);
    }

    private processInsertDocCmd(cmd: FCInsertDocCmd) {
        const doc = new FreedrawingDoc(cmd.state.getGlobalId(), cmd.meta.targetId);
        const ctrl = new FreedrawingDocCtrl(this.editor, doc, cmd.meta.viewport);
        ctrl.viewport = cmd.meta.viewport;

        this.editor.docByViewport.set(cmd.meta.viewport.id, doc.id);

        const docRegistry = this.editor.regMan.register<DefaultVDocCtrl>(freedrawingDocReg(cmd.meta.viewport.id));
        docRegistry.addEntity(doc.id, ctrl);

        this.editor.regMan.register<FreedrawingLayerRenderer>(freedrawingLayerReg(cmd.meta.viewport.id, doc.id));
        this.editor.regMan.register<FreedrawingObjCtrl<any>>(freedrawingObjectReg(cmd.meta.viewport.id, doc.id));

        this.editor.crdFeature.docCreationsCheck$.next([
            {
                editor: this.editor,
                vm: cmd.meta.viewport,
                localId: doc.id,
            },
        ]);
    }
}

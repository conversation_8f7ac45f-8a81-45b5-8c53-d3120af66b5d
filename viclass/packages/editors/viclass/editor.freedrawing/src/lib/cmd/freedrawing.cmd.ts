import {
    AbstractCommand,
    Cmd,
    CmdMeta,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    Position,
    Rectangle,
    ToolState,
} from '@viclass/editor.core';
import {
    CmdTypeProto,
    EndPreviewCmdProto,
    InsertDocCmdProto,
    InsertLayerCmdProto,
    InsertLineObjV2CmdProto,
    InsertPartialEraserObjCmdProto,
    InsertPartialPencilObjCmdProto,
    InsertShapeObjCmdProto,
    RemoveFreedrawingObjCmdProto,
    RemoveLayerCmdProto,
    StartPreviewCmdProto,
    UpdatePreviewCmdProto,
} from '@viclass/proto/editor.freedrawing';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import {
    CommonToolState,
    EraserObj,
    EraserToolState,
    FreedrawingObj,
    FreedrawingToolType,
    LineObjV2,
    LineV2ToolState,
    PenObj,
    ShapeObj,
} from '../freedrawing.models';
import {
    buildBoundaryProto,
    buildCommonToolStateProto,
    buildEraserObjProto,
    buildEraserToolStateProto,
    buildLineObjV2Proto,
    buildLineObjV2ToolStateProto,
    buildPencilObjProto,
    buildPositionProto,
    buildShapeObjProto,
} from '../freedrawing.util';
import { FreedrawingObjCtrl } from '../objects/freedrawing.obj.ctrl';

/**
 *
 * <AUTHOR>
 */

export class InsertDocCmd extends AbstractCommand<CmdTypeProto> {
    state: InsertDocCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
        this.state = new InsertDocCmdProto();
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array) {
        return InsertDocCmdProto.deserializeBinary(buf);
    }
}

export class InsertLayerCmd extends AbstractCommand<CmdTypeProto> {
    state: InsertLayerCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
        this.state = new InsertLayerCmdProto().setZindex(0);
    }

    setZindex(zindex: number) {
        this.state.setZindex(zindex);
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array) {
        return InsertLayerCmdProto.deserializeBinary(buf);
    }
}

export class InsertShapeObjCmd extends AbstractCommand<CmdTypeProto> {
    private _state: InsertShapeObjCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array): InsertShapeObjCmdProto {
        return InsertShapeObjCmdProto.deserializeBinary(buf);
    }

    get state(): InsertShapeObjCmdProto {
        return this._state;
    }

    public set state(v: InsertShapeObjCmdProto) {
        this._state = v;
    }

    setState(obj: ShapeObj, layerId: number) {
        this._state = new InsertShapeObjCmdProto().setLayerId(layerId).setShapeObj(buildShapeObjProto(obj));
    }
}

export class UpdateShapeObjCmd extends AbstractCommand<CmdTypeProto> {
    private _state: InsertShapeObjCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array): InsertShapeObjCmdProto {
        return InsertShapeObjCmdProto.deserializeBinary(buf);
    }

    get state(): InsertShapeObjCmdProto {
        return this._state;
    }

    public set state(v: InsertShapeObjCmdProto) {
        this._state = v;
    }

    setState(obj: ShapeObj, layerId: number) {
        this._state = new InsertShapeObjCmdProto().setLayerId(layerId).setShapeObj(buildShapeObjProto(obj));
    }
}

export class InsertPartialPencilObjCmd extends AbstractCommand<CmdTypeProto> {
    private _state: InsertPartialPencilObjCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
    }

    get state(): InsertPartialPencilObjCmdProto {
        return this._state;
    }

    public set state(v: InsertPartialPencilObjCmdProto) {
        this._state = v;
    }

    serialize(): Uint8Array {
        return this._state.serializeBinary();
    }

    deserialize(buf: Uint8Array): InsertPartialPencilObjCmdProto {
        return InsertPartialPencilObjCmdProto.deserializeBinary(buf);
    }

    setState(obj: PenObj, layerId: number) {
        this._state = new InsertPartialPencilObjCmdProto().setLayerId(layerId).setPencilObj(buildPencilObjProto(obj));
    }
}

export class InsertPartialEraserObjCmd extends AbstractCommand<CmdTypeProto> {
    private _state: InsertPartialEraserObjCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
    }

    get state(): InsertPartialEraserObjCmdProto {
        return this._state;
    }

    public set state(v: InsertPartialEraserObjCmdProto) {
        this._state = v;
    }

    serialize(): Uint8Array {
        return this._state.serializeBinary();
    }

    deserialize(buf: Uint8Array): InsertPartialEraserObjCmdProto {
        return InsertPartialEraserObjCmdProto.deserializeBinary(buf);
    }

    setState(obj: EraserObj, layerId: number) {
        this._state = new InsertPartialEraserObjCmdProto().setLayerId(layerId).setEraserObj(buildEraserObjProto(obj));
    }
}

export class InsertLineObjV2Cmd extends AbstractCommand<CmdTypeProto> {
    private _state: InsertLineObjV2CmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array): InsertLineObjV2CmdProto {
        return InsertLineObjV2CmdProto.deserializeBinary(buf);
    }

    get state(): InsertLineObjV2CmdProto {
        return this._state;
    }

    public set state(v: InsertLineObjV2CmdProto) {
        this._state = v;
    }

    setState(obj: LineObjV2, layerId: number) {
        this._state = new InsertLineObjV2CmdProto().setLayerId(layerId).setLineObj(buildLineObjV2Proto(obj));
    }
}

export class StartPreviewCmd extends AbstractCommand<CmdTypeProto> {
    state: StartPreviewCmdProto;
    onCreatePreviewObj?: (previewObj: FreedrawingObjCtrl<any>) => void;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);

        this.meta.reliable = true;
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array): StartPreviewCmdProto {
        return StartPreviewCmdProto.deserializeBinary(buf);
    }

    setState(toolType: FreedrawingToolType, object: FreedrawingObj<any>, layerId?: number) {
        switch (toolType) {
            case 'LineTool':
            case 'HexagonTool':
            case 'OvalTool':
            case 'RectangleTool':
            case 'TriangleTool': {
                const shape = object as ShapeObj;
                this.state = new StartPreviewCmdProto().setToolType(toolType).setShape(buildShapeObjProto(shape));
                break;
            }
            case 'PencilTool': {
                const pencil = object as PenObj;
                this.state = new StartPreviewCmdProto()
                    .setToolType(toolType)
                    .setPencil(buildPencilObjProto(pencil))
                    .setLayerId(layerId);
                break;
            }
            case 'EraserTool': {
                const eraser = object as EraserObj;
                this.state = new StartPreviewCmdProto()
                    .setToolType(toolType)
                    .setEraser(buildEraserObjProto(eraser))
                    .setLayerId(layerId);
                break;
            }
            case 'LineV2Tool': {
                const line = object as LineObjV2;
                this.state = new StartPreviewCmdProto()
                    .setToolType(toolType)
                    .setLineV2(buildLineObjV2Proto(line))
                    .setLayerId(layerId);
                break;
            }
            default:
                break;
        }
    }

    setOnCreatePreviewObj(handler: (previewObj: FreedrawingObjCtrl<any>) => void) {
        this.onCreatePreviewObj = handler;
    }
}

export class UpdatePreviewCmd extends AbstractCommand<CmdTypeProto> {
    state: UpdatePreviewCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
    }

    updateBoundary(toolType: FreedrawingToolType, boundary: Rectangle, endPoint: boolean) {
        this.state = new UpdatePreviewCmdProto()
            .setToolType(toolType)
            .setEndPoint(endPoint)
            .setBoundary(buildBoundaryProto(boundary));
    }

    updatePosition(toolType: FreedrawingToolType, position: Position, endPoint: boolean, layerId: number) {
        this.state = new UpdatePreviewCmdProto()
            .setToolType(toolType)
            .setEndPoint(endPoint)
            .setLayerId(layerId)
            .setPoint(buildPositionProto(position));
    }

    updateContent(toolType: FreedrawingToolType, content: string, isTyping: boolean) {
        this.state = new UpdatePreviewCmdProto()
            .setToolType(toolType)
            .setEndPoint(true)
            .setContent(content)
            .setTyping(isTyping);
    }

    updateToolState(toolType: FreedrawingToolType, toolState: ToolState) {
        switch (toolType) {
            case 'CommonPropertiesTool':
            case 'LineTool':
            case 'HexagonTool':
            case 'OvalTool':
            case 'RectangleTool':
            case 'TriangleTool':
            case 'PencilTool': {
                const obj = toolState as CommonToolState;
                this.state = new UpdatePreviewCmdProto()
                    .setToolType(toolType)
                    .setEndPoint(true)
                    .setCommonToolState(buildCommonToolStateProto(obj));
                break;
            }
            case 'EraserPropertiesTool':
            case 'EraserTool': {
                const obj = toolState as EraserToolState;
                this.state = new UpdatePreviewCmdProto()
                    .setToolType(toolType)
                    .setEndPoint(true)
                    .setEraserToolState(buildEraserToolStateProto(obj));
                break;
            }
            case 'LineV2Tool': {
                const obj = toolState as LineV2ToolState;
                this.state = new UpdatePreviewCmdProto()
                    .setToolType(toolType)
                    .setEndPoint(true)
                    .setLineV2ToolState(buildLineObjV2ToolStateProto(obj));
                break;
            }
            default:
                break;
        }
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array): UpdatePreviewCmdProto {
        return UpdatePreviewCmdProto.deserializeBinary(buf);
    }
}

export class EndPreviewCmd extends AbstractCommand<CmdTypeProto> {
    state: EndPreviewCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
        this.state = new EndPreviewCmdProto();
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array) {
        return EndPreviewCmdProto.deserializeBinary(buf);
    }
}

export class RemoveFreedrawingObjCmd extends AbstractCommand<CmdTypeProto> {
    state: RemoveFreedrawingObjCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
    }

    setState(layerId: number, objId: number) {
        this.state = new RemoveFreedrawingObjCmdProto().setLayerId(layerId).setObjId(objId);
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array) {
        return RemoveFreedrawingObjCmdProto.deserializeBinary(buf);
    }
}

export class RemoveLayerCmd extends AbstractCommand<CmdTypeProto> {
    state: RemoveLayerCmdProto;

    constructor(meta: CmdMeta) {
        super(meta, meta.cmdType);
        this.state = new RemoveLayerCmdProto();
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    deserialize(buf: Uint8Array) {
        return RemoveLayerCmdProto.deserializeBinary(buf);
    }
}

export function deserializer(meta: CmdMeta, stateData: Uint8Array): Cmd<FCCmdTypeProto | CmdTypeProto> {
    let cmd: Cmd<FCCmdTypeProto | CmdTypeProto>;
    const cmdType = meta.cmdType as FCCmdTypeProto | CmdTypeProto;

    switch (cmdType) {
        case FCCmdTypeProto.INSERT_DOC: {
            cmd = new FCInsertDocCmd(meta);
            break;
        }
        case FCCmdTypeProto.INSERT_LAYER: {
            cmd = new FCInsertLayerCmd(meta);
            break;
        }
        case FCCmdTypeProto.REMOVE_DOC: {
            cmd = new FCRemoveDocCmd(meta);
            break;
        }
        case FCCmdTypeProto.RELOAD_DOC: {
            cmd = new FCReloadDocCmd(meta);
            break;
        }

        case CmdTypeProto.START_PREVIEW: {
            cmd = new StartPreviewCmd(meta);
            break;
        }
        case CmdTypeProto.END_PREVIEW: {
            cmd = new EndPreviewCmd(meta);
            break;
        }
        case CmdTypeProto.UPDATE_CONTENT_PREVIEW:
        case CmdTypeProto.UPDATE_BOUNDARY_PREVIEW:
        case CmdTypeProto.UPDATE_POSITION_PREVIEW:
        case CmdTypeProto.UPDATE_TOOL_STATE_PREVIEW: {
            cmd = new UpdatePreviewCmd(meta);
            break;
        }
        case CmdTypeProto.INSERT_SHAPE_OBJ: {
            cmd = new InsertShapeObjCmd(meta);
            break;
        }
        case CmdTypeProto.INSERT_PARTIAL_PENCIL_OBJ: {
            cmd = new InsertPartialPencilObjCmd(meta);
            break;
        }
        case CmdTypeProto.INSERT_PARTIAL_ERASER_OBJ: {
            cmd = new InsertPartialEraserObjCmd(meta);
            break;
        }
        case CmdTypeProto.REMOVE_OBJ: {
            cmd = new RemoveFreedrawingObjCmd(meta);
            break;
        }
        case CmdTypeProto.REMOVE_LAYER: {
            cmd = new RemoveLayerCmd(meta);
            break;
        }
        case CmdTypeProto.INSERT_LINE_OBJ_V2: {
            cmd = new InsertLineObjV2Cmd(meta);
            break;
        }

        default:
            throw new Error(`invalid cmd type: ${cmdType}`);
    }

    const state = cmd.deserialize(stateData);
    cmd.state = state;

    return cmd;
}

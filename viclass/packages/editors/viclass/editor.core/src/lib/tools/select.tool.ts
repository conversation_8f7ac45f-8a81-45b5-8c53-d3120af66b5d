import { BehaviorSubject, Subscription } from 'rxjs';
import {
    BaseBoardViewportManager,
    BoardViewportManager,
    BoundaryRectangle,
    CoordinatorEvent,
    CoverLayerCtrl,
    Cursor,
    DocMappingLDEData,
    DocumentEditor,
    EditorBlurCES,
    EditorCoordinator,
    EditorType,
    InferredPointerEvent,
    KeyboardEventListener,
    MouseEventListener,
    NativeEventTarget,
    pointerCursor,
    PointerEventData, // Added
    PointerEventListener, // Added
    PointerHandlingItem,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen, // Added
    Tool,
    ToolBar,
    UIPointerEventData,
    UserInputHandlerType,
    VDocCtrl,
    VDocLayerCtrl,
    VEventData,
    VEventListener,
    ViewportManager,
    ViewportMode,
    xmlns,
} from '../api';
import { BoundaryChangeEventData, RectangleObjectBoundary } from '../boundary';
import { DocBoundaryInfo, DocCRDFeature, fcConvertProtoToBoundary, FCUpdateDocCmd } from '../common';
import { DefaultToolBar } from '../default.toolbar';
import { attrsNS, CachingReflowSync, mouseLocation } from '../util';
import { CommonTool, CommonToolType } from './common.tool';
import { HistoryItem, SupportFeatureHistory } from './history.tool';
import { ROBFeature } from './rob.feature';
import { SelectionFeature } from './selection.feature';

export const FEATURE_SELECTION = 'common_select_feature';
export const FEATURE_INTERNAL_SELECTION = 'common_internal_select_feature';

type SingleSelectionType = 'doc-selected' | 'doc-deselected';
type MultiSelectionType = 'doc-multi-selected' | 'doc-multi-deselected';
type InternalSelectionType = 'doc-internal-selected' | 'doc-internal-unselected';

type UpdateBoundaryHistoryItem = 'update-boundary';

export type SelectionEventType = SingleSelectionType | MultiSelectionType | InternalSelectionType;

export type SingleSelectionState = {
    selectCtx: SelectContext;
};

export type MultiSelectionState = {
    selectCtxs: SelectContext[];
};

export type SingleSelectionEvent = VEventData<
    Exclude<SelectionEventType, MultiSelectionType | InternalSelectionType>,
    SelectTool,
    SingleSelectionState
>;
export type MultiSelectionEvent = VEventData<
    Exclude<SelectionEventType, SingleSelectionType | InternalSelectionType>,
    SelectTool,
    MultiSelectionState
>;
export type SelectionEvent = SingleSelectionEvent | MultiSelectionEvent;

export interface SelectHitContextDetails {
    hitId: string | number; // the internal hit identify, may be element name or index, ....
}

export interface SelectHitContext {
    doc: VDocCtrl; // the document being hit
    hitDetails: SelectHitContextDetails;
}

export interface SelectContext {
    doc: VDocCtrl;
    supporter: SupportSelectFeature;
    selectDetails: any;
}

export type SelectContextWithBound = SelectContext & {
    boundary?: RectangleObjectBoundary;
};

export interface SupportSelectFeature {
    /**
     * This method ask the supporter of the select feature to check if a layer has
     * any data being hit by the mouse event
     * @param layer the layer belongs to the supporter
     * @param multiple true in case the selection is done with ctrl key
     * @param event the pointer event that the select tool is handling // Changed from mouse event
     */
    checkHit(layer: VDocLayerCtrl, event: UIPointerEventData<any>, multiple?: boolean): SelectHitContext | undefined; // Changed event type

    /**
     * IMPORTANT NOTE: the supporter implement this method SHOULD NOT INFORM THE selection feature about the selection
     * because this method is EXCLUSIVELY called by the select tool only. In effect, the implementation of this method
     * should not call selectDoc
     *
     * Request the supporter to actually select the data. The hit context is used to do the selection
     * to provide the supporter with necessary information about what is being selected.
     *
     * @param hitCtx
     * @param multiple true in case the selection is done with ctrl key
     * @returns a select context which normally contains the boundary of the selection
     */
    select(hitCtx: SelectHitContext, multiple?: boolean): SelectContext;

    /**
     * IMPORTANT NOTE: the supporter implement this method must call the selection feature to inform about the document
     * selection as this method is used to initiate the selection sequence from outside of the select tool
     *
     * Allow select a particular document programmatically.
     * @param multiple true in case the selection is done with ctrl key
     * @param doc
     */
    selectDoc(doc: VDocCtrl, multiple?: boolean): SelectContext;

    /**
     * IMPORTANT NOTE: the supporter implement this method SHOULD NOT INFORM THE selection feature about the deselection
     * because this method is EXCLUSIVELY called by the select tool only. In effect, the implementation of this method
     * should not call deselectDoc
     *
     * Request the supporter to actually deselect the document. The select context contains information about what is deselected
     * @param selectCtx
     */
    deselect(selectCtx: SelectContext);

    /**
     * IMPORTANT NOTE: the supporter implement this method must call the selection feature to inform about the document
     * deselection as this method is used to initiate the deselection sequence from outside of the select tool
     *
     * Allow deselect a document programmatically.
     * @param doc
     */
    deselectDoc(doc: VDocCtrl);
}

type UpdateBoundaryDoc = {
    oldBoundaryDoc: DocMappingLDEData;
    newBoundaryDoc: DocMappingLDEData;
};

export class UpdateBoundaryItem implements HistoryItem {
    constructor(
        public supporter: SupportFeatureHistory,
        public vm: ViewportManager,
        public type: UpdateBoundaryHistoryItem,
        public documents: UpdateBoundaryDoc[]
    ) {}
}

export interface SupportInternalSelectFeature {
    /**
     *
     * @param layer
     * @param event
     * @param multiple true in case the selection is done with ctrl key
     */
    checkHitInternal(layer: VDocLayerCtrl, event: UIPointerEventData<any>, multiple?: boolean): SelectHitContext; // Changed event type

    highlight(ctx: SelectHitContext);

    removeHighlight(ctx: SelectHitContext); // remove a previous highlight

    /**
     * @param ctx
     * @param multiple true in case the selection is done with ctrl key
     */
    selectElement(ctx: SelectHitContext, multiple?: boolean);

    /**
     * @param ctx
     */
    removeSelectedElement(ctx: SelectHitContext);

    /**
     * Clear the selected elements within a certain doc controller
     * @param doc
     */
    clearSelectedElInDoc(doc: VDocCtrl);

    // highlighting can be done through css as well
    // if some editors don't need js highlight, they can disable by setting this to true
    disableJsHighlight?: boolean;
}

export interface SelectToolState {
    numDocSelected: number;
}

export type AlignType = 'align-left' | 'align-right' | 'align-center' | 'align-top' | 'align-bottom' | 'align-middle';

export interface AlignToolState {
    alignMode?: AlignType;
}

export class SelectAlignTool implements Tool, PointerEventListener<NativeEventTarget<any>> {
    // Changed interface
    type: UserInputHandlerType = 'Tool';
    readonly pointerHandling: PointerHandlingItem[] = []; // Keep mouseHandling definition for now
    readonly pointerHandler: PointerEventListener<NativeEventTarget<any>> = this; // Changed property name and type
    readonly toolState: AlignToolState;

    toolbar?: AlignToolbar;

    private requestedFrame: boolean = false;
    // Changed type from MouseEventData to UIPointerEventData
    private readonly lastPointerMove$ = new BehaviorSubject<UIPointerEventData<any> | undefined>(undefined);
    private layer?: CoverLayerCtrl;

    private alignLine?: SVGLineElement;

    private isAligning: boolean = false;

    constructor(
        private selectTool: SelectTool,
        private _p: EditorCoordinator,
        private crdFeature?: DocCRDFeature
    ) {
        // Keep mouseHandling items for now, might need refactor to pointerHandling later
        this.pointerHandling.push(
            { event: 'pointermove', button: 0, pressedButtons: 0, pointerTypes: ['mouse'] },
            { event: 'pointerup', button: 0, keys: ['nokey'], pointerTypes: ['mouse'] },
            { event: 'pointermove', pointerTypes: ['touch', 'pen'] },
            { event: 'pointerup', pointerTypes: ['touch', 'pen'] }
        );

        this.toolState = {
            alignMode: undefined,
        };
    }

    childToolbar?: ToolBar<any, any>;

    get toolType(): AlignType | undefined {
        return this.toolState.alignMode;
    }

    onAttachViewport() {}
    onDetachViewport() {}

    // call by the toolbar
    async onBlur() {}

    // call by the toolbar
    async onFocus() {}

    onDisable() {}

    onEnable() {}

    // call by the toolbar
    async focus(toolName: AlignType) {
        // check toolName instead of this.toolType because we focus the new tool, not the old one
        if (!this.toolbar || this.toolbar.isDisabled() || this.toolbar.isToolDisable(toolName)) return;
        this.toolState.alignMode = toolName;
    }

    // call by the toolbar
    async blur(toolName: AlignType) {
        if (
            !this.toolbar ||
            !this.toolbar.viewport ||
            this.toolbar.isDisabled() ||
            (this.toolType && this.toolbar.isToolDisable(this.toolType))
        )
            return;

        if (toolName == this.toolState.alignMode) {
            delete this.toolState.alignMode;
            if (this.layer) {
                this.toolbar.viewport.sink(this.layer);
                this.toolbar.viewport.removeLayer(this.layer);
                delete this.layer;
                delete this.alignLine;
            }
        }
    }

    // Changed parameter type from MouseEventData to PointerEventData
    async onEvent(event: PointerEventData<NativeEventTarget<any>>): Promise<PointerEventData<NativeEventTarget<any>>> {
        if (!this.toolbar || !this.toolbar.viewport)
            throw new Error('Align tool has not been added to a toolbar or has not attached to a viewport');

        if (this.isAligning) return event;

        // Only handle UI Pointer Events
        if (!('nativeEvent' in event) || !('viewport' in event)) {
            return event;
        }
        const uiEvent = event as UIPointerEventData<NativeEventTarget<any>>;

        if (
            !this.toolType || // we don't know which tool is focus, then will not handle anything
            this.toolbar.isDisabled() ||
            this.toolbar.isToolDisable(this.toolType)
        )
            return uiEvent;

        switch (
            uiEvent.eventType // Use eventType from UIPointerEventData
        ) {
            case 'pointermove': // Changed from 'mousemove'
                this.lastPointerMove$.next(uiEvent);
                if (!this.requestedFrame) {
                    requestAnimationFrame(() => {
                        this.requestedFrame = false;
                        this.handleMouseMove();
                    });
                    this.requestedFrame = true;
                }

                break;
            case 'pointerup': // Changed from 'mouseup'
                // user might click & release immediately -> no mousemove
                if (this.lastPointerMove$.value) {
                    this.isAligning = true;
                    const startState = this.selectTool.calculateBoundaryForUpdateFromSelection();

                    for (const s of this.selectTool.curDocSelection$.value) this.handleMouseUp(s);

                    const toBeUpdated = this.selectTool.calculateBoundaryForUpdateFromSelection();
                    await this.crdFeature?.updateDocBoundary(
                        toBeUpdated,
                        this.toolbar.viewport,
                        {
                            startState: startState,
                        },
                        true
                    );
                    this.isAligning = false;
                }
                this.lastPointerMove$.next(undefined);

                uiEvent.continue = false;

                break;
        }

        return uiEvent;
    }

    left(b: BoundaryRectangle) {
        return Math.min(b.start!.x, b.end!.x);
    }

    right(b: BoundaryRectangle) {
        return Math.max(b.start!.x, b.end!.x);
    }

    bottom(b: BoundaryRectangle) {
        return Math.min(b.start!.y, b.end!.y);
    }

    top(b: BoundaryRectangle) {
        return Math.max(b.start!.y, b.end!.y);
    }

    handleMouseUp(ctx: SelectContextWithBound) {
        if (!this.lastPointerMove$.value)
            throw new Error('There is no last mouse move recorded. Unable to process mouse up.');

        // Use mouseLocation utility, assuming it handles UIPointerEventData
        const ePos = mouseLocation(this.lastPointerMove$.value);
        const b = ctx.boundary!;
        let deltaX = 0,
            deltaY = 0;

        const oldBoundary = structuredClone(b.boundary);

        switch (this.toolState.alignMode) {
            case 'align-left': {
                deltaX = ePos.x - this.left(b.boundary);
                deltaY = 0;
                break;
            }
            case 'align-right': {
                deltaX = ePos.x - this.right(b.boundary);
                deltaY = 0;
                break;
            }
            case 'align-center': {
                deltaX = ePos.x - this.left(b.boundary) - b.boundary.width! / 2;
                deltaY = 0;
                break;
            }
            case 'align-top': {
                deltaX = 0;
                deltaY = ePos.y - this.top(b.boundary);
                break;
            }
            case 'align-bottom': {
                deltaX = 0;
                deltaY = ePos.y - this.bottom(b.boundary);
                break;
            }
            case 'align-middle': {
                deltaX = 0;
                deltaY = ePos.y - this.top(b.boundary) + b.boundary.height! / 2;
                break;
            }
        }

        const newBoundary = {
            start: {
                x: b.boundary.start!.x + deltaX,
                y: b.boundary.start!.y + deltaY,
            },
            end: {
                x: b.boundary.end!.x + deltaX,
                y: b.boundary.end!.y + deltaY,
            },
            width: b.boundary.width,
            height: b.boundary.height,
        };

        b.resetBoundary(newBoundary);

        return { newBoundary, oldBoundary };
    }

    handleMouseMove() {
        if (!this.toolbar || !this.toolbar.viewport)
            if (!this.toolbar || !this.toolbar.viewport)
                throw new Error('Align tool has not been added to a toolbar or has not attached to a viewport');

        if (!this.lastPointerMove$.value) throw new Error('No last mouse move recorded');

        if (!this.layer) {
            this.layer = this.toolbar.viewport.requestLayer(CoverLayerCtrl, true, {
                viewport: this.toolbar.viewport,
            }) as CoverLayerCtrl;
            this.toolbar.viewport.float(this.layer);
        }

        if (!this.alignLine) {
            this.alignLine = document.createElementNS(xmlns, 'line');
            this.layer.renderingRoot.appendChild(this.alignLine);
        }

        // Use mouseLocation utility, assuming it handles UIPointerEventData
        const pos = mouseLocation(this.lastPointerMove$.value);
        const ePos = this.layer.nativeElPosition(pos);
        const zoom = (this.toolbar.viewport as BoardViewportManager).zoomLevel;

        let x1, x2, y1, y2;

        switch (this.toolState.alignMode) {
            case 'align-left':
            case 'align-right':
            case 'align-center': {
                x1 = x2 = ePos.x;
                y1 = ePos.y - 2000 * zoom;
                y2 = ePos.y + 2000 * zoom;
                break;
            }
            case 'align-top':
            case 'align-bottom':
            case 'align-middle': {
                y1 = y2 = ePos.y;
                x1 = ePos.x - 2000 * zoom;
                x2 = ePos.x + 2000 * zoom;
                break;
            }
        }

        attrsNS(this.alignLine, {
            x1: x1,
            y1: y1,
            x2: x2,
            y2: y2,
            class: 'vi-select-tool-align-line',
        });
    }
}

export class AlignToolbar extends DefaultToolBar<AlignType, SelectAlignTool> {
    override readonly keyboardHandler?: KeyboardEventListener<NativeEventTarget<any>>;
    // Keep mouseHandler for now, might need refactor later
    override readonly mouseHandler?: MouseEventListener<NativeEventTarget<any>>;
    override readonly pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    constructor(protected override coord: EditorCoordinator) {
        super(coord);
    }

    public override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                default: {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    override async focus(toolName: AlignType, transient?: boolean) {
        if (this.isDisabled() || this.isToolDisable(toolName)) return;

        await this.getTool(toolName)?.focus(toolName);
        await super.focus(toolName, transient);
    }

    override async blur(toolName: AlignType, transient?: boolean) {
        if (this.isDisabled() || this.isToolDisable(toolName)) return;

        // When blur AlignTool we must to call super.blur before this.getTool(toolName).blur(toolName) because:
        // 1. supper.blur => Check this.isToolActive (this.getTool(tool).toolState.alignMode == tool) for set _curTool = undeifined
        // 2. this.getTool(toolName).blur(toolName) => Delete this.getTool(tool).toolState.alignMode when blur
        await super.blur(toolName, transient);
        await this.getTool(toolName)?.blur(toolName);
    }

    override isToolActive(tool: AlignType): boolean {
        return this.getTool(tool)?.toolState.alignMode == tool;
    }
}

/**
 *
 * <AUTHOR>
 * <AUTHOR>
 */
export class SelectTool
    extends CommonTool
    implements PointerEventListener<NativeEventTarget<any>>, SupportFeatureHistory
{
    override readonly toolType: CommonToolType = 'select';
    override readonly pointerHandler: PointerEventListener<NativeEventTarget<any>> = this;
    curDocSelection$: BehaviorSubject<SelectContextWithBound[]> = new BehaviorSubject<SelectContextWithBound[]>([]);
    get curDocSelection(): SelectContextWithBound[] {
        return this.curDocSelection$.value;
    }
    readonly individualBoundaryListener?: VEventListener<BoundaryChangeEventData>;
    readonly groupBoundaryListener?: VEventListener<BoundaryChangeEventData>;

    /**
     * When there are multiple selection, a group boundary is created
     */
    groupBoundary = new BehaviorSubject<RectangleObjectBoundary | undefined>(undefined);

    override toolState: SelectToolState;
    childToolbar: AlignToolbar | undefined;

    private processingMultipleSelect: boolean = false;
    private lastTapTime: number = 0; // Timestamp of the last tap event
    private readonly DOUBLE_TAP_THRESHOLD = 300; // Milliseconds threshold for double tap

    lastPointerMoveSub: Subscription | undefined;

    private highlightElement: SelectHitContext[] = [];
    selectedElement: BehaviorSubject<SelectHitContext[]>;

    // Store the start boundary state of so that it can be used when
    // undo
    private startBoundaryState?: Map<EditorType, DocBoundaryInfo>;

    cursor = new BehaviorSubject<Cursor[] | undefined>(undefined); // in normal condition, select tool doesn't specify any cursor

    constructor(
        private _p: EditorCoordinator,
        private selectionFeature?: SelectionFeature,
        private robFeature?: ROBFeature,
        private crdFeature?: DocCRDFeature,
        // allow disabling select document because in some cases, like in word editor, document are only selected programmatically
        private disableSelectDocument?: boolean,
        private disableBoundaryMonitor?: boolean
    ) {
        super();

        if (!this.disableBoundaryMonitor) {
            this.individualBoundaryListener = this.INDIVIDUAL_BOUNDARY_MONITOR();
            this.groupBoundaryListener = this.GROUP_BOUNDARY_MONITOR();
        }

        this.pointerHandling.push(
            // mouse single select when focused
            { event: 'click', button: 0, pointerTypes: pointerTypeMouse },
            // touch multi-select when focused (double tap will be single select)
            { event: 'tap', pointerTypes: pointerTypeDyn, numTouch: 1 },
            // pen multi-select when not focused (double tap will be single select)
            { event: 'tap', pointerTypes: pointerTypePen, numPointer: 1 },

            // mouse multi-select when focused
            { event: 'click', button: 0, keys: ['ctrl'], pointerTypes: pointerTypeMouse },

            { event: 'pointermove', pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', pressedButtons: 0, keys: ['ctrl'], pointerTypes: pointerTypeMouse },

            { event: 'pointermove', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // touch multi select when not focused (double tap will be single select)
            { event: 'tap', pointerTypes: pointerTypeDyn, numTouch: 1, global: true },
            // pen multi select when not focused (double tap will be single select)
            { event: 'tap', pointerTypes: pointerTypePen, numPointer: 1, global: true },
            // mouse select when tool is not focused
            { event: 'click', button: 0, pointerTypes: pointerTypeMouse, global: true },
            // mouse multi-select when tool is not focused
            { event: 'click', button: 0, keys: ['ctrl'], pointerTypes: pointerTypeMouse, global: true },
            // mouse highlight when select tool not focused
            { event: 'pointermove', pressedButtons: 0, pointerTypes: pointerTypeMouse, keys: ['nokey'], global: true },
            // mouse highlight when tool not focused
            {
                event: 'pointermove',
                pressedButtons: 0,
                keys: ['ctrl'],
                pointerTypes: pointerTypeMouse,
                global: true,
            }
        );

        this.toolState = {
            numDocSelected: 0,
        };

        _p.registerCoordEventListener(this.CoordEventListener(this));

        this.selectedElement = new BehaviorSubject<SelectHitContext[]>([]);
    }

    async undo(item: UpdateBoundaryItem): Promise<void> {}

    async redo(item: UpdateBoundaryItem): Promise<void> {}

    override onAttachViewport() {
        if (!this.toolbar.viewport) throw new Error('Viewport is not attached to the toolbar');

        // add select tool to selection feature
        this.selectionFeature?.addSelectTool(this.toolbar.viewport.id, this);

        if (
            !this.disableBoundaryMonitor &&
            this.toolbar.viewport instanceof BaseBoardViewportManager &&
            !this.childToolbar
        ) {
            this.childToolbar = new AlignToolbar(this._p);
            const alignTool = new SelectAlignTool(this, this._p, this.crdFeature);
            alignTool.toolbar = this.childToolbar;

            this.childToolbar.addTool('align-left', alignTool);
            this.childToolbar.addTool('align-right', alignTool);
            this.childToolbar.addTool('align-center', alignTool);
            this.childToolbar.addTool('align-top', alignTool);
            this.childToolbar.addTool('align-bottom', alignTool);
            this.childToolbar.addTool('align-middle', alignTool);

            this.childToolbar.attachViewport(this.toolbar.viewport);
        }
    }

    override onDetachViewport() {
        if (this.childToolbar && this.toolbar.viewport) {
            this.childToolbar.detachViewport(this.toolbar.viewport);
            this.childToolbar.clearAllFocus();
            delete this.childToolbar;
        }
        this.lastPointerMoveSub?.unsubscribe();
    }

    override onFocus(): void {
        if (!this.childToolbar || !this.toolbar || !this.toolbar.viewport) return;

        const isEditMode = this.toolbar.viewport.mode === 'EditMode';
        this.childToolbar.tools.forEach((_tool, type) => {
            if (isEditMode) {
                this.childToolbar!.enableTool(type);
            } else {
                this.childToolbar!.disableTool(type);
            }
        });
    }

    override onBlur(): void {
        if (this.childToolbar?.curTool) this.childToolbar.blur(this.childToolbar.curTool, false);
    }

    override onDisable(): void {
        this.deselectAllDocCtx();
    }

    calculateBoundaryForUpdateFromSelection(ctxToChoose?: SelectContextWithBound): Map<EditorType, DocBoundaryInfo> {
        const state = new Map<EditorType, DocBoundaryInfo>();
        for (const s of this.curDocSelection$.value) {
            if (!s.boundary || (ctxToChoose && ctxToChoose != s)) continue; // skip any selection without a boundary, just in case
            const ctrl = s.doc;
            let l = state.get(s.doc.editor.editorType);
            if (!l) {
                l = { docs: [], boundaries: [] };
                state.set(s.doc.editor.editorType, l);
            }
            l.docs.push({ localId: ctrl.state.id, globalId: ctrl.state.globalId });
            l.boundaries.push(structuredClone(s.boundary.boundary)); // at this point, the selection must have a boundary
        }
        return state;
    }

    synchronizeBoundaryCmd(cmd: FCUpdateDocCmd) {
        const l = cmd.state.getDocsList();
        const bs = cmd.state.getNewboundariesList();

        for (const idx in l) {
            const id = l[idx];
            const b = fcConvertProtoToBoundary(bs[idx]);
            const sel = this.curDocSelection$.value.find(
                v =>
                    (v.doc.editor.id == cmd.meta.channelCode && v.doc.state.id == id.getLocalId()) ||
                    v.doc.state.globalId == id.getGlobalId()
            );
            if (sel) sel.boundary?.updateBoundary(b);
        }
        this.recalculateGroupBoundary();
    }

    INDIVIDUAL_BOUNDARY_MONITOR() {
        const self = this;
        return new (class implements VEventListener<BoundaryChangeEventData> {
            onEvent(eventData: BoundaryChangeEventData): BoundaryChangeEventData | Promise<BoundaryChangeEventData> {
                if (self.toolbar.isDisabled() || self.toolbar.isToolDisable(self.toolType)) return eventData;
                const s = self.curDocSelection$.value.find(v => v.boundary == eventData.source);

                if (!s) return eventData;

                if (!self.startBoundaryState) {
                    // we know only one boundary is moved, and it's the first selection
                    const doc = s.doc;
                    self.startBoundaryState = new Map<EditorType, DocBoundaryInfo>();
                    self.startBoundaryState.set(doc.editor.editorType, {
                        docs: [{ localId: doc.state.id, globalId: doc.state.globalId }],
                        boundaries: [structuredClone(eventData.source.boundary)],
                    });
                }

                const toBeUpdated = self.calculateBoundaryForUpdateFromSelection(s);
                self.crdFeature?.updateDocBoundary(
                    toBeUpdated,
                    self.toolbar.viewport!,
                    eventData.eventType === 'boundary-update-end' ? { startState: self.startBoundaryState } : undefined,
                    eventData.eventType === 'boundary-update-end'
                );
                self.recalculateGroupBoundary();

                if (eventData.eventType === 'boundary-update-end') delete self.startBoundaryState;

                return eventData;
            }
        })();
    }

    GROUP_BOUNDARY_MONITOR() {
        const self = this;
        return new (class implements VEventListener<BoundaryChangeEventData> {
            onEvent(eventData: BoundaryChangeEventData): BoundaryChangeEventData | Promise<BoundaryChangeEventData> {
                if (self.toolbar.isDisabled() || self.toolbar.isToolDisable(self.toolType)) return eventData;

                if (!self.startBoundaryState) {
                    self.startBoundaryState = self.calculateBoundaryForUpdateFromSelection(); // record the current data if doesn't have
                }

                for (const s of self.curDocSelection$.value) {
                    if (s.boundary) s.boundary.copyChange(eventData);
                }

                const toBeUpdated = self.calculateBoundaryForUpdateFromSelection();
                self.crdFeature?.updateDocBoundary(
                    toBeUpdated,
                    self.toolbar.viewport!,
                    eventData.eventType === 'boundary-update-end'
                        ? {
                              startState: self.startBoundaryState,
                          }
                        : undefined,
                    eventData.eventType === 'boundary-update-end'
                );

                if (eventData.eventType === 'boundary-update-end') {
                    delete self.startBoundaryState;
                }

                return eventData;
            }
        })();
    }

    resetBoundary(docId: number, boundary: BoundaryRectangle) {
        if (!this.toolbar.viewport) throw new Error('Tool has not been attached to viewport');
        if (this.disableBoundaryMonitor || this.curDocSelection.length < 1) return;

        const sel = this.curDocSelection.find(v => v.doc.state.id == docId);

        if (sel) sel.boundary?.resetBoundary(boundary);

        this.recalculateGroupBoundary();
    }

    getBoundaryOfCtrl(docCtrl: VDocCtrl): RectangleObjectBoundary | null {
        const boudary = this.curDocSelection.find(v => v.doc.state.id == docCtrl.state.id)?.boundary;
        if (boudary) return boudary;
        return null;
    }

    // calculate group boundary from individual boundaries
    recalculateGroupBoundary() {
        if (!this.toolbar.viewport) throw new Error('Tool has not been attached to viewport');

        if (this.disableBoundaryMonitor) return;

        if (this.curDocSelection$.value.length <= 1) {
            if (!this.groupBoundary.value) return;

            this.robFeature?.releaseROB(this.toolbar.viewport.id, this.groupBoundary.value);

            this.groupBoundary.next(undefined);
        } else {
            let top: number | undefined;
            let left: number | undefined;
            let bottom: number | undefined;
            let right: number | undefined;

            for (const s of this.curDocSelection$.value) {
                const sb = s.boundary!;
                if (!top || top < sb.boundary.start!.y || top < sb.boundary.end!.y) {
                    top = Math.max(sb.boundary.start!.y, sb.boundary.end!.y);
                }

                if (!bottom || bottom > sb.boundary.start!.y || bottom > sb.boundary.end!.y) {
                    bottom = Math.min(sb.boundary.start!.y, sb.boundary.end!.y);
                }

                if (!right || right < sb.boundary.start!.x || right < sb.boundary.end!.x) {
                    right = Math.max(sb.boundary.start!.x, sb.boundary.end!.x);
                }

                if (!left || left > sb.boundary.start!.x || left > sb.boundary.end!.x) {
                    left = Math.min(sb.boundary.start!.x, sb.boundary.end!.x);
                }
            }

            const vm = this.toolbar.viewport as BoardViewportManager;
            const groupMargin = 20 * vm.zoomLevel;

            const newRect = {
                start: {
                    x: left! - groupMargin,
                    y: top! + groupMargin,
                },
                end: {
                    x: right! + groupMargin,
                    y: bottom! - groupMargin,
                },
                width: right! - left! + 2 * groupMargin,
                height: top! - bottom! + 2 * groupMargin,
            };

            if (!this.groupBoundary.value) {
                // initialize group boundary
                this.initializeGroupBoundary(newRect);
            } else {
                this.groupBoundary.value.updateBoundary(newRect);
                this.groupBoundary.next(this.groupBoundary.value);
            }
        }
    }

    initializeGroupBoundary(newRect: BoundaryRectangle) {
        if (!this.toolbar.viewport) {
            throw new Error(
                'The select tool must work on a viewport. The toolbar containing this tool is not yet attached to a viewport.'
            );
        }

        if (!this.robFeature) throw new Error('No boundary provided');

        const newGroupBoundary = this.robFeature.requestROB(this.toolbar.viewport.id, newRect);
        newGroupBoundary.boundaryChange.registerListener(this.groupBoundaryListener!);
        newGroupBoundary.show();
        newGroupBoundary.enablePointerEvent();
        this.groupBoundary.next(newGroupBoundary);
    }

    onEvent(
        eventData: PointerEventData<NativeEventTarget<any>>
    ): PointerEventData<NativeEventTarget<any>> | Promise<PointerEventData<NativeEventTarget<any>>> {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return eventData;

        const vm = this.toolbar.viewport;
        if (!vm) return eventData;

        // --- Handle Inferred Tap Events ---
        if (eventData.eventType === 'tap') {
            const tapEvent = eventData as InferredPointerEvent;
            // Use the first pointer down event for location/keys info
            const firstDownEvent = tapEvent.pointerDowns?.values().next().value as UIPointerEventData<any> | undefined;

            if (!firstDownEvent || !firstDownEvent.source['mouseLocation']) {
                console.warn('Tap event lacks necessary data.');
                return tapEvent;
            }

            if (this.childToolbar?.curTool) return tapEvent; // Don't handle if child tool is active

            const now = Date.now();
            const isDoubleTap = now - this.lastTapTime < this.DOUBLE_TAP_THRESHOLD;
            this.lastTapTime = now; // Update last tap time regardless

            if (isDoubleTap) {
                // Treat double tap as a single select action, ignoring Ctrl
                const before = this.curDocSelection$.value[0]?.doc;
                if (!this.disableSelectDocument) this.handleSelectSingleDoc(vm, firstDownEvent); // Pass UI event
                const after = this.curDocSelection$.value[0]?.doc;
                if (after && after == before) {
                    this.handleSelectElement(vm, firstDownEvent, false); // multiple = false
                }
            } else {
                // Single tap, with Ctrl (multi-select)
                const hitSth = this.handleSelectElement(vm, firstDownEvent, true); // multiple = true
                if (!this.disableSelectDocument && !hitSth) this.handleSelectMultiDoc(vm, firstDownEvent); // Pass UI event
            }

            // Update tool state after handling tap
            this.toolState.numDocSelected = this.curDocSelection$.value.length;
            this.toolbar.update('select', this.toolState);

            // Prevent further processing for handled taps
            tapEvent.continue = false;
            // Note: Inferred events don't have a single nativeEvent to preventDefault on.
            // The underlying pointerup/down events might have already been handled.

            return tapEvent;
        }

        switch (eventData.eventType) {
            case 'click': {
                if (this.childToolbar?.curTool) return eventData;

                // we only check hit if the event comes from a mouse locatable layer
                if (!eventData.source['mouseLocation']) return eventData;

                const vm = this.toolbar.viewport;

                if (!vm) return eventData;

                if (!eventData.getKeys || eventData.getKeys.length == 0) {
                    const before = this.curDocSelection$.value[0]?.doc;
                    if (!this.disableSelectDocument) this.handleSelectSingleDoc(vm, eventData);
                    const after = this.curDocSelection$.value[0]?.doc;
                    if (after && after == before) {
                        this.handleSelectElement(vm, eventData, false);
                    }
                } else if (eventData.getKeys?.[0] == 'ctrl' || eventData.getKeys?.[0] == 'Ctrl') {
                    const selectionChanged = this.handleSelectElement(vm, eventData, true);
                    if (!this.disableSelectDocument && !selectionChanged) this.handleSelectMultiDoc(vm, eventData);
                }

                // update the tool state
                this.toolState.numDocSelected = this.curDocSelection$.value.length;
                this.toolbar.update('select', this.toolState);

                break;
            }
            case 'pointermove': {
                if (!eventData.source['mouseLocation']) return eventData;

                const vm = this.toolbar.viewport;
                if (!vm) return eventData;

                this.onMouseMove(eventData);

                break;
            }
        }
        return eventData;
    }

    /**
     * Handles selection of elements in the viewport
     * @param vm The viewport manager
     * @param eventData Mouse event data
     * @param multiple Whether to allow multiple selection (e.g. with Ctrl key)
     * @returns Boolean indicating if selection was changed (select, deselect, or clear)
     */
    private handleSelectElement(
        vm: ViewportManager,
        eventData: UIPointerEventData<NativeEventTarget<any>>,
        multiple: boolean
    ): boolean {
        // Return early if no document is selected or more than one is selected
        if (this.curDocSelection$.value.length !== 1) return false;

        const focused = this.curDocSelection$.value[0];
        // Get layers for the focused document
        const layers = vm.allLayers().filter(layer => layer.doc?.state.globalId == focused.doc.state.globalId);
        let selectionChanged = false;

        for (const layer of layers) {
            // Skip if layer doesn't support internal selection
            if (!layer.editor?.isSupportFeature(FEATURE_INTERNAL_SELECTION)) continue;

            const supporter = layer.editor.featureSupporter<SupportInternalSelectFeature>(FEATURE_INTERNAL_SELECTION)!;
            const ctx = supporter.checkHitInternal(layer, eventData, multiple);

            if (ctx?.hitDetails) {
                // Check if the hit element is already selected
                const selectedCtx = this.selectedElement.value.find(e => e.hitDetails.hitId === ctx.hitDetails.hitId);
                if (selectedCtx) {
                    // Deselect if already selected
                    supporter.removeSelectedElement(selectedCtx);
                    this.selectedElement.next(
                        this.selectedElement.value.filter(e => e.hitDetails.hitId !== selectedCtx.hitDetails.hitId)
                    );
                    selectionChanged = true;
                } else {
                    // Select new element
                    if (!multiple) {
                        // Clear previous selection if not in multi-select mode
                        supporter.clearSelectedElInDoc(focused.doc);
                        this.selectedElement.next([]);
                    }
                    supporter.selectElement(ctx, multiple);
                    this.selectedElement.next([...this.selectedElement.value, ctx]);
                    selectionChanged = true;
                }
            } else {
                // Even if nothing is hit, still clear if something was selected
                if (ctx && this.selectedElement.value.length > 0) {
                    selectionChanged = true;
                }
                supporter.clearSelectedElInDoc(focused.doc);
                this.selectedElement.next([]);
            }
        }

        return selectionChanged;
    }

    // The caching reflow sync, use to cache hit element and handle mouse move event
    protected mousemoveCachingReflowSync = new CachingReflowSync<
        UIPointerEventData<NativeEventTarget<any>>,
        SelectHitContext[]
    >();
    private onMouseMove(eventData: UIPointerEventData<NativeEventTarget<any>>) {
        if (this.curDocSelection$.value.length == 0 || this.curDocSelection$.value.length > 1) return;
        this.mousemoveCachingReflowSync.handleEvent(eventData, this.handleHighlightElement.bind(this), hitCtx => {
            if (hitCtx && hitCtx.length > 0) {
                eventData.continue = false;
                eventData.nativeEvent.preventDefault();
            }
        });
    }

    private handleHighlightElement(
        eventData: UIPointerEventData<NativeEventTarget<any>>
    ): SelectHitContext[] | undefined {
        const focused = this.curDocSelection$.value[0];
        const vm = this.toolbar.viewport;
        if (!vm) return undefined;

        const layers = vm
            .allLayers()
            .filter(layer => layer.doc && layer.doc.state.globalId == focused.doc.state.globalId);

        for (const layer of layers) {
            if (!layer.editor?.isSupportFeature(FEATURE_INTERNAL_SELECTION)) continue;
            if (focused) {
                const supporter =
                    layer.editor.featureSupporter<SupportInternalSelectFeature>(FEATURE_INTERNAL_SELECTION)!;
                if (supporter.disableJsHighlight === true) continue;

                this.highlightElement.forEach(h => supporter.removeHighlight(h));
                this.highlightElement = [];
                const ctx = supporter.checkHitInternal(layer, eventData, false);
                if (ctx?.hitDetails) {
                    supporter.highlight(ctx);
                    this.highlightElement = [ctx];
                }
            }
        }

        if (this.highlightElement && this.highlightElement.length > 0) {
            if (!this.cursor.value) this.cursor.next(pointerCursor);

            // I have actually processed this event
            eventData.continue = false;
            eventData.nativeEvent.preventDefault();
        } else if (this.cursor.value) this.cursor.next(undefined);

        return this.highlightElement;
    }
    /**
     * Sorts all layers in the viewport manager by their area (width × height) in ascending order.
     * Layers with missing or undefined boundaries are treated as having zero area.
     */
    private sortLayersBySize(vm: ViewportManager) {
        return vm
            .allLayers()
            .slice() // Clone the array to avoid mutating the original
            .sort((a, b) => {
                const boundaryA = a?.state?.getBoundary?.();
                const boundaryB = b?.state?.getBoundary?.();

                const areaA = (boundaryA?.width ?? 0) * (boundaryA?.height ?? 0);
                const areaB = (boundaryB?.width ?? 0) * (boundaryB?.height ?? 0);

                return areaA - areaB;
            });
    }

    handleSelectSingleDoc(vm: ViewportManager, eventData: UIPointerEventData<NativeEventTarget<any>>) {
        let hitSth: boolean = false;

        // TODO: make sure all layers are in order of z-index
        for (const lc of this.sortLayersBySize(vm)) {
            if (lc.editor && lc.editor.isSupportFeature(FEATURE_SELECTION)) {
                const supporter = lc.editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION)!;
                const hitCtx = supporter.checkHit(lc, eventData);

                if (hitCtx) {
                    const selectCtx = supporter.select(hitCtx);
                    if (selectCtx) this.addSelectDocContext(selectCtx);

                    this.recalculateGroupBoundary();
                    this.deselectAllDocCtx(hitCtx);
                    hitSth = true;
                    break;
                }
            }
        }

        // if hit nothing, deselect everything
        if (!hitSth) {
            this.deselectAllDocCtx();
            this.recalculateGroupBoundary();
        }
    }

    /**
     * Deselect all currently selected context, except for the one with the document specified by the input hit context
     * If a hit context is null, deselect everything
     * @param hitCtx
     */
    deselectAllDocCtx(hitCtx?: SelectHitContext, emission: boolean = true) {
        const contexts: SelectContextWithBound[] = [];
        const deselected: SelectContextWithBound[] = [];
        for (const s of this.curDocSelection$.value) {
            if (hitCtx && s.doc == hitCtx.doc) {
                contexts.push(s);
                continue; // if any current selection is the same as the hit ctx
            } else {
                deselected.push(s);
            }
        }

        this.deselectMultipleDocCtx(deselected, contexts, emission);
    }

    deselectDocCtx(selectCtx: SelectContextWithBound, emission: boolean = true) {
        const remaining = this.curDocSelection$.value.filter(s => s != selectCtx);
        this.deselectMultipleDocCtx([selectCtx], remaining, emission);
    }

    deselectMultipleDocCtx(
        multiple: SelectContextWithBound[],
        remaining: SelectContextWithBound[],
        emission: boolean = true
    ) {
        for (const ctx of multiple) {
            ctx.supporter.deselect(ctx);

            this.stopMonitorBoundary(ctx);

            ctx.boundary?.disablePointerEvent();
            ctx.boundary?.hide();
            ctx.boundary?.destroy();
        }
        this.curDocSelection$.next(remaining);

        this.recalculateGroupBoundary();

        // update the tool state
        this.toolState.numDocSelected = this.curDocSelection$.value.length;
        this.toolbar.update('select', this.toolState);

        if (emission) this.triggerMultiDeselectEvent(multiple);
    }

    /**
     * this method is used when editor already deselect and notify selection feature. This is different
     * from deselectDocCtx in that it doesn't call the editor to do the deselection.
     * @param ctx
     */
    removeSelectDocContext(ctx: SelectContext) {
        const selection: SelectContextWithBound[] = [];
        for (const s of this.curDocSelection$.value) {
            if (ctx && s.doc != ctx.doc) {
                selection.push(s);
                continue; // if any current selection is the same as the hit ctx
            } else {
                // we don't call deselect anymore, because this method is called
                // by the select feature to remove the context from the tool
                // it means that the feature supporter (e.g. editors) has already deselect
                // the mentioned context
                this.stopMonitorBoundary(s);

                s.boundary?.disablePointerEvent();
                s.boundary?.hide();
                s.boundary?.destroy();
            }
        }

        this.curDocSelection$.next(selection);
        this.recalculateGroupBoundary();

        // update the tool state
        this.toolState.numDocSelected = this.curDocSelection$.value.length;
        this.toolbar.update('select', this.toolState);
        this.triggerMultiDeselectEvent([ctx]);
    }

    /**
     * This method allows select a certain doc controller without having to deselect and then select. This result only in a single
     * selection event
     * @param editor which the doc should be selected
     * @param vm the viewport which the doc is on
     * @param docCtrl the document ctrl
     */
    selectOnlyDoc(editor: DocumentEditor, vm: ViewportManager, docCtrl: VDocCtrl) {
        if (editor.isSupportFeature(FEATURE_SELECTION)) {
            const supporter = editor.featureSupporter(FEATURE_SELECTION) as SupportSelectFeature;
            // first deselect everything without emission
            this.deselectAllDocCtx(undefined, false);
            supporter.selectDoc(docCtrl, false); // select the doc programmatically
        }
    }

    // this method is used when an object is selected internally
    addSelectDocContext(c: SelectContext, emission: boolean = true) {
        // prevent double selection: same VDocCtrl from the same SupportSelectFeature
        if (this.curDocSelection$.value.find(sel => sel.doc === c.doc && sel.supporter === c.supporter)) return;

        const ctx = this.getCtxWithBound(c);
        if (!ctx) {
            console.error('Unable to add context because there is no context with boundary');
            return;
        }

        this.curDocSelection$.value.push(ctx);
        this.curDocSelection$.next(this.curDocSelection$.value);
        this.monitorBoundary(ctx);

        this.recalculateGroupBoundary();

        // update the tool state
        this.toolState.numDocSelected = this.curDocSelection$.value.length;
        this.toolbar.update('select', this.toolState);

        if (emission) this.triggerMultiSelectEvent([ctx]);
    }

    /**
     * Generate a select context that has a rectangle boundary object
     * @param c original context
     */
    private getCtxWithBound(c: SelectContext): SelectContextWithBound | null {
        if (this.disableBoundaryMonitor || c['boundary']) return c as SelectContextWithBound;
        else {
            if (!c.doc.state.getLayers() || c.doc.state.getLayers().length == 0) return null;
            const layerState = c.doc.state.getLayers()[0];
            if (!layerState.getBoundary)
                throw new Error("Unable to create boundary for document! Layer doesn't support boundary value");
            const boundary = this.robFeature!.requestROB(c.doc.viewport.id, layerState.getBoundary());
            c['boundary'] = boundary;

            boundary.show();
            boundary.enablePointerEvent();

            return c as SelectContextWithBound;
        }
    }

    monitorBoundary(s: SelectContextWithBound) {
        // we need to check because a select context, e.g. of a document in full viewport mode might not have boundary
        if (s.boundary) s.boundary.boundaryChange.registerListener(this.individualBoundaryListener!);
    }

    stopMonitorBoundary(s: SelectContextWithBound) {
        // we need to check because a select context, e.g. of a document in full viewport mode might not have boundary
        if (s.boundary) s.boundary.boundaryChange.unregisterListener(this.individualBoundaryListener!);
    }

    handleSelectSingleDocCtrl(editor: DocumentEditor, docCtrl: VDocCtrl) {
        if (editor.isSupportFeature(FEATURE_SELECTION)) {
            const supporter = editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION)!;
            this.deselectAllDocCtx();
            const ctx = supporter.selectDoc(docCtrl);
            this.addSelectDocContext(ctx);
            this.recalculateGroupBoundary();
        }
    }

    handleSelectMultiDocCtrl(editor: DocumentEditor, docCtrl: VDocCtrl) {
        try {
            this.processingMultipleSelect = true;

            if (editor.isSupportFeature(FEATURE_SELECTION)) {
                const supporter = editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION)!;

                const selecting = this.curDocSelection$.value.find(s => s.doc.state.globalId == docCtrl.state.globalId);
                if (selecting) {
                    this.deselectDocCtx(selecting);
                } else {
                    const ctx = supporter.selectDoc(docCtrl);
                    this.addSelectDocContext(ctx);
                    this.recalculateGroupBoundary();
                }
            }
        } finally {
            this.processingMultipleSelect = false;
        }
    }

    handleSelectMultiDoc(vm: ViewportManager, eventData: UIPointerEventData<NativeEventTarget<any>>) {
        try {
            this.processingMultipleSelect = true;

            for (const lc of this.sortLayersBySize(vm)) {
                if (lc.editor && lc.editor.isSupportFeature(FEATURE_SELECTION)) {
                    const supporter = lc.editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION)!;
                    const hitCtx = supporter.checkHit(lc, eventData, true);

                    if (hitCtx) {
                        // if hit something

                        let hitCurrent: SelectContextWithBound | undefined;

                        // check if it hit any current selection
                        for (const s of this.curDocSelection$.value)
                            if (s.doc == hitCtx.doc) {
                                hitCurrent = s;
                                break;
                            }

                        if (hitCurrent) {
                            // hit current selection, we just need to deselect, that's all
                            this.deselectDocCtx(hitCurrent);
                        } else {
                            // else select the hit
                            const selectCtx = supporter.select(hitCtx, true);
                            this.addSelectDocContext(selectCtx);
                        }

                        this.recalculateGroupBoundary();

                        break;
                    }
                }
            }
        } finally {
            this.processingMultipleSelect = false;
        }
    }

    triggerMultiDeselectEvent(deselected: SelectContext[]) {
        if (deselected.length == 0) return;
        if (deselected.length == 1) {
            const origin: SingleSelectionEvent = {
                eventType: 'doc-deselected',
                source: this,
                state: {
                    selectCtx: deselected[0],
                },
            };
            deselected[0].doc.viewport.triggerContentEvent({
                source: FEATURE_SELECTION,
                vm: deselected[0].doc.viewport,
                originalEvent: origin,
            });
        } else {
            const origin: MultiSelectionEvent = {
                eventType: 'doc-multi-deselected',
                source: this,
                state: {
                    selectCtxs: deselected,
                },
            };
            deselected[0].doc.viewport.triggerContentEvent({
                source: FEATURE_SELECTION,
                vm: deselected[0].doc.viewport,
                originalEvent: origin,
            });
        }
    }

    triggerMultiSelectEvent(selected: SelectContextWithBound[]) {
        if (selected.length == 0) return;
        if (selected.length == 1) {
            const origin: SingleSelectionEvent = {
                eventType: 'doc-selected',
                source: this,
                state: {
                    selectCtx: selected[0],
                },
            };
            selected[0].doc.viewport.triggerContentEvent({
                source: FEATURE_SELECTION,
                vm: selected[0].doc.viewport,
                originalEvent: origin,
            });
        } else {
            const origin: MultiSelectionEvent = {
                eventType: 'doc-multi-selected',
                source: this,
                state: {
                    selectCtxs: selected,
                },
            };
            selected[0].doc.viewport.triggerContentEvent({
                source: FEATURE_SELECTION,
                vm: selected[0].doc.viewport,
                originalEvent: origin,
            });
        }
    }

    private CoordEventListener(tool: SelectTool): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            onEvent(event: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                if (tool.toolbar.isDisabled() || tool.toolbar.isToolDisable(tool.toolType)) return event;

                if (!tool.toolbar.viewport || event.state?.vmId != tool.toolbar.viewport.id) return event;

                switch (event.eventType) {
                    case 'editor-focus': {
                        // deselect all documents except document that belong to current focused editor
                        const es = event.state as EditorBlurCES;
                        const contexts: SelectContextWithBound[] = [];
                        const deselected: SelectContextWithBound[] = [];

                        for (const c of tool.curDocSelection$.value) {
                            if (c.doc.editor == es.editor) {
                                contexts.push(c);
                            } else {
                                deselected.push(c);
                            }
                        }
                        tool.deselectMultipleDocCtx(deselected, contexts);
                        break;
                    }

                    case 'editor-blur': {
                        if (tool.processingMultipleSelect) break; // if editor-blur is caused by multiple selection, don't deselect any thing
                        // deselect all document of editor is being blured
                        const es = event.state as EditorBlurCES;
                        const contexts: SelectContextWithBound[] = [];
                        const deselected: SelectContextWithBound[] = [];

                        for (const c of tool.curDocSelection$.value) {
                            if (c.doc.editor == es.editor) {
                                deselected.push(c);
                            } else {
                                contexts.push(c);
                            }
                        }

                        tool.deselectMultipleDocCtx(deselected, contexts);
                        break;
                    }
                    default:
                        break;
                }

                return event;
            }
        })();
    }
}

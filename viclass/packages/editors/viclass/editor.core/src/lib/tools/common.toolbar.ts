import { CommonTool, CommonToolType } from '.';
import {
    <PERSON>E<PERSON>,
    CoordinatorEventType,
    DefaultToolEventData,
    EditorCoordinator,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventListener,
    NativeEventTarget,
    PointerEventListener,
    VEventListener,
    ViewportMode,
} from '../api';
import { DefaultToolBar } from '../default.toolbar';
import { keyboardHandlingKey } from '../util';

/**
 * A local toolbar used on a specific viewport it contains common local tools for zoom
 * This toolbar needs to know which viewport it belongs to
 * <AUTHOR>
 * <AUTHOR>
 */
export class CommonToolbar extends DefaultToolBar<CommonToolType, CommonTool> {
    private commonToolShortcut: string[][] = [['h'], ['H'], ['g'], ['G']];

    readonly keyboardHandler?: KeyboardEventListener<NativeEventTarget<any>>;
    readonly mouseHandler?: MouseEventListener<NativeEventTarget<any>>;
    readonly pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    constructor(protected override coord: EditorCoordinator) {
        super(coord);

        // Create keyboard handling from shortcuts
        const shortcuts: Map<string, string[]> = new Map();

        this.commonToolShortcut.map(k => shortcuts.set(keyboardHandlingKey('keydown', k), k));

        shortcuts.forEach((k, _) =>
            this.keyboardHandling.push(
                new (class extends KeyboardHandlingItem {
                    override event: string = 'keydown';
                })(k)
            )
        );

        this.keyboardHandler = new this._keyboardHandler(this);
        this.keyboardHandling.push(
            new (class extends KeyboardHandlingItem {
                override global = false;
            })(['esc'])
        );

        this.mouseHandler = undefined;
    }

    public override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    this.enableTool(type);
                    break;
                }
                case 'ViewMode': {
                    this.enableTool(type);
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private _p: CommonToolbar) {}

        onEvent(event: KeyboardEventData<NativeEventTarget<any>>): KeyboardEventData<NativeEventTarget<any>> {
            if (this._p.isDisabled() || event.nativeEvent.repeat) return event;

            if (['h', 'H'].includes(event.nativeEvent.key)) {
                if (!this._p.isToolActive('pan')) this._p.focus('pan');
                else this._p.blur('pan');

                event.nativeEvent.preventDefault();
                event.continue = false;
            } else if (['g', 'G'].includes(event.nativeEvent.key)) {
                if (!this._p.isToolActive('select')) this._p.focus('select');
                else this._p.blur('select');

                event.nativeEvent.preventDefault();
                event.continue = false;
            } else if (event.nativeEvent.key === 'Escape' && this._p.curTool) {
                this._p.blur(this._p.curTool);
            }

            return event;
        }
    };
}

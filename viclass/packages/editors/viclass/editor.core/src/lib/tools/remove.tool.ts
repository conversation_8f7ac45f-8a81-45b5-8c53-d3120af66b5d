import { ReplaySubject } from 'rxjs';
import {
    DocumentEditor,
    DocumentId,
    EditorCoordinator,
    EditorType,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    NativeEventTarget,
    VDoc,
    ViewportId,
} from '../api';
import { DocCRDFeature, RemoveDocInfo } from '../common';
import { AwarenessFeature, buildLoadingAwarenessCmdOption, sendNotiMessage } from './awareness.feature';
import { CommonTool, CommonToolType } from './common.tool';
import {
    ContextMenuEvent,
    ContextMenuFeature,
    ContextMenuListenerFilter,
    FEATURE_CONTEXTMENU,
    ListMenu,
    SupportContextMenuFeature,
} from './contextmenu.feature';
import { SelectContext } from './select.tool';
import { SelectionFeature } from './selection.feature';

export const FEATURE_REMOVE = 'common_REMOVE_feature';

export declare type RemoveType = 'documents' | 'element';

const SUPPORTED_EDITORS = new Set<EditorType>([
    'GeometryEditor',
    'FreeDrawingEditor',
    'WordEditor',
    'MathEditor',
    'MathGraphEditor',
]);

export interface SupportRemoveFeature {
    /**
     * return false meaning remove tool take care for document, return true meaning Supporter handle itself
     * @param vpId the viewport id
     * @param isCutting if true, this is coming from cutting operation
     */
    remove(vpId: ViewportId, isCutting?: boolean): Promise<boolean>;
}

export class RemoveTool extends CommonTool implements SupportContextMenuFeature {
    readonly toolType: CommonToolType = 'remove';
    private _allowDelHotkey: boolean = true;
    private _enableDelDoc: boolean = true;
    private _delDocConfirmationApprovals?: (removeDocs: RemoveDocInfo[]) => Promise<Boolean>;
    private _removeDocMonitor?: (monitorObs: Map<DocumentId, ReplaySubject<void>>) => Promise<void>;
    private readonly _keyboardHandlingDelKeys = [
        new (class extends KeyboardHandlingItem {
            override event = 'keyup';
            override global = true;
        })(['Delete']),

        new (class extends KeyboardHandlingItem {
            override event = 'keydown';
            override global = true;
        })(['Delete']),
    ];

    get isEnableDelDoc() {
        return this._enableDelDoc;
    }

    constructor(
        private coord: EditorCoordinator,
        private crdFeature: DocCRDFeature,
        private selectionFeature: SelectionFeature,
        private contextMenuFeature?: ContextMenuFeature,
        private awarenessFeature?: AwarenessFeature
    ) {
        super();
    }

    /**
     * Set allow Delete hotkey or not. As in some context like Word, the Delete button is for remove text
     * instead of delete the whole word document.
     */
    allowDelHotkey(isAllow: boolean = false): this {
        this._allowDelHotkey = isAllow;
        if (isAllow) {
            if (!this.keyboardHandling.find(k => this._keyboardHandlingDelKeys.includes(k)))
                this.keyboardHandling.push(...this._keyboardHandlingDelKeys);
        } else {
            for (const keyboardHandlingDelKey of this._keyboardHandlingDelKeys) {
                this.keyboardHandling.splice(this.keyboardHandling.indexOf(keyboardHandlingDelKey), 1);
            }
        }
        return this;
    }

    /**
     * Set enable delete document or just allow to delete the child element
     */
    enableDelDoc(isEnable: boolean = true): this {
        this._enableDelDoc = isEnable;
        return this;
    }

    /**
     * Register a function to confirm delete document, which will be triggered from a confirmation UI
     */
    attachRemoveDocApprovals(approval: (removeDocs: RemoveDocInfo[]) => Promise<Boolean>) {
        this._delDocConfirmationApprovals = approval;
        return this;
    }

    attachRemoveDocMonitor(monitor: (monitorObs: Map<DocumentId, ReplaySubject<void>>) => Promise<void>) {
        this._removeDocMonitor = monitor;
        return this;
    }

    override onAttachViewport() {
        const contextMenuListenerKey: ContextMenuListenerFilter = {
            anyEditorType: true,
        };

        this.contextMenuFeature?.registerContextMenuEventListener(this.toolbar.viewport!.id, {
            filter: contextMenuListenerKey,
            handler: this.contextMenuFeatureListener,
        });
    }

    override onDetachViewport() {
        this.contextMenuFeature?.unRegisterContextMenuEventListener(
            this.toolbar.viewport!.id,
            this.contextMenuFeatureListener
        );
    }

    /**
     * Listen to the context menu event to add the delete option
     */
    contextMenuFeatureListener = async (eventData: ContextMenuEvent) => {
        switch (eventData.eventType) {
            case 'contextmenu': {
                const result: ListMenu[] = [];

                const selectedDocs = eventData.source.filter(e => SUPPORTED_EDITORS.has(e.doc.editor.editorType));
                const vm = eventData.state?.getViewportManager(eventData.viewportId);
                const canDelete = vm && vm.mode === 'EditMode' && selectedDocs.length > 0;
                result.push({
                    name: 'Xóa',
                    data: undefined,
                    onclick: (): Promise<any> => this.remove(eventData.viewportId),
                    disabled: !canDelete,
                    hotkeys: this._allowDelHotkey ? ['Delete'] : [],
                });

                return result;
            }
            default:
                break;
        }

        return [];
    };

    /**
     * @inheritdoc
     */
    override isSupportFeature(featureKey: string): boolean {
        return [FEATURE_CONTEXTMENU].includes(featureKey);
    }

    /**
     * @inheritdoc
     */
    override featureSupporter<T>(featureKey: string): T | undefined {
        if ([FEATURE_CONTEXTMENU].includes(featureKey)) {
            return this as unknown as T;
        }
        return undefined;
    }

    /**
     * Trigger remove feature on the current context.
     * It can be remove an element in side the document, or remove the currently selected documents
     */
    async remove(viewportId: ViewportId, history = true, isCutting = false): Promise<void> {
        const vm = this.coord.getViewportManager(viewportId);
        if (!vm) return;

        const selectionCtxs: SelectContext[] = this.selectionFeature
            .getCurrentSelections(viewportId)
            .filter(e => SUPPORTED_EDITORS.has(e.doc.editor.editorType));

        if (selectionCtxs.length < 1) return;

        if (selectionCtxs.length === 1) {
            const ctx = selectionCtxs[0];
            if (ctx.doc.editor.isSupportFeature(FEATURE_REMOVE)) {
                const supporter: SupportRemoveFeature = ctx.doc.editor.featureSupporter(FEATURE_REMOVE)!!;
                const rs = await supporter.remove(viewportId, isCutting);
                if (rs) return;
            }
        }

        if (!this._enableDelDoc) return;

        const toBeRemovedDocs: RemoveDocInfo[] = [];
        const toBeRemoved = new Map<EditorType, RemoveDocInfo[]>();
        for (const ctx of selectionCtxs) {
            let removeInfo = toBeRemoved.get(ctx.doc.editor.editorType);

            if (!removeInfo) {
                removeInfo = [];
                toBeRemoved.set(ctx.doc.editor.editorType, removeInfo);
            }

            const docInfo: RemoveDocInfo = {
                vmId: viewportId,
                localId: ctx.doc.state.id,
                globalId: ctx.doc.state.globalId,
                editor: ctx.doc.editor,
            };
            removeInfo.push(docInfo);
            toBeRemovedDocs.push(docInfo);
        }

        if (!toBeRemoved.size) return;

        if (this._delDocConfirmationApprovals && !isCutting) {
            // no need to confirm if this is a cutting operation
            if (!(await this._delDocConfirmationApprovals(toBeRemovedDocs))) return;
        }

        const processRemove = async () => {
            const vm = this.coord.getViewportManager(viewportId);
            const obs = new ReplaySubject();
            const monitor = new Map();
            toBeRemovedDocs.map(e => monitor.set(e.globalId, obs));
            this._removeDocMonitor?.(monitor);
            await this.crdFeature.removeDocument(vm, toBeRemoved, history);
            monitor.forEach(o => {
                o.next();
                o.complete();
            });
            if (!isCutting) sendNotiMessage(this.awarenessFeature!, viewportId, 'Xóa tài liệu thành công', 'success');
        };

        if (this.awarenessFeature && !isCutting) {
            this.awarenessFeature.useAwareness(
                viewportId,
                'Đang xóa',
                {
                    ...buildLoadingAwarenessCmdOption('awareness-remove'),
                    startAfterSeconds: 0,
                },
                processRemove
            );
        } else {
            await processRemove();
        }
    }

    /**
     * Remove a document from the viewport
     */
    async removeDoc(vpId: ViewportId, editor: DocumentEditor, doc: VDoc, history = true): Promise<void> {
        const removeDoc: RemoveDocInfo = {
            vmId: vpId,
            localId: doc.id,
            globalId: doc.globalId,
            editor: editor,
        };
        const vm = this.coord.getViewportManager(vpId);
        if (!vm || (this._delDocConfirmationApprovals && !(await this._delDocConfirmationApprovals([removeDoc]))))
            return;
        const toBeRemoved = new Map<EditorType, RemoveDocInfo[]>();
        toBeRemoved.set(editor.editorType, [removeDoc]);

        const monitor = new Map();
        monitor.set(removeDoc.globalId, new ReplaySubject(1));
        this._removeDocMonitor?.(monitor);
        await this.crdFeature.removeDocument(vm, toBeRemoved, history);
        monitor.forEach(o => {
            o.next();
            o.complete();
        });
    }

    /**
     * Listen and handle keyboard event of the Delete key
     */
    override readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public _p: RemoveTool) {}

        async onEvent(
            event: KeyboardEventData<NativeEventTarget<any>>
        ): Promise<KeyboardEventData<NativeEventTarget<any>>> {
            const vm = event.viewport;
            if (
                this._p.toolbar.isDisabled() ||
                this._p.toolbar.isToolDisable(this._p.toolType) ||
                event.nativeEvent.repeat ||
                !vm ||
                vm.mode != 'EditMode'
            )
                return event;

            // Handle delete hotkey
            if (event.nativeEvent.key === 'Delete' && this._p._allowDelHotkey) {
                const selectionCtxs = this._p.selectionFeature.getCurrentSelections(vm.id);
                if (selectionCtxs.length > 0) {
                    // stop next handler, cause some editor is handle delete key by them self
                    event.continue = false;
                    event.nativeEvent.preventDefault();
                    event.nativeEvent.stopPropagation();

                    // handle remove for keydown only, just ignore keyup
                    if (event.eventType === 'keydown') {
                        await this._p.remove(vm.id);
                    }
                }
            }

            return event;
        }
    })(this);
}

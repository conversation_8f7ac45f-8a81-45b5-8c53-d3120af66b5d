import { BehaviorSubject, combineLatest, firstValueFrom, map, mergeMap, of, Subscription, switchMap } from 'rxjs';
import {
    Cursor,
    defaultCursor,
    NativeEventTarget,
    PointerEventData, // Changed
    PointerEventListener, // Changed
    Position,
    UIPointerEventData, // Added
    ViewportId,
    ViewportManager,
} from '../api';
import { CursorManager } from './cursor.manager';

export const cmByVpId = new Map<ViewportId, CursorMonitor>();

const SUPPORTED_POINTER_TYPES = ['mouse', 'pen'];

/**
 * Logic to determine which cursor to be display is evaluated in following order:
 * - If there is some handlers processing the pointer event, use the cursor of the last handler
 * - If there is no handler processed the pointer event or all the handlers processed DON'T have any
 * cursor, then use active tool cursor
 * - If there is no active tool cursor then use the default cursor
 *
 * Cursor Monitors relate to each other base on its viewport relationship
 *
 * This monitor specifically tracks the *mouse* or *pen* cursor.
 *
 * <AUTHOR>
 */
export class CursorMonitor {
    readonly parent?: CursorMonitor;
    readonly positionSink$: BehaviorSubject<Position>;
    readonly cursorsSink$: BehaviorSubject<Cursor[]>;

    // record the last active handler
    private _processingHandler$ = new BehaviorSubject<PointerEventListener<any> | undefined>(undefined); // Changed type
    readonly processingHandler$ = this._processingHandler$.asObservable();

    private _activeTool$ = new BehaviorSubject<PointerEventListener<any> | undefined>(undefined); // Changed type
    readonly activeTool$ = this._activeTool$.asObservable();

    private _subscriptions: Subscription[] = [];

    private _active: boolean = true;
    private _cursorOut: boolean = true;

    get renderer() {
        return this.cm.renderer.get(this.localKey);
    }

    constructor(
        private readonly vm: ViewportManager,
        public readonly cm: CursorManager,
        public readonly localKey: string,
        private readonly backupCursor: Cursor[] = defaultCursor
    ) {
        cmByVpId.set(vm.id, this);

        const parentVm = vm.parentViewport;
        if (parentVm) {
            const pvid = parentVm.id;
            this.parent = cmByVpId.get(pvid);
            if (!this.parent) throw new Error("Cursor monitor of the parent viewport doesn't exist");
        }

        if (this.parent) {
            this.positionSink$ = this.parent.positionSink$;
            this.cursorsSink$ = this.parent.cursorsSink$;
        } else {
            this.positionSink$ = new BehaviorSubject<Position>({ x: 0, y: 0 });
            this.cursorsSink$ = new BehaviorSubject<Cursor[]>([]);
        }

        this.start();
    }

    start() {
        // Register as a Pointer interceptor
        this.vm.eventManager.registerPostPointerInterceptor(this.pointerHandler); // Changed

        this._subscriptions.push(
            combineLatest([this.processingHandler$, this.activeTool$])
                .pipe(
                    switchMap(([processingHandler, activeTool]) => {
                        // Logic remains the same, just operates on PointerEventListeners
                        if (processingHandler?.cursor)
                            return processingHandler.cursor.pipe(
                                mergeMap(c => {
                                    if (!c || c.length == 0) {
                                        if (activeTool?.cursor) return activeTool.cursor;
                                        return of([]);
                                    } else return of(c);
                                })
                            );
                        else if (activeTool?.cursor) return activeTool.cursor;
                        else return of([]);
                    }),
                    map(c => {
                        if (!c || c.length == 0) return [];
                        else return c;
                    })
                )
                .subscribe(c => {
                    if (!c || c.length == 0) c = this.backupCursor;
                    this.cursorsSink$.next(c);
                })
        );

        this.setupRootElMonitor();
        // init the cursor monitor with the first position
        this.cm.updatePosition(this.localKey, this.positionSink$.value);
        // init cursor sink subscription
        this._subscriptions.push(
            this.cursorsSink$.subscribe(cursors => {
                this.cm.updateCursor(this.localKey, cursors);
            })
        );
    }

    stop() {
        if (this._active) {
            this._active = false;
            this.setParentActive(true); // transfer the active state to parent on stop
        }
        this._processingHandler$.next(undefined);
        this._activeTool$.next(undefined);
        this._subscriptions.forEach(s => s.unsubscribe());
        this._subscriptions.length = 0;
        this.teardownRootElMonitor();
        // Unregister as a Pointer interceptor
        this.vm.eventManager.unregisterPostPointerInterceptor(this.pointerHandler); // Changed
    }

    private cursorOut = (event: PointerEvent) => {
        // Changed event type
        // Only react if the pointer leaving is the mouse/pen
        if (!SUPPORTED_POINTER_TYPES.includes(event.pointerType)) {
            return; // Ignore non-mouse/pen events
        }

        if (this._cursorOut) return;

        this._cursorOut = true;
        this.refreshRenderState();

        // if my cursor is out, I let my parent determine activity
        this.setParentActive(true);
    };

    private cursorIn = (event: PointerEvent) => {
        // Changed event type
        // Only react to the mouse/pen pointer entering
        if (!SUPPORTED_POINTER_TYPES.includes(event.pointerType)) {
            return; // Ignore non-mouse/pen entries
        }

        if (!this._cursorOut) return;

        this._cursorOut = false;

        // Listen for the viewport's pointermove event before the pointer handler processes it
        // Only add this listener if the cursor is actually in (prevents multiple adds)
        if (!this.isPreHandleViewportPointerEvent) {
            this.vm.rootEl.addEventListener('pointermove', this.preHandleViewportPointerEvent); // Changed event name
            this.isPreHandleViewportPointerEvent = true;
        }

        this.refreshRenderState();

        // if my cursor is in, my parent should not be active anymore
        this.setParentActive(false);
    };

    private isPreHandleViewportPointerEvent: boolean = false; // Renamed
    // Renamed method and changed event type
    private preHandleViewportPointerEvent = (nativeEvent: Event): void => {
        const pointerEvent = nativeEvent as PointerEvent;
        // Only update position for mouse/pen events in this pre-handler
        if (SUPPORTED_POINTER_TYPES.includes(pointerEvent.pointerType)) {
            const position = { x: pointerEvent.clientX, y: pointerEvent.clientY };
            this.positionSink$.next(position);
            this.cm.updatePosition(this.localKey, position);
            this.setParentActive(false);
        } else {
            // If a non-mouse/pen event occurs while this listener is active, remove the listener
            // as we are only interested in the mouse/pen position.
            this.vm.rootEl.removeEventListener('pointermove', this.preHandleViewportPointerEvent);
            this.isPreHandleViewportPointerEvent = false;
        }
    };

    // Initial check if pointer is already inside (using pointermove)
    private pointermove = (event: PointerEvent) => {
        // Changed event type
        // Check if it's a mouse/pen pointer before triggering cursorIn
        if (SUPPORTED_POINTER_TYPES.includes(event.pointerType)) {
            this.cursorIn(event); // Pass the event
            // Remove listener once the initial state is determined for the mouse/pen
            this.vm.rootEl.removeEventListener('pointermove', this.pointermove as any);
        }
        // Keep listening if it wasn't the mouse/pen, in case the mouse/pen enters later
    };

    // Set up listening to the viewport element using pointer events
    private setupRootElMonitor() {
        // Use pointer events for enter/leave
        this.vm.rootEl.addEventListener('pointerout', this.cursorOut as any);
        this.vm.rootEl.addEventListener('pointerleave', this.cursorOut as any);
        this.vm.rootEl.addEventListener('pointerover', this.cursorIn as any);
        this.vm.rootEl.addEventListener('pointerenter', this.cursorIn as any); // Added for completeness

        this.vm.rootEl.addEventListener('pointermove', this.pointermove as any);
    }

    // remove the pointer event listeners
    private teardownRootElMonitor() {
        this.vm.rootEl.removeEventListener('pointerout', this.cursorOut as any);
        this.vm.rootEl.removeEventListener('pointerleave', this.cursorOut as any);
        this.vm.rootEl.removeEventListener('pointerover', this.cursorIn as any);
        this.vm.rootEl.removeEventListener('pointerenter', this.cursorIn as any); // Added for completeness
        // Ensure pre-handler is removed if it was active
        if (this.isPreHandleViewportPointerEvent) {
            this.vm.rootEl.removeEventListener('pointermove', this.preHandleViewportPointerEvent);
            this.isPreHandleViewportPointerEvent = false;
        }

        this.vm.rootEl.removeEventListener('pointermove', this.pointermove as any);
    }

    /**
     *
     * @param h List of handlers that processed the event
     * @param reallyProcessed specified if there is an actual handler processed it (event.continue === false).
     * This determines the priority of selecting cursor.
     *
     * If reallyProcessed = true, then the last handler is the one whose cursor should be selected because it has actually
     * processed the event and set the handler to true
     *
     * If reallyProcessed = false, then the first one should be selected because it is the first in the handler stack to process
     */
    private updateProcessingHandler(h: PointerEventListener<any>[], reallyProcessed: boolean) {
        if (h.length == 0) {
            if (this._processingHandler$.value) this._processingHandler$.next(undefined);
        } else {
            if (reallyProcessed) {
                if (h[h.length - 1] != this._processingHandler$.value) {
                    this._processingHandler$.next(h[h.length - 1]);
                }
            } else {
                if (h[0] != this._processingHandler$.value) {
                    this._processingHandler$.next(h[0]);
                }
            }
        }
    }

    // update the pointer handler list of the current tool stack
    updateToolStack(h: PointerEventListener<any>[]) {
        // Changed type
        if (h.length == 0) {
            if (this._activeTool$.value) this._activeTool$.next(undefined);
        } else if (h[h.length - 1] != this._activeTool$.value) this._activeTool$.next(h[h.length - 1]);
    }

    // Changed from mouseHandler to pointerHandler
    private readonly pointerHandler = new (class implements PointerEventListener<NativeEventTarget<any>> {
        // Changed type
        constructor(readonly c: CursorMonitor) {}

        onEvent(
            event: PointerEventData<NativeEventTarget<any>> // Changed type
        ): PointerEventData<NativeEventTarget<any>> | Promise<PointerEventData<NativeEventTarget<any>>> {
            // Changed type
            if (!this.c.active) return event;

            // Only process UI Pointer Events with nativeEvent details
            if (!('nativeEvent' in event)) {
                return event;
            }
            const uiEvent = event as UIPointerEventData<NativeEventTarget<any>>;

            // --- MOUSE ONLY LOGIC ---
            // Only update cursor position and activity based on mouse/pen movements
            if (SUPPORTED_POINTER_TYPES.includes(uiEvent.nativeEvent.pointerType)) {
                // if the pre handle pointer event is active, remove it
                if (this.c.isPreHandleViewportPointerEvent) {
                    this.c.vm.rootEl.removeEventListener('pointermove', this.c.preHandleViewportPointerEvent); // Changed event name
                    this.c.isPreHandleViewportPointerEvent = false;
                }

                const position = { x: uiEvent.nativeEvent.clientX, y: uiEvent.nativeEvent.clientY };
                this.c.positionSink$.next(position);
                this.c.cm.updatePosition(this.c.localKey, position);
                this.c.setParentActive(false); // Indicate this monitor is handling the active cursor
            } else {
                // If it's not a mouse/pen event, don't update position or parent activity.
                // Allow the event to pass through for other potential handlers.
                return uiEvent;
            }
            // --- END MOUSE ONLY LOGIC ---

            // Update processing handler based on who handled the event (applies to mouse/pen events processed above)
            if (uiEvent.processedHandlers) {
                this.c.updateProcessingHandler(
                    uiEvent.processedHandlers as PointerEventListener<any>[],
                    !uiEvent.continue
                ); // Changed type
            }

            return uiEvent; // Return the (potentially modified) event
        }
    })(this);

    setParentActive(v: boolean) {
        if (this.parent) this.parent.active = v;
    }

    set active(v: boolean) {
        this._active = v;
        this.refreshRenderState();
    }

    get active(): boolean {
        return this._active;
    }

    // determine if the cursor should be shown or not
    private refreshRenderState() {
        if (this.active && !this._cursorOut) {
            this.cm.updatePosition(this.localKey, this.positionSink$.value);
            this.cm.showCursor(this.localKey);
        } else {
            this.cm.hideCursor(this.localKey);
        }
    }

    destroy() {
        // Ensure renderer is cleaned up before stopping listeners
        this.cm.destroy(); // Assuming CursorManager handles its own renderer cleanup correctly
        this.stop();
        cmByVpId.delete(this.vm.id); // Remove from global map
    }
}

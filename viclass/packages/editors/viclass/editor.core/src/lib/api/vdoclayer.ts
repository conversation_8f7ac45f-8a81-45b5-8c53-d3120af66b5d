import { DefaultEventEmitter } from '../default.event.source';
import { BoundaryRectangle, Position, ScreenPosition } from './common.structure';
import { DocLocalId, DocumentId, IdAble } from './data.model';
import { DocumentEditor, DocumentViewMode } from './editor';
import {
    DefaultMouseEventData,
    DefaultPointerEventData,
    KeyboardEventData,
    MouseEventData,
    NativeEventTarget,
    UIPointerEventData,
} from './events';
import { VDocCtrl } from './vdoc';
import { ViewportManager } from './viewport';

/**
 * Data of a document layer
 */
export interface VDocLayer extends IdAble {
    getBoundary?(): BoundaryRectangle;
    getZindex?(): number;
}

/**
 *
 */
export interface VDocLayerCtrl extends NativeEventTarget<VDocLayerCtrl> {
    /**
     * Retrieve the document this Layer belongs too. A layer might not belong to any document
     */
    doc?: VDocCtrl;

    /**
     * A layer might have a state set to it by the document editor
     */
    state?: VDocLayer;

    /**
     * The editor that requested this layer. If editor is not set, this must be a separation layer.
     */
    editor?: DocumentEditor;

    /**
     * The layer might call renderer to draw the content on the layer as it see needed
     */
    renderer?: LayerRenderer<any, any>;

    renderingRoot?: HTMLElement | SVGElement;

    /**
     * Each layer has a native element, this is the attachment point provided by the viewport
     * and the layer will append its content into this attachment point.
     */
    nativeEl: HTMLElement | SVGElement;

    /**
     * While zindex tell the order of the layer, a layer can be floated. Which means it emerges above other layers.
     * When there are multiple floating layers, the order of floating layers is decided by the order of float action being call
     *
     * This field tells if the layer is floating or not
     */
    isFloating: boolean;

    /**
     * The order of layer inside the viewport. Layer with higher zindex is on top, and lower zindex is below
     */
    zindex: number;

    /**
     * The boundary of the layer in the whiteboard coordinate
     */
    // TODO: remove this, a layer is mainly for attaching content, and it is not the concern of the editor to know the
    // boundary of the layer. This might be better represented as something internal to the layer.
    // boundary: BoundaryRectangle

    /**
     * The viewport manager which contains the native element of this layer
     */
    viewport: ViewportManager;

    /**
     * Convert from whiteboard position to the local element position
     * @param pos
     */
    // TODO: remove this, a layer is really just a way of attaching content to the viewport
    // it mainly provides the root element as the place holder. Layer is created by viewport
    // some kind of viewport & layer combination might not support local position, e.g when
    // an inline layer is created, the positioning of the layer within the viewport is
    // managed by html laying out

    // ------------------ localPos(pos: Position): ScreenPosition

    onRemoved?: () => void;

    /**
     * Attach a renderer to the layer. This renderer is used to render the content of the layer when
     * the viewport thinks it is necessary
     * @param renderer
     */
    attachRenderer<V extends ViewportManager, L extends BaseLayerCtrl>(renderer: LayerRenderer<V, L>);

    /**
     * The layout shifts (ex: from re-ordering the layers) can cause problems like reset the scroll state.
     * Use these hook to handle the effect of layout shift
     */
    beforeLayoutShift?: () => void;
    afterLayoutShift?: () => void;
}

export abstract class BaseLayerCtrl implements VDocLayerCtrl {
    doc?: VDocCtrl;
    state?: VDocLayer;
    editor?: DocumentEditor;
    abstract nativeEl: HTMLElement | SVGElement;
    isFloating: boolean = false;
    renderer?: LayerRenderer<any, any>;

    beforeLayoutShift?: () => void;
    afterLayoutShift?: () => void;

    /**
     * Initially, the layer should have zindex = -1, which means it has not been attached to the viewport
     */
    zindex: number = -1;

    abstract viewport: ViewportManager;

    nativeMouseEventEmitter?: DefaultEventEmitter<MouseEventData<VDocLayerCtrl>>;
    nativeKeyboardEventEmitter?: DefaultEventEmitter<KeyboardEventData<VDocLayerCtrl>>;
    nativePointerEventEmitter?: DefaultEventEmitter<UIPointerEventData<VDocLayerCtrl>>;

    get onRemoved() {
        return () => {};
    }

    attachRenderer<V extends ViewportManager, L extends BaseLayerCtrl>(renderer: LayerRenderer<V, L>) {
        this.renderer = renderer;
        if (this.viewport) setTimeout(() => renderer(this.viewport as V, this as unknown as L));
    }

    removeRenderer() {
        delete this.renderer;
    }

    get renderingRoot(): HTMLElement | SVGElement {
        return this.nativeEl;
    }

    protected mouseHandler = (event: Event) => {
        if (event.type == 'mouseup' || event.type == 'mousedown') {
            // NO preventDefault() because some event like selecting text can be affected
            event.stopPropagation();
        } else {
            const data = new DefaultMouseEventData<VDocLayerCtrl>(this.viewport, this, event as MouseEvent);
            this.nativeMouseEventEmitter?.emit(data);
        }
    };

    protected pointerHandler = (event: Event) => {
        const data = new DefaultPointerEventData<VDocLayerCtrl>(this.viewport, this, event as PointerEvent);
        this.nativePointerEventEmitter?.emit(data);
    };

    protected touchHandler = (event: Event) => {
        // disable touch event because already handle pointer event
        // NO preventDefault() because some event like selecting text can be affected
        event.stopPropagation();
    };

    protected registerMouseEventOnElement(...el: (HTMLElement | SVGElement)[]) {
        el.forEach(e => {
            Object.assign(e.style, { 'pointer-events': 'all' });
            e.addEventListener('wheel', this.mouseHandler);
            e.addEventListener('mousewheel', this.mouseHandler);
            e.addEventListener('contextmenu', this.mouseHandler);
        });
    }

    protected unregisterMouseEventsOnElement(...el: (HTMLElement | SVGElement)[]) {
        el.forEach((e: HTMLElement | SVGElement) => {
            Object.assign(e.style, { 'pointer-events': 'none' });
            e.removeEventListener('wheel', this.mouseHandler);
            e.removeEventListener('mousewheel', this.mouseHandler);
            e.removeEventListener('contextmenu', this.mouseHandler);
        });
    }

    protected registerPointerEventOnElement(...el: (HTMLElement | SVGElement)[]) {
        el.forEach(e => {
            Object.assign(e.style, { 'pointer-events': 'all', 'touch-action': 'none' });
            e.addEventListener('touchend', this.touchHandler);
            e.addEventListener('touchstart', this.touchHandler);
            e.addEventListener('mousedown', this.mouseHandler);
            e.addEventListener('mouseup', this.mouseHandler);

            e.addEventListener('click', this.pointerHandler);
            e.addEventListener('pointerdown', this.pointerHandler);
            e.addEventListener('pointerup', this.pointerHandler);
            e.addEventListener('pointermove', this.pointerHandler);
        });
    }

    protected unregisterPointerEventsOnElement(...el: (HTMLElement | SVGElement)[]) {
        el.forEach((e: HTMLElement | SVGElement) => {
            Object.assign(e.style, { 'pointer-events': 'none' });
            e.removeEventListener('touchend', this.touchHandler);
            e.removeEventListener('touchstart', this.touchHandler);
            e.removeEventListener('mousedown', this.mouseHandler);
            e.removeEventListener('mouseup', this.mouseHandler);

            e.removeEventListener('click', this.pointerHandler);
            e.removeEventListener('pointerdown', this.pointerHandler);
            e.removeEventListener('pointerup', this.pointerHandler);
            e.removeEventListener('pointermove', this.pointerHandler);
        });
    }
}

/**
 * A layer contain an unbounded canvas element
 */
export abstract class UnboundedGraphicLayerCtrl extends BaseLayerCtrl {
    /**
     * The canvas element exposed to user of the layer
     */
    abstract canvas: HTMLCanvasElement;
    abstract ctx: CanvasRenderingContext2D | WebGL2RenderingContext | WebGLRenderingContext;

    /**
     * A method convert from the board position to the position
     * on the canvas element
     * @param pos
     */
    abstract canvasPos(pos: Position): ScreenPosition;
}
/**
 * A layer contain a bounded canvas element
 */
export abstract class BoundedGraphicLayerCtrl extends BaseLayerCtrl {
    /**
     * The canvas element exposed to user of the layer
     */
    abstract canvas: HTMLCanvasElement;
    abstract ctx: CanvasRenderingContext2D | WebGL2RenderingContext | WebGLRenderingContext;

    /**
     * A method convert from the board position to the position
     * on the canvas element
     * @param pos
     */
    abstract canvasPos(pos: Position): ScreenPosition;

    abstract updateBoundary(b: BoundaryRectangle, triggerNotification?: boolean);

    /**
     * Boundary of the graphic layer on the board coordinate
     */
    abstract readonly boundary: BoundaryRectangle;
}

export type GraphicLayerCtrl = BoundedGraphicLayerCtrl | UnboundedGraphicLayerCtrl;
/**
 * A layer contain a DOMElement. Similar to bounded graphic, it should have a defined boundary within the viewport coordinate
 */
export abstract class DOMElementLayerCtrl extends BaseLayerCtrl {
    abstract domEl: HTMLElement | SVGElement;

    // For DOMElementLayerCtrl used in inline viewports, calling this method will have no effects
    abstract updateBoundary(b: BoundaryRectangle);

    /**
     * Boundary of the dom layer on the board coordinate
     */
    abstract readonly boundary: BoundaryRectangle;
}

export abstract class StickyLayerCtrl extends BaseLayerCtrl {
    abstract domEl: HTMLElement;
    abstract style: object;
}

/**
 * SVGLayers are alternatives to graphic layers when we want to work with svg graphics
 * instead of canvas graphic. They provide an svg context, within which the editor
 * can create svg primitives
 */
export abstract class BoundedSVGLayerCtrl extends BaseLayerCtrl {
    abstract svg: SVGElement;

    /**
     * A method convert from the board position to the position
     * on the canvas element
     * @param pos
     */
    abstract svgPos(pos: Position): ScreenPosition;

    abstract updateBoundary(b: BoundaryRectangle, triggerNotification?: boolean);

    /**
     * Boundary of the graphic layer on the board coordinate
     */
    abstract readonly boundary: BoundaryRectangle;
}

/**
 * SVGLayers are alternatives to graphic layers when we want to work with svg graphics
 * instead of canvas graphic. They provide an svg context, within which the editor
 * can create svg primitives
 */
export abstract class UnboundedSVGLayerCtrl extends BaseLayerCtrl {
    abstract svg: SVGElement;

    /**
     * A method convert from the board position to the position
     * on the canvas element
     * @param pos
     */
    abstract svgPos(pos: Position): ScreenPosition;
}

/**
 * A layer contain a DOMElement, but its width and height defined by the content within.
 */
export abstract class InlineLayerCtrl extends BaseLayerCtrl {}

/**
 * A layer that cover the whole viewport, its main purpose is to be used as the backdrop to capture events
 */
export abstract class CoverLayerCtrl extends BaseLayerCtrl {
    abstract nativeElPosition(position: Position): ScreenPosition;
}

/**
 * A renderer is a function that take in the viewport and the layer, produce the necessary DOM content
 * and then append the content to the placeholder element provided by the layer.
 */
export type LayerRenderer<V extends ViewportManager, L extends VDocLayerCtrl> = (viewport: V, layer: L) => void;

export type LayerImplementation<T extends VDocLayerCtrl> = abstract new (...args: any[]) => T;

/**
 * The options that provide information to viewport manager to help it determine the layer
 * to be provided. In many cases, the layer could have some of its attributes saved in coordinator
 * state (for example when layer is requested when the document is loaded)
 */
export interface LayerOptions {
    viewport?: ViewportManager;
    editor?: DocumentEditor;
    state?: VDocLayer;
    /**
     *
     */
    docGlobalId?: DocumentId;
    docLocalId?: DocLocalId;
    /**
     * When requesting a layer, editor might specify the position of the layer
     */
    boundary?: BoundaryRectangle;
    domElType?: string;
    nativeEl?: HTMLElement;

    /**
     *  style that will be applied to the root of the layer
     **/
    style?: object;
    /**
     * Optional. The view mode of the document (from config.server) to determine btw bounded and unbounbed layers
     */
    docViewMode?: DocumentViewMode;
}

export type LayerCreator<T extends VDocLayerCtrl, O extends LayerOptions> = (options: O) => T | Promise<T>;

/**
 * An interface represent an entity with the ability to convert a mouse event to a position
 */
export interface PositionLocatable {
    mouseLocation(eventData: MouseEventData<any>): Position;
}

/**
 * A interface represent an entity with a boundary in the board coordinate
 */
export interface HasNativeElementBoundary {
    nativeElBoundary: BoundaryRectangle;
}

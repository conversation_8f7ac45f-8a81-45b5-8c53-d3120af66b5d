import { ResponseType } from 'axios';
import { SelectionFeature } from '../tools';
import { CmdGateway } from './command';
import { BoundaryRectangle } from './common.structure';
import { DocMappingLDEData, EditorCoordinator } from './coordinator';
import { DocLocalId, DocumentId, EditorId, IdAble, ModuleLookup, ViewportId } from './data.model';
import { RegistryManager } from './entity.registry';
import { ViErrEventEmitter } from './error-handler';
import { FeatureSupporter } from './feature.supporter';
import { HasTool } from './tools';
import { VDocCtrl } from './vdoc';
import { ViewportManager } from './viewport';

export interface EditorBackendConnector {
    loadDocumentByLocalId(channelCode: number, localId: DocLocalId, responseType: ResponseType): Promise<any>;
    reloadDocumentByLocalId(channelCode: number, localId: DocLocalId, responseType: ResponseType): Promise<any>;
    loadDocumentByGlobalId(channelCode: number, globalId: DocumentId, responseType: ResponseType): Promise<any>;
}

export interface LoadingContext {
    connector: EditorBackendConnector;
    vm: ViewportManager;
    zIndexes: number[];
}

export type EditorType =
    | 'FreeDrawingEditor'
    | 'GeometryEditor'
    | 'WordEditor'
    | 'PhotoEditor'
    | 'PdfEditor'
    | 'MathEditor'
    | 'MathGraphEditor'
    | 'CommonToolsEditor'
    | 'ClassroomToolsEditor'
    | 'ZoomToolsEditor'
    | 'EmbedToolsEditor'
    | 'ComposerEditor'
    | 'ContextMenuToolsEditor'
    | 'ShareScreenEditor';

export type DocumentViewMode = 'full-viewport' | 'bounded';

export interface CreateDocumentResult {
    editor: DocumentEditor;
    globalId?: DocumentId;
    localId?: DocLocalId;
}

export interface CreatingContext {
    boundary?: BoundaryRectangle;
    vm: ViewportManager;
}

export interface CreatingLayerContext {
    boundary?: BoundaryRectangle;
    vm: ViewportManager;
    docId: DocLocalId;
    globalId: DocumentId;
}

export interface InternalPasteDocResult {
    globalId: DocumentId;
    editorType: EditorType;
    localDocEventState: DocMappingLDEData;
}

export enum OperationMode {
    LOCAL = 'LOCAL',
    CLOUD = 'CLOUD',
    MIXED = 'MIXED',
}

export interface DocLocalContent {
    version: number;
    content: any;
}

export interface SelectionFeatureSupportEditor extends DocumentEditor {
    selectionFeature: SelectionFeature;
}

export interface DocumentEditor extends FeatureSupporter, IdAble, HasTool<any, any> {
    readonly id: number;
    readonly editorType: EditorType;

    loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext): Promise<void>;

    loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext): Promise<void>;

    reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext): Promise<void>;

    /**
     * Any initialization that needs to be done before the editor can be used.
     * Ex: loading sub editors
     */
    initialize(): Promise<void>;

    /**
     * the coordinator is the one that instruct the editor to start loading document data, everytime the editor finish
     * loading a document it must inform the coordinator. When coordinator conclude that all necessary data has been loaded,
     * it will give the editor the green light and then, the processor can be ready
     */
    start(): Promise<void>;

    findDocumentByLocalId(vpId: ViewportId, docLocalId: DocLocalId): VDocCtrl | undefined;
    findDocumentByGlobalId(vpId: ViewportId, globalId: DocumentId): VDocCtrl | undefined;
    findAllDocuments(vpId: ViewportId): VDocCtrl[];

    /**
     * Duplicate the document record stored in backend, this method don't actually duplicate it on the viewer
     */
    duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>>;

    /**
     * Whether to optimize the loading process of the editor docs, configured by `config.server` use case.
     * Example: in MathEditor, optimizeLoading mode will render a markup element on first load,
     * and only replace it with the real math-field element that allow editing on doc focus
     */
    get optimizeLoading(): boolean;

    /**
     * Get the operation mode of the editor configured by `config.server` use case
     */
    get operationMode(): OperationMode;

    /**
     * Get the local content of the document. Only available in LOCAL or MIXED operation mode
     * or if the editor is not support local content
     */
    getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined;

    /**
     * Load document by local content. This method is only available in LOCAL or MIXED operation mode
     */
    loadDocumentByLocalContent(
        vm: ViewportManager,
        localId: DocLocalId,
        localContent: DocLocalContent
    ): Promise<VDocCtrl | undefined>;

    /**
     * Get the local content of the document from its global id for the case we want to convert a doc in CLOUD mode to LOCAL mode.
     * This method is only available in LOCAL or MIXED operation mode
     * @param globalId the global id of the document
     */
    getLocalContentFromGlobalId(
        globalId: DocumentId,
        loadingConnector: EditorBackendConnector
    ): Promise<DocLocalContent | undefined>;
}

export interface EditorLookup {
    /**
     *  Module Federation is used to expose the implementation of each editor as a Module Federation container,
     *  as such, loading such implementation requires both implementation name
     * (the container name as configured in the webpack configuration of each editor project). By right, we would
     * need the name of the exposed module too, but it is default to 'editorImpl'
     **/
    lookup: ModuleLookup;
    editorType: EditorType;
    settings?: any;
    /**
     * The styles modules to be used directly by the editor (not the editor UI style).
     * Example: math editor need some static style and fonts to render the LaTeX markup.
     */
    editorStyles?: ModuleLookup[];
}

export interface UserContext {
    username?: string;
    avatarUrl?: string;
    userId?: string;
    presenter?: Omit<UserContext, 'presenter'>;
}

export interface EditorConfig {
    id: EditorId;
    editorType: EditorType;
    channelCode: number;
    apiUri: string; // where the api server that the editor can connect to
    registryManager: RegistryManager;
    commandGateway: CmdGateway;
    attachmentUri?: string; // where the editor can upload its attachment
    docViewMode?: DocumentViewMode; // default to normal
    operationMode?: OperationMode; // defautl to CLOUD
    userCtxGetter?: () => UserContext; // for getting additional context like username in classroom
    optimizeLoading?: boolean;
}

export interface DocumentEditorFactory {
    factoryFn: (config: EditorConfig, coordinator: EditorCoordinator) => DocumentEditor;
    errHandlerInitializer?: (emitter: ViErrEventEmitter) => void;
}

export function createDummyLocalDocId(vpId: ViewportId, channelCode: number, localId: DocLocalId): DocumentId {
    return `LOCALDOC_${vpId}_${channelCode}_${localId}`;
}

export function isLocalContentDocId(docId: DocumentId): boolean {
    return !!docId?.startsWith('LOCALDOC_');
}

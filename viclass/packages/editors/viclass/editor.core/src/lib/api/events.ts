import { Observable } from 'rxjs';
import { CRUDC<PERSON>eR<PERSON>ult, Cursor, isCtrlOrMeta } from '../../public-api';
import { EditorCoordinator } from './coordinator';
import { DocLocalId, DocumentId, ViewportId } from './data.model';
import { DocumentEditor, EditorType } from './editor';
import { KeyboardHandlingItem, MouseHandlingItem, PointerHandlingItem, PointerType, SpecialKeyboard } from './tools';
import { VDocLayerCtrl } from './vdoclayer';
import { VDocObjCtrl } from './vdocobj';
import { ViewportManager, ViewportMode } from './viewport';

export type DocumentLayerEventType =
    | 'float'
    | 'sink'
    | 'moveup'
    | 'movedown'
    | 'movetop'
    | 'movebottom'
    | 'created'
    | 'destroyed';

export type DocumentObjectEventType = 'created' | 'destroyed' | 'changed';

export type DocumentEventType = 'created' | 'destroyed' | 'changed';

export interface VEventData<Type, SourceType, EventState> {
    source: SourceType;
    eventType: Type;
    state?: EventState;
    processedHandlers?: VEventListener<VEventData<Type, SourceType, EventState>>[];
}

export class CompletableData<T> {
    _promise: Promise<any>;
    rs!: (value: any) => void;
    rj!: (reason?: any) => void;

    constructor(public data: T) {
        this._promise = new Promise((rs, rj) => {
            this.rs = rs;
            this.rj = rj;
        });
    }

    get promise(): Promise<any> {
        return this._promise;
    }

    /**
     * Replace the promise of this completable data with a new promise which execute thenFunc before complete
     *
     * This allows the creator of the completable data to insert logic to be executed after the original promise
     * complete
     * @param thenFunc
     */
    composePromise(thenFunc: (result: any) => Promise<any> | any) {
        const myPromise = this._promise;
        this._promise = myPromise.then(thenFunc);
    }

    complete(value: any) {
        this.rs(value);
    }

    error(reason: any) {
        this.rj(reason);
    }
}

export class DefaultEventData<Type, SourceType> implements VEventData<Type, SourceType, any> {
    state: any;

    constructor(
        public eventType: Type,
        public source: SourceType
    ) {
        this.state = {};
    }
}

export type EditorFocusEventData = VEventData<string, any, any>;

export type UIPointerEventType =
    | 'click'
    | 'pointerdown'
    | 'pointerup'
    | 'pointermove'
    | 'pointercancel'
    | 'pointerout'
    | 'pointerover'
    | 'pointerenter'
    | 'pointerleave'
    | 'gotpointercapture'
    | 'lostpointercapture';

/**
 * Event manager interpret the pointer down and pointer up events to detect
 * long pressing and multiple pointers concurrent action such as two pointers are
 * down or up at the sametime to facilitate use cases such as showing context menu
 * when two pointers are up almost concurrently.
 *
 * Condition for concurrent pointer down:
 * - If a number of pointer down are detected within not more than Yms of each other consecutively
 * - All the pointer downs that constitute the cc-down must be within Zms of the first pointer down. This means
 * if the pointer downs are keep arriving, and it exceed the Zms, then the final cc-down are NOT triggered
 * - Everytime concurrent pointer down or long pointer down is fired it will not be triggered again until
 * all pointers are released
 *
 * Condition for long press to happen:
 *
 * - A number (1 or more) of pointers are down at almost the same time within X ms of the first pointer
 * - The same number of pointers are held for more than Y ms
 * - No pointer among the concurrent pointers moved for more than Z in distance
 *
 * Note: if there are more than 1 pointers are down, long press will only be emitted IF the concurrent down
 * pointer has been emitted.
 *
 * Condition for concurrent up to happen:
 *
 * - No more active pointers are active, all pointers are up at almost the same time, similar to concurrent pointer down event.
 */
export type InferredDPointerEventType = 'longpress' | 'cc-down' | 'cc-up' | 'tap';
export type PointerEventType = UIPointerEventType | InferredDPointerEventType | 'numdpointer';

export interface UIPointerEventData<Source> extends VEventData<UIPointerEventType, Source, void> {
    viewport: ViewportManager;
    nativeEvent: PointerEvent;

    // other currently active pointers, including pen
    activePointers?: ReadonlyMap<number, UIPointerEventData<any>>;

    // currently active touch pointer, without pen
    activeTouches?: ReadonlyMap<number, UIPointerEventData<any>>;

    readonly getKeys: SpecialKeyboard[];
    continue: boolean;
}

export type InferredPointerEvent = VEventData<InferredDPointerEventType, any, void> &
    (
        | {
              eventType: 'longpress' | 'cc-down';
              pointerDowns: Map<number, UIPointerEventData<any>>;
              touchDowns: Map<number, UIPointerEventData<any>>;
              pointerUps?: never;
              touchUps?: never;
          }
        | {
              eventType: 'cc-up';
              pointerUps: Map<number, UIPointerEventData<any>>;
              touchUps: Map<number, UIPointerEventData<any>>;
              pointerDowns?: never;
              touchDowns?: never;
          }
        | {
              eventType: 'tap';
              /**
               * List of pointer down events if any that cause the cc down
               * Must be set if ccdown. Must not set when not cc down
               */
              pointerDowns: Map<number, UIPointerEventData<any>>;
              touchDowns: Map<number, UIPointerEventData<any>>;
              /**
               * List of pointer up events if any that cause the cc-up.
               * Must be set if eventType is 'cc-up'. Must not be set otherwise.
               * The map key is the pointerId.
               */
              pointerUps: Map<number, UIPointerEventData<any>>;
              touchUps: Map<number, UIPointerEventData<any>>;
          }
    ) & {
        /**
         * The viewport that the inferred event occurred
         */
        viewport?: ViewportManager;

        continue: boolean;
    };

/**
 * In case of number of dynamic pointer changes, all listeners will be informed so that it might clean
 * up its state or cancel current processing.
 *
 * It is more convenient than having each listener register for pointer up and pointer down with
 * different number of pointers just to cancel existing behavior.
 *
 * For example, when user is drawing with current pointer and the user touch one more pointer to zoom
 * instead of register pointerdown and pointerup both when there are two and one pointer the pen tool
 * just need to care when the pointerdown and pointermove of the first pointer. To make sure drawing is
 * cancel properly when the number of pointer change, it just process num pointer change event for that purpose.
 *
 * IMPT: this event is emitted before the actual browser event is emitted to the handler
 */
export interface NumDPointerChange extends VEventData<'numdpointer', any, void> {
    changed: PointerEvent; // the latest of event of the pointer that cause number of pointer to change
    totalPointer: number; // number of pointer after the event occurs excluding non-dynamic pointers
    totalTouch: number;
    viewport: ViewportManager;

    continue: true; // the num pointer change CANNOT be stopped. It always is broadcasted to ALL registered handlers
}

export type PointerEventData<Source> = UIPointerEventData<Source> | NumDPointerChange | InferredPointerEvent;

export interface MouseEventData<Source> extends VEventData<string, Source, any> {
    viewport: ViewportManager;
    nativeEvent: MouseEvent | WheelEvent;

    readonly getKeys: SpecialKeyboard[];
    continue: boolean;
}
export interface KeyboardEventData<Source> extends VEventData<string, Source, any> {
    viewport: ViewportManager;
    nativeEvent: KeyboardEvent;

    readonly getKeys: (SpecialKeyboard | string)[];
    continue: boolean;
}

export type DocumentObjectEventData = DefaultEventData<DocumentObjectEventType, VDocObjCtrl<any>>;
export type DocumentLayerEventData = DefaultEventData<DocumentLayerEventType, VDocLayerCtrl>;
export type DocumentEventData = DefaultEventData<DocumentEventType, DocumentEditor>;

export type LocatableEvent<Source> = MouseEventData<Source> | UIPointerEventData<Source>;

/**
 * Commonly filter for pointer types
 */
export const pointerTypeMouse: PointerType[] = ['mouse'];
export const pointerTypeDyn: PointerType[] = ['touch'];
export const pointerTypePen: PointerType[] = ['pen'];
export const pointerTypePenMouse: PointerType[] = pointerTypeMouse.concat(pointerTypePen);
export const pointerTypePenDyn: PointerType[] = pointerTypeDyn.concat(pointerTypePen);

//----------- LISTENER

export interface VEventListener<T extends VEventData<any, any, any>> {
    onEvent(eventData: T): T | Promise<T>;

    onRegister?: () => void;
    onUnregister?: () => void;
}

export interface MouseEventListener<Source> extends VEventListener<MouseEventData<Source>> {
    cursor?: Observable<Cursor[] | undefined>;
}

export interface PointerEventListener<Source> extends VEventListener<PointerEventData<Source>> {
    cursor?: Observable<Cursor[] | undefined>;
}

// we distinguish between UIPointerEventListener and PointerEventListener
// the second one is used for handle UIEvent exclusively and is used mainly in the core
// the first one is used in the application to handle UI event and also tracking events such as numdpointer event
export interface UIPointerEventListener<Source> extends VEventListener<UIPointerEventData<Source>> {}

export type KeyboardEventListener<Source> = VEventListener<KeyboardEventData<Source>>;

//---------- EventSource

export interface VEventSource<T extends VEventData<any, any, any>> {
    registerListener(listener: VEventListener<T>);

    unregisterListener(listener: VEventListener<T>);

    hasHandler(): boolean;
}

export type VDocObjEventSource = VEventSource<DocumentObjectEventData>;

export type VDocLayerEventSource = VEventSource<DocumentLayerEventData>;

export type VDocEventSource = VEventSource<DocumentEventData>;

export class DefaultMouseEventData<Source> implements MouseEventData<Source> {
    private _continue: boolean;
    eventType: string;

    constructor(
        public viewport: ViewportManager,
        public source: Source,
        public nativeEvent: MouseEvent
    ) {
        this._continue = true;
        this.eventType = nativeEvent.type;
    }

    get getKeys(): SpecialKeyboard[] {
        const keys: SpecialKeyboard[] = [];
        if (isCtrlOrMeta(this.nativeEvent)) keys.push('ctrl');
        if (this.nativeEvent.altKey) keys.push('alt');
        if (this.nativeEvent.shiftKey) keys.push('shift');
        return keys;
    }

    get continue(): boolean {
        return this._continue;
    }

    set continue(v: boolean) {
        this._continue = v;
    }
}

export class DefaultPointerEventData<Source> implements UIPointerEventData<Source> {
    continue: boolean = true;
    eventType: UIPointerEventType;
    state?: void | undefined;
    processedHandlers?: VEventListener<VEventData<UIPointerEventType, Source, void>>[] | undefined;
    activePointers?: ReadonlyMap<number, UIPointerEventData<any>>;

    constructor(
        public viewport: ViewportManager,
        public source: Source,
        public nativeEvent: PointerEvent
    ) {
        this.eventType = nativeEvent.type as UIPointerEventType;
    }

    get getKeys(): SpecialKeyboard[] {
        const keys: SpecialKeyboard[] = [];
        if (isCtrlOrMeta(this.nativeEvent)) keys.push('ctrl');
        if (this.nativeEvent.altKey) keys.push('alt');
        if (this.nativeEvent.shiftKey) keys.push('shift');
        return keys;
    }
}

export class DefaultKeyboardEventData<Source> implements KeyboardEventData<Source> {
    private _continue: boolean;
    eventType: string;

    constructor(
        public viewport: ViewportManager,
        public source: Source,
        public nativeEvent: KeyboardEvent
    ) {
        this._continue = true;
        this.eventType = nativeEvent.type;
    }

    get getKeys(): (SpecialKeyboard | string)[] {
        const keys: (SpecialKeyboard | string)[] = [];
        if (isCtrlOrMeta(this.nativeEvent)) keys.push('ctrl');
        if (this.nativeEvent.altKey || this.nativeEvent.key == 'Alt') keys.push('alt');
        if (this.nativeEvent.shiftKey || this.nativeEvent.key == 'Shift') keys.push('shift');
        if (this.nativeEvent.key == 'Escape') keys.push('esc');
        if (!['Control', 'Alt', 'Shift', 'Escape', 'Meta'].includes(this.nativeEvent.key))
            keys.push(this.nativeEvent.key);
        return keys;
    }

    get continue(): boolean {
        return this._continue;
    }

    set continue(v: boolean) {
        this._continue = v;
    }
}

export interface NativeEventTarget<Source> {
    nativeMouseEventEmitter?: VEventSource<MouseEventData<Source>>;
    nativePointerEventEmitter?: VEventSource<UIPointerEventData<Source>>;
    nativeKeyboardEventEmitter?: VEventSource<KeyboardEventData<Source>>;
}

export interface EditorEventManager {
    coordinator: EditorCoordinator;

    /**
     * This method is normally called by coordinator to register mouse handling for editor tools and common tools
     * @param handlingItem
     * @param handler
     */
    registerMouseHandling<Source extends NativeEventTarget<Source>>(
        handlingItem: MouseHandlingItem,
        handler: MouseEventListener<NativeEventTarget<Source>>
    );
    registerPointerHandling<Source extends NativeEventTarget<Source>>(
        handlingItem: PointerHandlingItem,
        handler: PointerEventListener<NativeEventTarget<Source>>
    );
    registerKeyboardHandling<Source extends NativeEventTarget<Source>>(
        handlingItem: KeyboardHandlingItem,
        handler: KeyboardEventListener<NativeEventTarget<Source>>
    );

    resetMouseHandling();
    resetKeyboardHandling();
    resetPointerHandling();

    /**
     * Unlike normally registered mouse handling, mouse handling on specific layer is called by component (which is not an editor tool / toolbar) that create the sepcific
     * layer. These kind of components (e.g. an implementation of boundary display) can register events on this layer so that it can handle specific cross cutting logic,
     * e.g. the movement of the boundary when mouse dragging.
     * @param handlingItems
     * @param handler
     * @param layer
     */
    registerMouseHandlingOnLayer<L extends VDocLayerCtrl>(
        handlingItems: MouseHandlingItem[],
        handler: MouseEventListener<L>,
        layer: L
    );
    registerPointerHandlingOnLayer<L extends VDocLayerCtrl>(
        handlingItems: PointerHandlingItem[],
        handler: PointerEventListener<L>,
        layer: L
    );

    clearMouseHandlingOnLayer(layer: VDocLayerCtrl);
    clearPointerHandlingOnLayer(layer: VDocLayerCtrl);

    registerMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>);
    unregisterMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>);
    registerPostMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>);
    unregisterPostMouseInterceptor(handler: MouseEventListener<NativeEventTarget<any>>);

    registerPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>);
    unregisterPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>);
    registerPostPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>);
    unregisterPostPointerInterceptor(handler: PointerEventListener<NativeEventTarget<any>>);

    /**
     * In certain scenarios, an editor might want to capture all keyboard events if it originate from a particular elements
     * For example, in case of freedrawing editor text preview, while the user typing, we want all of the keystroke handled by the text tool.
     * The tool can choose to exclude some keys (i.e. allow those keys to pass through and reach other handlers)
     *
     * Effectively, this methods allow an editor at runtime to disable / enable certain behavior if needed.
     *
     * @param el
     * @param handler
     */
    captureAllKeyboardEvent<Source extends NativeEventTarget<Source>>(
        el: Element,
        handler: KeyboardEventListener<NativeEventTarget<Source>>
    );
    unCaptureAllKeyboardEvent(el: Element);

    /**
     * Additionally monitor external elements so that keyboards events occur on these elements are handled properly.
     * Viewport root elements are monitored by default.
     *
     * When keyboard events occur on monitored elements, they are routed according to the routing rules to correct listeners
     * added by tools / toolbars / coordinators / editors. The viewport set inside the event data will be the focused viewports.
     *
     * If there are multiple viewport being focused, the event manager will choose the first one.
     *
     * @param el
     */
    captureEventsFor(el: Element);
    uncaptureEventsFor(el: Element);

    /**
     * When user focus on an element (created by an editor at runtime), an editor might want to be activated
     * so that it can be the one receive events subsequently. For example, when an editable div is on the viewport,
     * and user want to continue editing, they focus on the editable box. At this moment, the editor must be acivated.
     *
     * The editor register the element by asking the event manager directly. Note that, the activation of an editor
     * is done through the coordinator, event manager must not call editor.onActivated directly. Instead, event manager
     * triggers the activation of editor through coordinator.activateEditor.
     *
     * @param el the element to monitor the focus event
     * @param editor the editor to activate.
     * @param focusListener
     */
    addFocusPoint(el: Element, editor: DocumentEditor, focusListener: VEventListener<EditorFocusEventData>);
    removeFocusPoint(el: Element, editor: DocumentEditor);

    /**
     * Attach a viewport to the event manager. For each attached viewport, the event manager will listen for the layer
     * events, and attach mouse listener to the layers' native elements.
     *
     * When mouse / keyboard events occur, the event manager needs to route the event to the correct destination
     *
     * @param vm the viewport manager that needs to have event managed
     */
    attachViewport(vm: ViewportManager);
    detachViewport(vm: ViewportManager);

    isEnable(): boolean;
    enable(): void;
    disable(): void;
}

export interface CoordinatorEvent
    extends VEventData<CoordinatorEventType | string, EditorCoordinator, CoordinatorEventState> {}

export type CoordinatorEventType =
    | 'viewport-added'
    | 'viewport-focusin'
    | 'viewport-focusout'
    | 'viewport-removed'
    | 'viewport-selected'
    | 'viewport-interactive-mode'
    | 'viewport-edit-mode'
    | 'viewport-view-mode'
    | 'viewport-disabled'
    | 'editor-focus'
    | 'editor-blur'
    | 'editor-added'
    | 'editor-removed'
    | 'new-doc'
    | 'remove-doc'
    | 'load-doc'
    | 'mark-doc-valid';

export const EventToViewportMode: Partial<Record<CoordinatorEventType, ViewportMode>> = {
    'viewport-interactive-mode': 'InteractiveMode',
    'viewport-edit-mode': 'EditMode',
    'viewport-view-mode': 'ViewMode',
    'viewport-disabled': 'Disabled',
} as const;

export interface CoordinatorEventState {
    vmId: ViewportId;
}

export interface NewDocChangeCES extends CoordinatorEventState {
    isReload?: boolean;
    docs: CRUDChangeResult[];
}

export interface EditorAddedCES extends CoordinatorEventState {
    editor: DocumentEditor;
}

export interface ViewportDisableCES extends CoordinatorEventState {
    flushCmd: boolean;
    cleanSelection?: boolean;
}

export type EditorFocusCES = EditorAddedCES;
export type EditorBlurCES = EditorAddedCES;

export type ViewportAddedCES = CoordinatorEventState;
export type ViewportFocusInCES = CoordinatorEventState;
export type ViewportFocusOutCES = CoordinatorEventState;
export type ViewportEnabledCES = CoordinatorEventState;
export type ViewportRemovedCES = CoordinatorEventState;
export type ViewportRenamedCES = CoordinatorEventState;
export type ViewportSelectedCES = CoordinatorEventState;

export interface DocInfoCES extends CoordinatorEventState {
    coordStateId: ViewportId;
    editorType: EditorType;
    docGlobalId: DocumentId;
    docLocalId: DocLocalId;
    docName?: string;
    /**
     * The original doc id in case of duplicated docs.
     * So we can infer some metadata for the duplicated doc from it (ex: doc name)
     */
    originalDocId?: DocumentId;
}

export interface MuitlDocInfoCES extends CoordinatorEventState {
    docInfos: DocInfoCES[];
}

export interface SelectedDocInfoCES extends CoordinatorEventState {
    coordStateId: ViewportId;
    editorType: EditorType;
    docGlobalId: DocumentId;
    docLocalId: DocLocalId;
    selected: string[];
    deselected: string[];
}

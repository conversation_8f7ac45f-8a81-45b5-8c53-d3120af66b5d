import {
    DocLocalContent,
    DocLocalId,
    DocumentEditor,
    DocumentId,
    DocumentViewMode,
    EditorBackendConnector,
    EditorConfig,
    EditorType,
    LoadingContext,
    OperationMode,
    RegistryManager,
    ToolBar,
    VDocCtrl,
    ViewportId,
    ViewportManager,
} from '.';
import { DocCRDFeature } from '../common';
import { RegistryDelegator } from '../delegators';

const legacyDocRegKeys = {
    FreeDrawingEditor: 'freedrawing',
    GeometryEditor: 'geo',
    WordEditor: 'word',
    MathEditor: 'math',
    MathGraphEditor: 'magh',
    ShareScreenEditor: 'sharescreen',
};

const legacyObjRegKeys = {
    ...legacyDocRegKeys,
    FreeDrawingEditor: 'frd',
};

function genericRegKey(edType: EditorType): string {
    const name = !edType.endsWith('Editor') ? edType.substring(0, edType.length - 6) : edType;
    return name.charAt(0).toLowerCase() + name.slice(1);
}

function getEditorRegKey(edType: EditorType): string {
    const legacyKey = legacyDocRegKeys[edType];
    return (legacyKey ? legacyKey : genericRegKey(edType)) + 'Editor';
}

function getObjRegKey(edType: EditorType): string {
    const legacyKey = legacyObjRegKeys[edType];
    return legacyKey ? legacyKey : genericRegKey(edType);
}

export abstract class EditorBase<TDoc extends VDocCtrl> implements DocumentEditor {
    abstract readonly id: number;
    abstract readonly editorType: EditorType;

    abstract loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext): Promise<void>;
    abstract loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext): Promise<void>;
    abstract reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext): Promise<void>;
    abstract initialize(): Promise<void>;
    abstract start(): Promise<void>;
    abstract duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>>;
    abstract get operationMode(): OperationMode;
    abstract getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined;
    abstract onFeatureInitialization(featureKey: string, feature: any): Promise<void>;
    abstract isSupportFeature(featureKey: string): boolean;
    abstract featureSupporter<T>(featureKey: string): T | undefined;
    abstract createToolbar(): ToolBar<any, any>;
    abstract crdFeature: DocCRDFeature;

    readonly regMan: RegistryManager;
    readonly regDelegator = new RegistryDelegator<TDoc>(this);
    readonly docViewMode: DocumentViewMode;
    readonly optimizeLoading: boolean;

    constructor(config: EditorConfig) {
        this.regMan = config.registryManager;
        this.docViewMode = config.docViewMode || 'bounded';
        this.optimizeLoading = config.optimizeLoading || false;
    }

    docReg = (vpId: ViewportId): string => {
        const editorKey = getEditorRegKey(this.editorType);
        const objKey = getObjRegKey(this.editorType);
        return `${editorKey}/${objKey}Doc/${vpId}`;
    };

    layerReg = (vpId: ViewportId, docId: DocLocalId): string => {
        const editorKey = getEditorRegKey(this.editorType);
        const objKey = getObjRegKey(this.editorType);
        return `${editorKey}/${objKey}Layer/${vpId}/${docId}`;
    };

    findDocumentByLocalId(vpId: ViewportId, docLocalId: DocLocalId): TDoc | undefined {
        return this.regDelegator.findDocumentByLocalId(vpId, docLocalId);
    }

    findDocumentByGlobalId(vpId: ViewportId, globalId: DocumentId): TDoc | undefined {
        return this.regDelegator.findDocumentByGlobalId(vpId, globalId);
    }

    findAllDocuments(vpId: ViewportId): TDoc[] {
        return this.regDelegator.findAllDocuments(vpId);
    }

    loadDocumentByLocalContent(
        vm: ViewportManager,
        localId: DocLocalId,
        localContent: DocLocalContent
    ): Promise<TDoc | undefined> {
        if (this.operationMode === 'CLOUD') {
            throw new Error(
                `editor ${this.editorType} does not support loading document by local content in CLOUD mode`
            );
        } else {
            throw new Error(`editor ${this.editorType} does not implement loadDocumentByLocalContent method`);
        }
    }
    getLocalContentFromGlobalId(
        globalId: DocumentId,
        loadingConnector: EditorBackendConnector
    ): Promise<DocLocalContent | undefined> {
        if (this.operationMode === 'CLOUD') {
            throw new Error(
                `editor ${this.editorType} does not support getting local content from global id in CLOUD mode`
            );
        } else {
            throw new Error(`editor ${this.editorType} does not implement getLocalContentFromGlobalId method`);
        }
    }
}

/// <reference types="@viclass/ww/typings" />

import { ModuleLookup } from './api/data.model';

export type LoadRemoteModuleOptions = {
    remoteEntry: string;
    remoteName: string;
    exposedModule: string;
};

export async function loadRemoteModule<T = any>(options: LoadRemoteModuleOptions): Promise<T> {
    return new Promise<T>(async (resolve, reject) => {
        viclass.mfeRT
            .loadRemoteModule({
                type: 'module',
                remoteEntry: options.remoteEntry,
                exposedModule: options.exposedModule,
            })
            .then(moduleInstance => {
                if (moduleInstance.default) return resolve(moduleInstance.default as T);
                else resolve(moduleInstance as T);
            });
    });
}

/**
 * Loads and applies one or multiple theme modules to a target document or shadow root.
 *
 * @param theme - A single ModuleLookup or array of ModuleLookup objects representing the theme(s) to load
 * @param targetRoot - Optional target document or shadow root to apply the stylesheets to. Defaults to the global document.
 */
export async function loadThemeModules(
    theme: ModuleLookup[] | ModuleLookup,
    targetRoot?: Document | ShadowRoot
): Promise<void> {
    // allow loading multiple themes
    const themes: ModuleLookup[] = Array.isArray(theme) ? theme : [theme];

    const promises: Promise<void>[] = [];
    for (const t of themes) {
        const p = loadRemoteModule(t as any).then(styleSheet => {
            if (styleSheet instanceof CSSStyleSheet) processStyleSheet(styleSheet, targetRoot);
        });

        promises.push(p);
    }

    await Promise.all(promises);
}

const appliedStyleSheets: CSSStyleSheet[] = [];

/**
 * Process and apply a stylesheet to a target document or shadow root.
 *
 * @param sheet - The CSSStyleSheet to apply
 * @param targetRoot - The target document or shadow root to apply the stylesheet to. Defaults to the global document.
 */
export function processStyleSheet(sheet: CSSStyleSheet, targetRoot?: Document | ShadowRoot) {
    const root = targetRoot || document;

    let currentSheets: CSSStyleSheet[] = root.adoptedStyleSheets;
    if (!currentSheets) currentSheets = [];

    // Get or initialize the applied stylesheets for this document
    if (root === document && appliedStyleSheets.includes(sheet)) {
        // if the root is the document, the sheet has been applied before, we know that we
        // are not inside a shadow root, so only apply once into the adopted stylesheets
        return;
    }

    root.adoptedStyleSheets = [...currentSheets, sheet];

    if (!appliedStyleSheets.includes(sheet)) {
        appliedStyleSheets.push(sheet);

        // for some reason, font-face rules are not working
        // hacky here, extract the font face rules and apply it to the page manually
        const styleToApply = document.createElement('style') as HTMLStyleElement;
        styleToApply.innerHTML = Array.from(sheet.cssRules)
            .filter(rule => rule instanceof CSSFontFaceRule)
            .map(rule => rule.cssText)
            .join('\n');

        document.head.appendChild(styleToApply);
    } else {
        console.log('The style sheet has been applied before to the document.');
    }
}

import { DocumentId } from '@viclass/editor.core';
import { SerializedEquation } from '../magh-lib';

export type MaghDocRenderProp = {
    screenUnit: number;
    scale: number;
    translation: number[];
    rotation: number[];
    valid: boolean;

    // grid
    axis: boolean;
    grid: boolean;
    detailGrid: boolean;
    showAxisArrows: boolean;
    showAxisLabels: boolean;
    usePiGrid: boolean;

    // default styles
    lineStyle: string;
    lineWidth: number;
    opacity: number;
};

export interface CreateMaghDocRequest {
    docRenderProps: MaghDocRenderProp;
}

export interface FetchDocResponse {
    id: DocumentId;
    version: number;
    equations: SerializedEquation[];
    docRenderProps: MaghDocRenderProp;
}

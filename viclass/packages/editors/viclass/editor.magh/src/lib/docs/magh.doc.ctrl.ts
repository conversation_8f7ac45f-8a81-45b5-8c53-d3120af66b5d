import {
    BoundaryRectangle,
    BoundedGraphicLayerCtrl,
    <PERSON><PERSON>ultVDocCtrl,
    DocumentViewMode,
    DOMElementLayerCtrl,
    GraphicLayerCtrl,
    HasBoundaryCtrl,
    LoadingContext,
    MouseEventData,
    mouseLocation,
    Position,
    SelectHitContext,
    UnboundedGraphicLayerCtrl,
    VDocLayerCtrl,
    ViewportManager,
} from '@viclass/editor.core';
import { syncContentUpdateCommand, syncDeleteEquationCommand, syncUpdateDocStateCommand } from '../cmds/proto.utils';
import { MathGraphHistoryDelegator } from '../history/magh.history.delegator';
import {
    Domain,
    EqType,
    Equation,
    EquationStyle,
    MathGraphLib,
    SerializedEquation,
    SerializedGlobalScope,
    SerializedInternalScope,
} from '../magh-lib';
import { MathGraphEditor } from '../magh.editor';
import { FetchDocResponse, MathGraphDoc, MathGraphLayer } from '../model';
import { validatePointerPos } from '../tools';

export class MathGraphDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    layer: GraphicLayerCtrl;
    private overlayLayer: DOMElementLayerCtrl | null = null;

    maghLib: MathGraphLib;

    private docViewMode: DocumentViewMode;
    private historyDelegator = new MathGraphHistoryDelegator(this);

    constructor(
        override editor: MathGraphEditor,
        public override state: MathGraphDoc,
        viewport: ViewportManager,
        private loadingContext?: LoadingContext
    ) {
        super(state, editor, viewport);
        this.docViewMode = editor.conf.docViewMode;
    }

    /**
     * Adds a layer to the document controller
     * @param layer The layer to add to this document controller
     */
    override addLayer(layer: VDocLayerCtrl): void {
        if (!(layer instanceof UnboundedGraphicLayerCtrl) && !(layer instanceof BoundedGraphicLayerCtrl)) return;

        super.addLayer(layer);

        this.layer = layer;
        this.state.addLayer(layer.state as MathGraphLayer);
    }

    /**
     * Cleans up resources when the document controller is removed
     * Destroys the document editor and history delegator, and removes associated layers
     */
    onRemove() {
        this.maghLib?.destroy();
        this.historyDelegator.destroy();

        // Clean up overlay layer
        if (this.overlayLayer) {
            this.removeLayer(this.overlayLayer);
            this.viewport.removeLayer(this.overlayLayer);
            this.overlayLayer = null;
        }

        this.state.layer = undefined;
        this.removeLayer(this.layer);
        this.viewport.removeLayer(this.layer);
    }

    /**
     * Checks if a mouse event hit this document controller
     * @param event The mouse event data
     * @param l The bounded graphic layer controller
     * @returns Select hit context if hit, undefined otherwise
     */
    checkHit(event: MouseEventData<any>, l: BoundedGraphicLayerCtrl): SelectHitContext {
        const chartView = this.maghLib.getChartView();
        if (chartView.layer === l) {
            const mousePos = mouseLocation(event);
            if (!validatePointerPos(mousePos, this)) {
                return undefined;
            }

            return {
                doc: this,
                hitDetails: undefined,
            };
        }

        return undefined;
    }

    /**
     * Disables edit mode on the document
     */
    disableEditMode() {
        this.maghLib.disable();
    }

    /**
     * Enables edit mode on the document
     */
    enableEditMode() {
        this.maghLib.enable();
    }

    /**
     * Fetches the MathGraph document content from the server
     * @returns Promise resolving to FetchDocResponse or null if loading context is not available
     */
    async getMathGraphDocContent(): Promise<FetchDocResponse | null> {
        if (!this.loadingContext) return null;

        return await this.editor.getDocumentContentByGlobalId(this.state.globalId, this.loadingContext);
    }

    /**
     * Unselects this document, disabling edit mode and sinking its layer
     */
    unselect() {
        this.disableEditMode();
        if (this.layer) this.viewport.sink(this.layer);
    }

    /**
     * Selects this document, floating its layer to the top
     */
    select() {
        if (this.layer) this.viewport.float(this.layer);
    }

    /**
     * Updates the boundary of the document when in bounded view mode
     * @param boundary The new boundary rectangle
     */
    updateBoundary(boundary: BoundaryRectangle) {
        if (this.docViewMode === 'bounded') {
            this.state.layer.boundary = boundary;

            // in full mode, the layer is an unbounded graphic layer
            (this.layer as BoundedGraphicLayerCtrl).updateBoundary(boundary);

            // Update overlay layer boundary if it exists
            this.overlayLayer?.updateBoundary(boundary);
        }
    }

    /**
     * Initializes the MathGraph document editor with equation data
     * @param equations Array of serialized equations to load
     */
    private initMathGraphDocContent(equations: SerializedEquation[]) {
        if (!this.maghLib) {
            this.maghLib = new MathGraphLib(this.layer, this.editor, this);
            this.maghLib.loadEquationData(equations);
        }
    }

    /**
     * Sets equations on the document editor and updates the wrapper layer.
     *
     * ! This only update internal state, not actually reload the chart or save anything to the server.
     * @param equations Array of serialized equations to set
     */
    setEquations(equations: SerializedEquation[]) {
        if (!this.maghLib) {
            this.initMathGraphDocContent(equations || []);
        } else {
            this.maghLib.loadEquationData(equations);
        }

        this.updateWrapperLayerByDocContent();
    }

    /**
     * Gets the current equations from the document editor
     * @returns Array of Equation objects
     */
    getEquations(): Equation[] {
        return this.maghLib.getEquations();
    }

    /**
     * Adds a new equation to the document with history tracking
     */
    addEquation(equationType: EqType) {
        const nextIdx = this.maghLib.getEquations().length;
        this.historyDelegator.withHistory(nextIdx, 'add', () => {
            this.maghLib.addEquation(equationType);
            this.sendContentUpdateCmd(nextIdx, true);
        });

        this.maghLib.reloadChart();
    }

    /**
     * Deletes an equation at the specified index with history tracking
     * @param index The index of the equation to delete
     */
    deleteEquation(index: number) {
        this.historyDelegator.withHistory(index, 'delete', () => {
            const deleted = this.maghLib.deleteEquation(index);
            if (!deleted) throw new Error('equation not found');
            this.sendDeleteEquationCmd(index);
        });

        this.maghLib.reloadChart();
    }

    /**
     * Updates the LaTeX expression for an equation at the specified index
     * @param index The index of the equation to update
     * @param latex The new LaTeX expression
     * @param finished Whether the edit operation is complete (blur the expression edit field)
     */
    updateExpression(index: number, latex: string, finished: boolean = true) {
        if (finished) {
            this.maghLib.setLatex(index, latex, finished);
            this.maghLib.reloadChart();
            return; // this is just to force reload chart and UI, no need to send cmd
        }

        this.historyDelegator.withHistory(index, 'update', () => {
            this.maghLib.setLatex(index, latex, finished);
            this.sendContentUpdateCmd(index);
        });
        this.maghLib.reloadChart();
    }

    /**
     * Updates the style for an equation at the specified index
     * @param index The index of the equation to update
     * @param styles The new equation styles to apply
     */
    updateStyle(index: number, styles: EquationStyle) {
        this.historyDelegator.withHistory(index, 'update', () => {
            this.maghLib.setStyles(index, styles);
            this.sendContentUpdateCmd(index);
        });

        this.maghLib.reloadChart();
    }

    /**
     * Sets the hidden state of an equation at the specified index
     * @param index The index of the equation to update
     * @param hidden Whether the equation should be hidden
     */
    setHidden(index: number, hidden: boolean) {
        this.historyDelegator.withHistory(index, 'update', () => {
            this.maghLib.setHidden(index, hidden);
            this.sendContentUpdateCmd(index);
        });

        this.maghLib.reloadChart();
    }

    /**
     * Updates the global scope for an equation at the specified index
     * @param index The index of the equation to update
     * @param scope The new global scope to set
     */
    updateGlobalScope(index: number, scope: SerializedGlobalScope) {
        this.historyDelegator.withHistory(index, 'update', () => {
            this.maghLib.setGlobalScope(index, scope);
            this.sendContentUpdateCmd(index);
        });

        this.maghLib.reloadChart();
    }

    /**
     * Updates the internal scope for an equation at the specified index
     * @param index The index of the equation to update
     * @param scope The new internal scope to set
     */
    updateInternalScope(index: number, scope: SerializedInternalScope) {
        this.historyDelegator.withHistory(index, 'update', () => {
            this.maghLib.setInternalScope(index, scope);
            this.sendContentUpdateCmd(index);
        });

        this.maghLib.reloadChart();
    }

    /**
     * Updates the domain for an equation at the specified index
     * @param index The index of the equation to update
     * @param domain The new domain to set (optional, undefined removes domain)
     */
    updateDomain(index: number, domain: Domain) {
        this.historyDelegator.withHistory(index, 'update', () => {
            this.maghLib.setDomain(index, domain);
            this.sendContentUpdateCmd(index);
        });

        this.maghLib.reloadChart();
    }

    /**
     * Updates the wrapper layer's border style based on document content
     * Removes border when content exists, shows border when empty
     */
    updateWrapperLayerByDocContent() {
        const wrapper = this.layer.nativeEl;
        if (!wrapper) return;

        const equations = this.maghLib.getEquations();

        const hasContent = equations.some(plot => plot.rawExpression.trim().length > 0);
        if (hasContent) {
            wrapper.style.setProperty('border-color', 'transparent');
        } else {
            wrapper.style.removeProperty('border-color');
        }
    }

    /**
     * Sends a command to update equation content
     * @param plotIndex The index of the equation that was updated
     * @param inserted Whether the equation was newly inserted (default: false)
     */
    sendContentUpdateCmd(plotIndex: number, inserted = false) {
        const plot = this.maghLib.getEquations()[plotIndex];
        if (!plot) {
            throw new Error('plot not found');
        }

        this.state.version++;
        syncContentUpdateCommand(this, plotIndex, plot.serialize(), inserted);

        this.updateWrapperLayerByDocContent();
    }

    /**
     * Sends a command to delete an equation
     * @param plotIndex The index of the equation to delete
     */
    sendDeleteEquationCmd(plotIndex: number) {
        this.state.version++;
        syncDeleteEquationCommand(this, plotIndex);

        this.updateWrapperLayerByDocContent();
    }

    /**
     * Checks if the document is in bounded view mode
     * @returns True if document view mode is 'bounded', false otherwise
     */
    isBoundedView() {
        return this.editor.conf.docViewMode === 'bounded';
    }

    /**
     * Applies zoom to the chart view
     * @param level The zoom level to apply
     * @param zoomRoot Optional position to use as the zoom center
     */
    zoom(level: number, zoomRoot?: Position) {
        this.maghLib.getChartView().zoom(level, zoomRoot);
        this.maghLib.reloadChart();

        syncUpdateDocStateCommand(this);
    }

    /**
     * Centers the chart view on a specific point in chart coordinates
     * @param xInChartCoord X coordinate in chart coordinate system
     * @param yInChartCoord Y coordinate in chart coordinate system
     */
    lookAt(xInChartCoord: number, yInChartCoord: number) {
        this.maghLib.getChartView().lookAt(xInChartCoord, yInChartCoord);
        this.maghLib.reloadChart();

        syncUpdateDocStateCommand(this);
    }

    /**
     * Updates document rendering properties
     * @param changes Object containing key-value pairs of properties to update
     */
    updateDocRenderProp(changes: Record<string, any>) {
        Object.keys(changes).forEach(key => {
            this.state.docRenderProp[key] = changes[key];
        });

        this.maghLib.reloadChart();
        syncUpdateDocStateCommand(this);
    }

    /**
     * Gets the document boundary
     * @returns The current boundary rectangle or undefined
     */
    getBoundary(): BoundaryRectangle {
        return this.state.layer?.boundary;
    }

    /**
     * Gets the overlay layer for DOM elements (like LaTeX labels)
     * @returns The overlay layer or null if not created
     */
    get overlayLayerCtrl(): DOMElementLayerCtrl | null {
        return this.overlayLayer;
    }

    /**
     * Creates and configures the overlay layer for DOM elements
     * This layer is used for rendering LaTeX equations as DOM elements
     * @returns The created overlay layer
     */
    createOverlayLayer(): DOMElementLayerCtrl {
        if (this.overlayLayer) {
            return this.overlayLayer;
        }

        const layerCtrl = this.viewport.requestLayer(DOMElementLayerCtrl, true, {
            boundary: this.state.layer.boundary,
            docLocalId: this.state.id,
            docGlobalId: this.state.globalId,
            viewport: this.viewport,
            editor: this.editor,
            state: undefined, // No state needed for overlay layer
            domElType: 'div',
            docViewMode: this.docViewMode, // Pass docViewMode to support unbounded layer
        }) as DOMElementLayerCtrl;

        // Configure overlay layer properties
        layerCtrl.nativeEl.style.pointerEvents = 'none';
        layerCtrl.nativeEl.style.userSelect = 'none';
        layerCtrl.nativeEl.style.zIndex = '1000'; // Higher than main layer
        layerCtrl.doc = this;

        this.overlayLayer = layerCtrl;
        this.layers.push(layerCtrl);
        return layerCtrl;
    }
}

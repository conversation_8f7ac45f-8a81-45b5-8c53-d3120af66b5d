import { BoxedExpression } from '@cortex-js/compute-engine';
import { Equation } from './Equation';

export enum EqRelation {
    Equal = 0,
    Less = 1,
    Greater = 2,
    LessEqual = 3,
    GreaterEqual = 4,
    Invalid = 5, // Won't support this equation. Example: Inequal, Congruent
}

export enum EqType {
    /**
     * The normal/parametric equations that can be plotted
     */
    Plot = 0,
    /**
     * A scope variable that can be applied to other equations.
     * Example: a = 1
     * -> `f(x) = x^2 + a` equivalent to `f(x) = x^2 + 1`
     */
    ScopeVar = 1,
    /**
     * A point is a coordinate in the chart
     * Example: A=(1,2)
     */
    Point = 2,
}

export enum EqError {
    NONE = 0,
    EMPTY_CONTENT = 1,
    PARAMETRIC_CURVE_NEED_T_VAR = 2,
    PARAMETRIC_CURVE_NEED_2_SIDES = 3,
    PARAMETRIC_CURVE_NOT_ALLOW_XY = 4,
}

/**
 * MathJSON expression
 */
export type MathJsonExpr =
    | number
    | string
    | {
          [key: string]: any;
      }
    | [MathJsonExpr, ...MathJsonExpr[]];

export type BoxedExpr = BoxedExpression;

export type Scope = Record<string, any>;

export type EquationStyle = {
    color: string;
    lineWidth: number;
    lineStyle?: string;
    fillOpacity?: number;
};

export type PointEquationStyle = EquationStyle & {
    size?: number;
    showAxisLines?: boolean;
    showLabel?: boolean;
};

export type PlotEquationStyle = EquationStyle & {
    showDomainMarking?: boolean;
    showLabel?: boolean;
    customLabel?: string;
};

export type SerializedInternalScope = {
    min: number;
    max: number;
};

export type Domain = {
    min: number;
    max: number;
};

export type SerializedGlobalScope = {
    min: number;
    max: number;
    step: number;
};

export type SerializedEquation = {
    /**
     * LaTeX string for content
     */
    expression: string;
    hidden?: boolean;
} & (
    | {
          type: EqType.Plot | undefined;
          styles?: PlotEquationStyle;
          internalScope: SerializedInternalScope;
          domain?: Domain;
      }
    | {
          type: EqType.ScopeVar;
          styles?: EquationStyle;
          globalScope: SerializedGlobalScope;
      }
    | {
          type: EqType.Point;
          styles?: PointEquationStyle;
      }
);

export type GlobalScopeGetter = (eq: Equation<any>) => Scope;

import { Rectangle } from '@viclass/editor.core';
import { Bezier } from 'bezier-js';
import { MaghDocRenderProp } from '../../model';
import { MyPoint } from '../common/draw/MyPoint';
import { SegmentType } from '../common/draw/SegmentType';
import {
    calculateSpacing,
    drawCrosshatch,
    drawLabel,
    findOptimalLabelPosition,
    getLabelMeasure,
} from '../common/draw/utils';
import { EqType, PlotEquation } from '../common/kernel';
import { ImplicitSampler } from '../sampler/implicit/ImplicitSampler';
import { ParametricSampler } from '../sampler/parametric/ParametricSampler';
import { Chart } from './Chart';
import { EuclidianView } from './EuclidianView';
import { Destroyable, Drawable, Sampler } from './models';

export class Plot implements Drawable, Destroyable {
    private samplers: Sampler[];
    private path: MyPoint[] = [];

    private activeSampler?: <PERSON>pler;

    constructor(public equation: PlotEquation) {
        this.samplers = [new ImplicitSampler(), new ParametricSampler()];
    }

    destroy(): void {
        this.samplers.forEach(s => s.destroy());
        this.samplers.length = 0;
    }

    public updatePath(view: EuclidianView) {
        this.path = this.sampling(view);
    }

    /**
     * Sample points based on the domain.
     */
    private sampling(view: EuclidianView): MyPoint[] {
        if (
            !this.equation.isValid ||
            this.equation.equationType !== EqType.Plot // todo support function expression
        )
            return [];

        this.activateSamplers();
        if (!this.activeSampler) return [];

        try {
            return this.activeSampler.sampling(view, this.equation.effectiveDomain);
        } catch (e) {
            console.warn('sampling failed', e);
            return [];
        }
    }

    private activateSamplers() {
        for (const sampler of this.samplers) {
            if (sampler.canHandle(this.equation)) {
                sampler.setEquation(this.equation);
                this.activeSampler = sampler;
                break;
            }
        }
    }

    drawBackground(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        // Draw domain marking if enabled
        if (this.equation.styles.showDomainMarking) {
            this.drawDomainMarking(ctx, view);
        }

        // TODO: implement later for inequality
    }

    /**
     * Draw the graph based on sampled points and smooth the curve using Bezier curves.
     * @param ctx The canvas context to draw the graph.
     * @param view The domain for which points are being sampled.
     */
    public draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        // Sample points from the given domain
        const points = this.path;
        if (points.length === 0) return;

        if (Chart.DEBUG_MODE) {
            this.activeSampler.drawDebug(ctx, view, drp);
        }

        // Begin drawing the graph
        ctx.beginPath();
        const vpScale = view.getViewportScale();
        const lineWeight = this.equation.styles.lineWidth || 1;

        ctx.strokeStyle = this.equation.styles.color || 'red';
        ctx.lineWidth = lineWeight * vpScale;
        ctx.lineCap = 'round';

        ctx.setLineDash(this.getLineDashes(this.equation.styles.lineStyle, lineWeight * vpScale));

        // Start drawing based on sampled points
        let skipPoints = 3;
        for (let i = 0; i < points.length; i += skipPoints) {
            const point = points[i];
            if (!point) break;

            skipPoints = 1;
            // If the point is a MOVE_TO segment, move without drawing
            if (point.segmentType === SegmentType.MOVE_TO) {
                ctx.moveTo(view.chartToLayerX(point.x), view.chartToLayerY(point.y));
                continue;
            }

            if (point.segmentType !== SegmentType.LINE_TO) continue;

            // Add smooth Bezier curve between points using auxiliary points OR quadratic curve if not enough points
            const p0 = points[i - 1] || point;
            const p1 = point;
            const p2 = points[i + 1] || p1;
            const p3 = points[i + 2] || p2;

            if (p2.segmentType !== SegmentType.LINE_TO) {
                skipPoints = 1;
                ctx.quadraticCurveTo(
                    view.chartToLayerX((p0.x + p1.x) / 2),
                    view.chartToLayerY((p0.y + p1.y) / 2),
                    view.chartToLayerX(p1.x),
                    view.chartToLayerY(p1.y)
                );
                continue;
            }

            if (p3.segmentType !== SegmentType.LINE_TO) {
                skipPoints = 2;
                ctx.quadraticCurveTo(
                    view.chartToLayerX((p0.x + p1.x) / 2),
                    view.chartToLayerY((p0.y + p1.y) / 2),
                    view.chartToLayerX(p1.x),
                    view.chartToLayerY(p1.y)
                );
                ctx.quadraticCurveTo(
                    view.chartToLayerX((p1.x + p2.x) / 2),
                    view.chartToLayerY((p1.y + p2.y) / 2),
                    view.chartToLayerX(p2.x),
                    view.chartToLayerY(p2.y)
                );
                continue;
            }

            skipPoints = 3;
            const bzPoints = new Bezier([p0, p1, p2, p3]).points;
            ctx.moveTo(view.chartToLayerX(bzPoints[0].x), view.chartToLayerY(bzPoints[0].y));

            if (bzPoints.length === 3) {
                ctx.quadraticCurveTo(
                    view.chartToLayerX(bzPoints[1].x),
                    view.chartToLayerY(bzPoints[1].y),
                    view.chartToLayerX(bzPoints[2].x),
                    view.chartToLayerY(bzPoints[2].y)
                );
            } else if (bzPoints.length === 4) {
                ctx.bezierCurveTo(
                    view.chartToLayerX(bzPoints[1].x),
                    view.chartToLayerY(bzPoints[1].y),
                    view.chartToLayerX(bzPoints[2].x),
                    view.chartToLayerY(bzPoints[2].y),
                    view.chartToLayerX(bzPoints[3].x),
                    view.chartToLayerY(bzPoints[3].y)
                );
            }
        }

        // Finalize and stroke the path
        ctx.stroke();

        // Draw the test points
        if (Chart.DEBUG_MODE) {
            for (let i = 0; i < points.length; i++) {
                const point = points[i];

                ctx.beginPath();
                if (point.segmentType === SegmentType.MOVE_TO) {
                    ctx.fillStyle = 'red';
                } else if (point.segmentType === SegmentType.LINE_TO) {
                    ctx.fillStyle = 'blue';
                }
                ctx.arc(view.chartToLayerX(point.x), view.chartToLayerY(point.y), 3 * vpScale, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
    }

    drawLabels(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        if (!this.equation.styles.showLabel) {
            this.hideLatexElementLabel();
            return;
        }

        const vpScale = view.getViewportScale() ?? 1;
        const customLabel = this.equation.styles.customLabel;

        // Not draw if too few points, this can be the firsts points on the edges with no connected lines
        if (this.path.length <= 10 && this.path.filter(p => p.segmentType === SegmentType.LINE_TO).length <= 3) {
            this.hideLatexElementLabel();
            return;
        }

        // Use the first point of the path as the label origin
        const originPoint = this.path.find(p => p.segmentType === SegmentType.LINE_TO);
        if (!originPoint) {
            this.hideLatexElementLabel();
            return;
        }

        const originX = view.chartToLayerX(originPoint.x);
        const originY = view.chartToLayerY(originPoint.y);
        const padding = 10 * vpScale;

        if (customLabel) {
            this.hideLatexElementLabel();

            // Draw custom label as plain text (fallback to canvas for custom labels)
            const fontSize = 14 * vpScale;
            const textMetrics = getLabelMeasure(ctx, customLabel, fontSize);
            const textWidth = textMetrics.width;
            const textHeight = fontSize;

            // Find optimal position to avoid clipping
            const optimalPosition = findOptimalLabelPosition(
                { x: originX, y: originY },
                textWidth,
                textHeight,
                view,
                padding
            );

            drawLabel(ctx, customLabel, optimalPosition.bottomLeft, {
                fontSize: fontSize,
                textColor: this.equation.styles.color,
                strokeText: false,
            });
        } else if (this.equation.latexElement) {
            // Draw LaTeX canvas
            const labelRect = this.equation.latexElement.getBoundingClientRect();
            const labelWidth = labelRect.width * vpScale;
            const labelHeight = labelRect.height * vpScale;

            // Find optimal position to avoid clipping
            const optimalPosition = findOptimalLabelPosition(
                { x: originX, y: originY },
                labelWidth,
                labelHeight,
                view,
                padding
            );

            const translateX = optimalPosition.topLeft.x - view.left;
            const translateY = view.top - optimalPosition.topLeft.y;

            Object.assign(this.equation.latexElement.style, {
                transform: `translate(${translateX}px, ${translateY}px) scale(${vpScale})`,
                display: 'block',
            });
        }
    }

    private hideLatexElementLabel() {
        if (this.equation.latexElement) {
            this.equation.latexElement.style.display = 'none';
        }
    }

    /**
     * Draw 45-degree crosshatch pattern outside the domain boundaries
     */
    private drawDomainMarking(ctx: CanvasRenderingContext2D, view: EuclidianView): void {
        const domain = this.equation.effectiveDomain;
        const vpScale = view.getViewportScale();
        const spacing = calculateSpacing(view.docCtrl, view.viewport);
        const lineWeight = 0.5 * vpScale; // Similar to detail grid

        ctx.strokeStyle = this.equation.styles.color;
        ctx.lineWidth = lineWeight;
        ctx.setLineDash([]);

        const leftChart = view.layerToChartX(view.left);
        const rightChart = view.layerToChartX(view.right);
        const topChart = view.layerToChartY(view.top);
        const bottomChart = view.layerToChartY(view.bottom);

        // Draw crosshatch pattern outside domain
        const crosshatchSpacing = spacing / 5; // Detail grid spacing

        // Left side (x < domain.min)
        if (isFinite(domain.min) && leftChart < domain.min) {
            const leftRegion: Rectangle = {
                start: { x: leftChart, y: bottomChart },
                end: { x: Math.min(domain.min, rightChart), y: topChart },
            };
            drawCrosshatch(ctx, view, leftRegion, crosshatchSpacing);
        }

        // Right side (x > domain.max)
        if (isFinite(domain.max) && rightChart > domain.max) {
            const rightRegion: Rectangle = {
                start: { x: Math.max(domain.max, leftChart), y: bottomChart },
                end: { x: rightChart, y: topChart },
            };
            drawCrosshatch(ctx, view, rightRegion, crosshatchSpacing);
        }
    }

    private getLineDashes(key: string, scale: number): number[] {
        const dash = 2 * scale;
        const gap = 2 * scale;
        const dot = 0; // will be draw as a dot because linecap is round

        switch (key) {
            case 'dashed':
                return [dash, gap];
            case 'dotted':
                return [dot, gap];
            case 'dashed-dotted':
                return [dash, gap, dot, gap];
            default:
                return [];
        }
    }
}

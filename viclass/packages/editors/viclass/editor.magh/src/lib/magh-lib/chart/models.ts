import { MaghDocRenderProp } from '../../model';
import { MyPoint } from '../common/draw/MyPoint';
import { Domain, PlotEquation } from '../common/kernel';
import { EuclidianView } from './EuclidianView';

/**
 * Interface for objects that can be drawn on a canvas.
 * The drawing process follows this order:
 * 1. drawBackground (optional) - Draw background elements
 * 2. Chart draws grid
 * 3. draw - Draw main content
 * 4. drawLabels (optional) - Draw labels and annotations
 */
export interface Drawable {
    /**
     * Draws background elements before the main content.
     */
    drawBackground?(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void;

    /**
     * Draws the main content of the object.
     */
    draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void;

    /**
     * Draws labels and annotations after the main content.
     */
    drawLabels?(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void;
}

export interface Destroyable {
    destroy(): void;
}

export interface Sampler extends Destroyable {
    canHandle(eq: PlotEquation): boolean;

    setEquation(eq: PlotEquation): void;

    sampling(view: EuclidianView, domain?: Domain): MyPoint[];

    drawDebug(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void;
}

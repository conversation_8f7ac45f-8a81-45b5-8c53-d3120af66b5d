import { isNil } from 'lodash';
import { MaghDocRenderProp } from '../../../model';
import { EuclidianView } from '../../chart/EuclidianView';
import { Plot } from '../../chart/Plot';
import { latexToElement } from '../draw/utils';
import { PlotEquationAnalyzer } from './analyzers';
import { Engine } from './Engine';
import { Equation } from './Equation';
import { RangeScope } from './scope/RangeScope';
import { Domain, EqType, GlobalScopeGetter, MathJsonExpr, PlotEquationStyle, SerializedEquation } from './types';

/**
 * Wrapper for math expression.
 * Will parse the math expression into MathJSON, analyze the equation, and compile it.
 */
export class PlotEquation extends Equation<PlotEquationStyle> {
    public readonly internalScope = new RangeScope(0, 1);
    public domain?: Domain;

    private _analyzer: PlotEquationAnalyzer;

    private _plot: Plot;

    // Label caching mechanism
    private _labelElement: HTMLElement | null = null;
    private _lastLabelContent: string = '';

    override get isValid(): boolean {
        return this._analyzer.valid;
    }

    get isImplicit(): boolean {
        return this._analyzer.implicit;
    }

    get isParametricArc(): boolean {
        return this._analyzer.parametricCurve;
    }

    get equationType(): EqType {
        return EqType.Plot;
    }

    get supportInternalScope(): boolean {
        return this.isValid && this.isParametricArc;
    }

    get isPreferInterval(): boolean {
        return this._analyzer.preferInterval;
    }

    get effectiveDomain(): Domain {
        return {
            min: !isNil(this.domain?.min) && isFinite(this.domain?.min) ? this.domain.min : -Infinity,
            max: !isNil(this.domain?.max) && isFinite(this.domain?.max) ? this.domain.max : Infinity,
        };
    }

    get latexElement(): HTMLElement | null {
        return this._labelElement;
    }

    override styles: PlotEquationStyle = {
        color: 'red',
        lineWidth: 2,
        showDomainMarking: false,
        showLabel: false,
        customLabel: '',
    };

    constructor(
        engine: Engine,
        scopeGetter: GlobalScopeGetter,
        private overlayContainer: HTMLElement,
        private redrawCb: Function
    ) {
        super(engine, scopeGetter);
        this._analyzer = new PlotEquationAnalyzer(this, this.engine);
        this.setExpression(this.defaultExpression);

        this._plot = new Plot(this);
        this._analyzer.analyze();
    }

    updatePath(view: EuclidianView) {
        this._plot.updatePath(view);
    }

    /**
     * Updates the cached label preview for rendering
     * This method is debounced to prevent excessive updates
     */
    updateLabelCache(force = false): void {
        // custom label will be write directly into canvas as text so no cache needed
        if (this.hidden || !this.styles.showLabel || this.styles.customLabel) return;

        // Only update cache if content has changed or force update is requested
        const labelContent = this._raw;
        if (labelContent === this._lastLabelContent && this._labelElement && !force) return;

        try {
            this._lastLabelContent = labelContent;
            this._labelElement?.remove();
            this._labelElement = latexToElement(labelContent, {
                color: this.styles.color,
                fontSize: 14,
            });

            this.overlayContainer.appendChild(this._labelElement);
            this.redrawCb();
        } catch (error) {
            console.warn('Failed to generate label element:', error);
            this._labelElement = null;
        }
    }

    override drawBackground(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        if (this.hidden || !this.isValid) return;
        this._plot.drawBackground(ctx, view, drp);
    }

    override draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        if (this.hidden || !this.isValid) return;
        this._plot.draw(ctx, view, drp);
    }

    override drawLabels(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        if (this.hidden || !this.isValid) return;

        this._plot.drawLabels(ctx, view, drp);
    }

    public override setExpression(rawExpr: string): boolean {
        this._raw = rawExpr;
        if (!rawExpr) return false;

        try {
            // analyze and apply transformation if necessary
            const mathJson = this._analyzer.analyze();
            if (!this.isValid) return false;

            this.compileEquation(mathJson);
            return true;
        } catch (e) {
            this.reset();

            console.warn('compile failed', e);
            return false;
        }
    }

    /**
     * Simplify the equation by move the right-hand side to the left-hand side.
     * So we will only need to evaluate the left-hand side on sampling
     * Ex: 'x>y' -> lhs: 'x-y', rhs: 0
     */
    private compileEquation(mathJson: MathJsonExpr): void {
        if (!this.isValid) return;

        let lhs: MathJsonExpr;
        let rhs: MathJsonExpr;

        if (this._analyzer.onlyLhs) {
            lhs = mathJson;
            rhs = 0;
        } else if (this._analyzer.parametricCurve) {
            // ["Pair", `f1(t)`, `f2(t)`]
            const sequence = mathJson as MathJsonExpr[];
            lhs = sequence[1] as MathJsonExpr;
            rhs = sequence[2] as MathJsonExpr;
        } else {
            if (!Array.isArray(mathJson)) throw new Error('invalid equation for simplify');

            lhs = mathJson[1] as MathJsonExpr;
            rhs = mathJson[2] as MathJsonExpr;

            if (rhs !== 0 && this.equationType !== EqType.ScopeVar) {
                lhs = ['Add', lhs, ['Negate', rhs]];
                rhs = 0;
            }
        }

        this._lhs = this.engine.box(lhs);
        this._rhs = this.engine.box(rhs);

        this._compiledLhs = this.lhs?.compile() as any;
        this._compiledRhs = this.rhs?.compile() as any;
    }

    protected override reset(): void {
        super.reset();
        this._analyzer.reset();
    }

    override serialize(): SerializedEquation {
        return {
            ...super.serialize(),
            type: EqType.Plot,
            internalScope: {
                min: this.internalScope.min,
                max: this.internalScope.max,
            },
            domain: { ...this.domain },
        };
    }

    override deserialize(serialized: SerializedEquation): void {
        const oldColor = this.styles.color;
        super.deserialize(serialized);

        // check isNil for backward compatibility
        if (!isNil(serialized.type) && serialized.type !== EqType.Plot) {
            throw new Error('invalid equation type for deserialize');
        }

        const serializeData = serialized as Extract<SerializedEquation, { type: EqType.Plot }>;
        if (!isNil(serializeData.internalScope)) {
            this.internalScope.min = serializeData.internalScope.min;
            this.internalScope.max = serializeData.internalScope.max;
        }

        if (!isNil(serializeData.domain)) {
            this.domain = { ...serializeData.domain };
        }

        // Update label cache after deserialization
        const isColorChanged = oldColor !== this.styles.color;
        this.updateLabelCache(isColorChanged);
    }
}

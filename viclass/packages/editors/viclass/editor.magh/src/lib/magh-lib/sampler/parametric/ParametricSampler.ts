import { constants, Interval, isEmpty, isWhole } from 'interval-arithmetic';
import { MaghDocRenderProp } from '../../../model';
import { EuclidianView, Sampler } from '../../chart';
import { CurveEvaluable, Domain, MathJsonExpr, MyPoint, PlotEquation } from '../../common';
import { evaluateInterval, hasInfinity } from '../../evaluator';
import {
    CurvePlotter,
    Gap,
    GeneralCurve,
    GeneralPathClippedForCurvePlotter,
    ParametricCurve,
    PathPlotter,
} from './curve-plotter';
import { DrawInterval } from './DrawInterval';

type IntervalTuple = [Interval, Interval];

function getX(tuple: IntervalTuple): Interval {
    return tuple[0];
}

function getY(tuple: IntervalTuple): Interval {
    return tuple[1];
}

export class ParametricSampler implements Sampler {
    private static readonly MIN_SAMPLE_SIZE = 200;
    private static readonly MAX_SAMPLE_SIZE = 5000;

    private equation!: PlotEquation;
    private expression!: MathJsonExpr;
    private drawInterval = new DrawInterval();
    private pathPlotter?: PathPlotter;

    private lastY: Interval = constants.EMPTY;

    private lastPoints: MyPoint[] = [];

    destroy(): void {
        this.lastPoints.length = 0;
    }

    canHandle(eq: PlotEquation): boolean {
        return eq.isValid && !eq.isImplicit;
    }

    setEquation(eq: PlotEquation): void {
        this.equation = eq;
        if (this.equation.isImplicit) throw new Error('Can not evaluate implicit equation with ParametricSampler');

        const expr = this.equation.lhs?.toMathJson();
        if (!expr) throw new Error('invalid equation');

        this.expression = expr;
    }

    sampling(view: EuclidianView, domain?: Domain): MyPoint[] {
        let points: MyPoint[] = [];
        if (this.equation.isPreferInterval) {
            // Apply domain restriction for interval sampling
            const effectiveDomain = domain || { min: -Infinity, max: Infinity };
            const xMin = Math.max(view.getXmin(), effectiveDomain.min);
            const xMax = Math.min(view.getXmax(), effectiveDomain.max);

            // If no intersection, return empty
            if (xMin >= xMax) {
                this.lastPoints = [];
                return [];
            }

            const samplerSize = this.getSampleSize(view);
            const intervalSamples: IntervalTuple[] = this.intervalSampling(xMin, xMax, samplerSize);
            points = this.toIntervalPath(view, intervalSamples);
        } else {
            points = this.paramericSamping(view, domain);
        }

        this.lastPoints = points;
        return points;
    }

    private getSampleSize(view: EuclidianView): number {
        return Math.min(
            ParametricSampler.MAX_SAMPLE_SIZE,
            Math.max(ParametricSampler.MIN_SAMPLE_SIZE, Math.floor(view.width))
        );
    }

    private intervalSampling(startX: number, endX: number, steps: number): IntervalTuple[] {
        const globalScope = this.equation.getEvaluateScope();
        const result: IntervalTuple[] = [];
        // sampling with interval step by step
        const stepSize = (endX - startX) / steps;
        for (let i = 0; i <= steps; i++) {
            const loX = startX + i * stepSize;
            const hiX = startX + (i + 1) * stepSize;
            const x = new Interval(loX, hiX);
            let y: Interval;
            try {
                y = evaluateInterval(this.expression, { ...globalScope, x: x });
            } catch (e) {
                console.warn('Evaluate interval error', this.expression, x, e);
                y = constants.EMPTY;
            }

            result.push([x, y]);
        }

        return result;
    }

    private toIntervalPath(view: EuclidianView, pathTuples: IntervalTuple[]): MyPoint[] {
        const points: MyPoint[] = [];

        for (const tuple of pathTuples) {
            const disconnect = isEmpty(getY(tuple));
            if (disconnect) {
                this.noJoinForNextTuple();
            } else {
                points.push(...this.drawTuple(view, tuple));
            }

            this.drawInterval.setJoinToPrevious(!disconnect);
        }

        return points;
    }

    private drawTuple(view: EuclidianView, tuple: IntervalTuple): MyPoint[] {
        if (!isEmpty(this.lastY)) {
            return this.drawTupleJoined(view, tuple);
        } else {
            return this.drawTupleIndependent(tuple);
        }
    }

    private drawTupleJoined = (view: EuclidianView, tuple: IntervalTuple): MyPoint[] => {
        if (isWhole(getY(tuple))) {
            return this.drawWhole(view, getX(tuple));
        } else {
            if (!isEmpty(this.lastY)) {
                return this.drawNormal(view, tuple);
            }
        }
        return [];
    };

    private drawTupleIndependent = (tuple: IntervalTuple): MyPoint[] => {
        const [lastValue, points]: [Interval, MyPoint[]] = this.drawInterval.drawIndependent(getX(tuple), getY(tuple));
        this.lastY = lastValue;
        return points;
    };

    private drawWhole = (view: EuclidianView, x: Interval): MyPoint[] => {
        const points = this.drawInterval.drawWhole(view, x);
        this.noJoinForNextTuple();
        return points;
    };

    private drawNormal(view: EuclidianView, tuple: IntervalTuple): MyPoint[] {
        if (hasInfinity(getY(tuple))) {
            return this.drawNormalInfinity(view, tuple);
        } else {
            return this.drawNormalJoined(tuple);
        }
    }

    private drawNormalJoined = (tuple: IntervalTuple): MyPoint[] => {
        const y: Interval = getY(tuple);

        const points = this.drawInterval.drawJoined(this.lastY, getX(tuple), y);
        this.lastY = y;
        return points;
    };

    private drawNormalInfinity = (view: EuclidianView, tuple: IntervalTuple): MyPoint[] => {
        const x: Interval = getX(tuple);
        const y: Interval = getY(tuple);

        let points: MyPoint[] = [];
        if (view.containsY(y.lo) && !isFinite(y.hi)) {
            points = this.drawInterval.leftToTop(view, x, y);
            this.lastY = new Interval(view.getYmax());
        } else {
            if (view.containsY(y.hi)) {
                points = this.drawInterval.leftToBottom(view, x, y);
                this.lastY = new Interval(view.getYmin());
            } else {
                this.lastY = constants.EMPTY;
            }
        }

        return points;
    };

    private noJoinForNextTuple() {
        this.lastY = constants.EMPTY;
    }

    paramericSamping(view: EuclidianView, domain?: Domain): MyPoint[] {
        const fillCurve = false;
        if (!this.pathPlotter) {
            this.pathPlotter = new GeneralPathClippedForCurvePlotter(view);
        }

        this.pathPlotter.reset();
        const curve: CurveEvaluable = this.equation.isParametricArc
            ? new ParametricCurve(this.equation)
            : new GeneralCurve(view, this.equation);

        let minParam = curve.getMinParameter();
        let maxParam = curve.getMaxParameter();

        if (domain && !this.equation.isParametricArc) {
            // only use domain on non ParametricArc, because parametric curves are using `t` as parameter
            minParam = Math.max(domain.min ?? -Infinity, minParam);
            maxParam = Math.min(domain.max ?? Infinity, maxParam);
        }

        CurvePlotter.plotCurve(
            curve,
            minParam,
            maxParam,
            view,
            this.pathPlotter,
            false,
            fillCurve ? Gap.CORNER : Gap.MOVE_TO
        );

        return this.pathPlotter.getPoints();
    }

    drawDebug(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {}
}

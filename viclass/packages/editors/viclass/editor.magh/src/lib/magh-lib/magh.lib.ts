import { GraphicLayerCtrl } from '@viclass/editor.core';
import { Subscription } from 'rxjs';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphEditor } from '../magh.editor';
import { Chart, EuclidianView } from './chart';
import {
    Domain,
    EqType,
    Equation,
    EquationStyle,
    PlotEquation,
    PlotEquationStyle,
    SerializedEquation,
    SerializedGlobalScope,
    SerializedInternalScope,
    VarEquation,
} from './common';

export class MathGraphLib {
    private _chart!: Chart;

    constructor(
        public layer: GraphicLayerCtrl,
        public editor: MathGraphEditor,
        public docCtrl: MathGraphDocCtrl
    ) {
        this._chart = new Chart(docCtrl, this.layer);

        this.init();
    }

    private init() {
        this._chart.start();
    }

    destroy() {
        this._chart.destroy();
    }

    /**
     * Hook to be triggered on equations updated
     * @param callback
     * @returns unsubscribe function
     */
    onEquationsUpdated(callback: () => void): Subscription {
        return this._chart.equationsUpdated$.subscribe(callback);
    }

    enable() {
        // console.log('enable');
    }

    disable() {
        // console.log('disable');
    }

    loadEquationData(datas: SerializedEquation[]) {
        this._chart.clearEquations();
        for (let i = 0; i < datas.length; i++) {
            const eq = this._chart.createEquation(datas[i].type);
            eq.deserialize(datas[i]);
            this._chart.equations.push(eq);
        }

        this.updateVarEqsDuplicatedState();
        this._chart.equationsUpdated$.next();
    }

    updateEquationAt(i: number, data: SerializedEquation) {
        const eq = this._chart.equations[i];
        if (eq) {
            eq.deserialize(data);
        } else {
            const newEq = this._chart.createEquation(data.type);
            newEq.deserialize(data);
            this._chart.equations.splice(i, 0, newEq);
        }

        if (data.type === EqType.ScopeVar) {
            this.updateVarEqsDuplicatedState();
        }

        this._chart.equationsUpdated$.next();
    }

    getChartView(): EuclidianView {
        return this._chart.view;
    }

    getEquations(): Equation[] {
        return this._chart.equations;
    }

    addEquation(equationType: EqType): Equation {
        const eq = this._chart.createEquation(equationType);
        this._chart.addEquation(eq);
        this._chart.equationsUpdated$.next();
        return eq;
    }

    deleteEquation(index: number): boolean {
        const deleted = this._chart.removeEquationByIndex(index);

        if (deleted?.equationType === EqType.ScopeVar) {
            this.updateVarEqsDuplicatedState();
        }

        if (deleted) {
            this._chart.equationsUpdated$.next();
        }

        return !!deleted;
    }

    setLatex(index: number, latex: string, finished?: boolean) {
        this._chart.setExpression(index, latex, !finished);

        const equation = this._chart.equations[index];
        if (equation?.equationType === EqType.ScopeVar) {
            this.updateVarEqsDuplicatedState();
        }

        // Update label cache for PlotEquation when finished
        if (finished && equation?.equationType === EqType.Plot) {
            (equation as PlotEquation).updateLabelCache();
        }
    }

    getLatex(index: number): string | undefined {
        return this._chart.equations[index]?.rawExpression;
    }

    setHidden(index: number, hidden: boolean) {
        const equation = this._chart.equations[index];
        equation.hidden = hidden;

        if (!hidden && equation.equationType === EqType.Plot) {
            (equation as PlotEquation).updateLabelCache();
        }

        this._chart.equationsUpdated$.next();
    }

    getColorPalette(): string[] {
        return this._chart.colorPalette;
    }

    setStyles(index: number, styles: EquationStyle) {
        const equation = this._chart.equations[index];
        const oldStyles = equation.styles;
        equation.styles = styles;

        // Update label cache if color changed and equation is PlotEquation
        if (equation.equationType === EqType.Plot) {
            const oldS = oldStyles as PlotEquationStyle;
            const newS = styles as PlotEquationStyle;
            if (
                oldS.color !== newS.color || // change label color
                (newS.showLabel && !oldS.showLabel) || // toggle show label OFF -> ON
                (!newS.customLabel && oldS.customLabel) // edit custom label from non-empty to empty
            )
                (equation as PlotEquation).updateLabelCache(true);
        }

        this._chart.equationsUpdated$.next();
    }

    setGlobalScope(index: number, scope: SerializedGlobalScope) {
        const equation = this._chart.equations[index];
        if (equation.equationType !== EqType.ScopeVar || !(equation instanceof VarEquation)) {
            throw 'Invalid Equation Type';
        }

        const eqScope = equation.globalScope;
        eqScope.min = scope.min;
        eqScope.max = scope.max;
        eqScope.step = scope.step;
    }

    setInternalScope(index: number, scope: SerializedInternalScope) {
        const equation = this._chart.equations[index];
        if (equation.equationType !== EqType.Plot || !(equation instanceof PlotEquation)) {
            throw 'Invalid Equation Type';
        }

        const eqScope = equation.internalScope;
        eqScope.min = scope.min;
        eqScope.max = scope.max;
    }

    setDomain(index: number, domain: Domain) {
        const equation = this._chart.equations[index];
        if (equation.equationType !== EqType.Plot || !(equation instanceof PlotEquation)) {
            throw 'Invalid Equation Type';
        }

        equation.domain = domain;
        this._chart.equationsUpdated$.next();
    }

    focus() {
        this.layer.canvas.focus();
    }

    reloadChart() {
        this._chart.reloadChart();
    }

    updateVarEqsDuplicatedState() {
        const existingKeys = new Set<string>();

        for (const equation of this._chart.equations) {
            if (equation.equationType === EqType.ScopeVar && equation.isValid) {
                const eq = equation as VarEquation;
                const eqScope = eq.globalScope;

                eq.keyDuplicated = existingKeys.has(eqScope.key);
                existingKeys.add(eqScope.key);
            }
        }
    }
}

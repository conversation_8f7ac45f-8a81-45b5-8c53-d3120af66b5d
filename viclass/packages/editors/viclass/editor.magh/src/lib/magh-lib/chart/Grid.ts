import { MaghDocRenderProp } from '../../model';
import { calculatePiSpacing, calculateSpacing, drawLabel, drawLine, getLabelMeasure } from '../common/draw/utils';
import { EuclidianView } from './EuclidianView';
import { Drawable } from './models';

enum LineWeight {
    DetailGrid = 0.5,
    Grid = 1,
    Axis = 1.5,
}

enum LineColor {
    Grid = 'lightgray',
    Axis = 'black',
}

export class Grid implements Drawable {
    draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp) {
        const scale = view.getViewportScale();

        const leftChart = view.layerToChartX(view.left);
        const rightChart = view.layerToChartX(view.right);
        const topChart = view.layerToChartY(view.top);
        const bottomChart = view.layerToChartY(view.bottom);

        // Use π-based spacing when Pi Grid is enabled
        const spacing = drp?.usePiGrid
            ? calculatePiSpacing(view.docCtrl, view.viewport)
            : calculateSpacing(view.docCtrl, view.viewport);

        const noThinerLine = drp?.usePiGrid ? 4 : 5;

        const startX = leftChart > 0 ? Math.floor(leftChart / spacing) * spacing : 0;
        const endX = rightChart < 0 ? Math.floor(rightChart / spacing + 1) * spacing : 0;
        const startY = bottomChart > 0 ? Math.floor(bottomChart / spacing) * spacing : 0;
        const endY = topChart < 0 ? Math.floor(topChart / spacing + 1) * spacing : 0;

        ctx.setLineDash([]);

        for (let x = startX; x <= rightChart; x += spacing) {
            if (drp?.grid)
                drawLine(ctx, view.chartToLayerX(x), view.top, view.chartToLayerX(x), view.bottom, {
                    lineWeight: LineWeight.DetailGrid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis && drp?.showAxisLabels) {
                drawLine(
                    ctx,
                    view.chartToLayerX(x),
                    view.chartToLayerY(0),
                    view.chartToLayerX(x),
                    view.chartToLayerY(0) - 5 * scale,
                    {
                        lineWeight: LineWeight.Axis * scale,
                        color: LineColor.Axis,
                    }
                );
                this.drawAxisLabel(ctx, view.chartToLayerX(x), view.chartToLayerY(0), x, scale, 'x', drp);
            }
            if (drp?.detailGrid)
                for (let i = 0; i < noThinerLine; i++)
                    drawLine(
                        ctx,
                        view.chartToLayerX(x + (spacing * i) / noThinerLine),
                        view.top,
                        view.chartToLayerX(x + (spacing * i) / noThinerLine),
                        view.bottom,
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
        }
        for (let x = endX; x >= leftChart; x -= spacing) {
            if (drp?.grid)
                drawLine(ctx, view.chartToLayerX(x), view.top, view.chartToLayerX(x), view.bottom, {
                    lineWeight: LineWeight.Grid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis && drp?.showAxisLabels) {
                drawLine(
                    ctx,
                    view.chartToLayerX(x),
                    view.chartToLayerY(0),
                    view.chartToLayerX(x),
                    view.chartToLayerY(0) - 5 * scale,
                    {
                        lineWeight: LineWeight.Axis * scale,
                        color: LineColor.Axis,
                    }
                );
                this.drawAxisLabel(ctx, view.chartToLayerX(x), view.chartToLayerY(0), x, scale, 'x', drp);
            }
            if (drp?.detailGrid)
                for (let i = 0; i < 10; i++)
                    drawLine(
                        ctx,
                        view.chartToLayerX(x - (spacing * i) / noThinerLine),
                        view.top,
                        view.chartToLayerX(x - (spacing * i) / noThinerLine),
                        view.bottom,
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
        }

        for (let y = startY; y <= topChart; y += spacing) {
            if (drp?.grid)
                drawLine(ctx, view.right, view.chartToLayerY(y), view.left, view.chartToLayerY(y), {
                    lineWeight: LineWeight.Grid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis && drp?.showAxisLabels) {
                drawLine(
                    ctx,
                    view.chartToLayerX(0),
                    view.chartToLayerY(y),
                    view.chartToLayerX(0) - 5 * scale,
                    view.chartToLayerY(y),
                    {
                        lineWeight: LineWeight.Axis * scale,
                        color: LineColor.Axis,
                    }
                );
                this.drawAxisLabel(ctx, view.chartToLayerX(0), view.chartToLayerY(y), y, scale, 'y', drp);
            }
            if (drp?.detailGrid)
                for (let i = 0; i < 10; i++) {
                    drawLine(
                        ctx,
                        view.right,
                        view.chartToLayerY(y + (spacing * i) / noThinerLine),
                        view.left,
                        view.chartToLayerY(y + (spacing * i) / noThinerLine),
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
                }
        }
        for (let y = endY; y >= bottomChart; y -= spacing) {
            if (drp?.grid)
                drawLine(ctx, view.right, view.chartToLayerY(y), view.left, view.chartToLayerY(y), {
                    lineWeight: LineWeight.Grid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis && drp?.showAxisLabels) {
                drawLine(
                    ctx,
                    view.chartToLayerX(0),
                    view.chartToLayerY(y),
                    view.chartToLayerX(0) - 5 * scale,
                    view.chartToLayerY(y),
                    {
                        lineWeight: LineWeight.Axis * scale,
                        color: LineColor.Axis,
                    }
                );
                this.drawAxisLabel(ctx, view.chartToLayerX(0), view.chartToLayerY(y), y, scale, 'y', drp);
            }
            if (drp?.detailGrid)
                for (let i = 0; i < 10; i++) {
                    drawLine(
                        ctx,
                        view.right,
                        view.chartToLayerY(y - (spacing * i) / noThinerLine),
                        view.left,
                        view.chartToLayerY(y - (spacing * i) / noThinerLine),
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
                }
        }

        if (drp?.axis) {
            if (bottomChart < 0 && topChart > 0) {
                drawLine(ctx, view.left, view.chartToLayerY(0), view.right, view.chartToLayerY(0), {
                    lineWeight: LineWeight.Axis * scale,
                    color: LineColor.Axis,
                });

                // Draw arrow at the end of x-axis if showAxisArrows is true
                if (drp?.showAxisArrows) {
                    const arrowSize = 10 * scale;
                    ctx.beginPath();
                    ctx.moveTo(view.right, view.chartToLayerY(0));
                    ctx.lineTo(view.right - arrowSize, view.chartToLayerY(0) - arrowSize / 2);
                    ctx.lineTo(view.right - arrowSize, view.chartToLayerY(0) + arrowSize / 2);
                    ctx.closePath();
                    ctx.fillStyle = LineColor.Axis;
                    ctx.fill();
                }
            }

            if (leftChart < 0 && rightChart > 0) {
                drawLine(ctx, view.chartToLayerX(0), view.bottom, view.chartToLayerX(0), view.top, {
                    lineWeight: LineWeight.Axis * scale,
                    color: LineColor.Axis,
                });

                // Draw arrow at the end of y-axis if showAxisArrows is true
                if (drp?.showAxisArrows) {
                    const arrowSize = 10 * scale;
                    ctx.beginPath();
                    ctx.moveTo(view.chartToLayerX(0), view.top);
                    ctx.lineTo(view.chartToLayerX(0) - arrowSize / 2, view.top - arrowSize);
                    ctx.lineTo(view.chartToLayerX(0) + arrowSize / 2, view.top - arrowSize);
                    ctx.closePath();
                    ctx.fillStyle = LineColor.Axis;
                    ctx.fill();
                }
            }
        }
    }

    private drawAxisLabel(
        ctx: CanvasRenderingContext2D,
        x: number,
        y: number,
        label: string | number,
        scale: number,
        axis: 'x' | 'y',
        drp: MaghDocRenderProp
    ) {
        let displayLabel: string;

        if (typeof label === 'number') {
            if (drp?.usePiGrid && label !== 0) {
                // Convert to pi units
                const piValue = label / Math.PI;
                const roundedPiValue = Math.round(piValue * 100) / 100;

                if (Math.abs(roundedPiValue - 1) < 0.01) {
                    displayLabel = 'π';
                } else if (Math.abs(roundedPiValue + 1) < 0.01) {
                    displayLabel = '-π';
                } else if (Math.abs(roundedPiValue) < 0.01) {
                    displayLabel = '0';
                } else if (Math.abs(roundedPiValue - Math.round(roundedPiValue)) < 0.01) {
                    const intValue = Math.round(roundedPiValue);
                    displayLabel = intValue === 1 ? 'π' : intValue === -1 ? '-π' : `${intValue}π`;
                } else {
                    displayLabel = `${roundedPiValue}π`;
                }
            } else {
                displayLabel = String(Math.round(label * 100) / 100);
            }
        } else {
            displayLabel = label;
        }

        const fontSize = 14 * scale;
        const measure = getLabelMeasure(ctx, displayLabel, fontSize);
        const textOptions = {
            textColor: 'black',
            fontSize: fontSize,
        };

        if (displayLabel === '0') drawLabel(ctx, displayLabel, { x: x - 20 * scale, y: y - 20 * scale }, textOptions);
        else if (axis === 'x')
            drawLabel(ctx, displayLabel, { x: x - measure.width / 2, y: y - 20 * scale }, textOptions);
        else if (axis === 'y')
            drawLabel(ctx, displayLabel, { x: x - measure.width - 15 * scale, y: y - fontSize / 2 }, textOptions);
    }
}

import { isNil } from 'lodash';
import { MaghDocRenderProp } from '../../../model';
import { EuclidianView } from '../../chart/EuclidianView';
import { Destroyable, Drawable } from '../../chart/models';
import { Engine } from './Engine';
import { BoxedExpr, EqType, EquationStyle, GlobalScopeGetter, SerializedEquation } from './types';

/**
 * Wrapper for math expression.
 * Will parse the math expression into MathJSON, analyze the equation, and compile it.
 */
export abstract class Equation<S extends EquationStyle = EquationStyle> implements Drawable, Destroyable {
    static readonly X_VAR = 'x';
    static readonly Y_VAR = 'y';
    static readonly PARAMETRIC_VAR = 't';

    protected _raw!: string;
    protected _lhs?: BoxedExpr;
    protected _rhs?: BoxedExpr;
    protected _compiledLhs?: (args: Record<string, any>) => number | undefined;
    protected _compiledRhs?: (args: Record<string, any>) => number | undefined;

    public hidden = false;

    public get defaultExpression(): string {
        return '';
    }

    abstract get equationType(): EqType;

    public styles: S = <S>{
        color: 'red',
        lineWidth: 2,
    };

    get rawExpression(): string {
        return this._raw;
    }

    abstract get isValid(): boolean;

    get lhs(): BoxedExpr | undefined {
        return this.isValid ? this._lhs : undefined;
    }

    get rhs(): BoxedExpr | undefined {
        return this.isValid ? this._rhs : undefined;
    }

    constructor(
        protected engine: Engine,
        protected scopeGetter: GlobalScopeGetter
    ) {}

    destroy(): void {
        this.reset();
    }

    abstract updatePath(view: EuclidianView): void;

    drawBackground(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {} // default nothing to draw

    abstract draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void;

    drawLabels(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {} // default nothing to draw

    public getEvaluateScope() {
        return this.scopeGetter(this);
    }

    public setExpression(rawExpr: string): boolean {
        this._raw = rawExpr;
        if (!rawExpr) return false;

        return true;
    }

    public evaluateLhs(scope: Record<string, any>): number {
        if (!this.isValid || !this._compiledLhs) return NaN;
        const globalScope = this.getEvaluateScope();
        return (
            this._compiledLhs({
                ...globalScope,
                ...scope,
            }) ?? NaN
        );
    }

    public evaluateRhs(scope: Record<string, any>): number {
        if (!this.isValid || !this._compiledRhs) return NaN;
        const globalScope = this.getEvaluateScope();
        return (
            this._compiledRhs({
                ...globalScope,
                ...scope,
            }) ?? NaN
        );
    }

    protected reset(): void {
        this._lhs = undefined;
        this._rhs = undefined;
        this._compiledLhs = undefined;
        this._compiledRhs = undefined;
    }

    serialize(): SerializedEquation {
        return <SerializedEquation>{
            type: this.equationType,
            expression: this.rawExpression,
            hidden: this.hidden,
            styles: this.styles as EquationStyle,
        };
    }

    deserialize(serialized: SerializedEquation): void {
        this.setExpression(serialized.expression);

        if (!isNil(serialized.hidden)) {
            this.hidden = serialized.hidden;
        }
        if (!isNil(serialized.styles)) {
            this.styles = serialized.styles as S;
        }
    }
}

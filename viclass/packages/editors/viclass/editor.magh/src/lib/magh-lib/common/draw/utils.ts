import { htmlToCanvas } from '@viclass/editor.coordinator/common';
import { BaseBoardViewportManager, Position, Rectangle, ViewportManager } from '@viclass/editor.core';
import { convertLatexToMarkup } from 'lib-mathlive';
import { MathGraphDocCtrl } from '../../../docs/magh.doc.ctrl';
import { EuclidianView } from '../../chart';

// Determine the orientation of the triplet (p1, p2, q)
function orientation(p: Position, q: Position, r: Position) {
    const val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
    if (val === 0) return 0; // Collinear
    return val > 0 ? 1 : 2; // Clock or counterclockwise
}

// Check if point q lies on line segment pr
function onSegment(p: Position, q: Position, r: Position) {
    return (
        q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y)
    );
}

/**
 * MARK: does line intersect line
 * Checks if two line segments intersect.
 *
 * @param {number[]} p1 - The first point of the first line segment.
 * @param {number[]} p2 - The second point of the first line segment.
 * @param {number[]} q1 - The first point of the second line segment.
 * @param {number[]} q2 - The second point of the second line segment.
 * @return {boolean} Returns true if the line segments intersect, false otherwise.
 */
export function doesLineIntersectLine(p1: Position, p2: Position, q1: Position, q2: Position): boolean {
    const o1 = orientation(p1, p2, q1);
    const o2 = orientation(p1, p2, q2);
    const o3 = orientation(q1, q2, p1);
    const o4 = orientation(q1, q2, p2);

    if (o1 !== o2 && o3 !== o4) return true;

    if (o1 === 0 && onSegment(p1, q1, p2)) return true;
    if (o2 === 0 && onSegment(p1, q2, p2)) return true;
    if (o3 === 0 && onSegment(q1, p1, q2)) return true;
    if (o4 === 0 && onSegment(q1, p2, q2)) return true;

    return false;
}

/**
 * MARK: does line intersect rectangle
 * Checks if a line segment intersects a rectangle.
 *
 * @param {number} c1 - The coordinates of the starting point of the line segment.
 * @param {number} c2 - The coordinates of the ending point of the line segment.
 * @param {number} r1 - The coordinates of the top-left corner of the rectangle.
 * @param {number} r2 - The coordinates of the bottom-right corner of the rectangle.
 * @return {boolean} Returns true if the line segment intersects the rectangle, otherwise returns false.
 */
export function doesLineIntersectRectangle(c1: Position, c2: Position, r1: Position, r2: Position): boolean {
    // Rectangle corners
    const topLeft = r1;
    const topRight = { x: r2.x, y: r1.y };
    const bottomLeft = { x: r1.x, y: r2.y };
    const bottomRight = r2;

    // Check if the line intersects any of the rectangle's sides
    if (
        doesLineIntersectLine(c1, c2, topLeft, topRight) ||
        doesLineIntersectLine(c1, c2, topLeft, bottomLeft) ||
        doesLineIntersectLine(c1, c2, bottomLeft, bottomRight) ||
        doesLineIntersectLine(c1, c2, topRight, bottomRight)
    ) {
        return true;
    }

    return false;
}

/**
 * MARK: does line intersect ellipse
 * Checks if a line segment intersects an ellipse segment.
 *
 * @param {number} c1 - The coordinates of the starting point of the line segment.
 * @param {number} c2 - The coordinates of the ending point of the line segment.
 * @param {number} e - The coordinates of the center of the ellipse.
 * @param {number} a - The horizontal radius of the ellipse.
 * @param {number} b - The vertical radius of the ellipse.
 * @param {number} ellipseAngle - The angle of rotation of the ellipse in radians.
 * @param {number} startAngle - The starting angle of the ellipse segment in radians.
 * @param {number} endAngle - The ending angle of the ellipse segment in radians.
 * @param {boolean} isClockwise - Indicates whether the ellipse segment is in clockwise or counter-clockwise direction.
 * @return {boolean} Returns true if the line segment intersects the ellipse segment, otherwise returns false.
 */
export function doesLineIntersectEllipseSegment(
    c1: Position,
    c2: Position,
    e: Position,
    a: number,
    b: number,
    ellipseAngle: number, // radian
    startAngle: number, // radian
    endAngle: number, // radian
    isClockwise: boolean // true nếu cung ellipse theo chiều kim đồng hồ, false nếu ngược chiều
): boolean {
    // Biến đổi tọa độ
    const cosAngle = Math.cos(-ellipseAngle);
    const sinAngle = Math.sin(-ellipseAngle);

    const dx1 = c1.x - e.x;
    const dy1 = c1.y - e.y;
    const dx2 = c2.x - e.x;
    const dy2 = c2.y - e.y;

    const x1Rot = dx1 * cosAngle - dy1 * sinAngle;
    const y1Rot = dx1 * sinAngle + dy1 * cosAngle;
    const x2Rot = dx2 * cosAngle - dy2 * sinAngle;
    const y2Rot = dx2 * sinAngle + dy2 * cosAngle;

    // Tính toán giao điểm
    const A = (x2Rot - x1Rot) ** 2 / (a * a) + (y2Rot - y1Rot) ** 2 / (b * b);
    const B = (2 * x1Rot * (x2Rot - x1Rot)) / (a * a) + (2 * y1Rot * (y2Rot - y1Rot)) / (b * b);
    const C = x1Rot ** 2 / (a * a) + y1Rot ** 2 / (b * b) - 1;

    const discriminant = B * B - 4 * A * C;

    if (discriminant < 0) return false;

    const t1 = (-B + Math.sqrt(discriminant)) / (2 * A);
    const t2 = (-B - Math.sqrt(discriminant)) / (2 * A);

    // Kiểm tra xem giao điểm có nằm trong đoạn thẳng và cung ellipse
    if (!(0 <= t1 && t1 <= 1) && !(0 <= t2 && t2 <= 1)) return false;

    for (const t of [t1, t2]) {
        if (0 <= t && t <= 1) {
            const xIntersect = x1Rot + t * (x2Rot - x1Rot);
            const yIntersect = y1Rot + t * (y2Rot - y1Rot);

            let angle = Math.atan2(yIntersect, xIntersect);
            if (angle < 0) angle += 2 * Math.PI;

            if (isClockwise)
                if (startAngle <= angle && angle <= endAngle) return true;
                else if (endAngle <= angle && angle <= startAngle) return true;
        }
    }

    return false;
}

/**
 * MARK: does rectangle intersect ellipse segment
 * Checks if a rectangle intersects an ellipse segment.
 *
 * @param {number} c1 - The coordinates of the top-left corner of the rectangle.
 * @param {number} c2 - The coordinates of the bottom-right corner of the rectangle.
 * @param {number} e - The coordinates of the center of the ellipse.
 * @param {number} a - The horizontal radius of the ellipse.
 * @param {number} b - The vertical radius of the ellipse.
 * @param {number} ellipseAngle - The angle of rotation of the ellipse in radians.
 * @param {number} startAngle - The starting angle of the ellipse segment in radians.
 * @param {number} endAngle - The ending angle of the ellipse segment in radians.
 * @param {boolean} isClockwise - Indicates whether the ellipse segment is in clockwise or counter-clockwise direction.
 * @return {boolean} Returns true if the rectangle intersects the ellipse segment, otherwise returns false.
 */
export function doesRectangleIntersectEllipseSegment(
    c1: Position,
    c2: Position,
    e: Position,
    a: number,
    b: number,
    ellipseAngle: number, // radian
    startAngle: number, // radian
    endAngle: number, // radian
    isClockwise: boolean
): boolean {
    const checkLines = [
        [c1.x, c1.y, c2.x, c1.y],
        [c1.x, c1.y, c1.x, c2.y],
        [c1.x, c2.y, c2.x, c2.y],
        [c2.x, c1.y, c2.x, c2.y],
    ];
    for (const line of checkLines) {
        const [x1, y1, x2, y2] = line;
        if (
            doesLineIntersectEllipseSegment(
                { x: x1, y: y1 },
                { x: x2, y: y2 },
                e,
                a,
                b,
                ellipseAngle,
                startAngle,
                endAngle,
                isClockwise
            )
        )
            return true;
    }

    return false;
}

/**
 * MARK: calculate bottom left by angle xy size
 * Calculates the bottom-left coordinates of a label based on its angle, x and y coordinates,
 * label width, and label height.
 *
 * @param {number} angle - The angle of the label in degrees.
 * @param {number} l - The coordinates of the label.
 * @param {number} labelWidth - The width of the label.
 * @param {number} labelHeight - The height of the label.
 * @return {{x: number, y: number}} An object with the x and y coordinates of the bottom-left point of the label,
 * or null if the angle is not within the range of 0 to 360.
 */
export function calculateBottomLeftByAngleXYSize(
    angle: number,
    l: Position,
    labelWidth: number,
    labelHeight: number
): { x: number; y: number } {
    // các trường hợp right, bottom, left, top
    if (360 - 3 < angle || angle < 3) {
        return { x: l.x, y: l.y - labelHeight / 2 }; // x, y will be center-left of the label
    } else if (90 - 3 < angle && angle < 90 + 3) {
        return { x: l.x - labelWidth / 2, y: l.y }; // x, y will be bottom-center of the label
    } else if (180 - 3 < angle && angle < 180 + 3) {
        return { x: l.x - labelWidth, y: l.y - labelHeight / 2 }; // x, y will be center-right of the label
    } else if (270 - 3 < angle && angle < 270 + 3) {
        return { x: l.x - labelWidth / 2, y: l.y - labelHeight }; // x, y will be top-center of the label
    }

    if (angle > 0 && angle < 90) return l; // x, y will be top-left of the label
    if (angle > 90 && angle < 180) return { x: l.x - labelWidth, y: l.y }; // x, y will be top-right of the label
    if (angle > 180 && angle < 270) return { x: l.x - labelWidth, y: l.y - labelHeight }; // x, y will be bottom-right of the label
    if (angle > 270 && angle < 360) return { x: l.x, y: l.y - labelHeight }; // x, y will be bottom-left of the label

    return { x: l.x - labelWidth, y: l.y + labelHeight }; // default top-left
}

/**
 * MARK: get xy from angle and vertex
 * Calculates the x and y coordinates based on the given angle, vertex coordinates, and radius.
 *
 * @param {number} angle - The angle in degrees.
 * @param {number} c - The coordinates of the vertex.
 * @param {number} radius - The radius.
 * @return {{x: number, y: number, angle: number}} An object containing the calculated x and y coordinates and the angle.
 */
export function getXYFromAngleAndVertex(
    angle: number,
    v: Position,
    radius: number
): { x: number; y: number; angle: number } {
    angle %= 360;
    const radian = angle * (Math.PI / 180); // Chuyển đổi độ sang radian
    const pointX = v.x + radius * Math.cos(radian);
    const pointY = v.y + radius * Math.sin(radian);
    return { x: pointX, y: pointY, angle };
}

/**
 * MARK: get label center points from vertex
 * Generates the center points of labels for a given vertex.
 *
 * @param {number} v - The coordinates of the vertex.
 * @param {number} radius - The radius of the vertex.
 * @param {number} initialAngle - The initial angle of the vertex (radian).
 * @yields {Object} - The center point and angle of each label.
 */
export function* getLabelCenterPointsFromVertex(
    v: Position,
    radius: number,
    initialAngle: number // radian
) {
    yield getXYFromAngleAndVertex(initialAngle, v, radius);
    const angleIncrement = 22.5; // Góc giữa các điểm
    const step = Math.floor(360 / angleIncrement);
    let angle = initialAngle != null ? initialAngle : 225; // Góc của điểm đầu tiên
    for (let i = 0; i < step; i++) {
        angle %= 360;
        yield getXYFromAngleAndVertex(angle, v, radius);
        angle += angleIncrement;
    }
}

export function getLabelMeasure(ctx: CanvasRenderingContext2D, content: string, fontSize: number): TextMetrics {
    ctx.save();
    ctx.beginPath();

    ctx.font = fontSize + 'px Montserrat';

    const labelMetrics = ctx.measureText(content);

    ctx.closePath();
    ctx.restore();

    return labelMetrics;
}

export function drawLabel(
    ctx: CanvasRenderingContext2D,
    content: string,
    bottomLeft: Position,
    options?: {
        fontSize?: number;
        textColor?: string;
        radAngleFromXAxis?: number; // 0 - 360
        flipVertical?: boolean;
        flipHorizontal?: boolean;
        padding?: {
            top?: number;
            left?: number;
        };
        strokeText?: boolean;
    }
) {
    const fontSize = options?.fontSize ?? 16;
    const textColor = options?.textColor ?? 'black';
    const radAngleFromXAxis = options?.radAngleFromXAxis ?? 0;
    const paddingTop = options?.padding?.top ?? 0;
    const paddingLeft = options?.padding?.left ?? 0;
    const flipVertical = options?.flipVertical ?? false;
    const flipHorizontal = options?.flipHorizontal ?? false;
    const labelMeasure = getLabelMeasure(ctx, content, fontSize);
    const withStrokeText = options.strokeText || options.strokeText === undefined;

    ctx.save();
    ctx.beginPath();

    ctx.font = fontSize + 'px Montserrat';
    ctx.fillStyle = textColor;

    ctx.translate(bottomLeft.x, bottomLeft.y);
    ctx.rotate(radAngleFromXAxis - Math.PI);
    ctx.scale(flipHorizontal ? 1 : -1, flipVertical ? -1 : 1);

    // draw stroke text
    if (withStrokeText) {
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.strokeText(content, paddingLeft, paddingTop);
    }

    ctx.fillText(content, flipHorizontal ? -(paddingLeft + labelMeasure.width) : paddingLeft, paddingTop);

    ctx.closePath();
    ctx.restore();
}

export function drawLine(
    ctx: CanvasRenderingContext2D,
    x1: number,
    y1: number,
    x2: number,
    y2: number,
    options: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
        withoutSave?: boolean;
    }
) {
    const color = options?.color ?? 'black';
    const lineWeight = options?.lineWeight ?? 1;
    const withoutSave = options?.withoutSave ?? false;

    if (!withoutSave) ctx.save(); // Save the current context state
    ctx.beginPath();

    if (options?.lineDash) ctx.setLineDash(options.lineDash);
    ctx.lineWidth = lineWeight;
    ctx.strokeStyle = color;

    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();

    ctx.closePath();
    if (!withoutSave) ctx.restore();
}

export function drawRect(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    options: {
        fillColor?: string;
        strokeColor?: string;
        lineWeight?: number;
        lineDash?: number[];
        withoutSave?: boolean;
    }
) {
    const fillColor = options?.fillColor ?? 'transparent';
    const strokeColor = options?.strokeColor ?? 'black';
    const lineWeight = options?.lineWeight ?? 1;
    const withoutSave = options?.withoutSave ?? false;

    if (!withoutSave) ctx.save(); // Save the current context state
    ctx.beginPath();

    if (options?.lineDash?.length) ctx.setLineDash(options.lineDash);
    ctx.fillStyle = fillColor;
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = lineWeight;

    ctx.rect(x, y, width, height);
    ctx.fill();
    ctx.stroke();
    if (!withoutSave) ctx.restore();
}

export function drawArc(
    ctx: CanvasRenderingContext2D,
    startCoords: Position,
    radius: number,
    startAngle: number,
    endAngle: number,
    options: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
    }
) {
    const color = options?.color ?? 'black';
    const lineWeight = options?.lineWeight ?? 1;

    ctx.save();
    ctx.beginPath();

    if (options?.lineDash?.length) ctx.setLineDash(options.lineDash);
    ctx.lineWidth = lineWeight;
    ctx.strokeStyle = color;

    ctx.moveTo(startCoords.x, startCoords.y);
    ctx.arc(startCoords.x, startCoords.y, radius, startAngle, endAngle, false);
    ctx.stroke();

    ctx.closePath();
    ctx.restore();
}

export function drawParallelLines(
    ctx: CanvasRenderingContext2D,
    start: Position,
    end: Position,
    padding: number = 6, // Define Distance for Parallel Lines (in pixels). Only for `rep` > 1
    rep: number = 1, // Replica
    options?: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
    }
) {
    if (rep < 1 || rep !== Math.floor(rep)) return;

    if (rep === 1) {
        drawLine(ctx, start.x, start.y, end.x, end.y, options);
        return;
    }

    // Calculate Direction Vector
    const dx = start.x - end.x;
    const dy = start.y - end.y;

    // Calculate Perpendicular Vector
    const perpVectorX = -dy;
    const perpVectorY = dx;

    // Normalize Perpendicular Vector
    const magnitude = Math.sqrt(perpVectorX * perpVectorX + perpVectorY * perpVectorY);
    const normalizedPerpX = perpVectorX / magnitude;
    const normalizedPerpY = perpVectorY / magnitude;

    const initPerCoords =
        rep % 2 !== 0
            ? [start, end]
            : [
                  {
                      x: start.x - normalizedPerpX * (padding / 2),
                      y: start.y - normalizedPerpY * (padding / 2),
                  },
                  {
                      x: end.x - normalizedPerpX * (padding / 2),
                      y: end.y - normalizedPerpY * (padding / 2),
                  },
              ];

    const curPerCoords = [
        {
            x: initPerCoords[0].x,
            y: initPerCoords[0].y,
        },
        {
            x: initPerCoords[1].x,
            y: initPerCoords[1].y,
        },
    ]; // Clone initPerCoords
    let jumpSide: 'left' | 'right' = 'right';

    for (let i = 0; i < rep; i++) {
        curPerCoords[0].x += normalizedPerpX * (padding * i) * (jumpSide === 'right' ? -1 : 1);
        curPerCoords[0].y += normalizedPerpY * (padding * i) * (jumpSide === 'right' ? -1 : 1);
        curPerCoords[1].x += normalizedPerpX * (padding * i) * (jumpSide === 'right' ? -1 : 1);
        curPerCoords[1].y += normalizedPerpY * (padding * i) * (jumpSide === 'right' ? -1 : 1);

        drawLine(ctx, curPerCoords[0].x, curPerCoords[0].y, curPerCoords[1].x, curPerCoords[1].y, options);

        jumpSide = jumpSide === 'right' ? 'left' : 'right'; // Switch side
    }
}

export function getAngleWithOx(c1: Position, c2: Position) {
    const deltaX = c2.x - c1.x;
    const deltaY = c2.y - c1.y;

    let angleInRadians = Math.atan2(deltaY, deltaX);

    // Chuyển góc về khoảng từ 0 đến 2π (nếu cần)
    angleInRadians = angleInRadians >= 0 ? angleInRadians : angleInRadians + 2 * Math.PI;

    // chuyển góc về độ
    return (angleInRadians * 180) / Math.PI;
}

export function calculateZoomLevel(docCtrl: MathGraphDocCtrl, viewport: ViewportManager) {
    return ((viewport as BaseBoardViewportManager)?.zoomLevel ?? 1) / (docCtrl.state.docRenderProp?.scale ?? 1);
}

export function calculateSpacing(docCtrl: MathGraphDocCtrl, viewport: ViewportManager) {
    const noThinerLine = 1;
    const zoomLevel = calculateZoomLevel(docCtrl, viewport);

    // Adjust grid spacing based on zoom level
    let spacing = noThinerLine;
    const ratioToRescaleSpacing = 1.25;
    while (spacing / zoomLevel / noThinerLine >= ratioToRescaleSpacing) spacing /= 2;
    while (noThinerLine / (spacing / zoomLevel) >= ratioToRescaleSpacing) spacing *= 2;

    if (spacing <= 0.1) spacing = 0.1;
    else if (spacing <= 0.2) spacing = 0.2;
    else if (spacing <= 0.5) spacing = 0.5;
    else spacing = Math.round(spacing);

    return spacing;
}

/**
 * Calculate π-based spacing for Pi Grid mode
 * Returns spacing values like π, π/2, π/4, 2π, 4π based on zoom level
 */
export function calculatePiSpacing(docCtrl: MathGraphDocCtrl, viewport: ViewportManager) {
    const basePi = Math.PI;
    const zoomLevel = calculateZoomLevel(docCtrl, viewport);

    // Start with π as base spacing
    let spacing = basePi;
    const ratioToRescaleSpacing = 1.25;

    // Adjust π spacing based on zoom level - similar logic to calculateSpacing
    while (spacing / zoomLevel >= ratioToRescaleSpacing * basePi) spacing /= 2;
    while (basePi / (spacing / zoomLevel) >= ratioToRescaleSpacing) spacing *= 2;

    // Snap to common π fractions/multiples for clean display
    if (spacing <= basePi / 8)
        spacing = basePi / 8; // π/8
    else if (spacing <= basePi / 4)
        spacing = basePi / 4; // π/4
    else if (spacing <= basePi / 2)
        spacing = basePi / 2; // π/2
    else if (spacing <= basePi)
        spacing = basePi; // π
    else spacing = Math.min(Math.round(spacing / basePi), 8) * basePi; // nπ where n is integer

    return spacing;
}

export function calculateAngle(vStart: number[], vEnd: number[]) {
    const startAngleInRad = Math.atan2(vStart[1], vStart[0]);
    const endAngleInRad = (() => {
        let angle = Math.atan2(vEnd[1], vEnd[0]);
        while (angle > startAngleInRad) angle -= 2 * Math.PI;
        return angle;
    })(); // normalize by startAngleInRad
    return (() => {
        let angle = Math.round(Math.abs(((Math.abs(startAngleInRad - endAngleInRad) - 2 * Math.PI) / Math.PI) * 180));
        while (angle < 0) angle += 360;
        while (angle > 360) angle -= 360;
        return angle;
    })(); // normalize by 360 degree
}

/**
 * Draw 45-degree crosshatch lines in the specified region
 */
export function drawCrosshatch(
    ctx: CanvasRenderingContext2D,
    view: EuclidianView,
    region: Rectangle,
    spacing: number
): void {
    const { start, end } = region;
    const regionWidth = end.x - start.x;
    const regionHeight = end.y - start.y;

    // Draw diagonal lines from bottom-left to top-right
    for (let offset = -regionHeight; offset <= regionWidth + regionHeight; offset += spacing) {
        const lineStart: Position = { x: start.x + offset, y: start.y };
        const lineEnd: Position = { x: start.x + offset + regionHeight, y: end.y };

        // Clip line to the region boundaries
        const clippedLine = clipLineToRegion(lineStart, lineEnd, region);
        if (clippedLine) {
            drawLine(
                ctx,
                view.chartToLayerX(clippedLine.start.x),
                view.chartToLayerY(clippedLine.start.y),
                view.chartToLayerX(clippedLine.end.x),
                view.chartToLayerY(clippedLine.end.y),
                { lineWeight: ctx.lineWidth, color: ctx.strokeStyle as string }
            );
        }
    }

    // Draw diagonal lines from top-left to bottom-right
    for (let offset = -regionHeight; offset <= regionWidth + regionHeight; offset += spacing) {
        const lineStart: Position = { x: start.x + offset, y: end.y };
        const lineEnd: Position = { x: start.x + offset + regionHeight, y: start.y };

        // Clip line to the region boundaries
        const clippedLine = clipLineToRegion(lineStart, lineEnd, region);
        if (clippedLine) {
            drawLine(
                ctx,
                view.chartToLayerX(clippedLine.start.x),
                view.chartToLayerY(clippedLine.start.y),
                view.chartToLayerX(clippedLine.end.x),
                view.chartToLayerY(clippedLine.end.y),
                { lineWeight: ctx.lineWidth, color: ctx.strokeStyle as string }
            );
        }
    }
}

/**
 * Clip a line to fit within the specified rectangular region
 */
function clipLineToRegion(lineStart: Position, lineEnd: Position, region: Rectangle): Rectangle | null {
    // Simple line clipping using parametric form
    let t1 = 0,
        t2 = 1;
    const dx = lineEnd.x - lineStart.x;
    const dy = lineEnd.y - lineStart.y;

    // Check each boundary
    const boundaries = [
        { normal: -dx, distance: lineStart.x - region.start.x }, // left boundary
        { normal: dx, distance: region.end.x - lineStart.x }, // right boundary
        { normal: -dy, distance: lineStart.y - region.start.y }, // bottom boundary
        { normal: dy, distance: region.end.y - lineStart.y }, // top boundary
    ];

    for (const { normal, distance } of boundaries) {
        if (normal === 0) {
            if (distance < 0) return null; // Line is parallel to and outside boundary
        } else {
            const t = distance / normal;
            if (normal < 0) {
                t1 = Math.max(t1, t); // Entry point
            } else {
                t2 = Math.min(t2, t); // Exit point
            }
            if (t1 > t2) return null; // No intersection with region
        }
    }

    // Calculate clipped line endpoints
    return {
        start: {
            x: lineStart.x + t1 * dx,
            y: lineStart.y + t1 * dy,
        },
        end: {
            x: lineStart.x + t2 * dx,
            y: lineStart.y + t2 * dy,
        },
    };
}

/**
 * Converts LaTeX mathematical expressions to HTML element for DOM overlay rendering
 *
 * This function takes a LaTeX string and converts it into an HTML element that can be
 * positioned as an overlay on the graph. It's useful for displaying mathematical formulas
 * and equations as DOM elements instead of canvas-rendered content.
 */
export function latexToElement(
    latex: string,
    options: {
        color?: string;
        fontSize?: number;
    } = {}
): HTMLElement {
    if (options?.color) latex = `\\textcolor{${options.color}}{${latex}}`;

    const markup = convertLatexToMarkup(latex);
    const elem = document.createElement('span');
    elem.innerHTML = markup;

    if (options?.fontSize) elem.style.fontSize = `${options.fontSize}px`;

    // Configure element for overlay positioning
    elem.style.position = 'absolute';
    elem.style.pointerEvents = 'none';
    elem.style.userSelect = 'none';
    elem.style.whiteSpace = 'nowrap';
    elem.style.transformOrigin = 'top left';

    return elem;
}

/**
 * Find optimal label position to avoid clipping
 */
export function findOptimalLabelPosition(
    origin: Position,
    labelWidth: number,
    labelHeight: number,
    view: EuclidianView,
    padding: number
): { bottomLeft: Position; topLeft: Position } {
    // Define possible angles for label placement (in degrees)
    // Priority order: right, bottom-right, top-right, bottom, top, bottom-left, top-left, left
    const candidateAngles = [0, 315, 45, 270, 90, 225, 135, 180];

    for (const angle of candidateAngles) {
        const bottomLeft = calculateBottomLeftByAngleXYSize(angle, origin, labelWidth, labelHeight);
        const topLeft = { x: bottomLeft.x, y: bottomLeft.y - labelHeight };
        const bottomRight = { x: bottomLeft.x + labelWidth, y: bottomLeft.y };

        // Check if label fits within view boundaries with padding
        if (isFitOnView(view, padding, topLeft, bottomRight)) {
            return { bottomLeft, topLeft };
        }
    }

    // If no position fits perfectly, use constrained positioning
    // Start with default position (bottom-right of origin)
    const bottomLeft = calculateBottomLeftByAngleXYSize(315, origin, labelWidth, labelHeight);

    // Constrain horizontally
    if (bottomLeft.x < view.left + padding) {
        bottomLeft.x = view.left + padding;
    } else if (bottomLeft.x + labelWidth > view.right - padding) {
        bottomLeft.x = view.right - padding - labelWidth;
    }

    // Constrain vertically
    if (bottomLeft.y - labelHeight > view.top - padding) {
        bottomLeft.y = view.top - padding - labelHeight;
    } else if (bottomLeft.y < view.bottom + padding) {
        bottomLeft.y = view.bottom + padding;
    }

    const topLeft = { x: bottomLeft.x, y: bottomLeft.y + labelHeight };
    return { bottomLeft, topLeft };
}

function isFitOnView(view: EuclidianView, padding: number, topLeft: Position, bottomRight: Position): boolean {
    const fitsHorizontally = topLeft.x >= view.left + padding && bottomRight.x <= view.right - padding;
    const fitsVertically = topLeft.y <= view.top - padding && bottomRight.y >= view.bottom + padding;
    return fitsHorizontally && fitsVertically;
}

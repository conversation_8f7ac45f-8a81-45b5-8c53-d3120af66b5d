import { DocumentId } from '@viclass/editor.core';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphEditor } from '../magh.api';
import { MathGraphTool } from './magh.tool';
import { MathGraphSettingsState, MathGraphToolType } from './models';

export class MathGraphSettingsTool extends MathGraphTool<MathGraphSettingsState> {
    override toolType: MathGraphToolType = 'MaghSettingsTool';
    override toolState: MathGraphSettingsState = new MathGraphSettingsState();

    private focusedDocId?: DocumentId;

    constructor(editor: MathGraphEditor) {
        super(editor);
    }

    override onEnable(): void {
        this.onDocAttached();
    }

    override onDisable(): void {
        this.onDocDetached();
    }

    onDocAttached(docCtrl?: MathGraphDocCtrl): void {
        docCtrl = docCtrl ? docCtrl : this.getFocusedMathGraphDocCtrls()?.[0];
        if (docCtrl) {
            this.focusedDocId = docCtrl.state.globalId;
            this.reloadToolState();
        }
    }

    onDocDetached(docCtrl?: MathGraphDocCtrl): void {
        if (!docCtrl || docCtrl.state.globalId === this.focusedDocId) {
            this.focusedDocId = undefined;
        }
    }

    updateSetting(changes: Record<string, any>) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => {
            docCtrl.updateDocRenderProp(changes);
            this.reloadToolState();
        }, true);
    }

    private getSettingsState(): MathGraphSettingsState {
        const state = new MathGraphSettingsState();

        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => {
            const renderProps = docCtrl.state.docRenderProp;
            state.axis = renderProps.axis;
            state.grid = renderProps.grid;
            state.detailGrid = renderProps.detailGrid;
            state.showAxisArrows = renderProps.showAxisArrows;
            state.showAxisLabels = renderProps.showAxisLabels;
            state.usePiGrid = renderProps.usePiGrid;
            state.lineStyle = renderProps.lineStyle;
            state.lineWidth = renderProps.lineWidth;
            state.opacity = renderProps.opacity;
        });

        return state;
    }

    private changeToolState(newState: MathGraphSettingsState) {
        this.toolState = { ...newState };
        this.toolbar.update('MaghSettingsTool', this.toolState);
    }

    private reloadToolState() {
        this.changeToolState(this.getSettingsState());
    }
}

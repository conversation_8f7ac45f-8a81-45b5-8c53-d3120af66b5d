import { MouseEventData, NativeEventTarget, ToolEventData, ToolState } from '@viclass/editor.core';
import { Domain, EqType, RangeScope, ScopeVariable, SerializedEquation } from '../magh-lib';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphToolBar } from './magh.toolbar';

export type MathGraphToolType =
    | 'CreateMathGraphDocumentTool'
    | 'ContentEditorTool'
    | 'UpdateEquationsTool'
    | 'MaghZoomTool'
    | 'MaghPanTool'
    | 'MaghSettingsTool';

export type MathGraphToolEventData = ToolEventData<MathGraphToolBar, MathGraphToolType>;

export type HeadingWrapperStyle = {
    fontFamily: string;
    fontSize: string;
    fontColor: string;
    backgroundColor: string;
    isBold: boolean;
    isItalic: boolean;
    isUnderline: boolean;
    isStrikethrough: boolean;
};

export class MaghZoomToolState implements ToolState {
    zoomLevel: number;
}
export class MaghPanToolState implements ToolState {
    doc: MathGraphDocCtrl;
}

export type MaghMouseEvent = MouseEventData<NativeEventTarget<any>>;

export type EquationState = SerializedEquation & {
    index: number;
    isValid: boolean;
    defaultValue: string;
} & (
        | {
              equationType: EqType.Plot;
              isImplicit: boolean;
              isParametricArc: boolean;

              supportInternalScope: boolean;
              internalScope: RangeScope;
              domain: Domain;

              // Label properties
              showLabel?: boolean;
              customLabel?: string;
          }
        | {
              equationType: EqType.ScopeVar;
              globalScope: ScopeVariable;
              keyDuplicated: boolean;
          }
        | {
              equationType: EqType.Point;
          }
    );

export class EquationsState implements ToolState {
    equations: EquationState[] = [];
    colors: string[] = [];
}
export class MathGraphSettingsState implements ToolState {
    axis: boolean = true;
    grid: boolean = true;
    detailGrid: boolean = false;
    showAxisArrows: boolean = false;
    showAxisLabels: boolean = true;
    usePiGrid: boolean = false;
    lineStyle: string = '';
    lineWidth: number = 2;
    opacity: number = 1;
}

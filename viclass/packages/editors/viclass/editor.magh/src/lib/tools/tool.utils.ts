import { Position, ScreenPosition } from '@viclass/editor.core';
import { MaghDocRenderProp } from '../model';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphEditor } from '../magh.api';

export function defaultDocRenderProp(): MaghDocRenderProp {
    const docRenderProp = <MaghDocRenderProp>{
        scale: 1,
        screenUnit: 50,

        translation: [0, 0, 0],
        rotation: [0, 0, 0],

        // grid
        axis: true,
        grid: true,
        detailGrid: false,
        showAxisArrows: false,
        showAxisLabels: true,
        usePiGrid: false,

        lineStyle: 'solid',
        lineWidth: 1,
        opacity: 0.5,
    };

    return docRenderProp;
}

export function calculatePosInLayer(pos: Position, docCtrl: MathGraphDocCtrl): ScreenPosition {
    if (!docCtrl) throw 'doc is undefined';
    if ((docCtrl.editor as MathGraphEditor).conf.docViewMode == 'bounded') {
        const bd = docCtrl.state.layer.boundary;

        const tx = Math.min(bd.start.x, bd.end.x);
        const ty = Math.max(bd.start.y, bd.end.y);
        const w = bd.width;
        const h = bd.height;

        const center: Position = { x: tx + w / 2, y: ty - h / 2 }; // center point in board coordinate
        const posInLayer: Position = {
            x: pos.x - center.x,
            y: pos.y - center.y,
        };

        return posInLayer;
    } else return pos;
}

export function validatePointerPos(pos: Position, doc: MathGraphDocCtrl): boolean {
    if (!doc) return false;
    if ((doc.editor as MathGraphEditor).conf.docViewMode == 'bounded') {
        const bd = doc.state.layer.boundary;

        const tx = Math.min(bd.start.x, bd.end.x);
        const ty = Math.max(bd.start.y, bd.end.y);

        const w = bd.width;
        const h = bd.height;

        const bx = tx + w;
        const by = ty - h;

        // check pos inside document boundary or not
        return pos.x > tx && pos.x < bx && pos.y > by && pos.y < ty;
    } else return true;
}

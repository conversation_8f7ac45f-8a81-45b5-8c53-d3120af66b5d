import { DocumentId } from '@viclass/editor.core';
import { Subscription } from 'rxjs';
import { PlotEquation, PointEquation, VarEquation } from '../magh-lib';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { Domain, EqType, EquationStyle, SerializedGlobalScope, SerializedInternalScope } from '../magh.api';
import { MathGraphTool } from './magh.tool';
import { EquationsState, EquationState, MathGraphToolType } from './models';

const DEFAULT_TOOLSTATE: EquationsState = {
    equations: [],
    colors: [],
};

export class UpdateEquationsTool extends MathGraphTool<EquationsState> {
    override toolType: MathGraphToolType = 'UpdateEquationsTool';
    override toolState: EquationsState = { ...DEFAULT_TOOLSTATE };
    private subscription?: Subscription;
    private focusedDocId?: DocumentId;

    getEquationsState(): EquationState[] {
        const docCtrls = this.getFocusedMathGraphDocCtrls();
        if (!docCtrls?.length) return [];

        return docCtrls[0].maghLib.getEquations().map((eq, idx) => {
            switch (eq.equationType) {
                case EqType.ScopeVar: {
                    const equation = eq as VarEquation;
                    return {
                        ...equation.serialize(),
                        defaultValue: equation.defaultExpression,
                        index: idx,
                        isValid: equation.isValid,
                        equationType: EqType.ScopeVar,
                        globalScope: equation.globalScope,
                        keyDuplicated: equation.keyDuplicated,
                    };
                }
                case EqType.Point: {
                    const equation = eq as PointEquation;
                    return {
                        ...equation.serialize(),
                        defaultValue: equation.defaultExpression,
                        index: idx,
                        isValid: equation.isValid,
                        equationType: EqType.Point,
                    };
                }
                case EqType.Plot:
                default: {
                    const equation = eq as PlotEquation;
                    return {
                        ...equation.serialize(),
                        defaultValue: equation.defaultExpression,
                        index: idx,
                        isValid: equation.isValid,
                        equationType: EqType.Plot,
                        isImplicit: equation.isImplicit,
                        isParametricArc: equation.isParametricArc,
                        supportInternalScope: equation.supportInternalScope,
                        internalScope: equation.internalScope,
                        showLabel: equation.styles.showLabel,
                        customLabel: equation.styles.customLabel,
                        domain: equation.effectiveDomain,
                    };
                }
            }
        });
    }

    updateExpression(index: number, latex: string, finished = true) {
        this.executeInFocusedDocCtrl(
            (docCtrl: MathGraphDocCtrl) => docCtrl.updateExpression(index, latex, finished),
            finished // refocus on the viewport on finish editing
        );
    }

    updateVisibility(index: number, hidden: boolean) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => docCtrl.setHidden(index, hidden), true);
    }

    addEquation(equationType: EqType) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => docCtrl.addEquation(equationType), true);
    }

    deleteEquation(index: number) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => docCtrl.deleteEquation(index), true);
    }

    updateStyle(index: number, style: EquationStyle) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => docCtrl.updateStyle(index, style), true);
    }

    updateInternalScope(index: number, scope: SerializedInternalScope) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => docCtrl.updateInternalScope(index, scope));
    }

    updateGlobalScope(index: number, scope: SerializedGlobalScope) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => docCtrl.updateGlobalScope(index, scope));
    }

    updateDomain(index: number, domain: Domain) {
        this.executeInFocusedDocCtrl((docCtrl: MathGraphDocCtrl) => docCtrl.updateDomain(index, domain), true);
    }

    override onEnable(): void {
        this.onDocAttached();
    }

    override onDisable(): void {
        this.onDocDetached();
    }

    override onAttachViewport(): void {
        this.changeToolState({ ...DEFAULT_TOOLSTATE });
    }

    onDocAttached(docCtrl?: MathGraphDocCtrl): void {
        // remove any old subscription
        this.subscription?.unsubscribe();

        const _docCtrl = docCtrl ? docCtrl : this.getFocusedMathGraphDocCtrls()?.[0];
        if (_docCtrl) {
            this.focusedDocId = _docCtrl.state.globalId;
            this.subscription = _docCtrl.maghLib.onEquationsUpdated(() =>
                this.changeToolState({
                    equations: this.getEquationsState(),
                    colors: _docCtrl.maghLib.getColorPalette(),
                })
            );

            this.changeToolState({
                equations: this.getEquationsState(),
                colors: _docCtrl.maghLib.getColorPalette(),
            });
        }
    }

    onDocDetached(docCtrl?: MathGraphDocCtrl): void {
        if (!docCtrl || docCtrl.state.globalId === this.focusedDocId) {
            if (this.subscription) {
                this.subscription.unsubscribe();
                this.subscription = undefined;
            }

            this.changeToolState({ ...DEFAULT_TOOLSTATE });
            this.focusedDocId = undefined;
        }
    }

    private changeToolState(newState: EquationsState) {
        this.toolState.colors = newState.colors;

        // Reuse existing equation instances if possible, update their properties, and handle add/remove.
        // Must do this instead of replacing the entire array to maintain references in the UI.
        // Otherwise the plot UI will repeatedly re-render (destroy-init again) along with the mathfield on it
        const oldEquations = this.toolState.equations;
        const newEquations = newState.equations;

        // Update existing equations
        for (let i = 0; i < Math.min(oldEquations.length, newEquations.length); i++) {
            Object.assign(oldEquations[i], newEquations[i]);
        }

        // Add new equations if needed
        if (newEquations.length > oldEquations.length) {
            for (let i = oldEquations.length; i < newEquations.length; i++) {
                oldEquations.push({ ...newEquations[i] });
            }
        }

        // Remove extra equations if needed
        if (oldEquations.length > newEquations.length) {
            oldEquations.length = newEquations.length;
        }

        this.toolbar.update('UpdateEquationsTool', this.toolState);
    }
}

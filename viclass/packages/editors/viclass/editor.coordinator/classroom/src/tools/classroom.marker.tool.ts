import {
    Cmd,
    CmdChannel,
    CmdCombinator,
    CmdMeta,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventData,
    mouseLocation,
    NativeEventTarget,
    newCursor,
    PointerEventData,
    PointerEventListener,
    pointerTypeDyn,
    pointerTypePenMouse,
    Position,
    Rectangle,
    reliableCmdMeta,
    SelectionFeature,
    ToolState,
    UnboundedSVGLayerCtrl,
    ViewportId,
} from '@viclass/editor.core';
import { CmdTypeProto, PositionProto } from '@viclass/proto/editor.core';
import { getStroke } from 'perfect-freehand';
import { BehaviorSubject } from 'rxjs';
import { ClassroomCoordinator } from '../classroom.coordinator';
import { SyncMarkerDeleteCmd, SyncMarkerPreviewCmd, SyncMarkerSegmentCmd } from '../coordinator.state.cmd';
import { AddMarkerPreviewPointState, AddMarkerSegmentState } from '../data.model';
import { ClassroomTool, ClassroomToolType } from './classroom.tool';

const SAMPLING_DISTANCE_THRESHOLD = 100;
const MOUSE_TIME_THROTTLE = 10; // ms
const enableDebugPath = false;

let lastColor = 0;

function average(a: number, b: number) {
    return (a + b) / 2;
}

export function convertProtoToPosition(proto: PositionProto): Position {
    return { x: proto.getX(), y: proto.getY() };
}

function filterDuplicatePoints(positions: Position[]): Position[] {
    return positions.filter((p, i) => {
        if (i === 0) return true; // Always keep the first point
        const prev = positions[i - 1];
        return p.x !== prev.x || p.y !== prev.y; // Keep the point if it's not a duplicate of the previous one
    });
}

// Function to generate an SVG path from a list of positions
function generateSvgPath(positions: Position[], startWith: 'M' | 'L'): string {
    if (positions.length === 0) return '';

    // Start with the first position (M command)
    let path = `${startWith} ${positions[0].x} ${positions[0].y}`;

    // Add each position using the L command (line to)
    for (let i = 1; i < positions.length; i++) {
        path += ` L ${positions[i].x},${positions[i].y}`;
    }

    return path;
}

export type LineId = string;
type UserId = string;

export interface MarkerLine {
    svgPath: SVGPathElement;
    debugPath?: SVGPathElement;
    previewPoints: Position[];
    points: Position[];
    color: string;
    size: number;
    pathData: string;
    lastDrawnIndex: number;
}

export interface MarkerInfo {
    lines: Map<LineId, MarkerLine>;
    avatarSvg?: SVGElement;
    avatarTimedOut?: number; // Used to hide the avatar after a certain time
}

export class ClassroomMarkerState implements ToolState {
    svg: SVGElement;
    color: string = '#F772FF';
    active: boolean = false;
    size: number = 2;
    requestedFrame: boolean = false;
    userMarkers: Map<UserId, MarkerInfo> = new Map<UserId, MarkerInfo>();
}

class CmdCombinatorImpl implements CmdCombinator {
    constructor(private tool: ClassroomMarkerTool) {}

    isCombining: boolean = false;
    cmdType: number;
    viewport: string;

    timeThreshold = 500;
    counterThreshold = 50;

    buffer: Position[] = [];
    timeCheckerId: any = undefined;
    isCompleted: boolean = false;
    previewToRemove: number = 0;

    increasePreviewCount(count: number) {
        if (count == -1)
            // remove all
            this.previewToRemove = -1;
        else this.previewToRemove += count;
    }

    startSequence(sender: (cmd: Cmd<any>) => void) {
        this.isCompleted = false;
    }

    combine(cmd: Cmd<any>) {
        if (cmd.meta.cmdType == CmdTypeProto.SYNC_MARKER_PREVIEW) {
            const updateCmd = cmd as SyncMarkerPreviewCmd;
            this.buffer.push(convertProtoToPosition(updateCmd.state.getMarker().getPoint()));

            if (updateCmd.state.getMarker().getEndPoint() == true) {
                this.endSequenceAndFlush();
            } else {
                this.counterChecker();

                if (!this.timeCheckerId) {
                    // start the timer checker if not yet
                    this.timeCheckerId = setTimeout(() => this.timeChecker(), this.timeThreshold);
                }
            }
        }
    }

    private timeChecker() {
        this.clearTimer();
        if (this.isCombining || this.isCompleted) return;
        this.isCombining = true;

        // send all remaining
        const points = this.buffer;
        this.buffer = [];
        this.createAndSendCmd(points);

        this.isCombining = false;
    }

    private async createAndSendCmd(points: Position[]) {
        if (points.length == 0) return;

        const userId = this.tool.coord.userId;
        const coordId = this.tool.toolbar.viewport.id;
        const coordState = this.tool.coord.getCoordState(coordId);
        const lineId = this.tool.localLineId;

        if (!coordState) return;

        const meta: CmdMeta = reliableCmdMeta(
            this.tool.coord.getVmByState(coordId),
            0,
            -1,
            CmdTypeProto.SYNC_MARKER_SEGMENT
        );

        const cmd = new SyncMarkerSegmentCmd(meta);
        cmd.setCoordId(coordId);
        cmd.setMarkerUpdate({
            lineId: lineId,
            points: points,
            userId: userId,
            color: this.tool.toolState.color,
            size: this.tool.toolState.size,
            numPreviewRemoved: this.previewToRemove,
        });

        this.previewToRemove = 0;

        return await this.tool.coord.cmdChannel.receive(cmd);
    }

    flushBuffer() {
        if (this.isCombining) return;
        this.isCombining = true;

        // clear time checker scheduler
        this.clearTimer();

        // send all remaining
        const points = this.buffer;
        this.buffer = [];
        this.createAndSendCmd(points);

        this.isCombining = false;
    }

    private counterChecker() {
        if (this.buffer.length < this.counterThreshold || this.isCombining) return;
        this.isCombining = true;

        this.clearTimer();

        const points = this.buffer.splice(0, this.counterThreshold);
        this.createAndSendCmd(points);

        this.isCombining = false;
    }

    setChannel(cmdChannel: CmdChannel) {
        throw new Error('Method not implemented.');
    }

    clearTimer() {
        if (this.timeCheckerId) {
            clearTimeout(this.timeCheckerId);
            delete this.timeCheckerId;
        }
    }

    async endSequenceAndFlush(): Promise<void> {
        const completed = this.isCompleted;
        this.isCompleted = true;

        if (!completed) this.flushBuffer();
    }
}

export class ClassroomMarkerTool extends ClassroomTool {
    override toolType: ClassroomToolType = 'markertool';
    layerCtrl?: UnboundedSVGLayerCtrl;

    override toolState?: ClassroomMarkerState = new ClassroomMarkerState();
    localLineId: string;
    cmdCombinator: CmdCombinatorImpl;

    private focusTeardowns = new Map<ViewportId, () => void>();

    constructor(
        public coord: ClassroomCoordinator,
        private selectionFeature: SelectionFeature
    ) {
        super();

        this.keyboardHandling.push(
            new (class extends KeyboardHandlingItem {
                override global = true;
            })(['alt', 'shift', 'd']),
            new (class extends KeyboardHandlingItem {
                override global = true;
            })(['alt', 'shift', 'D'])
        );

        this.pointerHandling.push(
            { event: 'longpress', disable: true },

            { event: 'pointerdown', button: 0, pointerTypes: pointerTypePenMouse, keys: ['nokey'] },
            { event: 'pointerup', button: 0, pointerTypes: pointerTypePenMouse },
            { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypePenMouse, keys: ['nokey'] },

            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 }
        );

        this.cmdCombinator = new CmdCombinatorImpl(this);
    }

    override readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public _p: ClassroomMarkerTool) {}

        async onEvent(
            event: KeyboardEventData<NativeEventTarget<any>>
        ): Promise<KeyboardEventData<NativeEventTarget<any>>> {
            if (!this._p.toolbar.viewport)
                throw new Error('Event cannot be handled because toolbar is not attached to a viewport');

            if (
                this._p.toolbar.isDisabled() ||
                this._p.toolbar.isToolDisable(this._p.toolType) ||
                event.nativeEvent.repeat
            )
                return event;

            switch (event.nativeEvent.key) {
                case 'd':
                case 'D':
                    if (event.nativeEvent.altKey && event.nativeEvent.shiftKey) {
                        this._p.eraseMyDrawing();
                        event.nativeEvent.preventDefault();
                    }
                    break;
            }

            event.nativeEvent.preventDefault();

            return event;
        }
    })(this);

    override onFocus(): void {
        const viewport = this.toolbar.viewport;
        if (!viewport) return;
        if (this.focusTeardowns.has(viewport.id)) {
            this.focusTeardowns.get(viewport.id)();
            this.focusTeardowns.delete(viewport.id);
        }

        const selections = this.selectionFeature.getCurrentSelections(viewport.id);

        const changedElements: (HTMLElement | SVGElement)[] = [];
        selections.forEach(selection => {
            for (const layer of selection.doc.getLayers()) {
                // disable touch action on contenteditable child of layer
                layer.nativeEl.classList.add('marker-touch-action-none');
                changedElements.push(layer.nativeEl);
            }
        });

        this.focusTeardowns.set(viewport.id, () => {
            changedElements.forEach(el => el?.classList?.remove('marker-touch-action-none'));
        });

        if (viewport.mode !== 'Disabled') {
            const ham = this.coord.hams.get(viewport.id);
            ham?.disableToolbars();
        }
    }

    override onBlur(): void {
        const viewport = this.toolbar.viewport;
        if (!viewport) return;
        if (this.focusTeardowns.has(viewport.id)) {
            this.focusTeardowns.get(viewport.id)();
            this.focusTeardowns.delete(viewport.id);
        }

        if (viewport.mode !== 'Disabled') {
            const ham = this.coord.hams.get(viewport.id);
            ham?.enableToolbars();
        }
    }

    async adjustSettings(changedFields: Record<string, any>): Promise<void> {
        const toolState = { ...this.toolState };
        Object.keys(changedFields).forEach(key => {
            toolState[key] = changedFields[key];

            const currentCursor = this.pointerHandler.cursor.value;

            if (key === 'color') {
                currentCursor[0].color = changedFields[key];
            }

            if (key === 'size') {
                currentCursor[0].size = changedFields[key];
            }

            this.pointerHandler.cursor.next(currentCursor);
        });

        this.toolState = toolState;

        this.toolbar.update('markertool', {
            ...this.toolState,
        });
    }

    private layerPos(boardPos: Position) {
        return this.layerCtrl.svgPos(boardPos);
    }

    toggleTool() {
        if (this.toolState.active) {
            this.toolbar.blur('markertool');
            this.toolState.active = false;
        } else {
            this.toolbar.focus('markertool');
            this.toolState.active = true;
        }

        this.toolbar.update('markertool', {
            ...this.toolState,
        });
    }

    private checkAndCreateMarkerInfoFromState(
        markerState: AddMarkerSegmentState | AddMarkerPreviewPointState,
        isPreview: boolean
    ): MarkerInfo {
        const { userId, lineId, color, size } = markerState;
        let userMarker = this.toolState.userMarkers.get(userId);

        let point;
        if ('point' in markerState) point = markerState.point;
        else if ('points' in markerState) point = markerState.points[0];

        // Create a new line marker if user doesn't exist or line doesn't exist
        if (point && (!userMarker || !userMarker.lines.has(lineId))) {
            const svgPath = this.createSVGPath('svg-path');
            const debugPath = enableDebugPath ? this.createSVGPath('debug-path') : undefined;

            if (!userMarker) {
                const userCtx = this.coord.userCtxGetter();
                const profile = userCtx?.getProfileById(userId);
                const avatarElement =
                    userId !== userCtx.userId && profile
                        ? this.createAvatarElement(profile.avatarUrl, profile.name || profile.username || profile.email)
                        : undefined;
                if (avatarElement) {
                    this.layerCtrl.svg.appendChild(avatarElement);
                }

                userMarker = {
                    lines: new Map<LineId, MarkerLine>(),
                    avatarSvg: avatarElement,
                };
                this.toolState.userMarkers.set(userId, userMarker);
            }
            this.toolState.userMarkers.get(userId).lines.set(lineId, {
                svgPath: svgPath,
                debugPath: enableDebugPath ? debugPath : undefined,
                color: color,
                size: size,
                points: [],
                previewPoints: [],
                pathData: '',
                lastDrawnIndex: 0,
            });
        }

        return this.toolState.userMarkers.get(userId);
    }

    private createAvatarElement(avatarUrl: string, displayName: string): SVGElement {
        const NS = 'http://www.w3.org/2000/svg';
        const group = document.createElementNS(NS, 'g');
        group.setAttribute('class', 'marker-avatar-group idle');

        // Giả sử SVG marker có kích thước 48x48, tâm là (24,24)
        const markerIconUrl =
            getComputedStyle(document.documentElement)
                .getPropertyValue('--marker-icon-url')
                ?.replace(/url\(["']?|["']?\)/g, '') ?? 'cursor_marker.svg';
        const markerIcon = document.createElementNS(NS, 'image');
        markerIcon.setAttributeNS('http://www.w3.org/1999/xlink', 'href', markerIconUrl);
        markerIcon.setAttribute('x', '-24');
        markerIcon.setAttribute('y', '-24');
        markerIcon.setAttribute('width', '48');
        markerIcon.setAttribute('height', '48');
        markerIcon.setAttribute('class', 'marker-icon');
        group.appendChild(markerIcon);

        // Avatar hình tròn bên phải icon
        const avatarGroup = document.createElementNS(NS, 'g');
        avatarGroup.setAttribute('transform', 'translate(30,0)'); // 30px sang phải tâm icon

        const avatarCircle = document.createElementNS(NS, 'circle');
        avatarCircle.setAttribute('cx', '0');
        avatarCircle.setAttribute('cy', '0');
        avatarCircle.setAttribute('r', '12');
        avatarCircle.setAttribute('fill', '#eee');
        avatarGroup.appendChild(avatarCircle);

        const avatarImg = document.createElementNS(NS, 'image');
        avatarImg.setAttributeNS(
            'http://www.w3.org/1999/xlink',
            'href',
            avatarUrl || '/static/assets/images/avatar-man.png'
        );
        avatarImg.setAttribute('x', '-12');
        avatarImg.setAttribute('y', '-12');
        avatarImg.setAttribute('width', '24');
        avatarImg.setAttribute('height', '24');
        avatarImg.setAttribute('clip-path', 'circle(12px at 12px 12px)');
        avatarGroup.appendChild(avatarImg);

        // Create background text for white outline effect
        const textBg = document.createElementNS(NS, 'text');
        textBg.setAttribute('x', '18');
        textBg.setAttribute('y', '6');
        textBg.setAttribute('font-size', '12');
        textBg.setAttribute('fill', 'white');
        textBg.setAttribute('stroke', 'white');
        textBg.setAttribute('stroke-width', '4');
        textBg.setAttribute('font-family', 'Montserrat, sans-serif');
        textBg.setAttribute('class', 'marker-avatar-name-bg');
        textBg.textContent = displayName;
        avatarGroup.appendChild(textBg);

        // Create foreground text
        const text = document.createElementNS(NS, 'text');
        text.setAttribute('x', '18');
        text.setAttribute('y', '6');
        text.setAttribute('font-size', '12');
        text.setAttribute('fill', '#333');
        text.setAttribute('font-family', 'Montserrat, sans-serif');
        text.setAttribute('class', 'marker-avatar-name');
        text.textContent = displayName;
        avatarGroup.appendChild(text);

        group.appendChild(avatarGroup);

        return group;
    }

    updateMarkerPaths(markerState: AddMarkerSegmentState | AddMarkerPreviewPointState, isPreview: boolean = false) {
        const { lineId, color, size } = markerState;

        const userMarker = this.checkAndCreateMarkerInfoFromState(markerState, isPreview);
        // Update existing line marker
        const lineMarker = userMarker.lines.get(lineId);

        if (isPreview) {
            // MarkerUpdateState is used for preview, where "point" is defined
            lineMarker.previewPoints.push((markerState as AddMarkerPreviewPointState).point);
        } else {
            const ms = markerState as AddMarkerSegmentState;

            if (ms.numPreviewRemoved === -1) lineMarker.previewPoints = [];
            else lineMarker.previewPoints.splice(0, ms.numPreviewRemoved);

            //console.log("Num preview removed", ms.numPreviewRemoved)

            lineMarker.points = filterDuplicatePoints([...lineMarker.points, ...ms.points]);

            if (enableDebugPath) {
                let color = 'red';
                if (lastColor == 1) {
                    color = 'yellow';
                    lastColor = 0;
                } else {
                    lastColor = 1;
                    color = 'red';
                }

                for (const p of ms.points) {
                    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    circle.setAttribute('cx', `${p.x}`);
                    circle.setAttribute('cy', `${p.y}`);
                    circle.setAttribute('r', `2`);
                    circle.setAttribute('fill', color);
                    circle.setAttribute('opacity', '0.3');
                    this.layerCtrl.svg.appendChild(circle);
                }
            }
        }

        const position = this.renderPath(lineMarker, color, size, isPreview);

        this.updateAvatarPosition(markerState.userId, userMarker, position);
    }

    /**
     * Get the boundary of all marker lines for the fit-all feature
     */
    getMarkerBoundary(): Rectangle | null {
        const allPoints: Position[] = [];
        this.toolState.userMarkers.forEach(marker => {
            marker.lines.forEach(line => {
                allPoints.push(...line.points);
                allPoints.push(...line.previewPoints);
            });
        });
        if (!allPoints.length) return null;

        return allPoints.reduce(
            (acc, p) => {
                if (!acc) {
                    return { start: p, end: p };
                }
                // ! Negate y to make it consistent with the board coordinate system
                acc.start = {
                    x: Math.min(acc.start.x, p.x),
                    y: Math.min(acc.start.y, -p.y),
                };
                acc.end = {
                    x: Math.max(acc.end.x, p.x),
                    y: Math.max(acc.end.y, -p.y),
                };
                return acc;
            },
            <Rectangle>{
                start: { x: Infinity, y: Infinity },
                end: { x: -Infinity, y: -Infinity },
            }
        );
    }

    private getSvgPathFromStroke(points, closed = true) {
        const len = points.length;

        if (len < 4) {
            return ``;
        }

        let a = points[0];
        let b = points[1];
        const c = points[2];

        let result = `M${a[0].toFixed(2)},${a[1].toFixed(2)} Q${b[0].toFixed(
            2
        )},${b[1].toFixed(2)} ${average(b[0], c[0]).toFixed(2)},${average(b[1], c[1]).toFixed(2)} T`;

        for (let i = 2, max = len - 1; i < max; i++) {
            a = points[i];
            b = points[i + 1];
            result += `${average(a[0], b[0]).toFixed(2)},${average(a[1], b[1]).toFixed(2)} `;
        }

        if (closed) {
            result += 'Z';
        }

        return result;
    }

    /**
     * Reconstruct the svg objects. isPreview means only update preview path
     * @param lineMarker
     * @param color
     * @param size
     * @param isPreview
     */
    private renderPath(lineMarker: MarkerLine, color: string, size: number, isPreview = false): Position | undefined {
        const points = [...lineMarker.points, ...lineMarker.previewPoints];
        if (!points.length) return undefined;

        const stroke = getStroke(points, {
            size: size,
            thinning: 0.5,
            streamline: 0.5,
        });
        const pathData = this.getSvgPathFromStroke(stroke);

        lineMarker.svgPath.setAttribute('d', pathData);
        lineMarker.svgPath.setAttribute('opacity', '0.8');
        lineMarker.svgPath.setAttribute('fill', color);
        lineMarker.svgPath.setAttribute('shape-rendering', 'crispEdge');

        if (enableDebugPath) {
            const debugPathData = generateSvgPath([...lineMarker.points, ...lineMarker.previewPoints], 'M');
            lineMarker.debugPath.setAttribute('d', debugPathData);
            lineMarker.debugPath.setAttribute('stroke', '#000');
            lineMarker.debugPath.setAttribute('stroke-width', `2`);
            lineMarker.debugPath.setAttribute('fill', 'none');
        }

        return points[points.length - 1];
    }

    private updateAvatarPosition(userId: string, userMarker: MarkerInfo, position: Position) {
        if (userMarker.avatarSvg && position) {
            const isPresenter = this.coord.userCtxGetter().presenter.userId === userId;
            userMarker.avatarSvg.setAttribute('class', `marker-avatar-group ${isPresenter ? 'presenting' : ''}`);
            userMarker.avatarSvg.setAttribute('transform', `translate(${position.x}, ${position.y})`);

            if (userMarker.avatarTimedOut) {
                clearTimeout(userMarker.avatarTimedOut);
            }
            userMarker.avatarTimedOut = setTimeout(() => {
                userMarker.avatarSvg?.setAttribute('class', 'marker-avatar-group idle');
                userMarker.avatarTimedOut = undefined;
            }, 1000);
        }
    }

    eraseDrawing(userId: string) {
        const markerInfo = this.toolState.userMarkers.get(userId);
        if (!markerInfo) return;

        // Remove each SVG path and delete the line entry
        markerInfo.lines.forEach(line => {
            this.layerCtrl.svg.removeChild(line.svgPath);

            if (line.debugPath) {
                this.layerCtrl.svg.removeChild(line.debugPath);
                const circle = this.layerCtrl.svg.getElementsByTagName('circle');
                for (let i = 0; i < circle.length; i++) {
                    this.layerCtrl.svg.removeChild(circle[i]);
                }
            }
        });

        // Remove the user's line entries and the user marker
        this.toolState.userMarkers.delete(userId);
    }

    eraseAllDrawing() {
        for (const userId of this.toolState.userMarkers.keys()) {
            this.eraseDrawing(userId);
            this.syncEraseMyDrawing(userId);
        }
    }

    createSVGPath(className) {
        const path = this.layerCtrl.svg.appendChild(document.createElementNS('http://www.w3.org/2000/svg', 'path'));
        path.setAttribute('class', className);
        return path;
    }

    applyMarkerDelete(userId: string) {
        this.eraseDrawing(userId);
    }

    private createMarkerPreviewCmd(
        coordId: string,
        point: Position,
        userId: string,
        lineId: LineId,
        startPoint: boolean,
        endPoint: boolean
    ): SyncMarkerPreviewCmd {
        const meta: CmdMeta = reliableCmdMeta(
            this.coord.getVmByState(coordId),
            0,
            -1,
            startPoint ? CmdTypeProto.SYNC_MARKER_UPDATE_START : CmdTypeProto.SYNC_MARKER_PREVIEW
        );
        const cmd = new SyncMarkerPreviewCmd(meta, meta.cmdType);
        cmd.setCoordId(coordId);
        cmd.setMarkerUpdate({
            lineId: lineId,
            point: point,
            userId: userId,
            color: this.toolState.color,
            size: this.toolState.size,
            endPoint: endPoint,
        });

        return cmd;
    }

    private async syncMarkerPreview(
        point: Position,
        lineId: LineId,
        startPoint: boolean = false,
        endPoint: boolean = false
    ): Promise<void> {
        const userId = this.coord.userId;
        const coordId = this.toolbar.viewport.id;

        const cmd = this.createMarkerPreviewCmd(coordId, point, userId, lineId, startPoint, endPoint);
        await this.coord.cmdChannel.receive(cmd);
    }

    private async combineMarkerPreview(
        point: Position,
        lineId: LineId,
        previewRemove: number,
        endPoint: boolean = false
    ): Promise<void> {
        const userId = this.coord.userId;
        const coordId = this.toolbar.viewport.id;

        // we only care if a point is a start point in previewing to ensure visual accuracy
        // for combination (send actual points as batch, start point is already incldued when combining, so can always use false for start point)
        const cmd = this.createMarkerPreviewCmd(coordId, point, userId, lineId, false, endPoint);
        this.cmdCombinator.increasePreviewCount(previewRemove);
        await this.cmdCombinator.combine(cmd);
        if (previewRemove == -1) this.cmdCombinator.flushBuffer();
    }

    eraseMyDrawing() {
        this.eraseDrawing(this.coord.userId);
        this.syncEraseMyDrawing(this.coord.userId);
    }

    private async syncEraseMyDrawing(userId): Promise<void> {
        const coordId = this.toolbar.viewport.id;

        const meta: CmdMeta = reliableCmdMeta(this.coord.getVmByState(coordId), 0, -1, CmdTypeProto.SYNC_MARKER_DELETE);
        const cmd = new SyncMarkerDeleteCmd(meta);
        cmd.setCoordId(coordId);
        cmd.setUserId(userId);
        return await this.coord.cmdChannel.receive(cmd);
    }

    override onAttachViewport() {
        if (!this.toolbar.viewport) {
            throw new Error('Viewport is not attached to the toolbar');
        }
        if (!this.layerCtrl || !this.layerCtrl.svg) {
            this.layerCtrl = this.toolbar.viewport.requestLayer(UnboundedSVGLayerCtrl, true, {
                viewport: this.toolbar.viewport,
            }) as UnboundedSVGLayerCtrl;
            Object.assign(this.layerCtrl.svg.style, {
                fill: 'none',
                strokeLinecap: 'round',
            });
            this.toolbar.viewport.sticky(this.layerCtrl);
        }
    }

    override onDetachViewport() {
        if (!this.toolbar.viewport) {
            throw new Error('Viewport is not attached to the toolbar');
        }
    }

    override readonly pointerHandler = new (class MarkerDraw implements PointerEventListener<NativeEventTarget<any>> {
        constructor(public _p: ClassroomMarkerTool) {}

        cursor = new BehaviorSubject([
            newCursor('vcon_cursor_brush', 5, '#F772FF', 0, 'icon'),
            newCursor('cursor_marker', 50, undefined, 0, 'image'),
        ]);

        drawing: boolean = false;
        mouseuping: boolean = false;
        lastRecorded?: MouseEventData<any>;
        currentEvent?: MouseEventData<any>;
        previewList?: MouseEventData<any>[];
        previewDistance: number = 0;
        lastConfirmedSegment?: MouseEventData<any>[];
        lastPointerMoveTime: number = 0;

        previewEvent(event: MouseEventData<NativeEventTarget<any>>, startPoint: boolean = false) {
            const mPos = mouseLocation(event);
            const point = this._p.layerPos(mPos);

            this._p.syncMarkerPreview(point, this._p.localLineId, startPoint);
        }

        async onEvent(
            event: PointerEventData<NativeEventTarget<any>>
        ): Promise<PointerEventData<NativeEventTarget<any>>> {
            if (!('nativeEvent' in event)) return event;

            if (!this._p.toolbar.viewport)
                throw new Error('Event cannot be handled because toolbar is not attached to a viewport');

            if (this._p.toolbar.isDisabled() || this._p.toolbar.isToolDisable(this._p.toolType)) return event;

            if (this.mouseuping) return event;
            console.log('Receive pointer event ', event.nativeEvent.pointerType, event.eventType);
            event.continue = false;
            switch (event.nativeEvent.type) {
                case 'pointerdown':
                    // create cmd combinator
                    this.cleanUp();
                    this._p.localLineId = this.generateRandomId();
                    this.drawing = true;
                    this.lastRecorded = event;
                    this.previewList = [this.lastRecorded];
                    this.lastConfirmedSegment = [];

                    this._p.cmdCombinator.startSequence(undefined);

                    this.previewEvent(event, true);
                    break;
                case 'pointermove':
                case 'pointerup':
                    if (!this.drawing) return event;

                    this.currentEvent = event; // record the event to be processed inside the animation frame

                    const now = new Date();
                    if (
                        event.nativeEvent.type != 'pointerup' &&
                        now.getTime() - this.lastPointerMoveTime < MOUSE_TIME_THROTTLE
                    )
                        return event;
                    else this.lastPointerMoveTime = now.getTime();

                    if (!this._p.toolState.requestedFrame) {
                        this._p.toolState.requestedFrame = true;
                        requestAnimationFrame(() => {
                            this._p.toolState.requestedFrame = false;

                            if (!this.currentEvent) {
                                this.drawing = false;
                                return;
                            }

                            this.previewEvent(this.currentEvent);
                            this.previewList.push(event);

                            if (this.previewList.length >= 2) {
                                const ne = this.previewList[this.previewList.length - 1].nativeEvent;
                                const lne = this.previewList[this.previewList.length - 2].nativeEvent;
                                this.previewDistance += Math.hypot(ne.clientX - lne.clientX, ne.clientY - lne.clientY);
                            }

                            if (
                                (this.previewDistance >= SAMPLING_DISTANCE_THRESHOLD || this.mouseuping) &&
                                this.previewList.length > 1
                            ) {
                                const boardPoints = [...this.lastConfirmedSegment, ...this.previewList].map(sp =>
                                    mouseLocation(sp)
                                );

                                for (let i = 0; i < boardPoints.length; i++) {
                                    const point = this._p.layerPos(boardPoints[i]);

                                    this._p.combineMarkerPreview(
                                        point,
                                        this._p.localLineId,
                                        i == 0 ? this.previewList.length : 0
                                    );
                                }

                                if (enableDebugPath) {
                                    this._p.cmdCombinator.flushBuffer(); // if debugging, don't wait till combine is done, just flush
                                }

                                this.previewList = [];
                                this.previewDistance = 0;

                                if (this.mouseuping) {
                                    // handle the mouse up point
                                    const pos = mouseLocation(this.currentEvent);
                                    const endPoint = this._p.layerPos(pos);
                                    this._p.combineMarkerPreview(endPoint, this._p.localLineId, -1);
                                }
                            }

                            if (this.mouseuping) this.cleanUp();
                        });
                    }

                    if (event.nativeEvent.type == 'pointerup') {
                        this.drawing = false;
                        this.mouseuping = true;
                    }

                    event.continue = false;

                    break;
            }

            return event;
        }

        cleanUp() {
            delete this.currentEvent;
            delete this.lastRecorded;
            delete this.previewList;
            this.mouseuping = false; // finished handle mouse up
            this._p.cmdCombinator.endSequenceAndFlush();
        }

        generateRandomId(length = 8) {
            const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let id = '';
            for (let i = 0; i < length; i++) {
                id += characters.charAt(Math.floor(Math.random() * characters.length));
            }
            return id;
        }
    })(this);
}

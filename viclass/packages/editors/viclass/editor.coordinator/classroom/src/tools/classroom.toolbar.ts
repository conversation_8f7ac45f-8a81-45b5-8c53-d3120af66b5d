import {
    DefaultT<PERSON>B<PERSON>,
    DefaultToolEventData,
    EditorCoordinator,
    isCtrlOrMeta,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    keyboardHandlingKey,
    MouseEventListener,
    NativeEventTarget,
    PointerEventListener,
    ViewportMode,
} from '@viclass/editor.core';
import { ClassroomCoordinator } from '../classroom.coordinator';
import { ClassroomTool, ClassroomToolType } from './classroom.tool';

/**
 * Toolbar specific to classroom
 */
export class ClassroomToolbar extends DefaultToolBar<ClassroomToolType, ClassroomTool> {
    private classroomToolShortcut: string[][] = [['ctrl', '.']];

    readonly keyboardHandler?: KeyboardEventListener<NativeEventTarget<any>>;
    readonly mouseHandler?: MouseEventListener<NativeEventTarget<any>>;
    readonly pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    constructor(private _coord: ClassroomCoordinator) {
        super(_coord as unknown as EditorCoordinator);

        // Create keyboard handling from shortcuts
        const shortcuts: Map<string, string[]> = new Map();

        this.classroomToolShortcut.map(k => shortcuts.set(keyboardHandlingKey('keydown', k), k));

        this.classroomToolShortcut.forEach(k =>
            this.keyboardHandling.push(
                new (class extends KeyboardHandlingItem {
                    override event: string = 'keydown';
                })(k)
            )
        );

        this.keyboardHandler = new this._keyboardHandler(this);
    }

    public override async onViewportModeChanged(vpMode: ViewportMode) {
        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    this.enableTool(type);
                    break;
                }
                case 'ViewMode': {
                    this.enableTool(type);
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private _p: ClassroomToolbar) {}

        onEvent(event: KeyboardEventData<NativeEventTarget<any>>): KeyboardEventData<NativeEventTarget<any>> {
            if (this._p.isDisabled() || event.nativeEvent.repeat) return event;

            if (isCtrlOrMeta(event.nativeEvent) && ['.'].includes(event.nativeEvent.key)) {
                this._p.eventSource.emit(new DefaultToolEventData('toggle', this._p, 'docsettingtool'));

                event.nativeEvent.preventDefault();
                event.continue = false;
            }

            return event;
        }
    };
}

import {
    <PERSON>T<PERSON><PERSON>,
    CoordinatorEvent,
    CursorManager,
    CursorMonitor,
    DefaultToolBar,
    EditorBlurCES,
    EditorEventManager,
    EditorFocusCES,
    EventToViewportMode,
    Tool,
    ToolBar,
    ToolEventData,
    ToolEventListener,
    UserInputHandler,
    VEventListener,
    ViewportDisableCES,
    ViewportEnabledCES,
    ViewportFocusOutCES,
    ViewportManager,
    ViewportMode,
    ViewportRemovedCES,
} from '@viclass/editor.core';
import { ClassroomCoordinator } from './classroom.coordinator';
import { ClassroomToolbar, ClassroomToolType } from './tools';

export const LOCAL_CURSOR = 'local_cursor';
export const PRESENTER_CURSOR = 'presenter_cursor';

/**
 * List of priority classroom tools that will disable the editor toolbars when active.
 * Example: markertool will be prefer to draw over the documents over other editor tools.
 */
const PRIORITY_CLASSROOM_TOOLS: ClassroomToolType[] = ['markertool'];

/**
 * Handlers and Global Handlers from tools, toolbar needs to be attached and detached
 * at the correct moment. This class listens to the events occurred from the coordinator activities
 * and decide which handlers to be attached.
 *
 * - tool-focus
 * - tool-blur
 * - viewport-enable
 * - viewport-disable
 */
export class HandlerAttachmentManager {
    timeout: number | undefined;

    private focusTool: ToolEventData<ToolBar<any, any>, any>[] = [];
    private externalMonitored: HTMLElement[] = [];
    private readonly commonToolbar: CommonToolbar;
    private readonly classroomToolbar: ClassroomToolbar;
    private editorToolbar: ToolBar<any, any> = undefined;

    readonly cursorManager: CursorManager;
    readonly cursorMonitor: CursorMonitor;

    private active = false;

    constructor(
        private coordinator: ClassroomCoordinator,
        private vm: ViewportManager
    ) {
        coordinator.coordEventEmitter.registerListener(this.coordListener);

        this.commonToolbar = coordinator.getCommonToolbar(vm.id);
        this.commonToolbar.registerToolListener(this.toolListener);

        this.classroomToolbar = coordinator.getClassroomToolbar(vm.id);
        this.classroomToolbar.registerToolListener(this.toolListener);

        // cursor manager
        this.cursorManager = new CursorManager(vm);
        this.cursorManager.add(LOCAL_CURSOR);
        this.cursorManager.add(PRESENTER_CURSOR, false);

        this.cursorMonitor = new CursorMonitor(vm, this.cursorManager, LOCAL_CURSOR);
    }

    public disableToolbars() {
        this.editorToolbar?.disable();
    }

    public enableToolbars() {
        this.editorToolbar?.enable();
    }

    private getEditorToolbars() {
        return Array.from(this.coordinator.editors.keys())
            .map(e => this.coordinator.getEditorToolbar(this.vm.id, e))
            .filter(Boolean);
    }

    private handleViewportModeChange(vpMode: ViewportMode) {
        if (!vpMode) return;

        const edToolbars = this.getEditorToolbars();
        if (vpMode === 'Disabled') {
            this.commonToolbar.disable();
            this.classroomToolbar.disable();
            this.editorToolbar?.disable();
        } else {
            this.commonToolbar.enable();
            this.classroomToolbar.enable();

            const isPriorityToolActive =
                this.classroomToolbar.activeTool &&
                PRIORITY_CLASSROOM_TOOLS.includes(this.classroomToolbar.activeTool.toolType);

            edToolbars.forEach(tb => (isPriorityToolActive ? tb?.disable() : tb?.enable()));
        }

        [this.commonToolbar, this.classroomToolbar, ...edToolbars].forEach(tb => {
            (tb as DefaultToolBar<any, any>)?.onViewportModeChanged(vpMode);
        });
    }

    private readonly coordListener = new (class implements VEventListener<CoordinatorEvent> {
        constructor(public m: HandlerAttachmentManager) {}
        async onEvent(eventData: CoordinatorEvent): Promise<CoordinatorEvent> {
            switch (eventData.eventType) {
                case 'editor-focus': {
                    const eS = eventData.state as EditorBlurCES;
                    if (!eS.vmId || eS.vmId !== this.m.vm.id) return eventData;

                    const tb = this.m.coordinator.getEditorToolbar(eS.vmId, eS.editor.editorType);
                    if (!tb) return eventData;

                    this.m.editorToolbar = tb;
                    tb.registerToolListener(this.m.toolListener);

                    break;
                }
                case 'editor-blur': {
                    const eS = eventData.state as EditorFocusCES;
                    if (!eS.vmId || eS.vmId !== this.m.vm.id) return eventData;

                    const tb = this.m.coordinator.getEditorToolbar(eS.vmId, eS.editor.editorType);
                    if (!tb) return eventData;

                    if (tb.curTool) await tb.blur(tb.curTool);
                    tb.unregisterToolListener(this.m.toolListener);
                    this.m.editorToolbar = undefined;

                    break;
                }
                case 'viewport-interactive-mode':
                case 'viewport-view-mode':
                case 'viewport-edit-mode': {
                    const veS = eventData.state as ViewportEnabledCES;

                    if (this.m.externalMonitored.length > 0)
                        for (const el of this.m.externalMonitored) {
                            const vm = this.m.coordinator.getViewportManager(veS.vmId);
                            vm.eventManager.captureEventsFor(el);
                        }

                    this.m.active = true;
                    this.m.handleViewportModeChange(EventToViewportMode[eventData.eventType]);
                    break;
                }
                case 'viewport-disabled': {
                    const veS = eventData.state as ViewportDisableCES;
                    const vm = this.m.coordinator.getViewportManager(veS.vmId);
                    if (veS.vmId !== this.m.vm.id) return eventData;

                    this.m.cursorManager.hideCursor(LOCAL_CURSOR);
                    this.m.cursorManager.hideCursor(PRESENTER_CURSOR);

                    // if additional elements being monitored, the event manager of the disabled viewport shouldn't capture anymore
                    if (this.m.externalMonitored.length > 0)
                        for (const el of this.m.externalMonitored) vm.eventManager.uncaptureEventsFor(el);
                    this.m.resetAll(vm);
                    this.m.active = false;
                    this.m.handleViewportModeChange(EventToViewportMode[eventData.eventType]);
                    return eventData;
                }
                // case 'viewport-focusin': {
                //     const veS = eventData.state as ViewportFocusInCES;
                //     if (veS.vmId !== this.m.vm.id) return eventData;
                //     return eventData;
                // }
                // case 'viewport-selected': {
                //     const veS = eventData.state as ViewportSelectedCES;
                //     if (veS.vmId !== this.m.vm.id) return eventData;
                //     return eventData;
                // }
                case 'viewport-focusout': {
                    const veS = eventData.state as ViewportFocusOutCES;
                    if (veS.vmId !== this.m.vm.id) return eventData;

                    // this.m.cursorMonitor.stop();
                    this.m.cursorManager.hideCursor(LOCAL_CURSOR);
                    this.m.cursorManager.hideCursor(PRESENTER_CURSOR);

                    return eventData;
                }
                case 'viewport-removed': {
                    const veS = eventData.state as ViewportRemovedCES;
                    if (veS.vmId !== this.m.vm.id) return eventData;

                    this.m.cursorMonitor.destroy();

                    return eventData;
                }
                default:
                    return eventData;
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    })(this);

    private readonly toolListener: ToolEventListener<ToolBar<any, any>, any> = new (class
        implements ToolEventListener<ToolBar<any, any>, any>
    {
        processingFocus = false;
        constructor(private m: HandlerAttachmentManager) {}

        isAncestorOf(t: ToolBar<any, Tool>, check: Tool): boolean {
            let result = false;
            if (check.childToolbar) {
                if (check.childToolbar === t) result = true;
                else
                    for (const tool of check.childToolbar.tools.values())
                        if (tool.childToolbar && this.isAncestorOf(t, tool)) {
                            result = true;
                            break;
                        }
            }

            return result;
        }

        async onEvent(
            eventData: ToolEventData<ToolBar<any, any>, any>
        ): Promise<ToolEventData<ToolBar<any, any>, any>> {
            if (eventData.eventType === 'change') return eventData;

            const t = eventData.source.getTool(eventData.toolType) as Tool;
            const s = eventData.source;

            // if the tool sending the event is not from the viewport this HAM is in charge of
            if (!s.viewport || s.viewport !== this.m.vm) {
                // console.error(
                //     'The viewport of the tool sending events is not the same as the viewport the HAM is responsible for'
                // );
                return eventData;
            }

            switch (eventData.eventType) {
                case 'focus': {
                    // if (t.toolbar.isDisabled() || t.toolbar.isToolDisable(t.toolType)) return eventData

                    const tobeBlur = [].concat(this.m.focusTool).reverse();
                    const remaining = [];

                    this.processingFocus = true;
                    for (const e of tobeBlur) {
                        // only blur tools which are not the ancestor of the focused tool
                        const tobeBlurTool = e.source.getTool(e.toolType);

                        if (this.isAncestorOf(s, tobeBlurTool)) remaining.unshift(e);
                        else e.source.blur(e.toolType);
                    }

                    this.processingFocus = false;
                    this.m.focusTool = [...remaining, eventData]; // focusTool now only contains the newly focused tool or its ancestor tool

                    if (t.childToolbar) t.childToolbar.registerToolListener(this.m.toolListener);
                    break;
                }

                case 'transient-focus': {
                    // if (t.toolbar.isDisabled() || t.toolbar.isToolDisable(t.toolType)) return eventData

                    // if the focus is transient, we don't blur existing tool
                    // so that when the transient focused tool is blurred, we can revert back to the existing tool in the correct order.
                    this.m.focusTool.push(eventData);

                    if (t.childToolbar) t.childToolbar.registerToolListener(this.m.toolListener);
                    break;
                }

                case 'blur':
                case 'transient-blur': {
                    if (t.childToolbar) t.childToolbar.unregisterToolListener(this.m.toolListener);

                    // if a tool is blur, we remove it from the focus tool and re-register the handler
                    this.m.focusTool = this.m.focusTool.filter(
                        e => e.source !== eventData.source || e.toolType !== eventData.toolType
                    );

                    break;
                }
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    })(this);

    destroy() {
        if (this.timeout) clearTimeout(this.timeout);

        this.vm.eventManager.resetKeyboardHandling();
        this.vm.eventManager.resetMouseHandling();

        this.coordinator.coordEventEmitter.unregisterListener(this.coordListener);

        this.commonToolbar.unregisterToolListener(this.toolListener);
        this.classroomToolbar.unregisterToolListener(this.toolListener);
        if (this.editorToolbar) this.editorToolbar.unregisterToolListener(this.toolListener);

        this.cursorMonitor.destroy();
        this.cursorManager.destroy();
    }

    additionalMonitoredElements(els: HTMLElement[]) {
        this.externalMonitored = this.externalMonitored.concat(els);

        // if there are an active viewport currently, notify its event manager to capture events for these additional elements
        if (this.active) {
            for (const el of els) {
                this.vm.eventManager.captureEventsFor(el);
            }
        }
    }

    private resetAll(vm: ViewportManager) {
        vm.eventManager.resetKeyboardHandling();
        vm.eventManager.resetMouseHandling();
        vm.eventManager.resetPointerHandling();
    }

    private registerHandler() {
        this.resetAll(this.vm);

        this.timeout = undefined;

        const em = this.vm.eventManager;
        if (!em) return;

        const focusTools: UserInputHandler[] = this.focusTool.map(e => e.source.getTool(e.toolType));
        const handlerStack: UserInputHandler[] = [
            this.commonToolbar,
            this.classroomToolbar,
            this.editorToolbar,
            ...focusTools,
        ];

        for (const uih of handlerStack)
            if (uih) {
                if (uih.mouseHandler)
                    for (const mh of uih.mouseHandling) if (!mh.global) em.registerMouseHandling(mh, uih.mouseHandler);

                if (uih.pointerHandler)
                    for (const mh of uih.pointerHandling)
                        if (!mh.global) em.registerPointerHandling(mh, uih.pointerHandler);

                if (uih.keyboardHandler)
                    for (const kh of uih.keyboardHandling)
                        if (!kh.global) em.registerKeyboardHandling(kh, uih.keyboardHandler);
            }

        this.registerGlobal(em, this.classroomToolbar); // register all global handling of the classroom toolbar
        if (this.commonToolbar) this.registerGlobal(em, this.commonToolbar); // register all global handling of the coordinator toolbar
        if (this.editorToolbar) this.registerGlobal(em, this.editorToolbar); // if there is a focus editor, register all global handling of the tools of that editor

        // update the tool stack in cursor manager so that it knows if focused handler has been changed
        this.cursorMonitor?.updateToolStack(handlerStack.map(h => h?.pointerHandler).filter(h => h !== undefined));
    }

    private registerGlobal(em: EditorEventManager, toolbar: ToolBar<any, Tool>) {
        if (toolbar.isDisabled()) return;
        // All global handling of the all tools within a toolbar
        toolbar.tools.forEach((tool, type) => {
            if (tool.mouseHandler)
                for (const mh of tool.mouseHandling) if (mh.global) em.registerMouseHandling(mh, tool.mouseHandler);

            if (tool.pointerHandler)
                for (const mh of tool.pointerHandling)
                    if (mh.global) em.registerPointerHandling(mh, tool.pointerHandler);

            if (tool.keyboardHandler)
                for (const kh of tool.keyboardHandling)
                    if (kh.global) em.registerKeyboardHandling(kh, tool.keyboardHandler);
        });
    }
}

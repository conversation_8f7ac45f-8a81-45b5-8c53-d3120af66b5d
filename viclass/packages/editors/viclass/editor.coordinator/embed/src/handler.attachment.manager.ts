import { WrappingInlineViewportManager } from '@viclass/editor.coordinator/common';
import {
    browser<PERSON>ursor,
    CommonToolbar,
    CoordinatorEvent,
    CursorManager,
    CursorMonitor,
    defaultCursor,
    DefaultToolBar,
    DocumentEditor,
    EditorEventManager,
    EditorFocusCES,
    EventToViewportMode,
    Tool,
    ToolBar,
    ToolEventData,
    ToolEventListener,
    UserInputHandler,
    VEventListener,
    ViewportDisableCES,
    ViewportEnabledCES,
    ViewportFocusInCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { EmbedCoordinator } from './embed.coordinator';

const LOCAL_CURSOR = 'local_cursor';

/**
 * Handlers and Global Handlers from tools, toolbar needs to be attached and detached
 * at the correct moment. This class listens to the events occurred from the coordinator activities
 * and decide which handlers to be attached.
 *
 * This handler attachment manager is designed to work with multiple concurrent viewport on the same page
 * It uses a single event manager and when a viewport is focused in, the handlers of the toolbars of that viewport
 * will be attached. The toolbar is retrieved from the coordinator.
 *
 * - editor focus
 * - editor blur
 * - tool-focus
 * - tool-blur
 * - viewport-focusin
 * - viewport-focusout
 *
 */
export class HandlerAttachmentManager {
    curEditor: DocumentEditor;
    timeout: any;
    toolListener: ToolEventListener<ToolBar<any, any>, any>;

    em: EditorEventManager;

    commonToolbar?: CommonToolbar;
    editorToolbar?: ToolBar<any, any>;
    focusedVM: ViewportManager;
    focusTool: ToolEventData<ToolBar<any, any>, any>[] = [];

    viewportFocusedStacks: Map<string, ToolEventData<ToolBar<any, any>, any>[]> = new Map();

    private externalMonitored: HTMLElement[] = [];

    cursorManager: CursorManager;
    private cursorMonitor: CursorMonitor;

    constructor(private coordinator: EmbedCoordinator) {
        coordinator.coordEventEmitter.registerListener(new this.CoordinatorListener(this));
        this.toolListener = new this.ToolListener(this);
        this.em = coordinator.em;
    }

    additionalMonitoredElements(els: HTMLElement[]) {
        this.externalMonitored = this.externalMonitored.concat(els);

        // if there are an active viewport currently, notify its event manager to capture events for these additional elements
        for (const el of els) {
            this.em.captureEventsFor(el);
        }
    }

    private getEditorToolbars() {
        return Array.from(this.coordinator.editors.keys())
            .map(e => this.coordinator.getEditorToolbar(this.focusedVM.id, e))
            .filter(Boolean);
    }

    private handleViewportModeChange(vpMode: ViewportMode) {
        if (!vpMode) return;

        const edToolbars = this.getEditorToolbars();
        if (vpMode === 'Disabled') {
            this.commonToolbar.disable();
            edToolbars.forEach(tb => tb?.disable());
        } else {
            this.commonToolbar.enable();
            edToolbars.forEach(tb => tb?.enable());
        }

        [this.commonToolbar, ...edToolbars].forEach(tb => {
            (tb as DefaultToolBar<any, any>)?.onViewportModeChanged(vpMode);
        });
    }

    private CoordinatorListener = class implements VEventListener<CoordinatorEvent> {
        constructor(private m: HandlerAttachmentManager) {}

        async onEvent(eventData: CoordinatorEvent): Promise<CoordinatorEvent> {
            switch (eventData.eventType) {
                case 'editor-focus':
                case 'editor-blur': {
                    const eS = eventData.state as EditorFocusCES;

                    if (!eS.vmId || eS.vmId != this.m.focusedVM.id) return eventData; // do nothing if this event is for a viewport that this HAM is not in charge of

                    // when an editor is focused, we listen to its tool event
                    const tb = this.m.coordinator.getEditorToolbar(eS.vmId, eS.editor.editorType);

                    if (!tb) return eventData;

                    if (eventData.eventType == 'editor-focus') {
                        tb.registerToolListener(this.m.toolListener);

                        if (tb.curTool) tb.blur(tb.curTool);

                        this.m.editorToolbar = tb;
                    } else {
                        if (tb.curTool) await tb.blur(tb.curTool);
                        tb.unregisterToolListener(this.m.toolListener);

                        delete this.m.editorToolbar;
                    }
                    break;
                }
                case 'viewport-focusin': {
                    const veSIn = eventData.state as ViewportFocusInCES;

                    this.m.focusedVM = this.m.coordinator.getViewportManager(veSIn.vmId);

                    // cursor manager
                    this.m.cursorManager = new CursorManager(this.m.focusedVM);
                    this.m.cursorManager.add(LOCAL_CURSOR);
                    this.m.cursorMonitor = new CursorMonitor(
                        this.m.focusedVM,
                        this.m.cursorManager,
                        LOCAL_CURSOR,
                        // currently, for wrapping inline viewport manager, event manager doesn't intercept any event
                        // from its layers. Hence, cursor monitor will not work, and we will just use the browser cursor
                        this.m.focusedVM instanceof WrappingInlineViewportManager ? browserCursor : defaultCursor
                    );

                    this.m.commonToolbar = this.m.coordinator.getCommonToolbar(veSIn.vmId);
                    this.m.commonToolbar.registerToolListener(this.m.toolListener);

                    this.m.focusTool = this.m.viewportFocusedStacks.get(veSIn.vmId) || [];

                    break;
                }
                case 'viewport-focusout': {
                    if (!this.m.focusedVM) break;

                    //let veSOut = eventData.state as ViewportFocusOutCES
                    this.m.resetAll();

                    // backup the list of all focus tools of a viewport so that when the viewport is focused in,
                    // we can restore the listening
                    if (this.m.focusTool.length > 0)
                        this.m.viewportFocusedStacks.set(this.m.focusedVM.id, this.m.focusTool);

                    if (this.m.commonToolbar) {
                        // there could be case that focusout occur when the page is just loaded
                        // and the mouse already in the viewport and hence no focusin was set
                        this.m.commonToolbar.unregisterToolListener(this.m.toolListener);
                        delete this.m.commonToolbar;
                    }

                    this.m.cursorMonitor.destroy();
                    this.m.cursorManager.destroy();
                    delete this.m.cursorMonitor;
                    delete this.m.cursorManager;
                    delete this.m.focusedVM;

                    break;
                }

                case 'viewport-interactive-mode':
                case 'viewport-view-mode':
                case 'viewport-edit-mode': {
                    const veS = eventData.state as ViewportEnabledCES;

                    if (this.m.externalMonitored.length > 0)
                        for (const el of this.m.externalMonitored) {
                            const vm = this.m.coordinator.getViewportManager(veS.vmId);
                            vm.eventManager.captureEventsFor(el);
                        }

                    this.m.handleViewportModeChange(EventToViewportMode[eventData.eventType]);
                    break;
                }
                case 'viewport-disabled': {
                    const veS = eventData.state as ViewportDisableCES;
                    const vm = this.m.coordinator.getViewportManager(veS.vmId);
                    if (veS.vmId !== this.m.focusedVM.id) return eventData;

                    this.m.cursorManager.hideCursor(LOCAL_CURSOR);

                    // if additional elements being monitored, the event manager of the disabled viewport shouldn't capture anymore
                    if (this.m.externalMonitored.length > 0)
                        for (const el of this.m.externalMonitored) vm.eventManager.uncaptureEventsFor(el);
                    this.m.handleViewportModeChange(EventToViewportMode[eventData.eventType]);
                    break;
                }

                default: // default, we break out and don't register handler
                    return eventData;
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    };

    /**
     * Handling attachment when tool focus / blur occurs
     */
    private ToolListener = class implements ToolEventListener<ToolBar<any, any>, any> {
        processingFocus: boolean = false;

        constructor(private m: HandlerAttachmentManager) {}

        isAncestorOf(t: ToolBar<any, Tool>, check: Tool): boolean {
            let result = false;
            if (check.childToolbar) {
                if (check.childToolbar == t) result = true;
                else {
                    for (const tool of check.childToolbar.tools.values()) {
                        if (tool.childToolbar && this.isAncestorOf(t, tool)) {
                            result = true;
                            break;
                        }
                    }
                }
            }

            return result;
        }

        async onEvent(
            eventData: ToolEventData<ToolBar<any, any>, any>
        ): Promise<ToolEventData<ToolBar<any, any>, any>> {
            if (eventData.eventType == 'change') return eventData;

            const t = eventData.source.getTool(eventData.toolType);
            const s = eventData.source;

            switch (eventData.eventType) {
                case 'focus': {
                    const tobeBlur = [].concat(this.m.focusTool).reverse();
                    const remaining = [];

                    this.processingFocus = true;
                    for (const e of tobeBlur) {
                        // only blur tools which are not the ancestor of the focused tool
                        const tobeBlurTool = e.source.getTool(e.toolType);

                        if (this.isAncestorOf(s, tobeBlurTool)) remaining.unshift(e);
                        else e.source.blur(e.toolType);
                    }

                    this.processingFocus = false;
                    this.m.focusTool = [...remaining, eventData]; // focusTool now only contains the newly focused tool or its ancestor tool

                    if (t.childToolbar) {
                        t.childToolbar.registerToolListener(this.m.toolListener);
                    }
                    break;
                }

                case 'transient-focus': {
                    // if the focus is transient, we don't blur existing tool
                    // so that when the transient focused tool is blurred, we can revert back to the existing tool in the correct order.
                    this.m.focusTool.push(eventData);
                    break;
                }
                case 'blur':
                case 'transient-blur': {
                    if (t.childToolbar) {
                        t.childToolbar.unregisterToolListener(this.m.toolListener);
                    }
                    // if a tool is blur, we remove it from the focus tool and re-register the handler
                    this.m.focusTool = this.m.focusTool.filter(
                        e => e.source != eventData.source || e.toolType != eventData.toolType
                    );

                    break;
                }
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    };

    private resetAll() {
        this.em.resetKeyboardHandling();
        this.em.resetMouseHandling();
        this.em.resetPointerHandling();
    }

    private registerHandler() {
        this.resetAll();

        delete this.timeout;

        const em = this.em;
        if (!em) return;

        const focusTools: UserInputHandler[] = this.focusTool.map(e => e.source.getTool(e.toolType));
        const handlerStack: UserInputHandler[] = [this.commonToolbar, this.editorToolbar, ...focusTools];

        // register all handling, except for the global ones
        for (const uih of handlerStack) {
            if (uih) {
                if (uih.mouseHandler)
                    for (const mh of uih.mouseHandling) if (!mh.global) em.registerMouseHandling(mh, uih.mouseHandler);

                if (uih.pointerHandler)
                    for (const mh of uih.pointerHandling)
                        if (!mh.global) em.registerPointerHandling(mh, uih.pointerHandler);

                if (uih.keyboardHandler)
                    for (const kh of uih.keyboardHandling)
                        if (!kh.global) em.registerKeyboardHandling(kh, uih.keyboardHandler);
            }
        }

        if (this.commonToolbar) this.registerGlobal(em, this.commonToolbar); // register all global handling of the coordinator toolbar
        if (this.editorToolbar) this.registerGlobal(em, this.editorToolbar); // if there is a focus editor, register all global handling of the tools of that editor

        // update the tool stack in cursor manager so that it knows if focused handler has been changed
        this.cursorMonitor?.updateToolStack(handlerStack.map(h => h?.pointerHandler).filter(h => h != undefined));
    }

    private registerGlobal(em: EditorEventManager, toolbar: ToolBar<any, Tool>) {
        if (toolbar.isDisabled()) return;
        // All global handling of the all tools within a toolbar
        toolbar.tools.forEach((tool, type) => {
            if (tool.mouseHandler) {
                for (const mh of tool.mouseHandling) {
                    if (mh.global) em.registerMouseHandling(mh, tool.mouseHandler);
                }
            }

            if (tool.pointerHandler)
                for (const mh of tool.pointerHandling)
                    if (mh.global) em.registerPointerHandling(mh, tool.pointerHandler);

            if (tool.keyboardHandler) {
                for (const kh of tool.keyboardHandling) {
                    if (kh.global) em.registerKeyboardHandling(kh, tool.keyboardHandler);
                }
            }
        });
    }
}

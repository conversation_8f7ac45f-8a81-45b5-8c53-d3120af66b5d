import {
    BaseBoardViewportManager,
    CoverLayerCtrl,
    DefaultEventData,
    DefaultEventEmitter,
    DocumentLayerEventData,
    DocumentLayerEventType,
    EditorEventManager,
    LayerCreator,
    LayerImplementation,
    LayerOptions,
    PanEventData,
    Position,
    ScreenPosition,
    VDocLayerCtrl,
    VEventListener,
    ZoomEventData,
} from '@viclass/editor.core';
import { debounceTime, map, Subject } from 'rxjs';
import { layerCreators } from './layer.creators';

export type WrappingViewportOptions = {
    noHandleEvent: boolean;
    noMoveBackground: boolean;
};

/**
 * This is a simple viewport that will use provided element as a board.
 * This is different from default board viewport in that it doesn't use SVG.
 *
 * Its main purpose is to display documents instead of using it for editing document.
 * All layer will have absolute positioning within this viewport, as oppose for inline
 * viewport where layer will have normal positioning and cannot be stacked on top of each
 * other.
 *
 * Sizing and the positioning of the viewport itself within the page is controlled by normal CSS
 * on the root el
 */
export class WrappingBoardViewportManager extends BaseBoardViewportManager {
    rootEl: HTMLElement | SVGElement;

    zoomLevel: number;
    viewportRoot: HTMLElement | SVGElement;

    sizeObs: ResizeObserver;

    eventManager: EditorEventManager;
    id: string;

    resizeSource: Subject<any> = new Subject();

    /**
     * The current position (in board coordinate) of the center point of the root element
     */
    currentLookAt: Position;
    /**
     * Position of the top left of the root El in the board coordinate system
     */
    _rootElInBoard: Position;

    layerEventSource: DefaultEventEmitter<DocumentLayerEventData>;

    layers: VDocLayerCtrl[] = [];

    floatStartIndex = 10;
    floating: VDocLayerCtrl[] = [];
    sticking: VDocLayerCtrl[] = [];

    // the layer use to listen mouse and keyboard event
    override readonly separationLayer: CoverLayerCtrl;

    moveBackground: boolean = true;

    requestedBackgroundFrame: boolean = false;

    /**
     *
     * @param el
     * @param handleEvent
     */
    constructor(el: HTMLElement | SVGElement, options?: Partial<WrappingViewportOptions>, id?: string) {
        super();

        /**
         * For this viewport manager, the root element is also where
         * layer elements are attached
         */
        this.viewportRoot = el;
        this.rootEl = el;
        this.zoomLevel = 1;
        this.id = id ? id : el.id;

        this.rootEl.classList.add('viewport-root-el');
        this.rootEl.tabIndex = -1;

        this.sizeObs = new ResizeObserver(entries => this.resizeSource.next(entries));
        this.resizeSource
            .pipe(
                debounceTime(200),
                map(entries => this.viewportResize(entries))
            )
            .subscribe();
        this.sizeObs.observe(this.viewportRoot);

        this.setSize();
        this.lookAt({ x: 0, y: 0 });

        if (!options?.noHandleEvent) {
            this.layerEventSource = new DefaultEventEmitter();
        }

        if (options?.noMoveBackground) {
            this.moveBackground = false;
        }

        Object.assign(this.viewportRoot.style, {
            overflow: 'hidden',
            touchAction: 'none',
        });

        this.panSource
            .pipe(
                map(({ pos, size, uiSource }) => this.panEmitter.emit(new PanEventData({ pos, size }, this, uiSource)))
            )
            .subscribe();

        /**
         * Request and add the layer to viewport right away
         */
        this.separationLayer = this.requestLayer(CoverLayerCtrl, true, {
            viewport: this,
        }) as CoverLayerCtrl;
    }

    get boardSize() {
        return { width: this.viewportWidth(), height: this.viewportHeight() };
    }

    get boardBoundary() {
        const topLeft = this.getBoardPos({ x: 0, y: 0 });
        const bottomRight = this.getBoardPos({
            x: this.viewportScreenWidth(),
            y: this.viewportScreenHeight(),
        });

        return {
            start: topLeft,
            end: bottomRight,
        };
    }

    lookAt(pos: Position) {
        this.currentLookAt = pos;
        this._rootElInBoard = {
            x: pos.x - this.viewportWidth() / 2,
            y: pos.y + this.viewportHeight() / 2,
        };
    }

    viewportResize(entries: ResizeObserverEntry[]) {
        if (!this.setSize()) return;
        this.lookAt({ x: this.currentLookAt.x, y: this.currentLookAt.y });
        this.panSource.next({
            pos: this.currentLookAt,
            size: { width: this._vswdith, height: this._vsheight },
        });
    }

    getBoardPos(pos: ScreenPosition): Position {
        // screen zoom is accumulated zoom level of this viewport and all of its parents -> we want only the parents
        const parentZoom = this.getScreenZoom() / this.zoomLevel;

        // the center point of the viewport is the current look at position
        return {
            x: this.currentLookAt.x + (pos.x * parentZoom - this._vswdith / 2.0) * this.zoomLevel,
            y: this.currentLookAt.y + (-pos.y * parentZoom + this._vsheight / 2.0) * this.zoomLevel,
        };
    }

    addLayer(layer: VDocLayerCtrl) {
        if (this.layers.indexOf(layer) >= 0) return;

        this.layers.push(layer);
        layer.zindex = this.layers.length - 1;

        if (this.floating.length == 0) this.viewportRoot.appendChild(layer.nativeEl);
        else this.viewportRoot.insertBefore(layer.nativeEl, this.floating[0].nativeEl);

        layer.viewport = this;

        if (layer.renderer) layer.renderer(this, layer);
    }

    requestLayer<T extends VDocLayerCtrl>(
        layerImpl: LayerImplementation<T>,
        add: boolean,
        options: LayerOptions
    ): Promise<T> | T {
        if (layerCreators.has(layerImpl)) {
            options.viewport = this;

            const creator = layerCreators.get(layerImpl) as LayerCreator<T, LayerOptions>;
            const layer = creator(options);

            if (layer instanceof Promise) {
                layer.then(l => this.setupLayer(add, l));
            } else {
                this.setupLayer(add, layer);
            }

            return layer;
        } else throw new Error(`Unable to create new layer. There is no implementation for ${layerImpl}`);
    }

    private setupLayer(add: boolean, layer: VDocLayerCtrl) {
        if (add) {
            this.addLayer(layer);
        }
        // prevent the layer from being selected on the board
        Object.assign(layer.nativeEl.style, {
            userSelect: 'none',
        });
    }

    removeLayer(layer: VDocLayerCtrl) {
        if (layer.isFloating) this.sink(layer);

        const index = this.layers.findIndex(v => v == layer);

        if (index >= 0) {
            this.layers.splice(index, 1);
        }

        for (let i = index; i < this.layers.length; i++) {
            this.layers[i].zindex--;
        }

        layer.nativeEl.remove(); // remove from the dom
        if (layer.onRemoved) layer.onRemoved();
    }

    renderOnTop(layer: VDocLayerCtrl) {
        layer.nativeEl.remove();
        this.viewportRoot.appendChild(layer.nativeEl);
        layer.nativeEl.focus();
    }

    renderBehind(layer: VDocLayerCtrl, referenced: VDocLayerCtrl) {
        layer.nativeEl.remove();
        this.viewportRoot.insertBefore(layer.nativeEl, referenced.nativeEl);
        referenced.nativeEl.focus();
    }

    getScreenX(x: number): number {
        return (x - this.currentLookAt.x) / this.zoomLevel + this._vswdith / 2.0;
    }

    getScreenY(y: number): number {
        return -(y - this.currentLookAt.y) / this.zoomLevel + this._vsheight / 2.0;
    }

    getScreenPos(pos: Position): ScreenPosition {
        return { x: this.getScreenX(pos.x), y: this.getScreenY(pos.y) };
    }

    centerAt(pos: Position) {
        this.lookAt(pos);
    }

    screenRoot(): Position {
        return this._rootElInBoard;
    }

    translateViewpoint(deltaXInScreen: number, deltaYInScreen: number, uiSource?: Event) {
        const dx = deltaXInScreen * this.zoomLevel;
        const dy = -deltaYInScreen * this.zoomLevel;

        this.lookAt({
            x: this.currentLookAt.x + dx,
            y: this.currentLookAt.y + dy,
        });

        if (this.moveBackground) {
            if (!this.requestedBackgroundFrame) {
                requestAnimationFrame(() => {
                    this.requestedBackgroundFrame = false;

                    Object.assign(this.rootEl.style, {
                        'background-position-x': Math.round(-this._rootElInBoard.x / this.zoomLevel),
                        'background-position-y': Math.round(this._rootElInBoard.y / this.zoomLevel),
                    });
                });
                this.requestedBackgroundFrame = true;
            }
        }

        /**
         * Let all the layer rerender themselves if they need to react to the pan even
         */
        this.panSource.next({
            pos: this.currentLookAt,
            size: { width: this._vswdith, height: this._vsheight },
            uiSource: uiSource,
        });
    }

    zoom(level: number, zoomRoot?: Position, uiSource?: any) {
        let lx = this.currentLookAt.x;
        let ly = this.currentLookAt.y;

        if (zoomRoot) {
            // calculate current new current look at
            lx = zoomRoot.x + ((this.currentLookAt.x - zoomRoot.x) * level) / this.zoomLevel;
            ly = zoomRoot.y + ((this.currentLookAt.y - zoomRoot.y) * level) / this.zoomLevel;
        }

        this.zoomLevel = level;

        this.lookAt({ x: lx, y: ly });
        this.zoomEmitter.emit(new ZoomEventData({ zoomLevel: level, vpPos: { x: lx, y: ly } }, this, uiSource));
    }

    registerLayerEventListener(handler: VEventListener<DocumentLayerEventData>) {
        if (this.layerEventSource) {
            this.layerEventSource.registerListener(handler);
        }
    }

    unregisterLayerEventListener(handler: VEventListener<DocumentLayerEventData>) {
        if (this.layerEventSource) {
            this.layerEventSource.unregisterListener(handler);
        }
    }

    unregisterAllLayerEventListeners() {
        this.layerEventSource.clearListeners();
    }

    float(layer: VDocLayerCtrl) {
        const index = this.floating.findIndex(v => v == layer);

        if (index >= 0) {
            // refloating, have to remove before push
            this.floating.splice(index, 1);
        } else {
            layer.nativeEl.classList.add('viclass-float-layer');
        }

        this.floating.push(layer);

        Object.assign(layer.nativeEl.style, {
            'z-index': ++this.floatStartIndex + '',
        });
        layer.nativeEl.focus();

        if (index == -1)
            this.layerEventSource
                .emit(new DefaultEventData<DocumentLayerEventType, VDocLayerCtrl>('float', layer))
                .finally(() => (layer.isFloating = true));
    }

    sink(layer: VDocLayerCtrl) {
        const i = this.floating.findIndex(v => v == layer);

        if (i == null || i < 0) return;
        else this.floating.splice(i, 1);

        layer.nativeEl.blur();
        layer.nativeEl.classList.remove('viclass-float-layer');

        Object.assign(layer.nativeEl.style, { 'z-index': layer.zindex });

        layer.isFloating = false;

        this.layerEventSource.emit(new DefaultEventData<DocumentLayerEventType, VDocLayerCtrl>('sink', layer));
    }

    sticky(layer: VDocLayerCtrl) {
        const index = this.sticking.findIndex(v => v == layer);

        const floatIndex = this.floating.findIndex(v => v == layer);

        if (floatIndex >= 0) {
            layer.nativeEl.classList.remove('viclass-float-layer');
            this.floating.splice(index, 1);
        }

        if (index >= 0) {
            this.sticking.splice(index, 1);
        } else {
            layer.nativeEl.classList.add('viclass-sticky-layer');
        }
        this.sticking.push(layer);

        Object.assign(layer.nativeEl.style, {
            'z-index': ++this.floatStartIndex + 1000 + '',
        });
    }

    topLayer(): VDocLayerCtrl | undefined {
        if (this.layers.length < 1) return undefined;
        return this.layers[this.layers.length - 1];
    }

    allLayers(): VDocLayerCtrl[] {
        return this.layers;
    }

    addLayerFactory<T extends VDocLayerCtrl, O extends LayerOptions>(
        impl: LayerImplementation<T>,
        factory: LayerCreator<T, O>
    ) {
        if (layerCreators.has(impl)) return;

        layerCreators.set(impl, factory);
    }

    override async destroy(): Promise<void> {
        await super.destroy();
        this.sizeObs?.disconnect();
        this.resizeSource?.complete();
    }
}

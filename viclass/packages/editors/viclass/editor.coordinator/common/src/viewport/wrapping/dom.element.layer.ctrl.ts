import {
    BoardViewportManager,
    BoundaryRectangle,
    DOMElementLayerCtrl,
    DefaultEventEmitter,
    DocumentEditor,
    HasNativeElementBoundary,
    LocatableEvent,
    MouseEventData,
    PanEventData,
    Position,
    PositionLocatable,
    VDocLayer,
    VDocLayerCtrl,
    VEventListener,
    ViewportManager,
    ZoomEventData,
    extractHeight,
    extractWidth,
    isSizeBoundaryChanged,
} from '@viclass/editor.core';
import { WrappingBoardViewportManager } from './wrapping.board.vm';
import { topLeftScreenPos } from './wrapping.util';

export class NoWrapDOMElementLayer extends DOMElementLayerCtrl {
    override nativeEl: HTMLElement | SVGElement;

    viewportChangeListener: VEventListener<any>;

    private _nativeElBoundary: BoundaryRectangle;
    private _onRemoved?: () => void;

    constructor(
        boundary: BoundaryRectangle,
        override state: VDoc<PERSON>ayer,
        override editor: DocumentEditor,
        override viewport: BoardViewportManager,
        domElType: string
    ) {
        super();

        this._nativeElBoundary = boundary;

        this.nativeMouseEventEmitter = new DefaultEventEmitter();
        this.nativePointerEventEmitter = new DefaultEventEmitter();

        this.nativeEl = document.createElement(domElType);
        this.nativeEl.classList.add('vi-dom-layer');

        Object.assign(this.nativeEl.style, {
            overflow: 'visible',
            position: 'absolute',
            touchAction: 'none',
            transformOrigin: 'top left',
            // optimize rendering (see https://developer.mozilla.org/en-US/docs/Web/CSS/content-visibility)
            // allow browser to omit rendering of elements that are outside the browser viewport
            contentVisibility: 'auto',
            // default to a relative large size on no boundary to prevent layout jump
            containIntrinsicSize: `auto ${boundary?.width || 2000}px auto ${boundary?.height || 2000}px`,
        });

        this.viewportChangeListener = this.initViewportChangeListener();

        this.viewport.panEventEmitter().registerListener(this.viewportChangeListener);
        this.viewport.zoomEventEmitter().registerListener(this.viewportChangeListener);

        this.adjustPosition();

        this.nativeMouseEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the mouse events from this
            this.registerMouseEventOnElement(this.nativeEl);
        };

        this.nativeMouseEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterMouseEventsOnElement(this.nativeEl);
        };

        this.nativePointerEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the pointer events from this
            this.registerPointerEventOnElement(this.nativeEl);
        };

        this.nativePointerEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterPointerEventsOnElement(this.nativeEl);
        };
    }

    get boundary() {
        return this._nativeElBoundary;
    }

    get domEl() {
        return this.nativeEl;
    }

    updateBoundary(b: BoundaryRectangle) {
        const isSizeChanged = isSizeBoundaryChanged(b, this._nativeElBoundary);
        this._nativeElBoundary = b;
        if (isSizeChanged) {
            this.adjustPosition();
            if (this.renderer) this.renderer(this.viewport, this);
        } else {
            this._nativeElBoundary = b;
            this.adjustPosition();
        }
    }

    private adjustPosition() {
        const sp = topLeftScreenPos(this.boundary, this.viewport);

        const w = extractWidth(this.boundary);
        const h = extractHeight(this.boundary);

        Object.assign(this.nativeEl.style, {
            transform: `translate(${Math.round(sp.x)}px,${Math.round(sp.y)}px) scale(${1 / this.viewport.zoomLevel})`,
            width: `${w}px`,
            height: `${h}px`,
        });
    }

    mouseLocation(eventData: LocatableEvent<VDocLayerCtrl>): Position {
        const event = eventData.nativeEvent;
        const domRect = this.nativeEl.getBoundingClientRect();

        // position inside the canvas but in screen coordinate system
        const elScrPos = {
            x: event.clientX - domRect.x,
            y: event.clientY - domRect.y,
        };

        // top left position
        const tx = Math.min(this.boundary.start.x, this.boundary.end.x);
        const ty = Math.max(this.boundary.start.y, this.boundary.end.y);

        // because elScrPos is the vector from the top left point to the mouse click position, in screen coordinate
        // hence, have to multiply with the zoom level to know the distance in the actual board coordinate system
        return {
            x: tx + elScrPos.x * this.viewport.zoomLevel,
            y: ty - elScrPos.y * this.viewport.zoomLevel,
        };
    }

    override get onRemoved() {
        return () => {
            this.viewport.zoomEventEmitter().unregisterListener(this.viewportChangeListener);

            if (this._onRemoved) this._onRemoved();
        };
    }

    override set onRemoved(value: () => void) {
        this._onRemoved = value;
    }

    protected initViewportChangeListener(): VEventListener<any> {
        const self = this;
        let requestedFrame = false;
        return new (class implements VEventListener<ZoomEventData | PanEventData> {
            timeout: any;

            onEvent(eventData: any): any {
                if (!requestedFrame) {
                    requestAnimationFrame(fNo => {
                        requestedFrame = false;

                        self.adjustPosition();

                        if (self.renderer) self.renderer(self.viewport, self);
                    });

                    requestedFrame = true;
                }

                return eventData;
            }
        })();
    }
}

export class NoWrapInlineDOMElementLayer extends DOMElementLayerCtrl {
    override boundary: BoundaryRectangle = undefined;
    nativeEl: HTMLElement | SVGElement;
    domEl: HTMLElement | SVGElement;

    constructor(
        public viewport: ViewportManager,
        editor?: DocumentEditor,
        state?: VDocLayer,
        el?: HTMLElement
    ) {
        super();

        this.editor = editor;
        this.state = state;

        if (!el) el = document.createElement('div');

        el.classList.add('vi-inline-dom-layer');
        this.nativeEl = el;
        this.domEl = this.nativeEl;

        this.nativeMouseEventEmitter = new DefaultEventEmitter();
        this.nativePointerEventEmitter = new DefaultEventEmitter();

        this.nativeMouseEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the mouse events from this
            this.registerMouseEventOnElement(this.nativeEl);
        };

        this.nativeMouseEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterMouseEventsOnElement(this.nativeEl);
        };

        this.nativePointerEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the pointer events from this
            this.registerPointerEventOnElement(this.nativeEl);
        };

        this.nativePointerEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterPointerEventsOnElement(this.nativeEl);
        };
    }

    override updateBoundary(b: BoundaryRectangle) {
        // do nothing
    }
}

/**
 * An unbounded DOM element layer controller that covers the entire viewport
 * Similar to NoWrapUnboundedGraphicLayerCtrl but for DOM elements
 */
export class NoWrapUnboundedDOMElementLayer
    extends DOMElementLayerCtrl
    implements PositionLocatable, HasNativeElementBoundary
{
    override nativeEl: HTMLElement | SVGElement;
    domEl: HTMLElement | SVGElement;

    nativeElBoundary: BoundaryRectangle;
    private oldViewportWidth: number = 0;
    private oldViewportHeight: number = 0;

    viewportChangeListener = this.initViewportChangeListener();
    private _onRemoved: () => void;

    constructor(
        override viewport: WrappingBoardViewportManager,
        override editor?: DocumentEditor,
        override state?: VDocLayer,
        domElType: string = 'div'
    ) {
        super();

        this.nativeMouseEventEmitter = new DefaultEventEmitter();
        this.nativePointerEventEmitter = new DefaultEventEmitter();

        this.nativeEl = document.createElement(domElType);
        this.domEl = this.nativeEl;
        this.nativeEl.classList.add('vi-wrapping-unbounded-dom-layer');

        // the unbounded DOM layer covers the whole viewport
        Object.assign(this.nativeEl.style, {
            position: 'absolute',
            top: 0,
            left: 0,
            overflow: 'visible',
            touchAction: 'none',
            transformOrigin: 'top left',
        });

        this.calculateBoundary();

        this.viewport.panEventEmitter().registerListener(this.viewportChangeListener);
        this.viewport.zoomEventEmitter().registerListener(this.viewportChangeListener);

        this.nativeMouseEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the mouse events from this
            this.registerMouseEventOnElement(this.nativeEl);
        };

        this.nativeMouseEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterMouseEventsOnElement(this.nativeEl);
        };

        this.nativePointerEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the pointer events from this
            this.registerPointerEventOnElement(this.nativeEl);
        };

        this.nativePointerEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterPointerEventsOnElement(this.nativeEl);
        };
    }

    get boundary() {
        return this.nativeElBoundary;
    }

    calculateBoundary() {
        this.nativeElBoundary = {
            start: this.viewport.getBoardPos({ x: 0, y: 0 }),
            end: this.viewport.getBoardPos({
                x: this.viewport.viewportScreenWidth(),
                y: this.viewport.viewportScreenHeight(),
            }),
        };

        if (
            this.oldViewportWidth != this.viewport.viewportScreenWidth() ||
            this.oldViewportHeight != this.viewport.viewportScreenHeight()
        ) {
            // store this so we don't have to change size and style everytime panning
            this.oldViewportWidth = this.viewport.viewportScreenWidth();
            this.oldViewportHeight = this.viewport.viewportScreenHeight();

            this.adjustPosition();
        }
    }

    private adjustPosition() {
        const zoom = this.viewport.getScreenZoom();

        Object.assign(this.nativeEl.style, {
            width: `${this.viewport.viewportScreenWidth() * zoom}px`,
            height: `${this.viewport.viewportScreenHeight() * zoom}px`,
            transform: `scale(${1 / zoom})`,
        });
    }

    override updateBoundary(b: BoundaryRectangle) {
        // For unbounded layer, we calculate boundary automatically
        // This method is kept for interface compatibility but does nothing
    }

    mouseLocation(eventData: MouseEventData<any>): Position {
        const event: MouseEvent = eventData.nativeEvent;
        const boundingRect = (event.target as Element).getBoundingClientRect();

        // because the unbounded layer cover exactly the same area as the viewportRoot element, we just simply using
        // the method from the viewport
        return this.viewport.getBoardPos({
            x: event.clientX - boundingRect.x,
            y: event.clientY - boundingRect.y,
        });
    }

    override get onRemoved() {
        return () => {
            this.viewport.zoomEventEmitter().unregisterListener(this.viewportChangeListener);
            this.viewport.panEventEmitter().unregisterListener(this.viewportChangeListener);

            if (this._onRemoved) this._onRemoved();
        };
    }

    override set onRemoved(value: () => void) {
        this._onRemoved = value;
    }

    protected initViewportChangeListener(): VEventListener<any> {
        const self = this;
        let requestedFrame = false;
        return new (class implements VEventListener<ZoomEventData | PanEventData> {
            onEvent(eventData: any): any {
                if (eventData instanceof PanEventData) self.calculateBoundary();

                if (!requestedFrame) {
                    requestAnimationFrame(fNo => {
                        requestedFrame = false;
                        self.adjustPosition();
                        if (self.renderer) self.renderer(self.viewport, self);
                    });
                    requestedFrame = true;
                }

                return eventData;
            }
        })();
    }
}

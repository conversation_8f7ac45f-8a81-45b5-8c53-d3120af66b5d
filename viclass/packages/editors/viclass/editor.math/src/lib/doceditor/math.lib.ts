import { ComputeEngine } from '@cortex-js/compute-engine';
import { EscapeDirection } from '@viclass/editor.core';
import {
    convertLatexToAsciiMath,
    convertLatexToMarkup,
    convertLatexToMathMl,
    Expression,
    MathfieldElement,
    MoveOutEvent,
    validateLatex,
} from 'lib-mathlive';
import { BehaviorSubject } from 'rxjs';
import { MathEditorConfig } from '../config';
import { MathDocCtrl } from '../docs/math.doc.ctrl';
import { setupVirtualKeyboardLayout } from '../latex/latex.utils';
import { MathEditor } from '../math.editor';
import { MathSyntaxError } from '../model';
import { setupLocale } from './locale';
import { MathHistoryManager, VirtualKeyboardManager } from './managers';

export type LatexState = {
    latex: string;
    errors: MathSyntaxError[];
};

/**
 * Wrapper arround mathlive to provide functionalities for math docs.
 * Includes getting the current content in different formats (LaTeX, MathML, AsciiMath),
 * inserting LaTeX, handle clipboard, virtual keyboard, and reacting to events from mathfield
 */
export class MathLib {
    private static initialized = false;

    private _lazyMathfield?: MathfieldElement;
    private isEnable = false;
    private lastTempLatex: string = '';
    private tempMarkup?: HTMLSpanElement;

    readonly virtualKeyboardManager = new VirtualKeyboardManager(this);
    readonly mathHistoryManager = new MathHistoryManager(this);

    readonly latexChanged$ = new BehaviorSubject<LatexState>({ latex: '', errors: [] });

    get mathfield(): MathfieldElement {
        if (!this._lazyMathfield) this.initMathfield();
        return this._lazyMathfield!;
    }

    constructor(
        public root: HTMLElement,
        public editor: MathEditor,
        public docCtrl: MathDocCtrl
    ) {
        MathLib.initMathlive(editor);

        if (!editor.conf.optimizeLoading) {
            this.initMathfield();
        } else {
            this.tempMarkup = document.createElement('span');
            Object.assign(this.tempMarkup.style, {
                display: 'inline-block',
                padding: '6px 7px 6px 5px', // the default style of container + content span inside mathfield
                fontSize: '20px',
            });
            this.root.appendChild(this.tempMarkup);
        }
    }

    /**
     * Lazily create Mathfield element, listen to events and initialize the managers
     * (which handle the features like virtual keyboard and history).
     * On optimize loading mode, this is only done on the first time the math doc is focused.
     */
    private initMathfield() {
        if (this._lazyMathfield) return;

        this._lazyMathfield = new MathfieldElement();
        Object.assign(this._lazyMathfield.style, {
            height: '100%',
            display: 'block',
            outline: 'none',
            borderColor: 'transparent',
            userSelect: 'auto',
        });

        // remove the temp markup node if it exists -> add new mathfield node
        this.tempMarkup?.remove();
        delete this.tempMarkup;
        this.root.appendChild(this._lazyMathfield);

        this._lazyMathfield.readOnly = !this.isEnable; // which is default to be readOnly
        this._lazyMathfield.menuItems = [];
        this._lazyMathfield.setValue(this.lastTempLatex);

        this._lazyMathfield.addEventListener('input', this.handleInputEvent);
        this._lazyMathfield.addEventListener('move-out', this.onMoveOut);

        const managers = [this.virtualKeyboardManager, this.mathHistoryManager];
        managers.forEach(m => m.init());
    }

    /**
     * Initialize the whole mathfield environment with ComputeEngine and other settings
     */
    private static initMathlive(editor: MathEditor) {
        if (MathLib.initialized) return;
        MathLib.initialized = true;

        const conf = editor.conf as MathEditorConfig;

        // @ts-ignore: missing properties expected because the private props are hidden in the definition
        MathfieldElement.computeEngine = new ComputeEngine();
        MathfieldElement.fontsDirectory = conf.fontsUri;
        MathfieldElement.soundsDirectory = conf.soundsUri;

        setupLocale();
    }

    /**
     * Cleanup the mathfield and the managers
     */
    destroy() {
        this.tempMarkup?.remove();
        delete this.tempMarkup;
        if (!this._lazyMathfield) return;

        this._lazyMathfield.removeEventListener('input', this.handleInputEvent);
        this._lazyMathfield.removeEventListener('move-out', this.onMoveOut);

        const managers = [this.virtualKeyboardManager, this.mathHistoryManager];
        managers.forEach(m => m.destroy());
        this._lazyMathfield.disconnectedCallback();
        this._lazyMathfield.remove();
        this._lazyMathfield = undefined as any; // force release from memory
    }

    private handleInputEvent = () => {
        this.docCtrl.saveContentUpdate();
        const latex = this.getLatex();
        this.latexChanged$.next({ latex, errors: validateLatex(latex) });
    };

    /**
     * Enable editing of the mathfield
     */
    enable() {
        this.isEnable = true;
        if (this._lazyMathfield) this._lazyMathfield.readOnly = false;
    }

    /**
     * Disable editing of the mathfield
     */
    disable() {
        this.isEnable = false;
        if (this._lazyMathfield) this._lazyMathfield.readOnly = true;
    }

    /**
     * Insert LaTeX into the mathfield. The provided LaTeX will be inserted and replace the current selection
     */
    insertLatex(latex: string) {
        this.mathfield.insert(latex, { selectionMode: 'placeholder', insertionMode: 'replaceSelection' });
    }

    /**
     * Set the LaTeX content of the mathfield. The difference from {@link insertLatex}
     * is that this will replace the entire content
     */
    setLatex(latex: string) {
        if (this._lazyMathfield) {
            this._lazyMathfield.setValue(latex);
        } else {
            this.updateTempMarkupWithLatex(latex);
        }

        this.latexChanged$.next({ latex, errors: validateLatex(latex) });
    }

    /**
     * Get the LaTeX content of the mathfield
     */
    getLatex(): string {
        return this._lazyMathfield ? this._lazyMathfield.getValue() : this.lastTempLatex;
    }

    /**
     * Validate if the current content is a valid LaTeX expression
     */
    validate(): MathSyntaxError[] {
        return validateLatex(this.getLatex());
    }

    /**
     * Get the mathfield content in MathML format
     */
    getMathML(): string {
        return convertLatexToMathMl(this.getLatex());
    }

    /**
     * Get the mathfield content in AsciiMath format
     */
    getAsciiMath(): string {
        return convertLatexToAsciiMath(this.getLatex());
    }

    /**
     * Get the mathfield content in MathJSON format
     */
    getMathJSON(): string {
        const expression = this.getExpression();
        return expression ? JSON.stringify(expression) : '';
    }

    /**
     * On document focus, this will focus on the mathfield to allow typing.
     * We will reset some settings before focusing as we can have multiple mathfields
     * with different setups in the page (i.g. the mathfield to edit equations in MathGraph/Geometry)
     */
    focus() {
        if (!this._lazyMathfield) this.initMathfield();

        setupVirtualKeyboardLayout();
        this._lazyMathfield.menuItems = []; // don't show mathlive menu
        this._lazyMathfield.focus();
    }

    /**
     * Returns the current boxed MathJSON expression of the mathfield
     * @see https://cortexjs.io/compute-engine/guides/expressions/
     */
    getExpression(): Expression | null {
        if (this._lazyMathfield) {
            return this._lazyMathfield.expression as Expression | null;
        }

        return MathfieldElement.computeEngine.box(this.lastTempLatex);
    }

    /**
     * Check if there is a selection in the mathfield.
     * Currently used to determine if we should copy only the selected LaTeX or the whole Math doc
     */
    hasSelection(): boolean {
        if (!this._lazyMathfield) return false;

        const selection = this._lazyMathfield.selection;
        // discontinous selection
        if (selection.ranges.length > 1) return true;

        // single selection that's not collapsed
        const range = selection.ranges[0];
        return range[0] !== range[1];
    }

    /**
     * Copy the current selection to the clipboard
     */
    copyToClipboard() {
        if (!this._lazyMathfield) this.initMathfield();

        // copy programatically -> block unnecessary event from leaking outside
        const copyBlock = (e: ClipboardEvent) => {
            e.preventDefault();
            e.stopPropagation();
        };
        this.root.addEventListener('copy', copyBlock);

        this._lazyMathfield.executeCommand('copyToClipboard');

        this.root.removeEventListener('copy', copyBlock);
    }

    /**
     * Delete the current selection in the mathfield
     */
    deleteSelection() {
        if (!this._lazyMathfield) this.initMathfield();

        this._lazyMathfield.executeCommand('deleteBackward');
    }

    /**
     * Paste the clipboard content into the mathfield
     */
    pasteFromClipboard() {
        if (!this._lazyMathfield) this.initMathfield();

        // paste programatically -> block unnecessary event from leaking outside
        const pasteBlock = (e: ClipboardEvent) => {
            e.preventDefault();
            e.stopPropagation();
        };
        this.root.addEventListener('paste', pasteBlock);

        this._lazyMathfield.focus();
        this._lazyMathfield.executeCommand('pasteFromClipboard');

        this.root.removeEventListener('paste', pasteBlock);
    }

    /**
     * Handle moving out of the mathfield to the parent context.
     * For example, when math doc is used as a sub-viewport inside word, press arrow key at the end of mathfield
     * will move the cursor out of math doc into the parent word doc to continue typing
     */
    private onMoveOut = (ev: CustomEvent<MoveOutEvent>) => {
        this._lazyMathfield?.blur();
        this.editor.selectDelegator.escape(this.docCtrl, ev.detail.direction as EscapeDirection);
    };

    private updateTempMarkupWithLatex(latex: string) {
        if (!this.tempMarkup) {
            console.warn('Something wrong. No temp markup to update the latex: ' + latex);
            return;
        }

        this.lastTempLatex = latex;
        this.tempMarkup.innerHTML = convertLatexToMarkup(latex);
    }
}

import { MathfieldElement } from 'lib-mathlive';
import { MathDocCtrl } from '../../docs/math.doc.ctrl';
import { MathEditor } from '../../math.editor';
import { MathLib } from '../math.lib';

export default abstract class MathManager {
    private readonly unsubscribeFns: Array<() => void> = [];

    protected get mathfield(): MathfieldElement {
        return this.mathLib.mathfield;
    }

    protected get mathEditor(): MathEditor {
        return this.mathLib.editor;
    }

    protected get docCtrl(): MathDocCtrl {
        return this.mathLib.docCtrl;
    }

    constructor(protected mathLib: MathLib) {}

    abstract init(): void;

    protected addUnsubscribe(fn: () => void): void {
        this.unsubscribeFns.push(fn);
    }

    destroy(): void {
        this.unsubscribeFns.forEach(fn => fn());
    }
}

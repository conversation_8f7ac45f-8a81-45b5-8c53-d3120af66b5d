import { VirtualKeyboardInterface } from 'lib-mathlive';
import { BehaviorSubject } from 'rxjs';
import Math<PERSON>anager from '../math.manager';

export class Virtual<PERSON>eyboardManager extends MathManager {
    readonly isShowing$ = new BehaviorSubject<boolean>(false);

    private get virtualKeyboard(): VirtualKeyboardInterface & EventTarget {
        return window.mathVirtualKeyboard;
    }

    override init(): void {
        this.mathfield.mathVirtualKeyboardPolicy = 'manual';

        const handler = () => this.isShowing$.next(!!this.virtualKeyboard?.visible);
        this.virtualKeyboard.addEventListener('virtual-keyboard-toggle', handler);
        this.addUnsubscribe(() => {
            this.virtualKeyboard?.removeEventListener('virtual-keyboard-toggle', handler);
            this.isShowing$.complete();
        });
    }

    setShowVirtualKeyboard(visible: boolean): void {
        if (visible) {
            this.virtualKeyboard.show({ animate: true });
            this.mathLib.focus();
        } else {
            this.virtualKeyboard.hide({ animate: true });
        }
    }
}

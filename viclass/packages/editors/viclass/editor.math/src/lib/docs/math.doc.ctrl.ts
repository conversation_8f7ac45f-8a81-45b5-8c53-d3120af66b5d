import {
    BoundaryRectangle,
    DOMElementLayerCtrl,
    <PERSON><PERSON><PERSON>VDoc<PERSON>trl,
    DocLocalContent,
    DocumentViewMode,
    HasBoundaryCtrl,
    KeyboardEventData,
    KeyboardEventListener,
    MouseEventData,
    MouseEventListener,
    OperationMode,
    SelectHitContext,
    VDocLayerCtrl,
    ViewportManager,
    isCtrlOrMeta,
    reliableCmdMeta,
    reliableSaveCmdMeta,
} from '@viclass/editor.core';
import { MathCmdTypeProto } from '@viclass/proto/editor.math';
import { UpdateContentCmd } from '../cmds/math.cmd';
import { MathLib } from '../doceditor/math.lib';
import { MathEditor } from '../math.editor';
import { MathDoc, MathLayer } from '../model';

const mathliveNativeHotkeys = new Set(['KeyC', 'KeyV', 'KeyX', 'KeyZ', 'KeyY', 'Enter', 'Backspace', 'Delete']);
// prettier-ignore
const mathliveNotHandleKeys = new Set(['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12', 'Escape', 'PrintScreen']);

function isMathHandled(event: KeyboardEvent): boolean {
    const isCtrl = isCtrlOrMeta(event);
    if (isCtrl && mathliveNativeHotkeys.has(event.code)) return true;
    if (!isCtrl && !event.altKey) {
        if (event.code.startsWith('Key') || event.code.startsWith('Digit')) return true;
        if (!mathliveNotHandleKeys.has(event.code)) return true;
    }

    return false;
}

export class MathDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    // keyboard listener that listens for all keyboard events originating from the math document body
    keyboardListener: KeyboardEventListener<any> = this.createKeyboardEventListener(this);
    mouseListener: MouseEventListener<any> = this.createMouseEventListener(this);

    layer: DOMElementLayerCtrl;

    root: HTMLDivElement;

    mathLib: MathLib;

    private docViewMode: DocumentViewMode;

    // --------------------

    constructor(
        override editor: MathEditor,
        public override state: MathDoc,
        viewport: ViewportManager
    ) {
        super(state, editor, viewport);
        this.docViewMode = editor.conf.docViewMode;
    }

    /**
     * @inheritdoc
     */
    override addLayer(layer: VDocLayerCtrl): void {
        if (!(layer instanceof DOMElementLayerCtrl)) return;

        super.addLayer(layer);

        this.layer = layer;
        this.state.addLayer(layer.state as MathLayer);
    }

    /**
     * Remove the document from the viewport
     */
    onRemove() {
        this.removeDocContent();

        this.state.layer = undefined;
        this.removeLayer(this.layer);
        this.viewport.removeLayer(this.layer);
    }

    /**
     * Remove the document content, include the mathfield other the DOM elements
     */
    removeDocContent() {
        this.mathLib?.destroy();

        this.root.remove();
        delete this.root;
    }

    /**
     * Check hit on the document on mouse selection
     */
    checkHit(event: MouseEventData<any>, l: DOMElementLayerCtrl): SelectHitContext {
        const rect = l.domEl.getBoundingClientRect();
        const posX = event.nativeEvent.clientX;
        const posY = event.nativeEvent.clientY;

        if (rect.left < posX && posX < rect.right && rect.top < posY && posY < rect.bottom) {
            return {
                doc: this,
                hitDetails: undefined,
            };
        }
        return undefined;
    }

    disableEditMode() {
        this.mathLib.disable();
    }

    enableEditMode() {
        this.mathLib.enable();
    }

    private createKeyboardEventListener(_p: MathDocCtrl): KeyboardEventListener<any> {
        return new (class implements KeyboardEventListener<any> {
            onEvent(eventData: KeyboardEventData<any>): KeyboardEventData<any> {
                if (isMathHandled(eventData.nativeEvent)) {
                    eventData.continue = false;
                } else {
                    // Math editor will handle all keys so nothing else should propagate to the outside.
                    // Here we use `stopPropagation` instead of `continue = false` because we need other key handlers
                    // from the math tools to handle it after this
                    eventData.nativeEvent.stopPropagation();
                }

                return eventData;
            }
        })();
    }

    private createMouseEventListener(_p: MathDocCtrl): MouseEventListener<any> {
        return new (class implements MouseEventListener<any> {
            onEvent(eventData: MouseEventData<any>): MouseEventData<any> {
                const target = eventData.nativeEvent.target as HTMLElement;
                if (target && _p.root?.contains(target)) {
                    eventData.continue = false;
                }
                return eventData;
            }
        })();
    }

    /**
     * Called by toolbar on doc-unfocused event to blur and disable the mathfield of the math document
     */
    unselect() {
        this.disableEditMode();
        if (this.layer) this.viewport.sink(this.layer);
    }

    /**
     * Called by toolbar on doc-focused event to refocus on the mathfield of math document
     */
    select() {
        if (this.layer) this.viewport.float(this.layer);
        this.mathLib.focus();
    }

    /**
     * Used by command processor to update the boundary of the math document
     */
    updateBoundary(boundary: BoundaryRectangle) {
        if (this.docViewMode == 'bounded') {
            this.state.layer.boundary = boundary;

            if (this.layer) this.layer.updateBoundary(boundary);
        }
    }

    /**
     * Initializes the layer content of the math document.
     * To be used by the command processor when insert new layer
     */
    initializeLayerContent() {
        this.setContent(this.state.content);
    }

    /**
     * Sets the content of the math document.
     * If `takeSnapshot` is true, a snapshot of the current history will be taken.
     *
     * @param content The LaTeX content of the math document.
     * @param takeSnapshot Whether a snapshot of the current history should be taken.
     */
    setContent(content: string, takeSnapshot = false) {
        if (!this.mathLib) {
            this.root = document.createElement('div') as HTMLDivElement;
            this.root.classList.add('math-root');
            this.layer.domEl.appendChild(this.root);

            this.mathLib = new MathLib(this.root, this.editor, this);
            this.mathLib.setLatex(content);

            Object.assign(this.root.style, {
                padding: '0',
                margin: '0',
                width: '100%',
                height: '100%',
            });
            /**
             * We want the click & mousedown event to only limited inside the word doc
             * and not be processed by the parent viewport. Otherwise the SelectTool of parent viewport
             * will be triggered and accidentally de-select the word doc.
             */
            const handleRootElementEvent = (e: MouseEvent) => {
                e.stopPropagation();
                if (e.button === 1) {
                    e.preventDefault(); // prevent middle mouse browser cursor
                }
            };
            this.root.addEventListener('click', handleRootElementEvent);
            this.root.addEventListener('mousedown', handleRootElementEvent);
        } else {
            this.mathLib.setLatex(content || '');
        }

        if (takeSnapshot) {
            this.mathLib.mathHistoryManager.onSnapshot();
        }

        this.editor.notifyContentVisibilityChange(this);
    }

    /**
     * Update the doc content by local content if the content version is newer.
     * Used in LOCAL mode to sync the doc content from peers to this document.
     */
    override updateByLocalContent(localContent: DocLocalContent): void {
        if (localContent.version <= this.state.version) return;

        this.state.version = localContent.version;
        this.setContent(localContent.content);
    }

    saveContentUpdate = async () => {
        this.state.version++;

        const meta =
            this.editor.operationMode !== OperationMode.LOCAL
                ? reliableSaveCmdMeta(
                      this.viewport,
                      this.state,
                      this.state.id,
                      this.state.id,
                      MathCmdTypeProto.UPDATE_CONTENT
                  )
                : reliableCmdMeta(this.viewport, this.state.id, this.state.id, MathCmdTypeProto.UPDATE_CONTENT);

        const cmd = new UpdateContentCmd(meta);
        cmd.setVersion(this.state.version);
        cmd.setGlobalId(this.state.globalId);
        cmd.setEquation(this.mathLib.getLatex(), this.mathLib.getMathJSON());
        await this.editor.sendCommand(cmd);

        this.editor.notifyContentVisibilityChange(this);

        if (this.editor.operationMode !== OperationMode.CLOUD) {
            this.editor.onUpdateLocalContent(this);
        }
    };

    getBoundary(): BoundaryRectangle {
        return this.state.layer?.boundary;
    }
}

/// <reference types="@viclass/ww/typings"/>
import { BoundaryDelegator } from '@viclass/editor.coordinator/classroom';
import {
    Cmd,
    CmdChannel,
    ContentVisibilityCheckFeature,
    ContextMenuFeature,
    CopyPasteDTO,
    createDummyLocalDocId,
    CRUDChangeResult,
    CRUDDelegator,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocPrepInfo,
    DocumentEditor,
    DocumentId,
    DOMElementLayerCtrl,
    EditorBackendConnector,
    EditorBase,
    EditorConfig,
    EditorCoordinator,
    EditorId,
    EditorType,
    ExportTarget,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FEATURE_CONTENT_VISIBILITY,
    FEATURE_COPYPASTE,
    FEATURE_CRD_DOC,
    FEATURE_CRUD,
    FEATURE_HISTORY,
    FEATURE_HTML_EXPORT,
    FEATURE_REMOVE,
    FEATURE_ROB,
    FEATURE_SELECTION,
    getDTOFromClipboard,
    HasSelectionFeature,
    HistoryFeature,
    InsertDocCtrlDelegator,
    LoadingContext,
    OperationMode,
    ROBFeature,
    SelectContext,
    SelectDelegator,
    SelectionFeature,
    SupportContentVisibilityCheckFeature,
    SupportCopyPasteFeature,
    SupportFeatureHistory,
    SupportHtmlExportFeature,
    SupportRemoveFeature,
    SupportSelectFeature,
    ThrottleCombinator,
    ToolBar,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { MathCmdTypeProto } from '@viclass/proto/editor.math';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { convertLatexToMarkup, MathfieldElement } from 'lib-mathlive';
import { deserializer } from './cmds/math.cmd';
import { MathCmdProcessor } from './cmds/math.cmd.processor';
import { MathDocCtrl } from './docs/math.doc.ctrl';
import { MathHistoryItem } from './history';
import { MathGateway } from './math.gateway';
import { MathDocInitData, MathDocLocalContent } from './math.models';
import { FetchDocResponse, MathDoc, MathLayer } from './model';
import {
    InsertLatexTool,
    MathContentTool,
    MathCreateDocumentTool,
    MathStyleTool,
    MathToolBar,
    MathToolType,
    VirtualKeyboardTool,
} from './tools';

/**
 * Implementation of the viclass math LaTeX editor
 */
export class MathEditor
    extends EditorBase<MathDocCtrl>
    implements
        DocumentEditor,
        SupportContentVisibilityCheckFeature,
        SupportCopyPasteFeature,
        SupportRemoveFeature,
        HasSelectionFeature,
        SupportFeatureHistory,
        SupportHtmlExportFeature
{
    readonly id: EditorId;
    readonly editorType: EditorType;

    private readonly _cmdChannel: CmdChannel;
    private readonly _cmdProcessor: MathCmdProcessor;
    private readonly _mathGateway: MathGateway;

    toolbars: Map<ViewportId, MathToolBar> = new Map();
    contextMenuFeature: ContextMenuFeature;

    selectionFeature: SelectionFeature;
    historyFeature: HistoryFeature;
    robFeature: ROBFeature;
    crdFeature: DocCRDFeature;
    contentVisibilityFeature: ContentVisibilityCheckFeature;

    crudDelegator: CRUDDelegator;

    // TODO:  here
    private readonly _operationMode: OperationMode;

    get operationMode(): OperationMode {
        return this._operationMode;
    }

    static readonly cmdChannelThrottle = 300;

    readonly boundaryDelegator = new BoundaryDelegator(this);

    getLocalContent(vpId: ViewportId, localId: DocLocalId): MathDocLocalContent | undefined {
        if (this.operationMode === OperationMode.CLOUD) return undefined;

        const docCtrl = this.findDocumentByLocalId(vpId, localId);
        if (!docCtrl) throw new Error(`math doc ${localId} of vp ${vpId} not found`);

        return {
            version: docCtrl.state.version,
            content: docCtrl.mathLib?.getLatex() || '',
        };
    }

    readonly selectDelegator = new SelectDelegator<MathDocCtrl>(this, {
        onSelect: this.onSelectDocCtrl.bind(this),
    });

    readonly insertDocDelegator = new InsertDocCtrlDelegator<MathDocCtrl, MathDoc>(
        this,
        (vp, state) => new MathDocCtrl(this, state, vp)
    );

    constructor(
        public conf: EditorConfig,
        private _coordinator: EditorCoordinator
    ) {
        super(conf);

        this.id = conf.id;
        this.editorType = conf.editorType;
        this._operationMode = conf.operationMode || OperationMode.CLOUD;

        this._mathGateway = new MathGateway(conf.apiUri);

        this._cmdProcessor = new MathCmdProcessor(this);
        this._cmdChannel = _coordinator.registerCmdChannel(this.id);
        this._cmdChannel.registerCmdListener(this._cmdProcessor);
        this._cmdChannel.registerCombinator(FCCmdTypeProto.PREVIEW_BOUNDARY, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(
            FCCmdTypeProto.UPDATE_BOUNDARY,
            new ThrottleCombinator(MathEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(
            MathCmdTypeProto.UPDATE_CONTENT,
            new ThrottleCombinator(MathEditor.cmdChannelThrottle)
        );

        this._cmdChannel.registerDeserializer(deserializer);

        this.crudDelegator = new CRUDDelegator(
            this,
            this.cmdChannel,
            this.regMan,
            { doc: this.docReg, layer: this.layerReg },
            this.generateInitDocData.bind(this)
        );
    }

    /**
     * Handle custom copy logic for the math editor.
     * When there is selection in the mathfield, copy only the selected LaTeX instead of the whole document
     *
     * @inheritdoc
     */
    async copy(sourceVpId: ViewportId): Promise<CopyPasteDTO | boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(sourceVpId);
        if (currDocs.length !== 1) return false;

        const docCtr = currDocs[0];
        if (docCtr.mathLib.hasSelection()) {
            docCtr.mathLib.copyToClipboard();
            return true;
        }

        return false;
    }

    /**
     * Handle custom paste logic for the math editor.
     * When the clipboard contains LaTeX, paste the LaTeX into the mathfield
     *
     * @inheritdoc
     */
    async paste(targetVpId: ViewportId, dto?: CopyPasteDTO): Promise<CopyPasteDTO | boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(targetVpId);
        if (currDocs.length !== 1) return false;

        const docCtr = currDocs[0];

        if (!dto) {
            dto = await getDTOFromClipboard();
        }
        if (dto) return false;

        docCtr.mathLib.pasteFromClipboard();
        return true;
    }

    /**
     * Handle custom delete logic for remove feature.
     * When there is selection in the mathfield, delete the selection
     *
     * @inheritdoc
     */
    async remove(vpId: string, isCutting?: boolean): Promise<boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(vpId);
        if (currDocs.length !== 1) return false;

        const docCtr = currDocs[0];
        if (docCtr.mathLib.hasSelection()) {
            docCtr.mathLib.deleteSelection();
            return true;
        }

        return false;
    }

    /**
     * Check if the content of the math document is visible to the user.
     * So when the document is empty, we can show a small border around the document in the UI.
     */
    isContentVisible(docCtrl: MathDocCtrl): boolean {
        return docCtrl.mathLib.getLatex().trim().length > 0;
    }

    async initialize(): Promise<void> {
        // do nothing
    }

    /**
     * Duplicate the given math documents in the backend.
     * ! This is only save the record, not actually insert the document on the viewer
     */
    async duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>> {
        return await this.mathGateway.duplicateDoc(docGlobalIds);
    }

    /**
     * Handler for undo operation of history feature
     */
    async undo(item: MathHistoryItem) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
        const docCtrl = docRegistry.getEntity(item.docId);
        if (!docCtrl) return;

        docCtrl.setContent(item.prevSnapshot || '');
        await docCtrl.saveContentUpdate();
    }

    /**
     * Handler for redo operation of history feature
     */
    async redo(item: MathHistoryItem) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
        const docCtrl = docRegistry.getEntity(item.docId);
        if (!docCtrl) return;

        docCtrl.setContent(item.snapshot);
        await docCtrl.saveContentUpdate();
    }

    async clearHistory(viewportId: ViewportId): Promise<void> {
        this.historyFeature?.clear(this, viewportId);
    }

    /**
     * Supporter for the custom HTML export feature.
     * @returns The selectors for elements to perform custom conversion before export to HTML.
     */
    getCustomHtmlExportSelectors(target: ExportTarget): string[] {
        return target === 'PDF' ? ['math-field'] : [];
    }

    /**
     * Supporter for the custom HTML export feature.
     * We need to convert math-field to markup when export target is `PDF` which will be used on another iframe.
     * For image, the default cloneNode is more efficient as everything are already loaded on the page
     */
    async customHtmlExportConverter(node: HTMLElement, target: ExportTarget): Promise<HTMLElement | null> {
        if (node instanceof MathfieldElement && target === 'PDF') {
            const latex = node.getValue('latex-expanded');
            const markup = convertLatexToMarkup(latex);
            const elem = document.createElement('span');
            elem.innerHTML = markup;
            return elem;
        }

        return null;
    }

    /**
     * Handle for the local content feature, when the editor is in LOCAL mode.
     * The will notify the coordinator that the content of the document has been updated
     * so it can be stored accordingly
     */
    onUpdateLocalContent(doc: MathDocCtrl) {
        const toBeUpdated: Map<EditorType, DocPrepInfo[]> = new Map();
        toBeUpdated.set(this.editorType, [{ localId: doc.state.id }]);

        this.crdFeature.onUpdateLocalContent(toBeUpdated, doc.viewport, false);
    }

    /**
     * remove document internally, meaning just remove doc without sync cmd
     *
     * @param vpId
     * @param docId
     */
    async internalRemoveDoc(vpId: ViewportId, docId: DocLocalId) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vpId);
        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vpId, docId);
        const docCtrl = docRegistry.getEntity(docId);
        if (docCtrl) {
            // because the deselect method doesn't wait for blur, cannot use it directly
            await this.selectDelegator.doBlurDocCtrl(docCtrl);
            const context: SelectContext = {
                doc: docCtrl,
                supporter: this.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION),
                selectDetails: {},
            };

            this.selectionFeature.onDeselect(context);
            docRegistry.removeEntity(docId);
            layerRegistry.removeEntity(docCtrl.layer.state.id);
            docCtrl.onRemove();
        }
    }

    get coordinator(): EditorCoordinator {
        return this._coordinator;
    }

    get cmdChannel(): CmdChannel {
        return this._cmdChannel;
    }

    get mathGateway(): MathGateway {
        return this._mathGateway;
    }

    sendCommand(cmd: Cmd<any>) {
        return this._cmdChannel.receive(cmd);
    }

    /**
     * Load document from the backend by the given global id
     */
    async loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByGlobalId(
            this._cmdChannel.channelCode,
            globalId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext);
    }

    /**
     * Load document from the backend by the given local id
     */
    async loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByLocalId(
            this._cmdChannel.channelCode,
            localId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext, localId);
    }

    /**
     * Load (initialize) the document from the given local content. When the editor is in LOCAL mode
     */
    override async loadDocumentByLocalContent(
        vm: ViewportManager,
        localId: DocLocalId,
        localContent: MathDocLocalContent
    ): Promise<MathDocCtrl | undefined> {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vm.id);

        if (!localId) localId = docRegistry.getAndIncrementId();

        if (!localContent) {
            console.warn('TODO: provide local content for math doc');
            localContent = {
                version: 0,
                content: '',
            };
        }

        const dummyGlobalId = createDummyLocalDocId(vm.id, this.id, localId);
        const doc = new MathDoc(localId, dummyGlobalId, localContent.content, localContent.version);

        const layerId = 1; // only one layer per document, so layer id is always 1

        const state = new MathLayer(layerId, localId);

        /**
         * Request the layer used by the document controller from the viewport
         * Provide what we know about the layer. The actual positioning of the layer
         * on the board is determined by the viewport.
         *
         * In the case of the classroom, the viewport manager will get the position from the
         * doc mapping, in other case, the layer will be created in a default position with
         * the provided width / height
         */
        const layerCtrl = vm.requestLayer(DOMElementLayerCtrl, true, {
            docLocalId: localId,
            docGlobalId: dummyGlobalId,
            viewport: vm,
            editor: this,
            state: state,
            domElType: 'div',
        }) as DOMElementLayerCtrl;

        state.boundary = layerCtrl.boundary;

        // let's start creating the doc controller
        const docCtrl = new MathDocCtrl(this, doc, vm);

        docRegistry.addEntity(doc.id, docCtrl);

        // link the layer with the doc controller
        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);
        docCtrl.setContent(localContent.content);

        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vm.id, localId);
        layerRegistry.addEntity(1, layerCtrl);

        return docCtrl;
    }

    override async getLocalContentFromGlobalId(
        globalId: DocumentId,
        connector: EditorBackendConnector
    ): Promise<DocLocalContent | undefined> {
        const response = await connector.loadDocumentByGlobalId(this._cmdChannel.channelCode, globalId, 'json');
        return {
            version: response.version,
            content: response.latex || '',
        };
    }

    /**
     * Reload document from the backend by the given local id
     */
    async reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        await this.loadDocumentByLocalId(localId, loadingContext);
    }

    /**
     * Fetch document from the backend by the given global id and return the API response
     */
    getDocumentContentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext): Promise<FetchDocResponse> {
        return loadingContext.connector.loadDocumentByGlobalId(this._cmdChannel.channelCode, globalId, 'json');
    }

    /**
     * Create and initialize the document controller from the API response
     */
    private async createDocumentCtrlFromResponseData(
        response: FetchDocResponse,
        loadingContext: LoadingContext,
        localId?: DocLocalId
    ): Promise<MathDocCtrl> {
        const docRegistry = this.regDelegator.getOrCreateDocReg(loadingContext.vm.id);

        // in scenarios where documents are not loaded by localId but only
        // global id is provided, we generate a local id for usage, it doesn't matter anyway
        if (!localId) localId = docRegistry.getAndIncrementId();

        const doc = new MathDoc(localId, response.id, response.latex, response.version);

        const layerId = 1; // only one layer per document, so layer id is always 1

        const state = new MathLayer(layerId, localId); // initially, the layer state doesn't have a boundary

        /**
         * Request the layer used by the document controller from the viewport
         * Provide what we know about the layer. The actual positioning of the layer
         * on the board is determined by the viewport.
         *
         * In the case of the classroom, the viewport manager will get the position from the
         * doc mapping, in other case, the layer will be created in a default position with
         * the provided width / height
         */
        const layerCtrl = loadingContext.vm.requestLayer(DOMElementLayerCtrl, true, {
            docLocalId: localId,
            docGlobalId: response.id,
            viewport: loadingContext.vm,
            editor: this,
            state: state,
            domElType: 'div',
        }) as DOMElementLayerCtrl;

        state.boundary = layerCtrl.boundary;

        // let's start creating the doc controller
        const docCtrl = new MathDocCtrl(this, doc, loadingContext.vm);

        docRegistry.addEntity(doc.id, docCtrl);

        // link the layer with the doc controller
        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);

        docCtrl.setContent(response.latex);
        // Save initial content as prev snapshot value
        docCtrl.mathLib.mathHistoryManager.prevSnapshot = response.latex || '';

        const layerRegistry = this.regDelegator.getOrCreateLayerReg(loadingContext.vm.id, localId);
        layerRegistry.addEntity(1, layerCtrl);

        return docCtrl;
    }

    /**
     * @inheritdoc
     */
    async start() {
        await this._cmdProcessor.start();
        await this._cmdChannel.start();
    }

    /**
     * @inheritdoc
     */
    featureSupporter<T>(featureKey: string): T {
        if (
            [
                FEATURE_CONTENT_VISIBILITY,
                FEATURE_HISTORY,
                FEATURE_COPYPASTE,
                FEATURE_REMOVE,
                FEATURE_HTML_EXPORT,
            ].includes(featureKey)
        )
            return this as unknown as T;

        if (featureKey == FEATURE_CRUD) return this.crudDelegator as T;
        if (featureKey == FEATURE_SELECTION) return this.selectDelegator as T;

        throw new Error(`Math Editor doesn't support feature ${featureKey}`);
    }

    /**
     * @inheritdoc
     */
    isSupportFeature(featureKey: string): boolean {
        return [
            FEATURE_SELECTION,
            FEATURE_ROB,
            FEATURE_HISTORY,
            FEATURE_CRD_DOC,
            FEATURE_CRUD,
            FEATURE_CONTENT_VISIBILITY,
            FEATURE_COPYPASTE,
            FEATURE_REMOVE,
            FEATURE_HTML_EXPORT,
        ].includes(featureKey);
    }

    /**
     * Attach the features to this editor when the feature is initialized
     *
     * @inheritdoc
     */
    onFeatureInitialization(featureKey: string, feature: any): Promise<void> {
        switch (featureKey) {
            case FEATURE_SELECTION: {
                this.selectionFeature = feature as SelectionFeature;
                this.boundaryDelegator.setSelectionFeature(this.selectionFeature);
                break;
            }
            case FEATURE_HISTORY: {
                this.historyFeature = feature as HistoryFeature;
                break;
            }
            case FEATURE_ROB: {
                this.robFeature = feature as ROBFeature;
                break;
            }
            case FEATURE_CRD_DOC:
                this.crdFeature = feature as DocCRDFeature;
                this.crdFeature.registerEditor(this, this.cmdChannel);
                break;
            case FEATURE_CONTENT_VISIBILITY: {
                this.contentVisibilityFeature = feature as ContentVisibilityCheckFeature;
                break;
            }
            default:
                break;
        }
        return Promise.resolve();
    }

    /**
     * Initialize the create document tool (to create doc in classroom)
     * @inheritdoc
     */
    initCreateDocTool(viewport: ViewportManager) {
        const createToolType: MathToolType = 'CreateMathDocumentTool';
        const createTool = this.crdFeature.generateCRDToolForViewport(
            viewport.id,
            createToolType,
            this,
            this.cmdChannel,
            this.robFeature,
            this.selectionFeature
        );

        return createTool;
    }

    /**
     * @inheritdoc
     */
    createToolbar(): ToolBar<any, any> {
        const tb = new MathToolBar(this);

        // floating ui is only available for bounded document
        if (this.conf.docViewMode == 'bounded') {
            tb.addTool('CreateMathDocumentTool', new MathCreateDocumentTool(this));
        }

        tb.addTool('ContentEditorTool', new MathContentTool(this));
        tb.addTool('VirtualKeyboardTool', new VirtualKeyboardTool(this));
        tb.addTool('InsertLatexTool', new InsertLatexTool(this));
        tb.addTool('MathStyleTool', new MathStyleTool(this));

        return tb;
    }

    /**
     * Generate the initial doc data for the CRUD feature on create doc
     *
     * @inheritdoc
     */
    async generateInitDocData(curChanges: CRUDChangeResult, insertDoc: FCInsertDocCmd, insertLayer: FCInsertLayerCmd) {
        let initData: MathDocInitData;
        let globalId: DocumentId;

        if (this.operationMode == OperationMode.LOCAL) {
            const localId = curChanges.expectedChanges[0].localId;
            globalId = createDummyLocalDocId(curChanges.vmId, this.id, localId);
            initData = { content: '' }; // empty latex by default
        } else {
            const response = await this.mathGateway.createDoc();
            globalId = response.id;
            initData = { content: response.latex };
        }

        const encoder = new TextEncoder();
        const bytes = encoder.encode(JSON.stringify(initData));
        insertDoc.state.setInitdata(bytes);
        insertDoc.state.setGlobalId(globalId);
    }

    /**
     * Handle the selection of math document. Includes checking if the document is editable and select it
     */
    onSelectDocCtrl(docCtrl: MathDocCtrl): void {
        const selectFeature = this.selectionFeature;

        const viewPortId = docCtrl.viewport.id;
        const contentTool = this.toolbars.get(viewPortId).getTool('ContentEditorTool') as MathContentTool;
        contentTool.checkFocusedDocEditable(viewPortId, docCtrl, selectFeature);

        (docCtrl as MathDocCtrl).select();
    }

    /**
     * Add the history item to the history manager
     */
    addHistoryItem(item: MathHistoryItem) {
        if (!this.historyFeature) return; // if history feature is not initialize, we simply ignore
        const manager = this.historyFeature.getHistoryManager(item.viewportId);

        manager.push(item);
    }

    /**
     * Notify the content visibility feature about the changes in the document
     */
    notifyContentVisibilityChange(docCtrl: MathDocCtrl) {
        this.contentVisibilityFeature?.onContentVisibilityChange(docCtrl);
    }
}

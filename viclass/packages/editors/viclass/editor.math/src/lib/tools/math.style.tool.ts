import { ToolState } from '@viclass/editor.core';
import { Style } from 'lib-mathlive';
import { MathDocCtrl } from '../docs/math.doc.ctrl';
import { MathEditor } from '../math.editor';
import { MathToolDocListener } from '../math.models';
import { MathTool } from './math.tool';
import { MathToolType } from './models';

export type StyleQueryResult = 'none' | 'some' | 'all';

export type MathFontSize = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
export type MathFontStyle = 'normal' | 'bold' | 'italic';

export interface MathStyleContext extends ToolState {
    fontSize: MathFontSize;
    fontStyle: MathFontStyle;
    color: string;
}

const DEFAULT_CONTEXT: MathStyleContext = {
    fontSize: 5,
    fontStyle: 'normal',
    color: '#121414',
};

/**
 * Tool to manage style of the current selection on math-field.
 * Include apply font-size, font-style and color and update the style state of the current selection.
 */
export class MathStyleTool extends MathTool<MathStyleContext> implements MathToolDocListener {
    override toolType: MathToolType = 'MathStyleTool';
    override toolState: MathStyleContext = { ...DEFAULT_CONTEXT };

    readonly colorPallete = ['#FFFFFF', '#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];

    readonly allowSizes: MathFontSize[] = [1, 3, 5, 7];

    private focusedDocId: string = '';
    private unsubscribe: Function = undefined;

    constructor(editor: MathEditor) {
        super(editor);
    }

    /**
     * Apply the font size to the current selection
     * @param size the allowed font size in number
     */
    applyFontSize(size: MathFontSize): void {
        this.applyStyle({ fontSize: size });
    }

    /**
     * Apply the font style to the current selection
     * @param style the allowed font style
     * */
    applyFontStyle(style: MathFontStyle): void {
        this.applyStyle({ variantStyle: style === 'normal' ? '' : style });
    }

    /**
     * Apply the font color to the current selection
     * @param color the allowed color
     */
    applyColor(color: string): void {
        this.applyStyle({ color });
    }

    /**
     * Apply the style to the current selection
     * @param style the style to apply
     */
    private applyStyle(style: Readonly<Style>): void {
        this.executeInFocusedDocCtrl(docCtrl => {
            docCtrl.mathLib.mathfield.applyStyle(style, { operation: 'toggle' });
            docCtrl.mathLib.mathfield.focus();
        });

        this.loadContext();
    }

    /**
     * Query the font size of the current selection
     * @param size the allowed font size in number
     * @returns is the font size exist in all, some or none of the current selection
     */
    private queryFontSize(size: MathFontSize): StyleQueryResult {
        return this.queryStyle({ fontSize: size });
    }

    /**
     * Query the font style of the current selection
     * @param style the allowed font style, exclude `normal` because it the state of no style at all
     * @returns is the font style exist in all, some or none of the current selection
     */
    private queryFontStyle(style: Exclude<MathFontStyle, 'normal'>): StyleQueryResult {
        return this.queryStyle({ variantStyle: style });
    }

    /**
     * Query the font color of the current selection
     * @param color the allowed color from palette
     * @returns is the color exist in all, some or none of the current selection
     */
    private queryColor(color: string): StyleQueryResult {
        return this.queryStyle({ color });
    }

    /**
     * Query the style of the currently selected doc
     */
    private queryStyle(style: Readonly<Style>): StyleQueryResult {
        return this.executeInFocusedDocCtrl<StyleQueryResult>(doc => doc.mathLib.mathfield.queryStyle(style)) ?? 'none';
    }

    /**
     * @inheritDoc
     */
    override onEnable(): void {
        this.onDocAttached();
    }

    /**
     *  @inheritDoc
     */
    override onDisable(): void {
        this.onDocDetached();
    }

    /**
     * Callback for doc attached event.
     * Setup for auto update the style context when selection change
     * @param docCtrl
     * @returns
     */
    onDocAttached(docCtrl?: MathDocCtrl): void {
        docCtrl = docCtrl ? docCtrl : this.getFocusedMathDocCtrls()?.[0];
        if (!docCtrl) return;

        if (this.unsubscribe) this.unsubscribe();

        const mf = docCtrl.mathLib.mathfield;
        mf.addEventListener('selection-change', this.loadContext);

        this.focusedDocId = docCtrl.state?.globalId || '';
        this.unsubscribe = () => mf?.removeEventListener('selection-change', this.loadContext);

        this.loadContext();
    }

    /**
     * Callback for doc detached event.
     * Cleanup the auto update of the style context
     * @param docCtrl
     */
    onDocDetached(docCtrl?: MathDocCtrl): void {
        if (!docCtrl || docCtrl.state.globalId === this.focusedDocId) {
            if (this.unsubscribe) {
                this.unsubscribe();
                this.unsubscribe = undefined;
            }
            this.focusedDocId = undefined;
        }
    }

    /**
     * Load the current style context to tool state
     */
    private loadContext = () => {
        if (this.getFocusedMathDocCtrls().length !== 1) {
            this.changeToolState(DEFAULT_CONTEXT);
            return;
        }

        const sizeMap = new Map<MathFontSize, StyleQueryResult>();
        for (const size of this.allowSizes) {
            sizeMap.set(size, this.queryFontSize(size));
        }

        // normal is the state of no bold or italic, so we can not query for it, only fallback to it
        const styleMap = new Map<MathFontStyle, StyleQueryResult>();
        styleMap.set('bold', this.queryFontStyle('bold'));
        styleMap.set('italic', this.queryFontStyle('italic'));

        const colorMap = new Map<string, StyleQueryResult>();
        for (const color of this.colorPallete) {
            colorMap.set(color, this.queryColor(color));
        }

        this.changeToolState({
            fontSize: this.pickRepresentValue<MathFontSize>(sizeMap, 5),
            fontStyle: this.pickRepresentValue<MathFontStyle>(styleMap, 'normal'),
            color: this.pickRepresentValue<string>(colorMap, '#121414'),
        });
    };

    /**
     * Pick a represent value from the value map of style of current selection.
     * Example, with the color style of { bold: 'all', italic: 'some', up: 'none' }, it will return `bold`
     * @param vals the value map
     * @param defaultVal the default value when all styles are 'none'
     */
    private pickRepresentValue<T>(vals: Map<T, StyleQueryResult>, defaultVal: T): T {
        if (!vals.size) return defaultVal;

        let firstSome: T = undefined;
        for (const [key, val] of vals) {
            if (val === 'all') {
                return key;
            }
            if (val === 'some' && firstSome === undefined) {
                firstSome = key;
            }
        }

        return firstSome === undefined ? defaultVal : firstSome;
    }

    /**
     * Util function to change the tool state and notify the toolbar
     */
    private changeToolState(newState: MathStyleContext) {
        this.toolState = { ...newState };
        this.toolbar.update('MathStyleTool', this.toolState);
    }
}

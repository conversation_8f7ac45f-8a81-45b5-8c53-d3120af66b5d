import {
    DocLocalId,
    InferredPointerEvent,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventListener,
    MouseHandlingItem,
    NativeEventTarget,
    NumDPointerChange,
    PointerEventData,
    PointerHandlingItem,
    Tool,
    ToolBar,
    ToolState,
    UserInputHandlerType,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { MathDocCtrl } from '../docs/math.doc.ctrl';
import { MathEditor } from '../math.editor';
import { mathDocReg } from '../math.utils';
import { MathKeyboardEvent } from '../model';
import { MathToolBar } from './math.toolbar';
import { MathToolEventData, MathToolType } from './models';

export abstract class MathTool<TState extends ToolState> implements Tool {
    readonly type: UserInputHandlerType = 'Tool';
    childToolbar?: ToolBar<any, Tool>;
    abstract readonly toolType: MathToolType;
    abstract readonly toolState: TState;
    mouseHandling: MouseHandlingItem[] = [];
    keyboardHandling: KeyboardHandlingItem[] = [];
    pointerHandling: PointerHandlingItem[] = [];

    toolbar: MathToolBar;

    constructor(public editor: MathEditor) {}

    mouseHandler: MouseEventListener<NativeEventTarget<any>>;
    readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public tool: MathTool<ToolState>) {}

        onEvent(event: MathKeyboardEvent): MathKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleKeyboardEvent(event);
        }
    })(this);

    protected getMathDoc(vm: ViewportManager, localId: DocLocalId): MathDocCtrl {
        return this.editor.regMan.registry<MathDocCtrl>(mathDocReg(vm.id))?.getEntity(localId);
    }

    registerMouseHandling(...handling: MouseHandlingItem[]) {
        if (!this.mouseHandling) this.mouseHandling = [];
        this.mouseHandling.push(...handling);
    }

    registerKeyboardHandling(...handling: KeyboardHandlingItem[]) {
        if (!this.keyboardHandling) this.keyboardHandling = [];
        this.keyboardHandling.push(...handling);
    }

    registerPointerHandling(...handling: PointerHandlingItem[]) {
        this.pointerHandling.push(...handling);
    }

    registerToolbar(toolbar: MathToolBar) {
        this.toolbar = toolbar;
    }

    onBlur() {}
    onFocus() {
        // refocus on the math field to continue typing
        const focusedDocs = this.getFocusedMathDocCtrls();
        if (focusedDocs.length === 1) {
            focusedDocs[0].mathLib.mathfield.focus();
        }
    }

    onDisable() {}
    onEnable() {}
    onAttachViewport() {}
    onDetachViewport() {}

    isFocusedSingleDoc(vpId: ViewportId): boolean {
        return this.editor.selectDelegator.getFocusedDocs(vpId)?.length == 1;
    }

    focusAble(vpId: ViewportId): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;

        return this.isFocusedSingleDoc(vpId);
    }

    protected getFocusedMathDocCtrls(): MathDocCtrl[] {
        const vm = this.toolbar.viewport;

        if (!vm) {
            throw new Error(
                'The toolbar of the math editor has not been attached to any viewport. Cannot insert document!'
            );
        }

        return this.editor.selectDelegator.getFocusedDocs(vm.id);
    }

    protected executeInFocusedDocCtrl<T>(cb: (doc: MathDocCtrl) => T): T | undefined {
        const docCtrls = this.getFocusedMathDocCtrls();
        if (!docCtrls?.length) return undefined;

        return cb(docCtrls[0]);
    }

    handleKeyboardEvent(event: MathKeyboardEvent): MathKeyboardEvent {
        return event;
    }

    handlePointerEvent(event: PointerEventData<any>): PointerEventData<any> {
        return event;
    }

    handleNonUIPointerEvent(event: NumDPointerChange | InferredPointerEvent): NumDPointerChange | InferredPointerEvent {
        return event;
    }

    handleToolEvent(event: MathToolEventData): MathToolEventData {
        switch (event.eventType) {
            case 'change': {
                this.processChangeToolEvent(event);
                break;
            }
            default:
                break;
        }

        return event;
    }

    protected async processChangeToolEvent(event: MathToolEventData): Promise<MathToolEventData> {
        return event;
    }
}

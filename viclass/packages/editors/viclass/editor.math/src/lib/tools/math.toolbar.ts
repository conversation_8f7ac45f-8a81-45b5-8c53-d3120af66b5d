import {
    <PERSON><PERSON><PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    KeyboardEventData,
    KeyboardEventListener,
    MouseEventData,
    MouseEventListener,
    NativeEventTarget,
    newCursor,
    PointerEventListener,
    ToolEventListener,
    ToolState,
    VEventListener,
    ViewportDisableCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { MathDocCtrl } from '../docs/math.doc.ctrl';
import { MathEditor } from '../math.editor';
import { MathDocEvent, MathDocFocusedES, MathToolDocListener } from '../math.models';
import { MathTool } from './math.tool';
import { MathToolEventData, MathToolType } from './models';

export class MathToolBar extends DefaultToolBar<MathToolType, MathTool<ToolState>> {
    override keyboardHandler: KeyboardEventListener<NativeEventTarget<any>>;
    override mouseHandler: MouseEventListener<NativeEventTarget<any>>;
    override pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    toolListener: ToolEventListener<MathToolBar, any>;
    mathDocEventListener: VEventListener<MathDocEvent>;

    constructor(private editor: MathEditor) {
        super(editor.coordinator);
        this.keyboardHandler = new this._keyboardHandler(this);
        this.mouseHandler = new this._mouseHandler();
        this.mouseHandling = [];
        this.toolListener = new this._toolListener(this);
        this.mathDocEventListener = new this.MathDocEventListener(this);
    }

    public override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    this.disableTool(type);
                    break;
                }
                case 'ViewMode': {
                    this.disableTool(type);
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    override attachViewport(viewport: ViewportManager): void {
        super.attachViewport(viewport);

        this.editor.toolbars.set(viewport.id, this);
        this.editor.selectDelegator.registerDocEventListener(this.mathDocEventListener);
    }

    override detachViewport(viewport: ViewportManager): void {
        super.detachViewport(viewport);

        if (this.mathDocEventListener) {
            this.editor.selectDelegator.unregisterDocEventListener(this.mathDocEventListener);
        }
        this.editor.toolbars.delete(viewport.id);
    }

    private _toolListener = class implements ToolEventListener<MathToolBar, any> {
        constructor(private toolbar: MathToolBar) {}

        onEvent(eventData: MathToolEventData): MathToolEventData | Promise<MathToolEventData> {
            if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(eventData.toolType)) return eventData;
            const tool = eventData.source.getTool(eventData.toolType);
            return tool ? tool.handleToolEvent(eventData) : eventData;
        }
    };

    protected override generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: MathToolBar) {}

            onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                const eType = eventData.eventType as CoordinatorEventType;
                const eventState = eventData.state;

                if (!this.toolbar.viewport || eventState.vmId != this.toolbar.viewport.id) return eventData;

                switch (eType) {
                    case 'viewport-disabled': {
                        const evs = eventState as ViewportDisableCES;
                        this.toolbar.editor.selectDelegator.onViewportDisabled(eventData.state.vmId, evs);
                        break;
                    }
                    case 'viewport-removed': {
                        this.toolbar.editor.selectDelegator.onViewportRemoved(eventData.state.vmId);
                        break;
                    }
                }

                return eventData;
            }
        })(this);
    }

    override addTool(toolType: MathToolType, tool: MathTool<ToolState>): void {
        super.addTool(toolType, tool);
        tool.registerToolbar(this);
    }

    private MathDocEventListener = class implements VEventListener<MathDocEvent> {
        constructor(private toolbar: MathToolBar) {}

        onEvent(eventData: MathDocEvent): MathDocEvent | Promise<MathDocEvent> {
            if (this.toolbar.isDisabled()) return eventData;

            if (!this.toolbar.viewport || eventData.state.vm.id != this.toolbar.viewport.id) return eventData;

            switch (eventData.eventType) {
                case 'doc-focused': {
                    const { vm, docCtrl } = eventData.state as MathDocFocusedES;
                    vm.eventManager.captureAllKeyboardEvent(docCtrl.root, this.toolbar.keyboardHandler);

                    const docListnenerTools: MathToolType[] = [
                        'VirtualKeyboardTool',
                        'InsertLatexTool',
                        'MathStyleTool',
                    ];
                    docListnenerTools.forEach(toolType => {
                        if (!this.toolbar.isToolDisable(toolType) || toolType === 'InsertLatexTool') {
                            const tool = this.toolbar.getTool(toolType) as unknown as MathToolDocListener;
                            tool.onDocAttached(docCtrl);
                        }
                    });

                    docCtrl.select();

                    this.toolbar.blur('CreateMathDocumentTool');
                    break;
                }
                case 'doc-unfocused': {
                    const { vm, docCtrl } = eventData.state as MathDocFocusedES;
                    vm.eventManager.unCaptureAllKeyboardEvent(docCtrl.root);

                    const docListnenerTools: MathToolType[] = [
                        'VirtualKeyboardTool',
                        'InsertLatexTool',
                        'MathStyleTool',
                    ];
                    docListnenerTools.forEach(toolType => {
                        if (!this.toolbar.isToolDisable(toolType) || toolType === 'InsertLatexTool') {
                            const tool = this.toolbar.getTool(toolType) as unknown as MathToolDocListener;
                            tool.onDocDetached(docCtrl);
                        }
                    });

                    docCtrl.unselect();
                    break;
                }
            }

            return eventData;
        }
    };

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private toolbar: MathToolBar) {}

        onEvent(
            event: KeyboardEventData<NativeEventTarget<any>>
        ): KeyboardEventData<NativeEventTarget<any>> | Promise<KeyboardEventData<NativeEventTarget<any>>> {
            if (this.toolbar.isDisabled()) return event;

            const vm = this.toolbar.viewport;
            if (vm) {
                const focusedDocCtrls: MathDocCtrl[] = this.toolbar.editor.selectDelegator.getFocusedDocs(vm.id) || [];
                if (focusedDocCtrls.length === 1 && focusedDocCtrls[0]) {
                    return focusedDocCtrls[0].keyboardListener.onEvent(event);
                }
            }

            return event;
        }
    };

    private _mouseHandler = class implements MouseEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject([newCursor('auto', 10, '#fff', 0, 'system')]);

        onEvent(event: MouseEventData<NativeEventTarget<any>>) {
            return event;
        }
    };
}

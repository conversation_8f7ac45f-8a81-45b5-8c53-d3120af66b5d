import { TableNode } from '@lexical/table';
import { EditorConfig } from 'lexical';

/**
 * A custom table node to make the table inline
 */
export class CustomTableNode extends TableNode {
    static override getType() {
        return 'custom-table';
    }

    static override clone(node: CustomTableNode) {
        return new CustomTableNode(node.__key);
    }

    override isInline(): boolean {
        return true;
    }

    override updateTableElement(prevNode: this | null, tableElement: HTMLTableElement, config: EditorConfig): void {
        super.updateTableElement(prevNode, tableElement, config);
        const colWidths = this.getColWidths();
        if (colWidths?.length) {
            const totalWidth = colWidths.reduce((acc, cur) => acc + cur, 0);
            tableElement.style.width = `${totalWidth}px`;
        }
    }
}

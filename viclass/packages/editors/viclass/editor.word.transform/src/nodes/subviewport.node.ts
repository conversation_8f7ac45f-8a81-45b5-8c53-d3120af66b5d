import { $wrapNodeInElement } from '@lexical/utils';
import { DocL<PERSON>alContent, DocLocalId, DocumentId, EditorType, Position, RectSize } from '@viclass/editor.core';
import {
    $createParagraphNode,
    $getNearestNodeFromDOMNode,
    $insertNodes,
    $isRootOrShadowRoot,
    $isTextNode,
    $nodesOfType,
    DOMConversionMap,
    DOMConversionOutput,
    DOMExportOutput,
    EditorConfig,
    ElementFormatType,
    LexicalEditor,
    LexicalNode,
    NodeKey,
    Spread,
} from 'lexical';
import { DecoratorBlockNode, SerializedDecoratorBlockNode } from './decorator.block.node';
import { $createEmptyTextToken } from './utils';

export type SubViewportNodeConfig = {
    id: string;
    editorType: EditorType;
    globalId?: DocumentId;
    localId?: DocLocalId;
    lookAt?: Position;
    zoom?: number;
    size?: RectSize;
    localContent?: DocLocalContent;
};

export type SerializedSubViewportNode = Spread<SubViewportNodeConfig, SerializedDecoratorBlockNode>;

export const SUBVIEWPORT_CONTAINER_CLASSNAME = 'vi-word-editor-viewport-container';
export const SUBVIEWPORT_RESIZABLE_CLASSNAME = 'vi-word-editor-resizable-container';
export const SUBVIEWPORT_CLASSNAME = 'vi-word-editor-viewport';

export const IMPORT_SUBVIEWPORT_ID = 'import_viewport';

const INLINE_EDITORS = ['MathEditor'];

export function isInlineSubViewport(editorType: EditorType) {
    return INLINE_EDITORS.includes(editorType);
}

/**
 * Node representing a sub-viewport.
 *
 * ! Must use the DecoratorNode as we use coordinator to render to render custom elements.
 * Lexical will attempt to remove the custom elements if this is the other kinds of nodes
 */
export class SubViewportNode extends DecoratorBlockNode {
    __id: string;
    __editorType: EditorType;
    __globalId: DocumentId;
    __localId: DocLocalId;
    __lookAt: Position;
    __zoom: number;
    __size: RectSize;
    __localContent?: DocLocalContent;

    get viewportId(): string {
        const self = this.getLatest();
        return self.__id;
    }

    get editorType(): EditorType {
        const self = this.getLatest();
        return self.__editorType;
    }

    get globalId(): DocumentId {
        const self = this.getLatest();
        return self.__globalId;
    }

    get localId(): DocLocalId {
        const self = this.getLatest();
        return self.__localId;
    }

    get size(): RectSize {
        const self = this.getLatest();
        return { ...self.__size };
    }

    get lookAt(): Position {
        const self = this.getLatest();
        return { ...self.__lookAt };
    }

    get zoom(): number {
        const self = this.getLatest();
        return self.__zoom;
    }

    get hasLocalContent(): boolean {
        const self = this.getLatest();
        return !!self.__localContent;
    }

    get localContent(): DocLocalContent | undefined {
        const self = this.getLatest();
        return self.__localContent;
    }

    set viewportId(viewportId: string) {
        const self = this.getWritable();
        self.__id = viewportId;
    }

    set globalId(globalId: DocumentId) {
        const self = this.getWritable();
        self.__globalId = globalId;
    }

    set localId(localId: DocLocalId) {
        const self = this.getWritable();
        self.__localId = localId;
    }

    set lookAt(pos: Position) {
        const self = this.getWritable();
        self.__lookAt = pos;
    }

    set zoom(zoom: number) {
        const self = this.getWritable();
        self.__zoom = zoom;
    }

    set size(size: RectSize) {
        const self = this.getWritable();
        self.__size = size;
    }

    set localContent(localContent: DocLocalContent) {
        const self = this.getWritable();
        self.__localContent = localContent;
    }

    override canIndent(): boolean {
        return this.isInline();
    }

    override isInline(): boolean {
        return isInlineSubViewport(this.__editorType);
    }

    constructor(
        id: string,
        editorType: EditorType,
        globalId?: DocumentId,
        localId?: DocLocalId,
        lookAt?: Position,
        zoom?: number,
        format?: ElementFormatType,
        key?: NodeKey,
        size?: RectSize,
        localContent?: DocLocalContent
    ) {
        super(format, key);
        this.__id = id;
        this.__editorType = editorType;
        this.__globalId = globalId;
        this.__localId = localId;
        this.__lookAt = lookAt || { x: 0, y: 0 };
        this.__zoom = zoom || 1;
        this.__size = size || { width: 0, height: 0 };
        this.__localContent = localContent;
    }

    static override getType(): string {
        return 'subviewport';
    }

    static override clone(node: SubViewportNode): SubViewportNode {
        return new SubViewportNode(
            node.__id,
            node.__editorType,
            node.__globalId,
            node.__localId,
            node.__lookAt,
            node.__zoom,
            node.__format,
            node.__key,
            node.__size,
            node.__localContent
        );
    }

    static override importJSON(serializedNode: SerializedSubViewportNode): SubViewportNode {
        // The imported sub viewport ID should always be a new one, to be replaced on SubViewportPlugin
        const node = $createSubViewportNode(
            IMPORT_SUBVIEWPORT_ID,
            serializedNode.editorType,
            serializedNode.globalId,
            1, // only 1 doc inside a sub viewport
            serializedNode.lookAt,
            serializedNode.zoom,
            serializedNode.format
        );
        node.__size = serializedNode.size;
        node.__localContent = serializedNode.localContent;
        return node;
    }

    override updateDOM(_prevNode: SubViewportNode, container: HTMLElement, _config: EditorConfig): false {
        const resizable = container.querySelector(`.${SUBVIEWPORT_RESIZABLE_CLASSNAME}`) as HTMLElement;
        const viewport = container.querySelector(`.${SUBVIEWPORT_CLASSNAME}`) as HTMLElement;

        viewport.id = this.__id;

        container.style.textAlign = this.__format || 'center';

        if (this.__globalId) {
            viewport.setAttribute('data-global-id', this.__globalId);
        }

        if (this.__localId !== undefined) {
            viewport.setAttribute('data-local-id', String(this.__localId));
        }

        viewport.setAttribute('data-look-at-x', String(this.__lookAt.x));
        viewport.setAttribute('data-look-at-y', String(this.__lookAt.y));
        viewport.setAttribute('data-zoom', String(this.__zoom));

        if (this.__localContent) {
            viewport.setAttribute('data-local-content', JSON.stringify(this.__localContent));
        }

        this.updateElementSizes(container, resizable);

        return false;
    }

    override exportDOM(editor: LexicalEditor): DOMExportOutput {
        const element = this.createDOM(null, editor);
        return { element };
    }

    override createDOM(config: EditorConfig, editor: LexicalEditor): HTMLElement {
        const elementType = this.isInline() ? 'span' : 'div';
        const container = document.createElement(elementType);
        container.className = SUBVIEWPORT_CONTAINER_CLASSNAME;
        const resizable = document.createElement(elementType);
        resizable.className = SUBVIEWPORT_RESIZABLE_CLASSNAME;
        const viewportEl = document.createElement(elementType);
        viewportEl.classList.add(SUBVIEWPORT_CLASSNAME);

        container.style.position = 'relative';
        container.style.textAlign = this.__format || 'center';

        resizable.style.display = 'inline-block';
        resizable.style.position = 'relative';

        viewportEl.id = this.__id;
        viewportEl.contentEditable = 'false';
        viewportEl.style.width = '100%';
        viewportEl.style.height = '100%';

        viewportEl.setAttribute('data-ed-type', this.__editorType);
        viewportEl.setAttribute('data-look-at-x', String(this.__lookAt.x));
        viewportEl.setAttribute('data-look-at-y', String(this.__lookAt.y));
        viewportEl.setAttribute('data-zoom', String(this.__zoom));
        viewportEl.setAttribute('tabindex', '0');

        if (this.isInline()) {
            container.style.minWidth = '20px';
            container.style.display = 'inline-block';
            container.classList.add('inline-viewport');
        } else {
            container.style.width = '100%';
            container.style.display = 'block';

            this.updateElementSizes(container, resizable);
        }

        if (this.__globalId) {
            viewportEl.setAttribute('data-global-id', this.__globalId);
        }

        if (this.__localId !== undefined) {
            viewportEl.setAttribute('data-local-id', String(this.__localId));
        }

        if (this.__localContent) {
            viewportEl.setAttribute('data-local-content', JSON.stringify(this.__localContent));
        }

        container.appendChild(resizable);
        resizable.appendChild(viewportEl);

        return container;
    }

    static override importDOM(): DOMConversionMap | null {
        return {
            span: (domNode: HTMLElement) => {
                if (!domNode.classList.contains(SUBVIEWPORT_CONTAINER_CLASSNAME)) {
                    return null;
                }
                return {
                    conversion: convertSubViewportElement,
                    priority: 4,
                };
            },
            div: (domNode: HTMLElement) => {
                if (!domNode.classList.contains(SUBVIEWPORT_CONTAINER_CLASSNAME)) {
                    return null;
                }
                return {
                    conversion: convertSubViewportElement,
                    priority: 4,
                };
            },
        };
    }

    override exportJSON(): SerializedSubViewportNode {
        return {
            ...super.exportJSON(),
            type: 'subviewport',
            version: 1,
            id: this.__id,
            editorType: this.__editorType,
            globalId: this.__globalId,
            localId: this.__localId,
            lookAt: this.__lookAt,
            zoom: this.__zoom,
            size: this.__size,
            localContent: this.__localContent,
        };
    }

    private updateElementSizes(container: HTMLElement, resizable: HTMLElement) {
        if (this.isInline()) return;

        const width = this.__size.width ? `${this.__size.width}px` : this.isInline() ? '50px' : '70%';
        const height = this.__size.height ? `${this.__size.height}px` : this.isInline() ? '50px' : '300px';

        resizable.style.width = width;
        resizable.style.height = height;
    }

    override decorate(): HTMLDivElement {
        return null; // no need for decorator here
    }
}

export function $createSubViewportNode(
    id: DocumentId,
    editorType: EditorType,
    globalId?: DocumentId,
    localId?: DocLocalId,
    lookAt: Position = { x: 0, y: 0 },
    zoom: number = 1,
    format: ElementFormatType = 'center'
): SubViewportNode {
    return new SubViewportNode(id, editorType, globalId, localId, lookAt, zoom, format);
}

export function $isSubViewportNode(node: SubViewportNode | LexicalNode | null | undefined): node is SubViewportNode {
    return node instanceof SubViewportNode;
}

export function $getParentSubViewportNodeFromDOM(domNode: Node): SubViewportNode | null {
    if (!(domNode instanceof HTMLElement)) return null;

    let node = domNode;

    while (node) {
        if (node.classList.contains(SUBVIEWPORT_CONTAINER_CLASSNAME))
            return $getNearestNodeFromDOMNode(domNode) as SubViewportNode;

        node = node.parentElement;
    }

    return null;
}

export function $getNodeBySubViewportId(vmId: string): SubViewportNode {
    const nodes = $nodesOfType(SubViewportNode);
    return nodes.find(n => n.viewportId === vmId);
}

function convertSubViewportElement(domNode: HTMLElement): null | DOMConversionOutput {
    const vpElem = domNode.querySelector(`.${SUBVIEWPORT_CLASSNAME}`);
    if (!vpElem) return null;

    // extract data viewport info
    const subVpId = IMPORT_SUBVIEWPORT_ID;
    const edType: EditorType = vpElem.getAttribute('data-ed-type') as EditorType;
    const globalId = vpElem.getAttribute('data-global-id');
    const lookAt = {
        x: Number(vpElem.getAttribute('data-look-at-x')),
        y: Number(vpElem.getAttribute('data-look-at-y')),
    };
    const zoom = Number(vpElem.getAttribute('data-zoom'));
    const format = (domNode.style.textAlign || 'center') as ElementFormatType;

    const localContent: DocLocalContent | null = parseLocalContentStr(vpElem.getAttribute('data-local-content'));

    // extract resizable info
    const resizable = domNode.querySelector(`.${SUBVIEWPORT_RESIZABLE_CLASSNAME}`) as HTMLElement;
    const size: RectSize = {
        width: 0,
        height: 0,
    };
    if (resizable?.style?.width?.endsWith('px')) {
        size.width = Number(resizable.style.width.replace('px', '')) || 0;
    }
    if (resizable?.style?.height?.endsWith('px')) {
        size.height = Number(resizable.style.height.replace('px', '')) || 0;
    }

    // create new node
    const node = $createSubViewportNode(subVpId, edType, globalId, 1, lookAt, zoom, format);
    node.__size = size;
    if (localContent) {
        node.__localContent = localContent;
    }
    return { node };
}

function parseLocalContentStr(localContentStr: string): DocLocalContent | null {
    if (!localContentStr) return null;
    let localContent: DocLocalContent | null = null;
    try {
        localContent = JSON.parse(localContentStr);
        if (isNaN(localContent?.version)) localContent = null;
        if (localContent?.content === null || localContent?.content === undefined) localContent = null;
    } catch (e) {
        console.warn('Parse local content failed:\n' + localContentStr, e);
    }
    return localContent;
}

export function $insertSubViewportNode(subVpNode: SubViewportNode) {
    $insertNodes([subVpNode]);
    if (isInlineSubViewport(subVpNode.__editorType)) {
        if ($isRootOrShadowRoot(subVpNode.getParent())) {
            $wrapNodeInElement(subVpNode, $createParagraphNode).select();
        }
        const nextNode = subVpNode.getNextSibling();
        if (!nextNode || !$isTextNode(nextNode)) {
            subVpNode.insertAfter($createEmptyTextToken());
        }

        const prevNode = subVpNode.getPreviousSibling();
        if (!$isTextNode(prevNode)) {
            subVpNode.insertBefore($createEmptyTextToken());
        }
    }
}

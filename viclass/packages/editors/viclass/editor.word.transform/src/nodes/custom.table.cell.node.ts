import { $isTableCellNode, SerializedTableCellNode, TableCellHeaderStates, TableCellNode } from '@lexical/table';
import {
    DOMConversionMap,
    DOMExportOutput,
    EditorConfig,
    isHTMLElement,
    LexicalEditor,
    LexicalNode,
    LexicalUpdateJSON,
    NodeKey,
} from 'lexical';

/**
 * Border style configuration for individual sides of a table cell
 */
export interface BorderSideStyle {
    width?: string;
    style?: string;
    color?: string;
}

/**
 * Border styles for all four sides of a table cell
 */
export interface BorderStyles {
    top?: BorderSideStyle;
    right?: BorderSideStyle;
    bottom?: BorderSideStyle;
    left?: BorderSideStyle;
}

/**
 * Custom serialized table cell node for the JSON exported by CustomTableCellNode
 */
export type CustomSerializedTableCellNode = SerializedTableCellNode & {
    borderStyle?: string; // Legacy single border style for backward compatibility
    borderStyles?: BorderStyles; // New per-side border styles
    version: 2; // Increment version for new border styles feature
};

/**
 * Custom table cell node with border style to override the original table cell node
 */
export class CustomTable<PERSON><PERSON>Node extends TableCellNode {
    __borderStyle?: string; // Legacy single border style for backward compatibility
    __borderStyles?: BorderStyles; // New per-side border styles

    /**
     * Getter for legacy border style (backward compatibility)
     */
    getBorderStyle(): string | undefined {
        return this.getLatest().__borderStyle;
    }

    /**
     * Setter for legacy border style (backward compatibility)
     */
    setBorderStyle(borderStyle: string | undefined): this {
        const self = this.getWritable();
        self.__borderStyle = borderStyle;
        return self;
    }

    /**
     * Getter for per-side border styles
     */
    getBorderStyles(): BorderStyles | undefined {
        return this.getLatest().__borderStyles;
    }

    /**
     * Setter for per-side border styles
     */
    setBorderStyles(borderStyles: BorderStyles | undefined): this {
        const self = this.getWritable();
        self.__borderStyles = borderStyles;
        return self;
    }

    /**
     * Get border style for a specific side
     */
    getBorderSideStyle(side: 'top' | 'right' | 'bottom' | 'left'): BorderSideStyle | undefined {
        const borderStyles = this.getBorderStyles();
        return borderStyles?.[side];
    }

    /**
     * Set border style for a specific side
     */
    setBorderSideStyle(side: 'top' | 'right' | 'bottom' | 'left', style: BorderSideStyle | undefined): this {
        const self = this.getWritable();
        if (!self.__borderStyles) {
            self.__borderStyles = {};
        }
        if (style) {
            self.__borderStyles[side] = style;
        } else {
            delete self.__borderStyles[side];
        }
        return self;
    }

    /**
     * Convert BorderSideStyle to CSS border string
     */
    private borderSideStyleToCSS(style: BorderSideStyle): string {
        const width = style.width || '1px';
        const borderStyle = style.style || 'solid';
        const color = style.color || '#000000';
        return `${width} ${borderStyle} ${color}`;
    }

    /**
     * Get CSS border styles for DOM rendering
     */
    private getCSSBorderStyles(): Partial<CSSStyleDeclaration> {
        const borderStyles = this.getBorderStyles();
        const legacyBorderStyle = this.getBorderStyle();

        // If we have new per-side border styles, use them
        if (borderStyles) {
            const styles: any = {};
            if (borderStyles.top) {
                styles.borderTop = this.borderSideStyleToCSS(borderStyles.top);
            }
            if (borderStyles.right) {
                styles.borderRight = this.borderSideStyleToCSS(borderStyles.right);
            }
            if (borderStyles.bottom) {
                styles.borderBottom = this.borderSideStyleToCSS(borderStyles.bottom);
            }
            if (borderStyles.left) {
                styles.borderLeft = this.borderSideStyleToCSS(borderStyles.left);
            }
            return styles;
        }

        // Fall back to legacy border style for backward compatibility
        if (legacyBorderStyle) {
            return { border: legacyBorderStyle };
        }

        return {};
    }

    constructor(headerState = TableCellHeaderStates.NO_STATUS, colSpan = 1, width?: number, key?: NodeKey) {
        super(headerState, colSpan, width, key);

        this.__borderStyle = undefined;
        this.__borderStyles = undefined;
    }

    /**
     * @inheritdoc
     */
    static override getType(): string {
        return 'custom-tablecell';
    }

    /**
     * @inheritdoc
     */
    static override clone(node: CustomTableCellNode): CustomTableCellNode {
        const clonedNode = new CustomTableCellNode(node.__headerState, node.__colSpan, node.__width, node.__key);
        clonedNode.__backgroundColor = node.__backgroundColor;
        clonedNode.__verticalAlign = node.__verticalAlign;

        clonedNode.__borderStyle = node.__borderStyle;
        clonedNode.__borderStyles = node.__borderStyles ? { ...node.__borderStyles } : undefined;
        return clonedNode;
    }

    /**
     * @inheritdoc
     */
    override createDOM(config: EditorConfig): HTMLTableCellElement {
        const dom = super.createDOM(config);

        const borderStyles = this.getCSSBorderStyles();
        Object.assign(dom.style, borderStyles);

        return dom;
    }

    /**
     * @inheritdoc
     */
    override updateDOM(prevNode: this): boolean {
        return (
            super.updateDOM(prevNode) ||
            prevNode.__borderStyle !== this.__borderStyle ||
            JSON.stringify(prevNode.__borderStyles) !== JSON.stringify(this.__borderStyles)
        );
    }

    /**
     * @inheritdoc
     */
    override exportDOM(editor: LexicalEditor): DOMExportOutput {
        const output = super.exportDOM(editor);

        if (isHTMLElement(output.element)) {
            const element = output.element as HTMLTableCellElement;
            const borderStyles = this.getCSSBorderStyles();
            Object.assign(element.style, borderStyles);
        }

        return output;
    }

    /**
     * Parse border styles from DOM element
     */
    private static parseBorderStylesFromDOM(domNode: HTMLElement): {
        borderStyle?: string;
        borderStyles?: BorderStyles;
    } {
        const style = domNode.style;
        const result: { borderStyle?: string; borderStyles?: BorderStyles } = {};

        // Check for individual border sides first (more specific)
        const borderTop = style.borderTop;
        const borderRight = style.borderRight;
        const borderBottom = style.borderBottom;
        const borderLeft = style.borderLeft;

        if (borderTop || borderRight || borderBottom || borderLeft) {
            const borderStyles: BorderStyles = {};

            if (borderTop) borderStyles.top = this.parseBorderSideFromCSS(borderTop);
            if (borderRight) borderStyles.right = this.parseBorderSideFromCSS(borderRight);
            if (borderBottom) borderStyles.bottom = this.parseBorderSideFromCSS(borderBottom);
            if (borderLeft) borderStyles.left = this.parseBorderSideFromCSS(borderLeft);

            result.borderStyles = borderStyles;
        }

        // Fall back to general border style for backward compatibility
        const border = style.border;
        if (border) {
            result.borderStyle = border;
        }

        return result;
    }

    /**
     * Parse a single border side from CSS string
     */
    private static parseBorderSideFromCSS(borderCSS: string): BorderSideStyle {
        // Simple parsing - in a real implementation, you might want more robust parsing
        const parts = borderCSS.trim().split(/\s+/);
        const style: BorderSideStyle = {};

        if (parts.length >= 1) style.width = parts[0];
        if (parts.length >= 2) style.style = parts[1];
        if (parts.length >= 3) style.color = parts.slice(2).join(' ');

        return style;
    }

    /**
     * To be called by lexical to import the custom table cell node from the HTML (usually from clipboard).
     * Here we override the original `importDOM` of TableCellNode with higher priority to override the original conversion
     *
     * @inheritdoc
     */
    static override importDOM(): DOMConversionMap | null {
        /**
         * ! this is a necessary hack to override the original conversion function of TableCellNode.
         * As the library does not export the original conversion function and the logic behind is complex.
         */
        const conversionMap = super.importDOM();
        const originalConversion = conversionMap['td'](null)!.conversion;

        const $convertCustomTableCellNodeElement = (domNode: HTMLElement) => {
            const output = originalConversion(domNode);
            const node = output.node as LexicalNode;

            if ($isTableCellNode(node)) {
                const customNode = new CustomTableCellNode(node.__headerState, node.__colSpan, node.__width);
                customNode.__backgroundColor = node.__backgroundColor;
                customNode.__verticalAlign = node.__verticalAlign;

                const { borderStyle, borderStyles } = this.parseBorderStylesFromDOM(domNode);

                if (borderStyles) {
                    customNode.setBorderStyles(borderStyles);
                } else if (borderStyle) {
                    customNode.setBorderStyle(borderStyle);
                }

                return { ...output, node: customNode };
            }
            return output;
        };

        return {
            td: () => ({
                conversion: $convertCustomTableCellNodeElement,
                priority: 1,
            }),
            th: () => ({
                conversion: $convertCustomTableCellNodeElement,
                priority: 1,
            }),
        };
    }

    /**
     * @inheritdoc
     */
    override updateFromJSON(serializedNode: LexicalUpdateJSON<CustomSerializedTableCellNode>): this {
        super.updateFromJSON(serializedNode);

        const { borderStyle, borderStyles } = serializedNode;

        // Handle backward compatibility
        if (borderStyles) {
            // New format with per-side border styles
            this.__borderStyles = borderStyles;
            this.__borderStyle = borderStyle; // Keep legacy for compatibility
        } else if (borderStyle) {
            // Legacy format with single border style
            this.__borderStyle = borderStyle;
            this.__borderStyles = undefined;
        }

        return this;
    }

    /**
     * @inheritdoc
     */
    override exportJSON(): CustomSerializedTableCellNode {
        return {
            ...super.exportJSON(),
            type: this.getType(),
            borderStyle: this.__borderStyle, // Keep for backward compatibility
            borderStyles: this.__borderStyles,
            version: 2,
        };
    }

    static override importJSON(serializedNode: CustomSerializedTableCellNode): CustomTableCellNode {
        const { borderStyle, borderStyles, version } = serializedNode;
        const node = new CustomTableCellNode(serializedNode.headerState, serializedNode.colSpan, serializedNode.width);

        // Update from JSON first to get all base properties
        node.updateFromJSON(serializedNode);

        // Handle backward compatibility for border styles
        if (version === 2 && borderStyles) {
            // New format with per-side border styles
            node.setBorderStyles(borderStyles);
        } else if (borderStyle) {
            // Legacy format with single border style
            node.setBorderStyle(borderStyle);
        }

        return node;
    }
}

/**
 * Check if the given node is a CustomTableCellNode
 */
export function $isCustomTableCellNode(node: LexicalNode | null | undefined): node is CustomTableCellNode {
    return node instanceof CustomTableCellNode;
}

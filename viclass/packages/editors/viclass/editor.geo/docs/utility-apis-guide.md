# Hướng dẫn sử dụng Utility APIs

## Tổng quan

Utility APIs trong VIClass Geometry Editor cung cấp một bộ công cụ mạnh mẽ để xây dựng các tool hình học phức tạp. Document này hướng dẫn cách sử dụng hiệu quả các APIs này.

## Quy trình phát triển Tool

### 1. Thiết kế Tool

Trước khi bắt đầu code, hãy xác định:
- **Input**: Tool cần những phần tử gì từ user?
- **Output**: Tool sẽ tạo ra phần tử gì?
- **Logic**: <PERSON><PERSON><PERSON> bư<PERSON><PERSON> tính toán cần thiết?
- **Validation**: Điều kiện hợp lệ cho input?

### 2. Implement Tool Structure

```typescript
import { GeometryTool } from './geo.tool';
import { vertex, stroke } from '../selectors';
import { buildPointConstruction } from './util.construction';

export class MyCustomTool extends GeometryTool<MyToolState> {
    constructor() {
        super('MyCustomTool');
        this.setupSelectors();
    }

    private setupSelectors() {
        // Thiết lập selectors cho input
    }

    protected async onPointerDown(event: GeoPointerEvent) {
        // Xử lý logic chính
    }
}
```

### 3. Sử dụng Utility Functions

Tích hợp các utility functions vào logic tool:

```typescript
import {
    calculateLineLineIntersection,
    buildIntersectionRequest,
    pickPointName,
    handleIfPointerNotInError
} from './util.tool';
```

## Patterns thường dùng

### Pattern 1: Tạo giao điểm

```typescript
async createIntersectionPoint(line1: RenderLine, line2: RenderLine) {
    const docCtrl = getFocusDocCtrl(this.editor);
    
    // 1. Tính toán giao điểm
    const intersections = calculateLineLineIntersection(line1, line2, docCtrl);
    if (intersections.length === 0) {
        throw new Error('Không có giao điểm');
    }

    // 2. Tạo tên tự động
    const pointName = pickPointName(docCtrl);

    // 3. Xây dựng construction request
    const request = buildIntersectionRequest({
        cgName: 'LineLine',
        outputName: pointName,
        paramA: {
            name: line1.name,
            elType: 'Line',
            labelType: 'Line',
            defId: 'aLine'
        },
        paramB: {
            name: line2.name,
            elType: 'Line', 
            labelType: 'Line',
            defId: 'aLine'
        }
    });

    // 4. Thực hiện construction
    await remoteConstruct(request, docCtrl);
}
```

### Pattern 2: Sắp xếp điểm theo hướng

```typescript
import { nthDirectionRotationV2, sortByRotationV2 } from '../nth.direction';

orderPointsAroundCenter(center: Point, points: Point[]): Point[] {
    // Sắp xếp điểm theo góc quay quanh tâm
    return sortByRotationV2(center, points);
}

findPointPosition(center: Point, targetPoint: Point, allPoints: Point[]): number {
    const referenceVector = vector(1, 0); // Vector tham chiếu
    return nthDirectionRotationV2(referenceVector, center, targetPoint, allPoints);
}
```

### Pattern 3: Xử lý phương trình toán học

```typescript
import { PolyBase, Complex } from '../solving';

solveQuadraticIntersection(a: number, b: number, c: number): Point[] {
    try {
        // Giải phương trình ax² + bx + c = 0
        const poly = PolyBase.getPoly([a, b, c]);
        const allRoots = poly.roots();
        const realRoots = Complex.filterRealRoots(allRoots);
        
        return realRoots.map(root => point(root.real, 0));
    } catch (error) {
        console.warn('Không thể giải phương trình:', error);
        return [];
    }
}
```

### Pattern 4: Chuyển đổi Flatten.js

```typescript
import { createFlattenLine, createFlattenCircle } from './util.flatten';

calculateAdvancedIntersection(renderLine: RenderLine, renderCircle: RenderCircle) {
    const docCtrl = getFocusDocCtrl(this.editor);
    
    // Chuyển đổi sang Flatten.js objects
    const flattenLine = createFlattenLine(renderLine, docCtrl);
    const flattenCircle = createFlattenCircle(renderCircle, docCtrl);
    
    // Sử dụng Flatten.js methods
    const intersections = flattenLine.intersect(flattenCircle);
    
    return intersections;
}
```

## Xử lý lỗi và Edge Cases

### 1. Pointer Error Handling

```typescript
protected async onPointerDown(event: GeoPointerEvent) {
    return handleIfPointerNotInError(this, async () => {
        // Logic chính của tool
        await this.processPointerEvent(event);
    });
}
```

### 2. Validation Input

```typescript
validateInput(elements: GeoRenderElement[]): boolean {
    // Kiểm tra số lượng elements
    if (elements.length < 2) {
        this.showError('Cần ít nhất 2 phần tử');
        return false;
    }

    // Kiểm tra type của elements
    const validTypes = ['RenderLine', 'RenderCircle'];
    if (!elements.every(el => validTypes.includes(el.type))) {
        this.showError('Chỉ hỗ trợ đường thẳng và đường tròn');
        return false;
    }

    return true;
}
```

### 3. Numerical Stability

```typescript
import { GeoEpsilon } from '../model/geo.models';

comparePoints(p1: Point, p2: Point): boolean {
    return Math.abs(p1.x - p2.x) < GeoEpsilon && 
           Math.abs(p1.y - p2.y) < GeoEpsilon;
}

filterNearDuplicates(points: Point[]): Point[] {
    return points.filter((point, index) => {
        return !points.slice(0, index).some(prevPoint => 
            comparePoints(point, prevPoint)
        );
    });
}
```

## Optimization Tips

### 1. Caching Calculations

```typescript
class MyTool extends GeometryTool<MyToolState> {
    private calculationCache = new Map<string, any>();

    private getCachedResult<T>(key: string, calculator: () => T): T {
        if (!this.calculationCache.has(key)) {
            this.calculationCache.set(key, calculator());
        }
        return this.calculationCache.get(key);
    }

    calculateExpensiveIntersection(line1: RenderLine, line2: RenderLine) {
        const cacheKey = `${line1.name}-${line2.name}`;
        return this.getCachedResult(cacheKey, () => {
            return calculateLineLineIntersection(line1, line2, this.docCtrl);
        });
    }
}
```

### 2. Batch Operations

```typescript
async createMultiplePoints(positions: Position[]) {
    const docCtrl = getFocusDocCtrl(this.editor);
    const requests: GeoElConstructionRequest[] = [];

    // Tạo tất cả requests trước
    positions.forEach((pos, index) => {
        const name = pickPointName(docCtrl, [], 
            requests.map(r => r.name));
        requests.push(buildPointConstruction(name, pos));
    });

    // Thực hiện batch construction
    await Promise.all(
        requests.map(request => remoteConstruct(request, docCtrl))
    );
}
```

### 3. Early Exit Conditions

```typescript
findIntersections(elements: GeoRenderElement[]): Point[] {
    // Early exit nếu không đủ elements
    if (elements.length < 2) return [];

    // Early exit nếu elements không hợp lệ
    if (!this.validateInput(elements)) return [];

    // Tiếp tục với logic chính
    return this.calculateIntersections(elements);
}
```

## Testing Utilities

### 1. Mock Data Creation

```typescript
// Test helper functions
function createMockLine(name: string, start: Point, end: Point): RenderLine {
    // Tạo mock RenderLine cho testing
}

function createMockCircle(name: string, center: Point, radius: number): RenderCircle {
    // Tạo mock RenderCircle cho testing
}
```

### 2. Assertion Helpers

```typescript
function assertPointsEqual(actual: Point[], expected: Point[], epsilon = 1e-10) {
    expect(actual.length).toBe(expected.length);
    actual.forEach((point, index) => {
        expect(Math.abs(point.x - expected[index].x)).toBeLessThan(epsilon);
        expect(Math.abs(point.y - expected[index].y)).toBeLessThan(epsilon);
    });
}
```

## Integration Examples

### Example 1: Perpendicular Bisector Tool

```typescript
export class PerpendicularBisectorTool extends GeometryTool<PerpendicularBisectorState> {
    constructor() {
        super('PerpendicularBisector');
        this.setupSelectors();
    }

    private setupSelectors() {
        this.selector = stroke({
            multiple: false,
            renderEl: true,
            highlightOnMatch: true
        });
    }

    protected async onPointerDown(event: GeoPointerEvent) {
        return handleIfPointerNotInError(this, async () => {
            const selectedStroke = this.selector.trySelect(event, this.doc);
            if (!selectedStroke) return;

            await this.createPerpendicularBisector(selectedStroke);
        });
    }

    private async createPerpendicularBisector(stroke: SelectedStroke) {
        const docCtrl = getFocusDocCtrl(this.editor);
        
        // 1. Tìm trung điểm
        const midpoint = this.calculateMidpoint(stroke);
        const midpointName = pickPointName(docCtrl);
        
        // 2. Tạo đường vuông góc
        const perpLineName = pickShapeName(docCtrl);
        
        // 3. Xây dựng construction requests
        const midpointRequest = buildPointConstruction(midpointName, midpoint);
        const perpLineRequest = buildPerpendicularLine(
            perpLineName,
            stroke.name,
            stroke.type,
            midpointName
        );

        // 4. Thực hiện construction
        await remoteConstruct(midpointRequest, docCtrl);
        await remoteConstruct(perpLineRequest, docCtrl);
    }

    private calculateMidpoint(stroke: SelectedStroke): Position {
        // Logic tính trung điểm
        const start = stroke.coord('start', this.doc.rendererCtrl);
        const end = stroke.coord('end', this.doc.rendererCtrl);
        
        return {
            x: (start[0] + end[0]) / 2,
            y: (start[1] + end[1]) / 2
        };
    }
}
```

### Example 2: Circle Through Three Points

```typescript
export class CircleThreePointsTool extends GeometryTool<CircleThreePointsState> {
    private points: RenderVertex[] = [];

    protected async onPointerDown(event: GeoPointerEvent) {
        return handleIfPointerNotInError(this, async () => {
            const point = this.vertexSelector.trySelect(event, this.doc);
            if (!point) return;

            this.points.push(point);

            if (this.points.length === 3) {
                await this.createCircle();
                this.reset();
            }
        });
    }

    private async createCircle() {
        const docCtrl = getFocusDocCtrl(this.editor);
        
        // 1. Tính tâm và bán kính từ 3 điểm
        const { center, radius } = this.calculateCircleFromThreePoints(this.points);
        
        // 2. Tạo tên cho circle
        const circleName = pickShapeName(docCtrl);
        
        // 3. Xây dựng construction request
        const request = buildCircleConstruction(circleName, center, radius);
        
        // 4. Thực hiện construction
        await remoteConstruct(request, docCtrl);
    }

    private calculateCircleFromThreePoints(points: RenderVertex[]) {
        // Sử dụng solving system để tìm tâm và bán kính
        const coords = points.map(p => p.coord('position', this.doc.rendererCtrl));
        
        // Giải hệ phương trình để tìm tâm đường tròn
        // (x-h)² + (y-k)² = r²
        // Với 3 điểm, ta có 3 phương trình và 3 ẩn (h, k, r)
        
        return this.solveCircleEquations(coords);
    }

    private solveCircleEquations(coords: number[][]): { center: Position, radius: number } {
        // Implementation sử dụng polynomial solving
        // ...
        return { center: { x: 0, y: 0 }, radius: 1 };
    }
}
```

## Debugging Tips

### 1. Logging Utilities

```typescript
function logIntersections(intersections: Point[], context: string) {
    console.group(`Intersections - ${context}`);
    intersections.forEach((point, index) => {
        console.log(`Point ${index}: (${point.x.toFixed(3)}, ${point.y.toFixed(3)})`);
    });
    console.groupEnd();
}
```

### 2. Visual Debugging

```typescript
function addDebugPoint(position: Position, color = 'red') {
    // Tạo debug point để visualize tính toán
    const debugPoint = new RenderVertex(
        `debug_${Date.now()}`,
        position,
        { color, size: 3 }
    );
    // Add to preview layer
}
```

### 3. Performance Monitoring

```typescript
function measurePerformance<T>(fn: () => T, label: string): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`${label}: ${(end - start).toFixed(2)}ms`);
    return result;
}
```

## Kết luận

Utility APIs cung cấp foundation mạnh mẽ để xây dựng các geometry tools phức tạp. Bằng cách kết hợp các patterns và best practices trong document này, bạn có thể tạo ra các tools hiệu quả và đáng tin cậy.

### Checklist cho Tool Development

- [ ] Thiết kế rõ ràng input/output
- [ ] Sử dụng appropriate selectors
- [ ] Implement error handling
- [ ] Validate input data
- [ ] Optimize performance
- [ ] Add comprehensive tests
- [ ] Document usage examples

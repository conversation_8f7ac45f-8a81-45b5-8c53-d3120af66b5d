# Utility APIs Examples

## Tổng quan

Document này cung cấp các ví dụ thực tế về cách sử dụng Utility APIs trong VIClass Geometry Editor để xây dựng các tool hình học phức tạp.

## Basic Examples

### Example 1: Tạ<PERSON> điểm giao của hai đường thẳng

```typescript
import {
    calculateLineLineIntersection,
    buildIntersectionRequest,
    pickPointName,
    remoteConstruct,
    getFocusDocCtrl
} from '@viclass/editor.geo';

async function createLineIntersection(line1: RenderLine, line2: RenderLine) {
    const docCtrl = getFocusDocCtrl(this.editor);
    
    // 1. Ki<PERSON>m tra giao điểm có tồn tại không
    const intersections = calculateLineLineIntersection(line1, line2, docCtrl);
    if (intersections.length === 0) {
        throw new Error('<PERSON> đường thẳng không giao nhau');
    }

    // 2. <PERSON><PERSON><PERSON> tên tự động cho điểm giao
    const pointName = pickPointName(docCtrl);

    // 3. Xây dựng construction request
    const request = buildIntersectionRequest({
        cgName: 'LineLine',
        outputName: pointName,
        paramA: {
            name: line1.name,
            elType: 'Line',
            labelType: 'Line',
            defId: 'aLine'
        },
        paramB: {
            name: line2.name,
            elType: 'Line',
            labelType: 'Line',
            defId: 'aLine'
        }
    });

    // 4. Thực hiện construction
    await remoteConstruct(request, docCtrl);
    
    return pointName;
}
```

### Example 2: Tạo đường thẳng song song

```typescript
import { buildParallelLine } from '@viclass/editor.geo';

async function createParallelLine(
    originalLine: RenderLine, 
    throughPoint: RenderVertex
) {
    const docCtrl = getFocusDocCtrl(this.editor);
    
    // 1. Tạo tên cho đường thẳng mới
    const lineName = pickShapeName(docCtrl);
    
    // 2. Xây dựng construction request
    const request = buildParallelLine(
        lineName,
        originalLine.name,
        originalLine.type,
        throughPoint.name
    );
    
    // 3. Thực hiện construction
    await remoteConstruct(request, docCtrl);
    
    return lineName;
}
```

### Example 3: Sắp xếp điểm theo góc quay

```typescript
import { sortByRotationV2, nthDirectionRotationV2 } from '@viclass/editor.geo';
import { point, vector } from '@flatten-js/core';

function orderVerticesAroundCenter(
    centerVertex: RenderVertex,
    vertices: RenderVertex[],
    docCtrl: GeoDocCtrl
): RenderVertex[] {
    // 1. Chuyển đổi sang Flatten.js Points
    const centerCoords = centerVertex.coord('position', docCtrl.rendererCtrl);
    const center = point(centerCoords[0], centerCoords[1]);
    
    const points = vertices.map(vertex => {
        const coords = vertex.coord('position', docCtrl.rendererCtrl);
        return point(coords[0], coords[1]);
    });
    
    // 2. Sắp xếp theo góc quay
    const sortedPoints = sortByRotationV2(center, points);
    
    // 3. Trả về vertices theo thứ tự đã sắp xếp
    return sortedPoints.map(sortedPoint => {
        return vertices.find(vertex => {
            const coords = vertex.coord('position', docCtrl.rendererCtrl);
            return Math.abs(coords[0] - sortedPoint.x) < 1e-10 &&
                   Math.abs(coords[1] - sortedPoint.y) < 1e-10;
        });
    }).filter(Boolean);
}
```

## Advanced Examples

### Example 4: Tool tạo tam giác đều

```typescript
import { GeometryTool } from './geo.tool';
import { vertex } from '../selectors';
import { buildPointConstruction, buildLineSegmentConstruction } from './util.construction';

export class EquilateralTriangleTool extends GeometryTool<EquilateralTriangleState> {
    private points: RenderVertex[] = [];

    constructor() {
        super('EquilateralTriangle');
        this.setupSelectors();
    }

    private setupSelectors() {
        this.vertexSelector = vertex({
            renderEl: true,
            preview: true,
            genPreview: true,
            highlightOnMatch: true
        });
    }

    protected async onPointerDown(event: GeoPointerEvent) {
        return handleIfPointerNotInError(this, async () => {
            const point = this.vertexSelector.trySelect(event, this.doc);
            if (!point) return;

            this.points.push(point);

            if (this.points.length === 2) {
                await this.createEquilateralTriangle();
                this.reset();
            }
        });
    }

    private async createEquilateralTriangle() {
        const docCtrl = getFocusDocCtrl(this.editor);
        
        // 1. Lấy tọa độ hai điểm đầu
        const p1Coords = this.points[0].coord('position', docCtrl.rendererCtrl);
        const p2Coords = this.points[1].coord('position', docCtrl.rendererCtrl);
        
        // 2. Tính điểm thứ 3 để tạo tam giác đều
        const thirdPoint = this.calculateThirdPoint(p1Coords, p2Coords);
        
        // 3. Tạo điểm thứ 3
        const p3Name = pickPointName(docCtrl);
        const p3Request = buildPointConstruction(p3Name, thirdPoint);
        await remoteConstruct(p3Request, docCtrl);
        
        // 4. Tạo các cạnh tam giác
        await this.createTriangleSides(this.points[0].name, this.points[1].name, p3Name);
    }

    private calculateThirdPoint(p1: number[], p2: number[]): Position {
        // Tính điểm thứ 3 của tam giác đều
        const dx = p2[0] - p1[0];
        const dy = p2[1] - p1[1];
        
        // Quay vector (dx, dy) một góc 60 độ
        const cos60 = Math.cos(Math.PI / 3);
        const sin60 = Math.sin(Math.PI / 3);
        
        const x3 = p1[0] + dx * cos60 - dy * sin60;
        const y3 = p1[1] + dx * sin60 + dy * cos60;
        
        return { x: x3, y: y3 };
    }

    private async createTriangleSides(p1Name: string, p2Name: string, p3Name: string) {
        const docCtrl = getFocusDocCtrl(this.editor);
        
        // Tạo 3 cạnh của tam giác
        const sides = [
            [p1Name, p2Name],
            [p2Name, p3Name],
            [p3Name, p1Name]
        ];

        for (const [start, end] of sides) {
            const sideName = pickShapeName(docCtrl);
            const request = buildLineSegmentConstruction(sideName);
            // Add points to construction
            request.paramSpecs.push(
                {
                    indexInCG: 0,
                    paramDefId: 'aPoint',
                    optional: false,
                    tplStrLangId: 'tpl-StartPoint',
                    params: { name: { type: 'singleValue', value: start } }
                },
                {
                    indexInCG: 1,
                    paramDefId: 'aPoint',
                    optional: false,
                    tplStrLangId: 'tpl-EndPoint',
                    params: { name: { type: 'singleValue', value: end } }
                }
            );
            
            await remoteConstruct(request, docCtrl);
        }
    }
}
```

### Example 5: Tool tìm tâm đường tròn ngoại tiếp

```typescript
import { Complex, PolyBase } from '../solving';

export class CircumcenterTool extends GeometryTool<CircumcenterState> {
    private vertices: RenderVertex[] = [];

    protected async onPointerDown(event: GeoPointerEvent) {
        return handleIfPointerNotInError(this, async () => {
            const vertex = this.vertexSelector.trySelect(event, this.doc);
            if (!vertex) return;

            this.vertices.push(vertex);

            if (this.vertices.length === 3) {
                await this.createCircumcenter();
                this.reset();
            }
        });
    }

    private async createCircumcenter() {
        const docCtrl = getFocusDocCtrl(this.editor);
        
        // 1. Lấy tọa độ 3 điểm
        const coords = this.vertices.map(v => 
            v.coord('position', docCtrl.rendererCtrl)
        );
        
        // 2. Tính tâm đường tròn ngoại tiếp
        const circumcenter = this.calculateCircumcenter(coords);
        
        // 3. Tạo điểm tâm
        const centerName = pickPointName(docCtrl);
        const centerRequest = buildPointConstruction(centerName, circumcenter);
        await remoteConstruct(centerRequest, docCtrl);
        
        // 4. Tính bán kính và tạo đường tròn
        const radius = this.calculateDistance(circumcenter, coords[0]);
        await this.createCircumcircle(centerName, radius);
    }

    private calculateCircumcenter(points: number[][]): Position {
        const [p1, p2, p3] = points;
        
        // Sử dụng công thức tính tâm đường tròn ngoại tiếp
        const d = 2 * (p1[0] * (p2[1] - p3[1]) + 
                      p2[0] * (p3[1] - p1[1]) + 
                      p3[0] * (p1[1] - p2[1]));
        
        if (Math.abs(d) < 1e-10) {
            throw new Error('Ba điểm thẳng hàng, không tạo được đường tròn');
        }
        
        const ux = ((p1[0] * p1[0] + p1[1] * p1[1]) * (p2[1] - p3[1]) +
                   (p2[0] * p2[0] + p2[1] * p2[1]) * (p3[1] - p1[1]) +
                   (p3[0] * p3[0] + p3[1] * p3[1]) * (p1[1] - p2[1])) / d;
        
        const uy = ((p1[0] * p1[0] + p1[1] * p1[1]) * (p3[0] - p2[0]) +
                   (p2[0] * p2[0] + p2[1] * p2[1]) * (p1[0] - p3[0]) +
                   (p3[0] * p3[0] + p3[1] * p3[1]) * (p2[0] - p1[0])) / d;
        
        return { x: ux, y: uy };
    }

    private calculateDistance(p1: Position, p2: number[]): number {
        const dx = p1.x - p2[0];
        const dy = p1.y - p2[1];
        return Math.sqrt(dx * dx + dy * dy);
    }

    private async createCircumcircle(centerName: string, radius: number) {
        const docCtrl = getFocusDocCtrl(this.editor);
        const circleName = pickShapeName(docCtrl);
        
        const request = new GeoElConstructionRequest(
            'Circle/CircleEC',
            'Circle',
            'FromCenterAndRadius'
        );
        request.name = circleName;
        request.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Center',
                params: { name: { type: 'singleValue', value: centerName } }
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-Radius',
                params: { value: { type: 'singleValue', value: radius.toString() } }
            }
        ];
        
        await remoteConstruct(request, docCtrl);
    }
}
```

### Example 6: Tool tạo ellipse từ 5 điểm

```typescript
import { intersectionEllipses, intersectionLineEllipse } from './util.intersections';
import { Coefficients } from '../solving/coefficients';

export class EllipseFromFivePointsTool extends GeometryTool<EllipseFromFivePointsState> {
    private points: RenderVertex[] = [];

    protected async onPointerDown(event: GeoPointerEvent) {
        return handleIfPointerNotInError(this, async () => {
            const point = this.vertexSelector.trySelect(event, this.doc);
            if (!point) return;

            this.points.push(point);

            if (this.points.length === 5) {
                await this.createEllipseFromFivePoints();
                this.reset();
            }
        });
    }

    private async createEllipseFromFivePoints() {
        const docCtrl = getFocusDocCtrl(this.editor);
        
        // 1. Lấy tọa độ 5 điểm
        const coords = this.points.map(v => 
            v.coord('position', docCtrl.rendererCtrl)
        );
        
        // 2. Giải hệ phương trình để tìm hệ số ellipse
        const coefficients = this.solveEllipseCoefficients(coords);
        
        // 3. Tạo ellipse từ hệ số
        await this.createEllipseFromCoefficients(coefficients);
    }

    private solveEllipseCoefficients(points: number[][]): Coefficients {
        // Phương trình ellipse tổng quát: Ax² + Bxy + Cy² + Dx + Ey + F = 0
        // Với 5 điểm, ta có 5 phương trình và 5 ẩn (A, B, C, D, E) với F = 1
        
        const matrix: number[][] = [];
        const constants: number[] = [];
        
        points.forEach(([x, y]) => {
            matrix.push([x*x, x*y, y*y, x, y]);
            constants.push(-1); // F = 1, nên phía bên phải là -1
        });
        
        // Giải hệ phương trình tuyến tính
        const solution = this.solveLinearSystem(matrix, constants);
        
        return new Coefficients(
            solution[0], // A
            solution[1], // B
            solution[2], // C
            solution[3], // D
            solution[4], // E
            1            // F
        );
    }

    private solveLinearSystem(matrix: number[][], constants: number[]): number[] {
        // Sử dụng Gaussian elimination hoặc các phương pháp khác
        // Đây là implementation đơn giản
        const n = matrix.length;
        const augmented = matrix.map((row, i) => [...row, constants[i]]);
        
        // Forward elimination
        for (let i = 0; i < n; i++) {
            // Tìm pivot
            let maxRow = i;
            for (let k = i + 1; k < n; k++) {
                if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
                    maxRow = k;
                }
            }
            
            // Swap rows
            [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]];
            
            // Eliminate column
            for (let k = i + 1; k < n; k++) {
                const factor = augmented[k][i] / augmented[i][i];
                for (let j = i; j <= n; j++) {
                    augmented[k][j] -= factor * augmented[i][j];
                }
            }
        }
        
        // Back substitution
        const solution = new Array(n);
        for (let i = n - 1; i >= 0; i--) {
            solution[i] = augmented[i][n];
            for (let j = i + 1; j < n; j++) {
                solution[i] -= augmented[i][j] * solution[j];
            }
            solution[i] /= augmented[i][i];
        }
        
        return solution;
    }

    private async createEllipseFromCoefficients(coefficients: Coefficients) {
        const docCtrl = getFocusDocCtrl(this.editor);
        const ellipseName = pickShapeName(docCtrl);
        
        // Chuyển đổi từ dạng tổng quát sang dạng chuẩn
        const { center, a, b, rotation } = coefficients.toStandardForm();
        
        // Tạo điểm tâm
        const centerName = pickPointName(docCtrl);
        const centerRequest = buildPointConstruction(centerName, center);
        await remoteConstruct(centerRequest, docCtrl);
        
        // Tạo ellipse
        const ellipseRequest = new GeoElConstructionRequest(
            'Ellipse/EllipseEC',
            'Ellipse',
            'FromCenterAndAxes'
        );
        ellipseRequest.name = ellipseName;
        ellipseRequest.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Center',
                params: { name: { type: 'singleValue', value: centerName } }
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-SemiMajorAxis',
                params: { value: { type: 'singleValue', value: a.toString() } }
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-SemiMinorAxis',
                params: { value: { type: 'singleValue', value: b.toString() } }
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-Rotation',
                params: { value: { type: 'singleValue', value: rotation.toString() } }
            }
        ];
        
        await remoteConstruct(ellipseRequest, docCtrl);
    }
}
```

## Complex Mathematical Examples

### Example 7: Giải phương trình bậc 4 cho giao điểm ellipse

```typescript
import { PolyBase, Complex } from '../solving';

function findEllipseIntersections(
    ellipse1: RenderEllipse, 
    ellipse2: RenderEllipse,
    docCtrl: GeoDocCtrl
): Point[] {
    // 1. Lấy hệ số của hai ellipse
    const coefs1 = ellipse1.getCoefficients(docCtrl);
    const coefs2 = ellipse2.getCoefficients(docCtrl);
    
    // 2. Tạo hệ phương trình
    // Ellipse 1: A₁x² + B₁xy + C₁y² + D₁x + E₁y + F₁ = 0
    // Ellipse 2: A₂x² + B₂xy + C₂y² + D₂x + E₂y + F₂ = 0
    
    // 3. Eliminate x để có phương trình bậc 4 theo y
    const quarticCoefficients = eliminateVariable(coefs1, coefs2);
    
    // 4. Giải phương trình bậc 4
    const poly = PolyBase.getPoly(quarticCoefficients);
    const allRoots = poly.roots();
    const realRoots = Complex.filterRealRoots(allRoots);
    
    // 5. Tìm x tương ứng với mỗi y
    const intersectionPoints: Point[] = [];
    
    realRoots.forEach(yRoot => {
        const y = yRoot.real;
        const xValues = solveForX(y, coefs1, coefs2);
        
        xValues.forEach(x => {
            // Kiểm tra điểm có thực sự nằm trên cả hai ellipse
            if (isPointOnEllipse(x, y, coefs1) && isPointOnEllipse(x, y, coefs2)) {
                intersectionPoints.push(point(x, y));
            }
        });
    });
    
    return intersectionPoints;
}

function eliminateVariable(coefs1: Coefficients, coefs2: Coefficients): number[] {
    // Implementation phức tạp để eliminate biến x
    // Trả về hệ số của phương trình bậc 4 theo y
    // [a₄, a₃, a₂, a₁, a₀] cho a₄y⁴ + a₃y³ + a₂y² + a₁y + a₀ = 0
    
    // Đây là một implementation đơn giản hóa
    return [1, 0, -2, 0, 1]; // Ví dụ
}

function solveForX(y: number, coefs1: Coefficients, coefs2: Coefficients): number[] {
    // Với y đã biết, giải phương trình bậc 2 để tìm x
    // Ax² + Bx + C = 0 với A, B, C phụ thuộc vào y
    
    const A = coefs1.A;
    const B = coefs1.B * y + coefs1.D;
    const C = coefs1.C * y * y + coefs1.E * y + coefs1.F;
    
    const discriminant = B * B - 4 * A * C;
    
    if (discriminant < 0) return [];
    if (discriminant === 0) return [-B / (2 * A)];
    
    const sqrtD = Math.sqrt(discriminant);
    return [
        (-B + sqrtD) / (2 * A),
        (-B - sqrtD) / (2 * A)
    ];
}

function isPointOnEllipse(x: number, y: number, coefs: Coefficients): boolean {
    const value = coefs.A * x * x + 
                  coefs.B * x * y + 
                  coefs.C * y * y + 
                  coefs.D * x + 
                  coefs.E * y + 
                  coefs.F;
    
    return Math.abs(value) < 1e-10;
}
```

### Example 8: Optimization với caching

```typescript
class IntersectionCache {
    private cache = new Map<string, Point[]>();
    
    getCachedIntersection(
        element1: GeoRenderElement,
        element2: GeoRenderElement,
        calculator: () => Point[]
    ): Point[] {
        const key = this.generateCacheKey(element1, element2);
        
        if (!this.cache.has(key)) {
            const result = calculator();
            this.cache.set(key, result);
        }
        
        return this.cache.get(key)!;
    }
    
    private generateCacheKey(el1: GeoRenderElement, el2: GeoRenderElement): string {
        // Tạo key duy nhất cho cặp elements
        const names = [el1.name, el2.name].sort();
        return `${names[0]}-${names[1]}`;
    }
    
    invalidate(elementName: string) {
        // Xóa cache liên quan đến element đã thay đổi
        const keysToDelete = Array.from(this.cache.keys())
            .filter(key => key.includes(elementName));
        
        keysToDelete.forEach(key => this.cache.delete(key));
    }
}

// Sử dụng cache trong tool
export class OptimizedIntersectionTool extends GeometryTool<any> {
    private intersectionCache = new IntersectionCache();
    
    private calculateIntersection(el1: GeoRenderElement, el2: GeoRenderElement): Point[] {
        return this.intersectionCache.getCachedIntersection(el1, el2, () => {
            // Actual calculation logic
            if (el1.type === 'RenderLine' && el2.type === 'RenderLine') {
                return calculateLineLineIntersection(el1, el2, this.docCtrl);
            }
            // ... other cases
            return [];
        });
    }
}
```

## Testing Examples

### Example 9: Unit test cho utility functions

```typescript
describe('Intersection Utilities', () => {
    let mockDocCtrl: GeoDocCtrl;
    
    beforeEach(() => {
        mockDocCtrl = createMockDocCtrl();
    });
    
    describe('calculateLineLineIntersection', () => {
        it('should find intersection of two perpendicular lines', () => {
            // Arrange
            const line1 = createMockLine('l1', [0, 0], [1, 0]); // Horizontal
            const line2 = createMockLine('l2', [0.5, -1], [0.5, 1]); // Vertical
            
            // Act
            const intersections = calculateLineLineIntersection(line1, line2, mockDocCtrl);
            
            // Assert
            expect(intersections).toHaveLength(1);
            expect(intersections[0].x).toBeCloseTo(0.5);
            expect(intersections[0].y).toBeCloseTo(0);
        });
        
        it('should return empty array for parallel lines', () => {
            // Arrange
            const line1 = createMockLine('l1', [0, 0], [1, 0]);
            const line2 = createMockLine('l2', [0, 1], [1, 1]);
            
            // Act
            const intersections = calculateLineLineIntersection(line1, line2, mockDocCtrl);
            
            // Assert
            expect(intersections).toHaveLength(0);
        });
    });
    
    describe('nthDirectionRotationV2', () => {
        it('should correctly order points around center', () => {
            // Arrange
            const center = point(0, 0);
            const points = [
                point(1, 0),   // 0°
                point(0, 1),   // 90°
                point(-1, 0),  // 180°
                point(0, -1)   // 270°
            ];
            const referenceVector = vector(1, 0);
            
            // Act & Assert
            expect(nthDirectionRotationV2(referenceVector, center, points[0], points)).toBe(1);
            expect(nthDirectionRotationV2(referenceVector, center, points[1], points)).toBe(2);
            expect(nthDirectionRotationV2(referenceVector, center, points[2], points)).toBe(3);
            expect(nthDirectionRotationV2(referenceVector, center, points[3], points)).toBe(4);
        });
    });
});

// Helper functions for testing
function createMockLine(name: string, start: number[], end: number[]): RenderLine {
    return {
        name,
        type: 'RenderLine',
        coord: (type: string) => type === 'start' ? start : end,
        orderedVector: () => [end[0] - start[0], end[1] - start[1]]
    } as RenderLine;
}

function createMockDocCtrl(): GeoDocCtrl {
    return {
        rendererCtrl: {
            // Mock renderer controller
        }
    } as GeoDocCtrl;
}
```

## Performance Examples

### Example 10: Batch processing với Promise.all

```typescript
async function createMultipleIntersections(
    linePairs: Array<[RenderLine, RenderLine]>
): Promise<string[]> {
    const docCtrl = getFocusDocCtrl(this.editor);
    
    // 1. Tính toán tất cả intersections song song
    const intersectionPromises = linePairs.map(async ([line1, line2]) => {
        const intersections = calculateLineLineIntersection(line1, line2, docCtrl);
        if (intersections.length === 0) return null;
        
        return {
            intersection: intersections[0],
            line1: line1.name,
            line2: line2.name
        };
    });
    
    const intersectionResults = await Promise.all(intersectionPromises);
    const validIntersections = intersectionResults.filter(Boolean);
    
    // 2. Tạo construction requests
    const constructionPromises = validIntersections.map(async (result) => {
        const pointName = pickPointName(docCtrl);
        const request = buildIntersectionRequest({
            cgName: 'LineLine',
            outputName: pointName,
            paramA: {
                name: result.line1,
                elType: 'Line',
                labelType: 'Line',
                defId: 'aLine'
            },
            paramB: {
                name: result.line2,
                elType: 'Line',
                labelType: 'Line',
                defId: 'aLine'
            }
        });
        
        await remoteConstruct(request, docCtrl);
        return pointName;
    });
    
    // 3. Thực hiện tất cả constructions song song
    return Promise.all(constructionPromises);
}
```

Các examples này minh họa cách sử dụng Utility APIs trong các tình huống thực tế, từ cơ bản đến phức tạp, bao gồm cả optimization và testing strategies.

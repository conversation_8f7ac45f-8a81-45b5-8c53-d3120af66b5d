# Utility APIs Reference

## Tổng quan

Document này cung cấp reference đ<PERSON>y đủ cho tất cả Utility APIs trong VIClass Geometry Editor, bao gồm các module tiện ích cho tính toán hình học, x<PERSON><PERSON> dựng phần tử, và giải phương trình toán học.

## C<PERSON>u trú<PERSON>

```
util/
├── util.tool.ts           # Công cụ chung và helper functions
├── util.construction.ts   # Xây dựng phần tử hình học
├── util.intersections.ts  # Tính toán giao điểm
├── util.flatten.ts        # Chuyển đổi Flatten.js geometry
├── nth.direction.ts       # Thuật toán định hướng và sắp xếp
└── solving/               # Hệ thống giải toán học
    ├── complex.ts         # Số phức
    ├── poly.solving.ts    # Giải phương trình đa thức
    └── coefficients.ts    # H<PERSON> số ellipse
```

## Utility Tool Functions

### Core Helper Functions

#### `isValidIdx(idx: number): boolean`

Kiểm tra index có hợp lệ không.

```typescript
import { isValidIdx } from '@viclass/editor.geo';

if (isValidIdx(selectedIndex)) {
    // Xử lý với index hợp lệ
}
```

#### `handleIfPointerNotInError(tool, fn, ...args)`

Xử lý lỗi pointer không nằm trong vùng hợp lệ.

```typescript
export function handleIfPointerNotInError(
    tool: GeometryTool<any>,
    fn: (...args: any[]) => any,
    ...argv: any[]
): Promise<any> | any
```

#### `pointsEqual(p1: number[], p2: number[], epsilon?: number): boolean`

So sánh hai điểm với độ chính xác epsilon.

### Registry Functions

#### `geoDocReg(coordStateId: string): string`

Tạo registry key cho document.

#### `geoLayerReg(coordinatorId: string, docId: number): string`

Tạo registry key cho layer.

#### `geoObjectReg(coordinatorId: string, docId: number): string`

Tạo registry key cho object.

### Name Generation

#### `pickPointName(docCtrl, excluded?, previousElementNames?): string`

Tạo tên tự động cho điểm.

#### `pickShapeName(docCtrl, excluded?, previousElementNames?): string`

Tạo tên tự động cho hình dạng.

## Construction Utilities

### Basic Construction

#### `buildPointConstruction(name: string, pointPos: Position): GeoElConstructionRequest`

Xây dựng request tạo điểm từ tọa độ.

```typescript
import { buildPointConstruction } from '@viclass/editor.geo';

const pointRequest = buildPointConstruction('A', { x: 100, y: 200 });
```

#### `buildLineSegmentConstruction(name: string): GeoElConstructionRequest`

Xây dựng request tạo đoạn thẳng.

### Intersection Construction

#### `buildIntersectionRequest(args): GeoElConstructionRequest`

Xây dựng request tạo giao điểm.

```typescript
const intersectionRequest = buildIntersectionRequest({
    cgName: 'LineLine',
    outputName: 'P',
    paramA: { name: 'l1', elType: 'Line', labelType: 'Line', defId: 'aLine' },
    paramB: { name: 'l2', elType: 'Line', labelType: 'Line', defId: 'aLine' },
    nth: 1
});
```

### Advanced Construction

#### `buildAngleConstructionFromLines(...): GeoElConstructionRequest`

Xây dựng góc từ hai đường thẳng.

#### `buildParallelLine(name, lineStartName, lineStartType, throughPointName): GeoElConstructionRequest`

Xây dựng đường thẳng song song.

#### `buildPerpendicularLine(name, lineStartName, lineStartType, throughPointName): GeoElConstructionRequest`

Xây dựng đường thẳng vuông góc.

## Intersection Calculations

### Basic Intersections

#### `calculateLineLineIntersection(line1, line2, docCtrl): Point[]`

Tính giao điểm hai đường thẳng.

```typescript
import { calculateLineLineIntersection } from '@viclass/editor.geo';

const intersections = calculateLineLineIntersection(line1, line2, docCtrl);
```

#### `calculateLineCircleIntersection(line, circle, docCtrl): Point[]`

Tính giao điểm đường thẳng và đường tròn.

#### `calculateCircleCircleIntersection(circle1, circle2, docCtrl): Point[]`

Tính giao điểm hai đường tròn.

### Advanced Intersections

#### `intersectionLineEllipse(line, ellipse, docCtrl): Point[]`

Tính giao điểm đường thẳng và ellipse.

#### `intersectionCircleEllipse(circle, ellipse, docCtrl): Point[]`

Tính giao điểm đường tròn và ellipse.

#### `intersectionEllipses(ellipse1, ellipse2, docCtrl): Point[]`

Tính giao điểm hai ellipse.

### Utility Functions

#### `isPointInSector(point, sector, docCtrl): boolean`

Kiểm tra điểm có nằm trong sector không.

## Flatten.js Conversion

### Line Conversion

#### `createFlattenLine(renderObj, docCtrl): Line`

Chuyển đổi RenderLine thành Flatten.js Line.

```typescript
import { createFlattenLine } from '@viclass/editor.geo';

const flattenLine = createFlattenLine(renderLine, docCtrl);
```

#### `createExtractFlattenLine(renderObj, docCtrl): Line | Segment | Ray`

Chuyển đổi với type detection tự động.

### Circle Conversion

#### `createFlattenCircle(renderCircle, docCtrl): Circle`

Chuyển đổi RenderCircle thành Flatten.js Circle.

```typescript
const flattenCircle = createFlattenCircle(renderCircle, docCtrl);
```

## Direction & Ordering Algorithms

### Line-based Direction

#### `nthDirectionByLine(vec, role, p): number`

Xác định điểm nằm ở phía nào của đường thẳng (1 hoặc 2).

```typescript
import { nthDirectionByLine } from '@viclass/editor.geo';

const direction = nthDirectionByLine([1, 0], [0, 0], [1, 1]); // Returns 1 or 2
```

#### `nthDirectionByLineV2(vec: Vector, role: Point, p: Point): number`

Phiên bản cải tiến sử dụng Flatten.js objects.

### Rotational Direction

#### `nthDirectionRotation(vec, role, p0, points): number`

Tìm vị trí thứ n của điểm khi xoay quanh tâm.

```typescript
const position = nthDirectionRotation(
    [1, 0],           // vector hướng
    [0, 0],           // tâm quay
    [1, 1],           // điểm cần tìm
    [[1,1], [0,1], [-1,1]]  // danh sách điểm
);
```

#### `nthDirectionRotationV2(vec: Vector, role: Point, p0: Point, points: Point[]): number`

Phiên bản cải tiến với Flatten.js objects.

### Linear Direction

#### `nthDirectionOnLine(vec, p0, points): number`

Tìm vị trí của điểm dọc theo hướng đường thẳng.

```typescript
const position = nthDirectionOnLine(
    [1, 0],           // vector hướng
    [1, 0],           // điểm cần tìm
    [[0,0], [1,0], [2,0]]   // danh sách điểm trên đường
);
```

#### `nthDirectionOnLineV2(vec: Vector, p0: Point, points: Point[]): number`

Phiên bản cải tiến.

### Advanced Sorting

#### `sortByRotationV2(center: Point, points: Point[]): Point[]`

Sắp xếp điểm theo góc quay quanh tâm.

#### `directionOfPointOnParallelVector(lineVector, linePoint, targetPoint): number`

Xác định hướng của điểm trên vector song song.

## Mathematical Solving System

### Complex Numbers

#### `Complex` Class

Lớp số phức với đầy đủ các phép toán.

```typescript
import { Complex } from '@viclass/editor.geo';

const z1 = new Complex(3, 4);
const z2 = new Complex(1, 2);
const result = z1.add(z2).mult(new Complex(2, 0));
```

**Methods:**
- `add(other: Complex): Complex` - Cộng
- `sub(other: Complex): Complex` - Trừ  
- `mult(other: Complex): Complex` - Nhân
- `div(other: Complex): Complex` - Chia
- `sqrt(): Complex` - Căn bậc hai
- `pow(n: number): Complex` - Lũy thừa
- `roots(n: number): Complex[]` - Căn bậc n

**Static Methods:**
- `filterRealRoots(roots: Complex[]): Complex[]` - Lọc nghiệm thực
- `sort(z: Complex[]): Complex[]` - Sắp xếp
- `removeDuplicates(z: Complex[]): Complex[]` - Loại bỏ trùng lặp

### Polynomial Solving

#### `PolyBase` Class

Base class cho giải phương trình đa thức.

```typescript
import { PolyBase } from '@viclass/editor.geo';

const poly = PolyBase.getPoly([1, -5, 6]); // x² - 5x + 6
const roots = poly.roots();
const realRoots = Complex.filterRealRoots(roots);
```

#### Specialized Classes

- `Linear` - Phương trình bậc 1
- `Quadratic` - Phương trình bậc 2  
- `Cubic` - Phương trình bậc 3
- `Quartic` - Phương trình bậc 4

### Coefficients

#### `Coefficients` Class

Xử lý hệ số cho ellipse và các phép biến đổi.

```typescript
import { Coefficients } from '@viclass/editor.geo';

const coefs = new Coefficients(A, B, C, D, E, F);
const transformed = coefs.transform(matrix);
```

## Error Handling

### `GeoPointerNotInError`

Lỗi khi pointer không nằm trong vùng hợp lệ.

### `CriticalErr`

Lỗi nghiêm trọng trong hệ thống.

## Best Practices

### Performance

1. **Sử dụng epsilon phù hợp** cho so sánh số thực
2. **Cache kết quả** tính toán phức tạp
3. **Kiểm tra input** trước khi tính toán

### Accuracy

1. **Sử dụng Complex.filterRealRoots()** để lọc nghiệm thực
2. **Kiểm tra degeneracy** trước khi giải
3. **Xử lý edge cases** như điểm trùng nhau

### Integration

1. **Sử dụng consistent naming** với pickPointName/pickShapeName
2. **Handle errors** với handleIfPointerNotInError
3. **Validate constructions** trước khi gửi request

## Examples

### Tạo giao điểm hai đường thẳng

```typescript
// 1. Tính toán giao điểm
const intersections = calculateLineLineIntersection(line1, line2, docCtrl);

// 2. Xây dựng construction request
const request = buildIntersectionRequest({
    cgName: 'LineLine',
    outputName: pickPointName(docCtrl),
    paramA: { name: line1.name, elType: 'Line', labelType: 'Line', defId: 'aLine' },
    paramB: { name: line2.name, elType: 'Line', labelType: 'Line', defId: 'aLine' }
});

// 3. Thực hiện construction
await remoteConstruct(request, docCtrl);
```

### Sắp xếp điểm theo góc

```typescript
const center = point(0, 0);
const points = [point(1, 0), point(0, 1), point(-1, 0), point(0, -1)];
const sorted = sortByRotationV2(center, points);
```

### Giải phương trình bậc 2

```typescript
const coefficients = [1, -3, 2]; // x² - 3x + 2
const poly = PolyBase.getPoly(coefficients);
const allRoots = poly.roots();
const realRoots = Complex.filterRealRoots(allRoots);
// realRoots = [Complex(1, 0), Complex(2, 0)]
```

import { BoundaryRectangle, Position } from '@viclass/editor.core';
import {
    BoundaryProto,
    CoordsProto,
    DocDefaultElRenderPropsProto,
    DocRenderPropProto,
    ElRenderPropsProto,
    LineTypeProto,
    PositionProto,
    PreviewAngleProto,
    PreviewCircleProto,
    PreviewCircleShapeProto,
    PreviewEllipseProto,
    PreviewEllipseShapeProto,
    PreviewLineProto,
    PreviewPolygonProto,
    PreviewSectorShapeProto,
    RenderAngleProto,
    RenderCircleProto,
    RenderCircleShapeProto,
    RenderElFlagProto,
    RenderEllipseProto,
    RenderEllipseShapeProto,
    RenderLineProto,
    RenderPolygonProto,
    RenderSectorProto,
    RenderSectorShapeProto,
    RenderVertexProto,
} from '@viclass/proto/editor.geo';
import {
    _angleGeoRenderProp,
    _circleGeoRenderProp,
    _circleShapeGeoRenderProp,
    _ellipseGeoRenderProp,
    _ellipseShapeGeoRenderProp,
    _lineGeoRenderProp,
    _lineSegmentGeoRenderProp,
    _pointGeoRenderProp,
    _polygonGeoRenderProp,
    _sectorGeoRenderProp,
    _sectorShapeGeoRenderProp,
    AngleGeoRenderProp,
    CircleGeoRenderProp,
    CircleShapeGeoRenderProp,
    DefaultGeoRenderProp,
    DocRenderProp,
    EllipseGeoRenderProp,
    EllipseShapeGeoRenderProp,
    GeoRelType,
    GeoRenderProp,
    GeoStrokeStyle,
    LineGeoRenderProp,
    LineSegmentGeoRenderProp,
    PointGeoRenderProp,
    PolygonGeoRenderProp,
    PreviewAngle,
    PreviewCircleShape,
    PreviewEllipseShape,
    PreviewLine,
    PreviewPolygon,
    PreviewSector,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderRay,
    RenderSector,
    RenderSectorShape,
    RenderVector,
    RenderVertex,
    SectorGeoRenderProp,
    SectorShapeGeoRenderProp,
    SettingPropertyType,
} from '../model';
import { buildPreviewAngleRenderProp, isElementLine } from '../tools/util.tool';

export function buildPositionProto(point: Position): PositionProto {
    return new PositionProto().setX(point.x).setY(point.y).setZ(point.z);
}

export function convertProtoToPosition(proto: PositionProto): Position {
    return { x: proto.getX(), y: proto.getY(), z: proto.getZ() };
}

export function buildBoundaryProto(boundary: BoundaryRectangle): BoundaryProto {
    return new BoundaryProto()
        .setStart(new PositionProto().setX(boundary.start.x).setY(boundary.start.y))
        .setEnd(new PositionProto().setX(boundary.end.x).setY(boundary.end.y));
}

export function convertProtoToBoundary(boundary: BoundaryProto): BoundaryRectangle {
    const start = {
        x: boundary.getStart().getX(),
        y: boundary.getStart().getY(),
    };
    const end = { x: boundary.getEnd().getX(), y: boundary.getEnd().getY() };
    return {
        start: start,
        end: end,
        width: Math.abs(end.x - start.x),
        height: Math.abs(end.y - start.y),
    };
}

export function buildCoordsProto(coords: number[]): CoordsProto {
    return new CoordsProto().setCoordsList(coords);
}

export function buildRenderElFlag(
    usable: boolean,
    valid: boolean,
    deleted?: boolean,
    unselectable?: boolean
): RenderElFlagProto {
    return new RenderElFlagProto().setUsable(usable).setValid(valid).setDeleted(deleted).setUnselectable(unselectable);
}

export function convertProtoToCoords(proto: CoordsProto): number[] {
    return proto.getCoordsList();
}

export function geoPP(prop: GeoRenderProp): ElRenderPropsProto {
    const pp = new ElRenderPropsProto();
    pp.setHidden(prop.hidden);

    return pp;
}

export function vertexPP(prop: PointGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setPointColor(prop.pointColor);
    pp.setShowPointLabel(prop.showPointLabel);
    pp.setPointLabelType(prop.pointLabelType);
    pp.setPointLabelFreeContent(prop.pointLabelFreeContent);
    return pp;
}

export function linePP(prop: LineGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setLineColor(prop.lineColor);
    pp.setLineWeight(prop.lineWeight);
    pp.setStrokeStyle(prop.strokeStyle);
    pp.setShowLabel(prop.showLabel);
    pp.setLabel(prop.label);
    pp.setSwapLabelPosition(prop.swapLabelPosition);
    return pp;
}

export function lineSegmentPP(prop: LineSegmentGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setLineColor(prop.lineColor);
    pp.setLineWeight(prop.lineWeight);
    pp.setStrokeStyle(prop.strokeStyle);
    pp.setShowLabel(prop.showLabel);
    pp.setLabelType(prop.labelType);
    pp.setLabel(prop.label);
    pp.setSwapLabelPosition(prop.swapLabelPosition);
    pp.setEnableEqualSegmentSign(prop.enableEqualSegmentSign);
    pp.setEqualSegmentSign(prop.equalSegmentSign);
    return pp;
}

export function sectorPP(prop: SectorGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setLineColor(prop.lineColor);
    pp.setLineWeight(prop.lineWeight);
    pp.setStrokeStyle(prop.strokeStyle);
    pp.setShowArcLabel(prop.showArcLabel);
    pp.setArcLabelType(prop.arcLabelType);
    pp.setArcLabelContent(prop.arcLabelContent);
    return pp;
}

export function ellipsePP(prop: EllipseGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setLineColor(prop.lineColor);
    pp.setLineWeight(prop.lineWeight);
    pp.setStrokeStyle(prop.strokeStyle);
    pp.setLabel(prop.label);
    return pp;
}

export function circlePP(prop: CircleGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setLineColor(prop.lineColor);
    pp.setLineWeight(prop.lineWeight);
    pp.setStrokeStyle(prop.strokeStyle);
    pp.setLabel(prop.label);
    return pp;
}

export function polygonPP(prop: PolygonGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setColor(prop.color);
    pp.setOpacity(prop.opacity);
    return pp;
}

export function circleShapePP(prop: CircleShapeGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setStrokeStyle(prop.strokeStyle);
    pp.setLineWeight(prop.lineWeight);
    pp.setColor(prop.color);
    pp.setOpacity(prop.opacity);
    return pp;
}

export function ellipseShapePP(prop: EllipseShapeGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setStrokeStyle(prop.strokeStyle);
    pp.setLineWeight(prop.lineWeight);
    pp.setColor(prop.color);
    pp.setOpacity(prop.opacity);
    return pp;
}

export function anglePP(prop: AngleGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setIsShowAngleSize(prop.isShowAngleSize);
    pp.setShowAngleTypes(prop.showAngleTypes);
    pp.setAngleArc(prop.angleArc);
    pp.setSpaceFromArcToCorner(prop.spaceFromArcToCorner);
    pp.setEnableEqualSegmentSign(prop.enableEqualSegmentSign);
    pp.setEqualSegmentSign(prop.equalSegmentSign);
    pp.setColor(prop.color);
    pp.setOpacity(prop.opacity);
    return pp;
}

export function sectorShapePP(prop: SectorShapeGeoRenderProp): ElRenderPropsProto {
    const pp = geoPP(prop);
    pp.setShowArcLabel(prop.showArcLabel);
    pp.setArcLabelType(prop.arcLabelType);
    pp.setArcLabelContent(prop.arcLabelContent);
    pp.setColor(prop.color);
    pp.setOpacity(prop.opacity);
    return pp;
}

export function convertSettingPropertiesToProto(props: {
    [key in SettingPropertyType]?: any;
}): ElRenderPropsProto {
    const proto = new ElRenderPropsProto();

    for (const prop in props) {
        switch (prop) {
            case 'color':
                proto.setColor(props.color);
                break;
            case 'lineColor':
                proto.setLineColor(props.lineColor);
                break;
            case 'pointColor':
                proto.setPointColor(props.pointColor);
                break;
            case 'strokeStyle':
                proto.setStrokeStyle(props.strokeStyle);
                break;
            case 'lineWeight':
                proto.setLineWeight(props.lineWeight);
                break;
            case 'hidden':
                proto.setHidden(props.hidden);
                break;
            case 'opacity':
                proto.setOpacity(props.opacity);
                break;
            case 'showLabel':
                proto.setShowLabel(props.showLabel);
                break;
            case 'labelType':
                proto.setLabelType(props.labelType);
                break;
            case 'label':
                proto.setLabel(props.label);
                break;
            case 'swapLabelPosition':
                proto.setSwapLabelPosition(props.swapLabelPosition);
                break;
            case 'spaceFromArcToCorner':
                proto.setSpaceFromArcToCorner(props.spaceFromArcToCorner);
                break;
            case 'showAngleTypes':
                proto.setShowAngleTypes(props.showAngleTypes);
                break;
            case 'angleArc':
                proto.setAngleArc(props.angleArc);
                break;
            case 'enableEqualSegmentSign':
                proto.setEnableEqualSegmentSign(props.enableEqualSegmentSign);
                break;
            case 'equalSegmentSign':
                proto.setEqualSegmentSign(props.equalSegmentSign);
                break;
            case 'showArcLabel':
                proto.setShowArcLabel(props.showArcLabel);
                break;
            case 'arcLabelType':
                proto.setArcLabelType(props.arcLabelType);
                break;
            case 'arcLabelContent':
                proto.setArcLabelContent(props.arcLabelContent);
                break;
            case 'isShowAngleSize':
                proto.setIsShowAngleSize(props.isShowAngleSize);
                break;
            case 'pointLabelType':
                proto.setPointLabelType(props.pointLabelType);
                break;
            case 'pointLabelFreeContent':
                proto.setPointLabelFreeContent(props.pointLabelFreeContent);
                break;
            case 'showPointLabel':
                proto.setShowPointLabel(props.showPointLabel);
                break;
        }
    }

    return proto;
}

export function convertProtoToSettingProperties<T extends GeoRenderProp>(proto: ElRenderPropsProto): T {
    const props: { [key in SettingPropertyType]?: any } = {};

    if (proto.hasColor()) props.color = proto.getColor();
    if (proto.hasLineColor()) props.lineColor = proto.getLineColor();
    if (proto.hasPointColor()) props.pointColor = proto.getPointColor();
    if (proto.hasStrokeStyle()) props.strokeStyle = proto.getStrokeStyle();
    if (proto.hasLineWeight()) props.lineWeight = proto.getLineWeight();
    if (proto.hasHidden()) props.hidden = proto.getHidden();
    if (proto.hasOpacity()) props.opacity = proto.getOpacity();
    if (proto.hasShowLabel()) props.showLabel = proto.getShowLabel();
    if (proto.hasLabelType()) props.labelType = proto.getLabelType();
    if (proto.hasLabel()) props.label = proto.getLabel();
    if (proto.hasSwapLabelPosition()) props.swapLabelPosition = proto.getSwapLabelPosition();
    if (proto.hasSpaceFromArcToCorner()) props.spaceFromArcToCorner = proto.getSpaceFromArcToCorner();
    if (proto.hasShowAngleTypes()) props.showAngleTypes = proto.getShowAngleTypes();
    if (proto.hasAngleArc()) props.angleArc = proto.getAngleArc();
    if (proto.hasEnableEqualSegmentSign()) props.enableEqualSegmentSign = proto.getEnableEqualSegmentSign();
    if (proto.hasEqualSegmentSign()) props.equalSegmentSign = proto.getEqualSegmentSign();
    if (proto.hasShowArcLabel()) props.showArcLabel = proto.getShowArcLabel();
    if (proto.hasArcLabelType()) props.arcLabelType = proto.getArcLabelType();
    if (proto.hasArcLabelContent()) props.arcLabelContent = proto.getArcLabelContent();
    if (proto.hasIsShowAngleSize()) props.isShowAngleSize = proto.getIsShowAngleSize();
    if (proto.hasPointLabelType()) props.pointLabelType = proto.getPointLabelType();
    if (proto.hasPointLabelFreeContent()) props.pointLabelFreeContent = proto.getPointLabelFreeContent();
    if (proto.hasShowPointLabel()) props.showPointLabel = proto.getShowPointLabel();

    return props as T;
}

// --------- VERTEX ---------------------

export function buildRenderVertexProto(vertex: RenderVertex): RenderVertexProto {
    const proto = new RenderVertexProto()
        .setRenderProp(vertex.renderProp ? vertexPP(vertex.renderProp) : undefined)
        .setName(vertex.name)
        .setRelIndex(vertex.relIndex)
        .setCoords(buildCoordsProto(vertex.coords))
        .setFlag(buildRenderElFlag(vertex.usable, vertex.valid, vertex.deleted, vertex.unselectable));

    if (vertex.movementPath) proto.setMovementPath(JSON.stringify(vertex.movementPath));

    return proto;
}

export function convertProtoToRenderVertex(proto: RenderVertexProto): RenderVertex {
    const vertex = new RenderVertex(
        proto.hasRenderProp() ? Object.assign(_pointGeoRenderProp(), proto.getRenderProp().toObject()) : undefined
    );
    vertex.name = proto.getName();
    vertex.coords = convertProtoToCoords(proto.getCoords());
    vertex.relIndex = proto.getRelIndex();
    if (proto.hasMovementPath()) vertex.movementPath = JSON.parse(proto.getMovementPath());
    vertex.usable = proto.getFlag().getUsable();
    vertex.deleted = proto.getFlag().getDeleted();
    vertex.valid = proto.getFlag().getValid();
    vertex.unselectable = proto.getFlag().getUnselectable();

    return vertex;
}

// ------- END VERTEX ------------------

// ------- LINE ------------------------

export function buildLineTypeProto(lineType: GeoRelType): LineTypeProto {
    switch (lineType) {
        case 'RenderLine':
            return LineTypeProto.LINE;
        case 'RenderLineSegment':
            return LineTypeProto.SEGMENT;
        case 'RenderVector':
            return LineTypeProto.VECTOR;
        case 'RenderRay':
            return LineTypeProto.RAY;
        default:
            return LineTypeProto.UNKNOWN;
    }
}

export function buildRenderLineProto(line: RenderLine): RenderLineProto {
    const proto = new RenderLineProto()
        .setRelIndex(line.relIndex)
        .setVertexRelIdxesList(line.vertexRelIdxes)
        .setName(line.name)
        .setLineType(buildLineTypeProto(line.type))
        .setElType(line.elType)
        .setStartPointIdx(line.startPointIdx)
        .setVectorList(line.vector)
        .setFlag(buildRenderElFlag(line.usable, line.valid, line.deleted, line.unselectable))
        .setLength(line.length)
        .setEndPointIdx(line.endPointIdx);

    if (line.renderProp)
        if (line.elType == 'LineSegment') {
            proto.setRenderProp(lineSegmentPP((line as RenderLineSegment).renderProp));
        } else {
            proto.setRenderProp(linePP(line.renderProp));
        }

    return proto;
}

export function buildPreviewLineProto(line: RenderLine): PreviewLineProto {
    const proto = new PreviewLineProto();

    const lineProto = buildRenderLineProto(line);
    proto.setLine(lineProto);

    if (line.pInfo) {
        const pInfo = line.pInfo;

        const refVert = pInfo.refPEl.filter(re => re.type == 'RenderVertex') as RenderVertex[];
        if (refVert.length > 0) proto.setPVertsList(refVert.map(v => buildRenderVertexProto(v)));

        if (pInfo.sVCoords) proto.setSVCoords(buildCoordsProto(pInfo.sVCoords));
        if (pInfo.eVCoords) proto.setEVCoords(buildCoordsProto(pInfo.eVCoords));
    }

    return proto;
}

export function convertProtoToLineType(proto: LineTypeProto): GeoRelType {
    switch (proto) {
        case LineTypeProto.LINE:
            return 'RenderLine';
        case LineTypeProto.VECTOR:
            return 'RenderVector';
        case LineTypeProto.SEGMENT:
            return 'RenderLineSegment';
        case LineTypeProto.RAY:
            return 'RenderRay';
        default:
            return null;
    }
}

function lineFromProp<T extends RenderLine>(
    propProto: ElRenderPropsProto | undefined,
    lineTypeProto: LineTypeProto
): T {
    const lineType = convertProtoToLineType(lineTypeProto);
    const prop = propProto
        ? Object.assign(
              lineType == 'RenderLineSegment' ? _lineSegmentGeoRenderProp() : _lineGeoRenderProp(),
              propProto.toObject()
          )
        : undefined;

    let line!: RenderLine;

    switch (lineType) {
        case 'RenderLineSegment':
            line = new RenderLineSegment(prop as LineSegmentGeoRenderProp | undefined);
            break;
        case 'RenderLine':
            line = new RenderLine(prop as LineGeoRenderProp | undefined);
            break;
        case 'RenderRay':
            line = new RenderRay(prop as LineGeoRenderProp | undefined);
            break;
        case 'RenderVector':
            line = new RenderVector(prop as LineGeoRenderProp | undefined);
            break;
        default:
            throw new Error('Unknown line type when create line from prop');
    }
    return line as T;
}

export function convertProtoToRenderLine<T extends RenderLine>(proto: RenderLineProto): T {
    const line = lineFromProp<RenderLine>(
        proto.hasRenderProp() ? proto.getRenderProp() : undefined,
        proto.getLineType()
    );
    line.relIndex = proto.getRelIndex();
    line.vertexRelIdxes = proto.getVertexRelIdxesList();
    line.name = proto.getName();
    line.startPointIdx = proto.getStartPointIdx();
    line.endPointIdx = proto.hasEndPointIdx() ? proto.getEndPointIdx() : null;
    line.vector = proto.getVectorList();
    line.usable = proto.getFlag().getUsable();
    line.deleted = proto.getFlag().getDeleted();
    line.valid = proto.getFlag().getValid();
    line.unselectable = proto.getFlag().getUnselectable();
    line.length = proto.getLength();
    return line as T;
}

export function convertProtoToPreviewLine<T extends RenderLine>(proto: PreviewLineProto): T {
    const el = convertProtoToRenderLine<T>(proto.getLine());
    const pInfo: PreviewLine = {
        refPEl: [],
    };

    if (proto.getPVertsList().length > 0) pInfo.refPEl = proto.getPVertsList().map(v => convertProtoToRenderVertex(v));

    if (proto.hasSVCoords()) pInfo.sVCoords = convertProtoToCoords(proto.getSVCoords());
    if (proto.hasEVCoords()) pInfo.eVCoords = convertProtoToCoords(proto.getEVCoords());

    el.pInfo = pInfo;

    return el;
}

// ------- END LINE --------------------

// ------- POLYGON ---------------------

export function buildRenderPolygonProto(polygon: RenderPolygon): RenderPolygonProto {
    return new RenderPolygonProto()
        .setRenderProp(polygon.renderProp ? polygonPP(polygon.renderProp) : undefined)
        .setRelIndex(polygon.relIndex)
        .setElType(polygon.elType)
        .setVertexRelIdxesList(polygon.vertexRelIdxes)
        .setLineRelIdxesList(polygon.lineRelIdxes)
        .setName(polygon.name)
        .setFacesList(polygon.faces)
        .setFlag(buildRenderElFlag(polygon.usable, polygon.valid, polygon.deleted, polygon.unselectable))
        .setArea(polygon.area)
        .setPerimeter(polygon.perimeter);
}

export function buildPreviewPolygonProto(polygon: RenderPolygon): PreviewPolygonProto {
    const relProto = buildRenderPolygonProto(polygon);
    const pProto = new PreviewPolygonProto();
    pProto.setPolygon(relProto);

    if (polygon.pInfo) {
        const pInfo = polygon.pInfo;
        const refVert = pInfo.refPEl.filter(re => re.type == 'RenderVertex') as RenderVertex[];
        if (refVert.length > 0) pProto.setPVertsList(refVert.map(v => buildRenderVertexProto(v)));

        const refEdge = pInfo.refPEl.filter(re => isElementLine(re)) as RenderLine[];
        if (refEdge.length > 0) pProto.setPEdgesList(refEdge.map(e => buildPreviewLineProto(e)));

        if (pInfo.verts.size > 0) {
            const map = pProto.getPCoordsMap();
            pInfo.verts.forEach((value, key) => {
                map.set(key, buildCoordsProto(value));
            });
        }
    }
    return pProto;
}

export function convertProtoToRenderPolygon(proto: RenderPolygonProto): RenderPolygon {
    const polygon = new RenderPolygon(
        proto.hasRenderProp() ? Object.assign(_polygonGeoRenderProp(), proto.getRenderProp().toObject()) : undefined
    );
    polygon.relIndex = proto.getRelIndex();
    polygon.vertexRelIdxes = proto.getVertexRelIdxesList();
    polygon.lineRelIdxes = proto.getLineRelIdxesList();
    polygon.name = proto.getName();
    polygon.faces = proto.getFacesList();
    polygon.usable = proto.getFlag().getUsable();
    polygon.deleted = proto.getFlag().getDeleted();
    polygon.valid = proto.getFlag().getValid();
    polygon.unselectable = proto.getFlag().getUnselectable();
    polygon.area = proto.getArea();
    polygon.perimeter = proto.getPerimeter();
    return polygon;
}

export function convertProtoToPreviewPolygon(proto: PreviewPolygonProto): RenderPolygon {
    const polygon = convertProtoToRenderPolygon(proto.getPolygon());
    const pInfo: PreviewPolygon = {
        refPEl: [],
    };

    if (proto.getPVertsList().length > 0) pInfo.refPEl = proto.getPVertsList().map(v => convertProtoToRenderVertex(v));
    if (proto.getPEdgesList().length > 0)
        proto.getPEdgesList().forEach(e => pInfo.refPEl.push(convertProtoToPreviewLine(e)));
    const map = proto.getPCoordsMap();

    if (map.getLength() > 0) {
        pInfo.verts = new Map();
        map.forEach((value, key) => {
            pInfo.verts.set(key, convertProtoToCoords(value));
        });
    }
    polygon.pInfo = pInfo;
    return polygon;
}

// ------- END POLYGON -----------------

// ------- CIRCLE ----------------------

/**
 * NOTE Preview circle is not needed. Use preview circle shape
 */

/**
 *
 * @param circle
 * @returns
 */
export function buildRenderCircleProto(circle: RenderCircle): RenderCircleProto {
    return new RenderCircleProto()
        .setRenderProp(circle.renderProp ? circlePP(circle.renderProp) : undefined)
        .setName(circle.name)
        .setRelIndex(circle.relIndex)
        .setElType(circle.elType)
        .setVertexRelIdxesList(circle.vertexRelIdxes)
        .setCenterPointIdx(circle.centerPointIdx)
        .setRadius(circle.radius)
        .setFlag(buildRenderElFlag(circle.usable, circle.valid, circle.deleted, circle.unselectable))
        .setLength(circle.length);
}

export function buildPreviewCircleProto(circle: RenderCircle): PreviewCircleProto {
    const el = buildRenderCircleProto(circle);
    const proto = new PreviewCircleProto();
    proto.setCircle(el);

    if (circle.pInfo) {
        const pInfo = circle.pInfo;
        const refVert = pInfo.refPEl.filter(re => re.type == 'RenderVertex') as RenderVertex[];
        if (refVert.length > 0) proto.setCVert(buildRenderVertexProto(refVert[0]));
        if (pInfo.cCoords) proto.setCCoords(buildCoordsProto(pInfo.cCoords));
    }

    return proto;
}

export function convertProtoToRenderCircle(proto: RenderCircleProto): RenderCircle {
    const c = new RenderCircle(
        proto.hasRenderProp() ? Object.assign(_circleGeoRenderProp(), proto.getRenderProp().toObject()) : undefined
    );
    c.name = proto.getName();
    c.relIndex = proto.getRelIndex();
    c.vertexRelIdxes = proto.getVertexRelIdxesList();
    c.centerPointIdx = proto.getCenterPointIdx();
    c.radius = proto.getRadius();
    c.usable = proto.getFlag().getUsable();
    c.deleted = proto.getFlag().getDeleted();
    c.valid = proto.getFlag().getValid();
    c.unselectable = proto.getFlag().getUnselectable();
    c.length = proto.getLength();
    return c;
}

export function convertProtoToPreviewCircle(proto: PreviewCircleProto): RenderCircle {
    const el = convertProtoToRenderCircle(proto.getCircle());
    const pInfo: PreviewCircleShape = {
        refPEl: [],
    };

    if (proto.hasCVert()) pInfo.refPEl.push(convertProtoToRenderVertex(proto.getCVert()));
    pInfo.cCoords = proto.getCCoords() && convertProtoToCoords(proto.getCCoords());
    el.pInfo = pInfo;

    return el;
}

// ------- END CIRCLE ------------------

// ------- CIRCLE SHAPE ----------------------

export function buildRenderCircleShapeProto(circle: RenderCircleShape): RenderCircleShapeProto {
    return new RenderCircleShapeProto()
        .setRenderProp(circle.renderProp ? circleShapePP(circle.renderProp) : undefined)
        .setName(circle.name)
        .setRelIndex(circle.relIndex)
        .setVertexRelIdxesList(circle.vertexRelIdxes)
        .setArcIdx(circle.arcRelIdx)
        .setRadius(circle.radius)
        .setCenterPointIdx(circle.centerPointIdx)
        .setFlag(buildRenderElFlag(circle.usable, circle.valid, circle.deleted, circle.unselectable))
        .setArea(circle.area)
        .setPerimeter(circle.perimeter);
}

export function buildPreviewCircleShapeProto(circle: RenderCircleShape): PreviewCircleShapeProto {
    const el = buildRenderCircleShapeProto(circle);
    const proto = new PreviewCircleShapeProto();
    proto.setCircle(el);

    if (circle.pInfo) {
        const pInfo = circle.pInfo;
        const refVert = pInfo.refPEl.filter(re => re.type == 'RenderVertex') as RenderVertex[];
        if (refVert.length > 0) proto.setCVert(buildRenderVertexProto(refVert[0]));

        const refArc = pInfo.refPEl.filter(re => re.type == 'RenderCircle') as RenderCircle[];
        if (refArc.length > 0) proto.setArc(buildRenderCircleProto(refArc[0]));

        if (pInfo.cCoords) proto.setCCoords(buildCoordsProto(pInfo.cCoords));
    }

    return proto;
}

export function convertProtoToRenderCircleShape(proto: RenderCircleShapeProto): RenderCircleShape {
    const circle = new RenderCircleShape(
        proto.getRenderProp() ? Object.assign(_circleShapeGeoRenderProp(), proto.getRenderProp().toObject()) : undefined
    );
    circle.name = proto.getName();
    circle.relIndex = proto.getRelIndex();
    circle.vertexRelIdxes = proto.getVertexRelIdxesList();
    circle.arcRelIdx = proto.getArcIdx();
    circle.radius = proto.getRadius();
    circle.centerPointIdx = proto.getCenterPointIdx();
    circle.usable = proto.getFlag().getUsable();
    circle.deleted = proto.getFlag().getDeleted();
    circle.valid = proto.getFlag().getValid();
    circle.unselectable = proto.getFlag().getUnselectable();
    circle.area = proto.getArea();
    circle.perimeter = proto.getPerimeter();
    return circle;
}

export function convertProtoToPreviewCircleShape(proto: PreviewCircleShapeProto): RenderCircleShape {
    const el = convertProtoToRenderCircleShape(proto.getCircle());
    const pInfo: PreviewCircleShape = {
        refPEl: [],
    };

    if (proto.hasCVert()) pInfo.refPEl.push(convertProtoToRenderVertex(proto.getCVert()));

    if (proto.hasArc()) pInfo.refPEl.push(convertProtoToRenderCircle(proto.getArc()));

    pInfo.cCoords = proto.getCCoords() && convertProtoToCoords(proto.getCCoords());
    el.pInfo = pInfo;

    return el;
}

// ------- END CIRCLE SHAPE ------------

// ------- ELLIPSE ---------------------

export function buildRenderEllipseProto(ellipse: RenderEllipse): RenderEllipseProto {
    const proto = new RenderEllipseProto()
        .setRenderProp(ellipse.renderProp ? ellipsePP(ellipse.renderProp) : undefined)
        .setRelIndex(ellipse.relIndex)
        .setName(ellipse.name)
        .setF1Idx(ellipse.f1Idx)
        .setF2Idx(ellipse.f2Idx)
        .setA(ellipse.a)
        .setB(ellipse.b)
        .setRotate(ellipse.rotate)
        .setFlag(buildRenderElFlag(ellipse.usable, ellipse.valid, ellipse.deleted, ellipse.unselectable));

    // Add center and vector indices for CENTER_VECTORS mode
    if (ellipse.centerIdx !== undefined && ellipse.centerIdx !== -1) {
        proto.setCenterIdx(ellipse.centerIdx);
    }
    if (ellipse.vaIdx !== undefined && ellipse.vaIdx !== -1) {
        proto.setVaIdx(ellipse.vaIdx);
    }
    if (ellipse.vbIdx !== undefined && ellipse.vbIdx !== -1) {
        proto.setVbIdx(ellipse.vbIdx);
    }

    return proto;
}

export function buildPreviewEllipseProto(ellipse: RenderEllipse): PreviewEllipseProto {
    const el = buildRenderEllipseProto(ellipse);
    const proto = new PreviewEllipseProto();
    proto.setEllipse(el);

    if (ellipse.pInfo) {
        const pInfo = ellipse.pInfo;
        const refVert = pInfo.refPEl.filter(re => re.type == 'RenderVertex') as RenderVertex[];
        if (refVert.length >= 1) proto.setF1Vert(buildRenderVertexProto(refVert[0]));
        if (refVert.length > 1) proto.setF2Vert(buildRenderVertexProto(refVert[1]));

        if (pInfo.f1) proto.setF1Coords(buildCoordsProto(pInfo.f1));
        if (pInfo.f2) proto.setF2Coords(buildCoordsProto(pInfo.f2));
        if (pInfo.vaCoords) proto.setVaCoords(buildCoordsProto(pInfo.vaCoords));
        if (pInfo.vbCoords) proto.setVbCoords(buildCoordsProto(pInfo.vbCoords));
        if (pInfo.center) proto.setCenterCoords(buildCoordsProto(pInfo.center));
    }

    return proto;
}

export function convertProtoToRenderEllipse(proto: RenderEllipseProto): RenderEllipse {
    const ellipse = new RenderEllipse(
        proto.hasRenderProp() ? Object.assign(_ellipseGeoRenderProp(), proto.getRenderProp().toObject()) : undefined
    );
    ellipse.name = proto.getName();
    ellipse.relIndex = proto.getRelIndex();
    ellipse.f1Idx = proto.getF1Idx();
    ellipse.f2Idx = proto.getF2Idx();
    ellipse.a = proto.getA();
    ellipse.b = proto.getB();
    ellipse.rotate = proto.getRotate();
    ellipse.usable = proto.getFlag().getUsable();
    ellipse.deleted = proto.getFlag().getDeleted();
    ellipse.valid = proto.getFlag().getValid();
    ellipse.unselectable = proto.getFlag().getUnselectable();
    ellipse.length = proto.getLength();

    // Add center and vector indices for CENTER_VECTORS mode
    if (proto.hasCenterIdx && proto.hasCenterIdx()) {
        ellipse.centerIdx = proto.getCenterIdx();
    }
    if (proto.hasVaIdx && proto.hasVaIdx()) {
        ellipse.vaIdx = proto.getVaIdx();
    }
    if (proto.hasVbIdx && proto.hasVbIdx()) {
        ellipse.vbIdx = proto.getVbIdx();
    }

    return ellipse;
}

export function convertProtoToPreviewEllipse(proto: PreviewEllipseProto): RenderEllipse {
    const el = convertProtoToRenderEllipse(proto.getEllipse());
    const pInfo: PreviewEllipseShape = {
        refPEl: [],
    };

    if (proto.hasF1Vert()) pInfo.refPEl.push(convertProtoToRenderVertex(proto.getF1Vert()));
    if (proto.hasF2Vert()) pInfo.refPEl.push(convertProtoToRenderVertex(proto.getF2Vert()));

    if (proto.hasF1Coords()) pInfo.f1 = convertProtoToCoords(proto.getF1Coords());
    if (proto.hasF2Coords()) pInfo.f2 = convertProtoToCoords(proto.getF2Coords());
    if (proto.hasVaCoords()) pInfo.vaCoords = convertProtoToCoords(proto.getVaCoords());
    if (proto.hasVbCoords()) pInfo.vbCoords = convertProtoToCoords(proto.getVbCoords());
    if (proto.hasCenterCoords()) pInfo.center = convertProtoToCoords(proto.getCenterCoords());

    el.pInfo = pInfo;
    return el;
}

// ------- END ELLIPSE -----------------

// ------- ELLIPSE SHAPE ---------------

export function buildRenderEllipseShapeProto(ellipse: RenderEllipseShape): RenderEllipseShapeProto {
    const proto = new RenderEllipseShapeProto()
        .setRenderProp(ellipse.renderProp ? ellipseShapePP(ellipse.renderProp) : undefined)
        .setRelIndex(ellipse.relIndex)
        .setArcIdx(ellipse.arcRelIdx)
        .setName(ellipse.name)
        .setF1Idx(ellipse.f1Idx)
        .setF2Idx(ellipse.f2Idx)
        .setA(ellipse.a)
        .setB(ellipse.b)
        .setRotate(ellipse.rotate)
        .setFlag(buildRenderElFlag(ellipse.usable, ellipse.valid, ellipse.deleted, ellipse.unselectable))
        .setArea(ellipse.area)
        .setPerimeter(ellipse.perimeter);

    // Add center and vector indices for CENTER_VECTORS mode
    if (ellipse.centerIdx !== undefined && ellipse.centerIdx !== -1) {
        proto.setCenterIdx(ellipse.centerIdx);
    }
    if (ellipse.vaIdx !== undefined && ellipse.vaIdx !== -1) {
        proto.setVaIdx(ellipse.vaIdx);
    }
    if (ellipse.vbIdx !== undefined && ellipse.vbIdx !== -1) {
        proto.setVbIdx(ellipse.vbIdx);
    }

    return proto;
}

export function convertProtoToRenderEllipseShape(proto: RenderEllipseShapeProto): RenderEllipseShape {
    const ellipse = new RenderEllipseShape(
        proto.hasRenderProp()
            ? Object.assign(_ellipseShapeGeoRenderProp(), proto.getRenderProp().toObject())
            : undefined
    );
    ellipse.name = proto.getName();
    ellipse.relIndex = proto.getRelIndex();
    ellipse.arcRelIdx = proto.getArcIdx();
    ellipse.f1Idx = proto.getF1Idx();
    ellipse.f2Idx = proto.getF2Idx();
    ellipse.a = proto.getA();
    ellipse.b = proto.getB();
    ellipse.rotate = proto.getRotate();
    ellipse.usable = proto.getFlag().getUsable();
    ellipse.deleted = proto.getFlag().getDeleted();
    ellipse.valid = proto.getFlag().getValid();
    ellipse.unselectable = proto.getFlag().getUnselectable();
    ellipse.area = proto.getArea();
    ellipse.perimeter = proto.getPerimeter();

    // Add center and vector indices for CENTER_VECTORS mode
    if (proto.hasCenterIdx && proto.hasCenterIdx()) {
        ellipse.centerIdx = proto.getCenterIdx();
    }
    if (proto.hasVaIdx && proto.hasVaIdx()) {
        ellipse.vaIdx = proto.getVaIdx();
    }
    if (proto.hasVbIdx && proto.hasVbIdx()) {
        ellipse.vbIdx = proto.getVbIdx();
    }

    return ellipse;
}

export function buildPreviewEllipseShapeProto(ellipse: RenderEllipseShape): PreviewEllipseShapeProto {
    const elProto = buildRenderEllipseShapeProto(ellipse);
    const proto = new PreviewEllipseShapeProto();

    if (ellipse.pInfo) {
        const pInfo = ellipse.pInfo;
        const refVert = pInfo.refPEl.filter(re => re.type == 'RenderVertex') as RenderVertex[];
        if (refVert.length >= 1) proto.setF1Vert(buildRenderVertexProto(refVert[0]));
        if (refVert.length > 1) proto.setF2Vert(buildRenderVertexProto(refVert[1]));

        const arc = pInfo.refPEl.filter(re => re.type == 'RenderEllipse') as RenderEllipse[];
        if (arc.length > 0) proto.setArc(buildRenderEllipseProto(arc[0]));

        if (pInfo.f1) proto.setF1Coords(buildCoordsProto(pInfo.f1));
        if (pInfo.f2) proto.setF2Coords(buildCoordsProto(pInfo.f2));
        if (pInfo.vaCoords) proto.setVaCoords(buildCoordsProto(pInfo.vaCoords));
        if (pInfo.vbCoords) proto.setVbCoords(buildCoordsProto(pInfo.vbCoords));
        if (pInfo.center) proto.setCenterCoords(buildCoordsProto(pInfo.center));
    }

    proto.setEllipse(elProto);

    return proto;
}

export function convertProtoToPreviewEllipseShape(proto: PreviewEllipseShapeProto): RenderEllipseShape {
    const el = convertProtoToRenderEllipseShape(proto.getEllipse());
    const pInfo: PreviewEllipseShape = {
        refPEl: [],
    };

    if (proto.hasF1Vert()) pInfo.refPEl.push(convertProtoToRenderVertex(proto.getF1Vert()));
    if (proto.hasF2Vert()) pInfo.refPEl.push(convertProtoToRenderVertex(proto.getF2Vert()));

    if (proto.hasF1Coords()) pInfo.f1 = convertProtoToCoords(proto.getF1Coords());
    if (proto.hasF2Coords()) pInfo.f2 = convertProtoToCoords(proto.getF2Coords());
    if (proto.hasVaCoords()) pInfo.vaCoords = convertProtoToCoords(proto.getVaCoords());
    if (proto.hasVbCoords()) pInfo.vbCoords = convertProtoToCoords(proto.getVbCoords());
    if (proto.hasCenterCoords()) pInfo.center = convertProtoToCoords(proto.getCenterCoords());

    if (proto.hasArc()) pInfo.refPEl.push(convertProtoToRenderEllipse(proto.getArc()));
    el.pInfo = pInfo;

    return el;
}

// ------- END ELLIPSE SHAPE -----------

// ------- SECTOR ----------------------

export function buildRenderSectorProto(arc: RenderSector): RenderSectorProto {
    return new RenderSectorProto()
        .setRenderProp(arc.renderProp ? sectorPP(arc.renderProp) : undefined)
        .setName(arc.name)
        .setRelIndex(arc.relIndex)
        .setElType(arc.elType)
        .setVertexRelIdxesList(arc.vertexRelIdxes)
        .setCenterPointIdx(arc.centerPointIdx)
        .setStartPointIdx(arc.startPointIdx)
        .setEndPointIdx(arc.endPointIdx)
        .setRadius(arc.radius)
        .setFlag(buildRenderElFlag(arc.usable, arc.valid, arc.deleted, arc.unselectable))
        .setLength(arc.length);
}

export function convertProtoToRenderSector(proto: RenderSectorProto): RenderSector {
    const arc = new RenderSector(
        proto.hasRenderProp() ? Object.assign(_sectorGeoRenderProp(), proto.getRenderProp().toObject()) : undefined
    );
    arc.name = proto.getName();
    arc.relIndex = proto.getRelIndex();
    arc.vertexRelIdxes = proto.getVertexRelIdxesList();
    arc.centerPointIdx = proto.getCenterPointIdx();
    arc.startPointIdx = proto.getStartPointIdx();
    arc.endPointIdx = proto.getEndPointIdx();
    arc.radius = proto.getRadius();
    arc.usable = proto.getFlag().getUsable();
    arc.deleted = proto.getFlag().getDeleted();
    arc.valid = proto.getFlag().getValid();
    arc.unselectable = proto.getFlag().getUnselectable();
    arc.length = proto.getLength();
    return arc;
}

// ------- END SECTOR ------------------

// ------- SECTOR SHAPE ----------------

export function buildRenderSectorShapeProto(sshape: RenderSectorShape): RenderSectorShapeProto {
    return new RenderSectorShapeProto()
        .setRenderProp(sshape.renderProp ? sectorShapePP(sshape.renderProp) : undefined)
        .setName(sshape.name)
        .setRelIndex(sshape.relIndex)
        .setElType(sshape.elType)
        .setVertexRelIdxesList(sshape.vertexRelIdxes)
        .setLineRelIdxesList(sshape.lineRelIdxes)
        .setArcIdx(sshape.arcRelIdx)
        .setCenterPointIdx(sshape.centerPointIdx)
        .setStartPointIdx(sshape.startPointIdx)
        .setEndPointIdx(sshape.endPointIdx)
        .setRadius(sshape.radius)
        .setFlag(buildRenderElFlag(sshape.usable, sshape.valid, sshape.deleted, sshape.unselectable))
        .setArea(sshape.area)
        .setLength(sshape.length);
}

export function buildPreviewSectorShapeProto(circularSector: RenderSectorShape): PreviewSectorShapeProto {
    const el = buildRenderSectorShapeProto(circularSector);
    const proto = new PreviewSectorShapeProto();
    proto.setSector(el);

    if (circularSector.pInfo) {
        const pInfo = circularSector.pInfo;
        const refVert = pInfo.refPEl.filter(r => r.type == 'RenderVertex') as RenderVertex[];
        const refSect = pInfo.refPEl.filter(r => r.type == 'RenderSector') as RenderSector[];
        proto.setPVertsList(refVert.map(v => buildRenderVertexProto(v)));
        if (refSect.length > 0) proto.setArc(buildRenderSectorProto(refSect[0]));

        if (pInfo.cCoords) proto.setCCoords(buildCoordsProto(pInfo.cCoords));
        if (pInfo.sCoords) proto.setSCoords(buildCoordsProto(pInfo.sCoords));
        if (pInfo.eCoords) proto.setECoords(buildCoordsProto(pInfo.eCoords));
    }

    return proto;
}

export function convertProtoToRenderSectorShape(proto: RenderSectorShapeProto): RenderSectorShape {
    const sector = new RenderSectorShape(
        proto.hasRenderProp() ? Object.assign(_sectorShapeGeoRenderProp(), proto.getRenderProp().toObject()) : undefined
    );
    sector.name = proto.getName();
    sector.relIndex = proto.getRelIndex();
    sector.vertexRelIdxes = proto.getVertexRelIdxesList();
    sector.lineRelIdxes = proto.getLineRelIdxesList();
    sector.arcRelIdx = proto.getArcIdx();
    sector.centerPointIdx = proto.getCenterPointIdx();
    sector.startPointIdx = proto.getStartPointIdx();
    sector.endPointIdx = proto.getEndPointIdx();
    sector.radius = proto.getRadius();
    sector.usable = proto.getFlag().getUsable();
    sector.deleted = proto.getFlag().getDeleted();
    sector.valid = proto.getFlag().getValid();
    sector.unselectable = proto.getFlag().getUnselectable();
    sector.area = proto.getArea();
    sector.length = proto.getLength();
    return sector;
}

export function convertProtoToPreviewSectorShape(proto: PreviewSectorShapeProto): RenderSectorShape {
    const el = convertProtoToRenderSectorShape(proto.getSector());
    const pInfo: PreviewSector = {
        refPEl: [],
    };
    el.pInfo = pInfo;

    if (proto.getPVertsList().length > 0) pInfo.refPEl = proto.getPVertsList().map(v => convertProtoToRenderVertex(v));
    if (proto.getArc()) pInfo.refPEl.push(convertProtoToRenderSector(proto.getArc()));

    if (proto.hasCCoords()) pInfo.cCoords = convertProtoToCoords(proto.getCCoords());
    if (proto.hasSCoords()) pInfo.sCoords = convertProtoToCoords(proto.getSCoords());
    if (proto.hasECoords()) pInfo.eCoords = convertProtoToCoords(proto.getECoords());

    return el;
}

// ------- END SECTOR SHAPE ------------

// ------- ANGLE -----------------------

export function buildRenderAngleProto(angle: RenderAngle): RenderAngleProto {
    return new RenderAngleProto()
        .setRenderProp(angle.renderProp ? anglePP(angle.renderProp) : undefined)
        .setName(angle.name)
        .setRelIndex(angle.relIndex)
        .setVertexRelIdxesList(angle.vertexRelIdxes)
        .setLineRelIdxesList(angle.lineRelIdxes)
        .setAnglePointIdx(angle.anglePointIdx)
        .setStartVIdx(angle.startVIdx)
        .setStartVDir(angle.startVDir)
        .setEndVIdx(angle.endVIdx)
        .setEndVDir(angle.endVDir)
        .setFlag(buildRenderElFlag(angle.usable, angle.valid, angle.deleted, angle.unselectable))
        .setDegree(angle.degree);
}

export function buildPreviewAngleProto(angle: RenderAngle): PreviewAngleProto {
    const proto = new PreviewAngleProto().setAngle(buildRenderAngleProto(angle));

    if (angle.pInfo) {
        const pInfo = angle.pInfo;
        if (pInfo.cCoords) proto.setCVertCoords(buildCoordsProto(pInfo.cCoords));
        if (pInfo.startV) proto.setStartV(buildCoordsProto(pInfo.startV));
        if (pInfo.endV) proto.setEndV(buildCoordsProto(pInfo.endV));

        const refVert = pInfo.refPEl.filter(re => re.type == 'RenderVertex') as RenderVertex[];
        if (refVert.length > 0) proto.setCVert(buildRenderVertexProto(refVert[0]));

        const refLines = pInfo.refPEl.filter(re => isElementLine(re)) as RenderLine[];
        if (refLines.length > 0) proto.setPLinesList(refLines.map(l => buildRenderLineProto(l)));
    }

    return proto;
}

export function convertProtoToRenderAngle(proto: RenderAngleProto): RenderAngle {
    const angle = new RenderAngle(
        Object.assign(
            buildPreviewAngleRenderProp(),
            proto.hasRenderProp() ? proto.getRenderProp().toObject() : undefined
        )
    );
    angle.name = proto.getName();
    angle.relIndex = proto.getRelIndex();
    angle.vertexRelIdxes = proto.getVertexRelIdxesList();
    angle.lineRelIdxes = proto.getLineRelIdxesList();
    angle.anglePointIdx = proto.getAnglePointIdx();
    angle.startVIdx = proto.getStartVIdx();
    angle.endVIdx = proto.getEndVIdx();
    angle.startVDir = proto.getStartVDir();
    angle.endVDir = proto.getEndVDir();
    angle.usable = proto.getFlag().getUsable();
    angle.deleted = proto.getFlag().getDeleted();
    angle.valid = proto.getFlag().getValid();
    angle.unselectable = proto.getFlag().getUnselectable();
    angle.degree = proto.getDegree();
    return angle;
}

export function convertProtoToPreviewAngle(proto: PreviewAngleProto): RenderAngle {
    const angle = convertProtoToRenderAngle(proto.getAngle());
    const pInfo: PreviewAngle = {
        refPEl: [],
    };
    angle.pInfo = pInfo;

    if (proto.hasCVert()) pInfo.refPEl.push(convertProtoToRenderVertex(proto.getCVert()));
    if (proto.hasCVertCoords()) pInfo.cCoords = convertProtoToCoords(proto.getCVertCoords());
    if (proto.hasStartV()) pInfo.startV = convertProtoToCoords(proto.getStartV());
    if (proto.hasEndV()) pInfo.endV = convertProtoToCoords(proto.getEndV());

    if (proto.getPLinesList().length > 0)
        proto.getPLinesList().forEach(l => pInfo.refPEl.push(convertProtoToRenderLine(l)));

    return angle;
}

// ------- END ANGLE -------------------

export function convertProtoToDocRenderProp(proto: DocRenderPropProto): DocRenderProp {
    const docRenderProp = <DocRenderProp>{};

    if (proto.hasScreenUnit()) docRenderProp.screenUnit = proto.getScreenUnit();
    if (proto.hasCanvasWidth()) docRenderProp.canvasWidth = proto.getCanvasWidth();
    if (proto.hasCanvasHeight()) docRenderProp.canvasHeight = proto.getCanvasHeight();
    if (proto.hasScale()) docRenderProp.scale = proto.getScale();
    if (proto.getTranslationList().length) docRenderProp.translation = proto.getTranslationList();
    else docRenderProp.translation = [0, 0, 0];
    if (proto.getRotationList().length) docRenderProp.rotation = proto.getRotationList();
    else docRenderProp.rotation = [0, 0, 0];
    if (proto.hasValid()) docRenderProp.valid = proto.getValid();
    if (proto.hasShadow()) docRenderProp.shadow = proto.getShadow();
    if (proto.hasShadowStyle()) docRenderProp.shadowStyle = proto.getShadowStyle();
    if (proto.hasAxis()) docRenderProp.axis = proto.getAxis();
    if (proto.hasGrid()) docRenderProp.grid = proto.getGrid();
    if (proto.hasDetailGrid()) docRenderProp.detailGrid = proto.getDetailGrid();
    if (proto.hasBorder()) docRenderProp.border = proto.getBorder();
    if (proto.hasBorderStyle()) docRenderProp.borderStyle = proto.getBorderStyle();
    if (proto.hasBorderColor()) docRenderProp.borderColor = proto.getBorderColor();
    if (proto.hasSnapMode()) docRenderProp.snapMode = proto.getSnapMode();
    if (proto.hasSnapToExistingPoints()) docRenderProp.snapToExistingPoints = proto.getSnapToExistingPoints();
    if (proto.hasSnapToGrid()) docRenderProp.snapToGrid = proto.getSnapToGrid();
    if (proto.hasNamingMode()) docRenderProp.namingMode = proto.getNamingMode();

    return docRenderProp;
}

export function convertDocRenderPropToProto(docRenderProp: DocRenderProp): DocRenderPropProto {
    const proto = new DocRenderPropProto();

    if (docRenderProp.screenUnit !== undefined) proto.setScreenUnit(docRenderProp.screenUnit);
    if (docRenderProp.canvasWidth !== undefined) proto.setCanvasWidth(docRenderProp.canvasWidth);
    if (docRenderProp.canvasHeight !== undefined) proto.setCanvasHeight(docRenderProp.canvasHeight);
    if (docRenderProp.scale !== undefined) proto.setScale(docRenderProp.scale);
    if (docRenderProp.translation !== undefined) proto.setTranslationList(docRenderProp.translation);
    if (docRenderProp.rotation !== undefined) proto.setRotationList(docRenderProp.rotation);
    if (docRenderProp.valid !== undefined) proto.setValid(docRenderProp.valid);
    if (docRenderProp.shadow !== undefined) proto.setShadow(docRenderProp.shadow);
    if (docRenderProp.shadowStyle !== undefined) proto.setShadowStyle(docRenderProp.shadowStyle);
    if (docRenderProp.axis !== undefined) proto.setAxis(docRenderProp.axis);
    if (docRenderProp.grid !== undefined) proto.setGrid(docRenderProp.grid);
    if (docRenderProp.detailGrid !== undefined) proto.setDetailGrid(docRenderProp.detailGrid);
    if (docRenderProp.border !== undefined) proto.setBorder(docRenderProp.border);
    if (docRenderProp.borderStyle !== undefined) proto.setBorderStyle(docRenderProp.borderStyle);
    if (docRenderProp.borderColor !== undefined) proto.setBorderColor(docRenderProp.borderColor);
    if (docRenderProp.snapMode !== undefined) proto.setSnapMode(docRenderProp.snapMode);
    if (docRenderProp.snapToExistingPoints !== undefined)
        proto.setSnapToExistingPoints(docRenderProp.snapToExistingPoints);
    if (docRenderProp.snapToGrid !== undefined) proto.setSnapToGrid(docRenderProp.snapToGrid);
    if (docRenderProp.namingMode !== undefined) proto.setNamingMode(docRenderProp.namingMode);

    return proto;
}

export function convertProtoToDocDefaultElRenderProps(proto: DocDefaultElRenderPropsProto): DefaultGeoRenderProp {
    const dderp = new DefaultGeoRenderProp(); // docDefaultElRenderProps
    if (proto.hasColor()) dderp.color = proto.getColor();
    if (proto.hasPointColor()) dderp.pointColor = proto.getPointColor();
    if (proto.hasLineColor()) dderp.lineColor = proto.getLineColor();
    if (proto.hasStrokeStyle()) dderp.strokeStyle = proto.getStrokeStyle() as GeoStrokeStyle;
    if (proto.hasLineWeight()) dderp.lineWeight = proto.getLineWeight();
    if (proto.hasOpacity()) dderp.opacity = proto.getOpacity();
    if (proto.hasSpaceFromArcToCorner()) dderp.spaceFromArcToCorner = proto.getSpaceFromArcToCorner();
    return dderp;
}

export function convertDocDefaultElRenderPropsToProto(
    docDefaultElRenderProps: DefaultGeoRenderProp
): DocDefaultElRenderPropsProto {
    const proto = new DocDefaultElRenderPropsProto();
    if (docDefaultElRenderProps.pointColor !== undefined) proto.setPointColor(docDefaultElRenderProps.pointColor);
    if (docDefaultElRenderProps.color !== undefined) proto.setColor(docDefaultElRenderProps.color);
    if (docDefaultElRenderProps.lineColor !== undefined) proto.setLineColor(docDefaultElRenderProps.lineColor);
    if (docDefaultElRenderProps.strokeStyle !== undefined)
        proto.setStrokeStyle(docDefaultElRenderProps.strokeStyle as GeoStrokeStyle);
    if (docDefaultElRenderProps.lineWeight !== undefined) proto.setLineWeight(docDefaultElRenderProps.lineWeight);
    if (docDefaultElRenderProps.opacity !== undefined) proto.setOpacity(docDefaultElRenderProps.opacity);
    if (docDefaultElRenderProps.spaceFromArcToCorner !== undefined)
        proto.setSpaceFromArcToCorner(docDefaultElRenderProps.spaceFromArcToCorner);
    return proto;
}

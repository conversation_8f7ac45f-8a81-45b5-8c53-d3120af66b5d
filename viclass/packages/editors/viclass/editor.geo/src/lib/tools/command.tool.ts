import {
    buildDocumentAwarenessCmdOption,
    ChangeToolEventData,
    DocumentId,
    ErrorHandlerDecorator,
    FEATURE_AWARENESS,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { syncEndPreviewModeCommand, syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryToolBar } from '../geo.toolbar';
import {
    ApplyConstructionResponse,
    ConstraintTemplateData,
    GeoElConstructionRequest,
    GeoRenderElement,
    InputCommandToolState,
    NOT_SET_VALUE,
    ParamSpecs,
    ParamStore,
    ParamStoreArray,
    ParamStoreValue,
    RenderVertex,
} from '../model';
import {
    GeometryToolType,
    GeoObjectType,
    GeoPointerEvent,
    GeoToolEventData,
    ParamKind,
    UserConstraintInputData,
    UserConstraintSelection,
    UserParamInput,
} from '../model/geo.models';
import { PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    getFocusDocCtrl,
    isLowercaseNamingElementTypes,
    isValidIdx,
    pickPointName,
    pickShapeName,
    requestElementNames,
} from './util.tool';

/**
 * Represents a tool for handling input commands to create geometric objects
 * based on user-defined constraints.
 */
export class InputCommandTool extends GeometryTool<InputCommandToolState> {
    /**
     * Stores the user's current selection of constraints.
     */
    private userConstraintSelection: UserConstraintSelection;

    /**
     * The type of the geometry tool.
     */
    readonly toolType = 'InputCommandTool';

    /**
     * A BehaviorSubject that indicates whether the tool is currently fetching constraint templates.
     * Subscribers will receive `true` when fetching starts and `false` when it completes or errors.
     */
    readonly isFetchingTemplates$ = new BehaviorSubject<boolean>(false);

    /**
     * Preview queue for managing preview operations.
     */
    private previewQueue = new PreviewQueue();

    /**
     * Resets the state of the tool, primarily by canceling any active constraints.
     */
    override resetState() {
        this.cancelConstraints();
    }

    /**
     * Handles mouse events. This tool currently does not process mouse events directly for drawing.
     * @param event The GeoPointerEvent to handle.
     * @returns The original event, as it's not modified.
     */
    override handleMouseEvent(event: GeoPointerEvent): GeoPointerEvent {
        // do nothing
        return event;
    }

    /**
     * Retrieves the current user constraint selection.
     * @returns The current UserConstraintSelection object, or undefined if none exists.
     */
    getUserConstraintSelection(): UserConstraintSelection {
        return this.userConstraintSelection;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async fetchTemplate(
        objType: GeoObjectType,
        keyword: string,
        lang?: string
    ): Promise<ConstraintTemplateData[]> {
        /**
         * Fetches constraint templates from the geometry gateway based on the object type and keyword.
         * @param objType The type of geometric object for which to fetch templates.
         * @param keyword A keyword to filter the templates.
         * @param lang The language for the templates (optional).
         * @returns A promise that resolves to an array of ConstraintTemplateData.
         */
        this.isFetchingTemplates$.next(true);

        try {
            const selectedTemplates =
                this.userConstraintSelection?.selectedConstraints?.map(sc => ({
                    template: sc.template,
                    possibleConstructors: sc.possibleConstructors,
                })) ?? [];
            const templates = await this.editor.geoGateway.fetchTpl(objType, keyword, selectedTemplates, lang);
            const result: ConstraintTemplateData[] = [...templates.values()].map(e => ({
                template: e[0].tplString,
                possibleConstraints: e,
            }));

            this.isFetchingTemplates$.next(false);
            return result;
        } catch (e: any) {
            this.isFetchingTemplates$.next(false);
            throw e;
        }
    }

    /**
     * Retrieves the names of all valid, usable, and non-deleted points in the currently active document.
     * @returns An array of point names.
     */
    currentPointNames(): string[] {
        const activeVP = this.toolbar.viewport;
        if (!activeVP) return [];

        const docCtrl = getFocusDocCtrl(this.editor, activeVP.id);
        if (!docCtrl) return [];

        return docCtrl.rendererCtrl.usableElements.filter(e => e.type === 'RenderVertex').map(e => e.name);
    }

    /**
     * Called when a user completes the input for a constraint.
     * Adds the completed constraint to the `userConstraintSelection`.
     * @param userInput The data for the completed user constraint.
     */
    onCompleteConstraint(userInput: UserConstraintInputData) {
        if (!this.userConstraintSelection) this.userConstraintSelection = new UserConstraintSelection();
        this.userConstraintSelection.selectedConstraints.push(userInput);
    }

    /**
     * Removes a constraint from the `userConstraintSelection` at the specified index.
     * @param index The index of the constraint to remove.
     */
    removeConstraint(index: number) {
        if (!this.userConstraintSelection || !isValidIdx(index)) return;

        this.userConstraintSelection.selectedConstraints.splice(index, 1);
    }

    /**
     * Submits the collected constraints to the geometry gateway to construct the geometric object.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    async submitConstraints() {
        const activeVP = this.toolbar.viewport;
        if (!this.userConstraintSelection) {
            console.error('No constraints selected');
            return;
        }

        const docCtrl = getFocusDocCtrl(this.editor, activeVP.id);
        const docGlobalId = docCtrl.state.globalId;

        if (!docGlobalId) {
            console.error('not found geo doc global id in viewport ', activeVP.id);
            return;
        }

        const toolState = this.toolState;
        // Number of constraints that the user has filled in.
        const numConstraintsFilled = this.userConstraintSelection.selectedConstraints.length;

        // group by constructor id and constraint group id
        const mapByCtrId = this.userConstraintSelection.selectedConstraints.reduce((map, el) => {
            for (const ctm of el.possibleConstructors)
                if (ctm.numConstraint <= numConstraintsFilled) {
                    const inputs = map.get(
                        JSON.stringify({
                            constructorId: ctm.constructorId,
                            cgId: ctm.cgId,
                        })
                    );
                    if (inputs) inputs.push(el);
                    else
                        map.set(
                            JSON.stringify({
                                constructorId: ctm.constructorId,
                                cgId: ctm.cgId,
                            }),
                            [el]
                        );
                }
            return map;
        }, new Map<string, UserConstraintInputData[]>());

        // Find the constructor that has the most matching constraints.
        const selection = [...mapByCtrId.entries()].reduce((pre, cur) => (pre[1].length >= cur[1].length ? pre : cur));
        const selectionKey: { constructorId: string; cgId: string } = JSON.parse(selection[0]);

        const construction = new GeoElConstructionRequest(
            selectionKey.constructorId,
            toolState.objType,
            selectionKey.cgId
        );

        construction.name = toolState.elName;

        // Build the parameter specifications for the construction request.
        construction.paramSpecs = selection[1].reduce((specs, userInput) => {
            if (selection[1].length === 1 && userInput.template.trim().length === 0) return specs;

            const constraintModel = userInput.possibleConstructors.find(
                m => m.constructorId === selectionKey.constructorId && m.cgId === selectionKey.cgId
            );

            if (!constraintModel) {
                throw new Error(`Missing constraint model for constructor ${selectionKey.constructorId}`);
            }

            // Group parameter inputs by their kind (e.g., 'name', 'value').
            const paramsMap = userInput.inputs.reduce((map, paramInput) => {
                const paramInputs = map.get(ParamKind[paramInput.paramKind]);
                if (paramInputs) paramInputs.push(paramInput);
                else map.set(ParamKind[paramInput.paramKind], [paramInput]);
                return map;
            }, new Map<string, UserParamInput[]>());

            // Convert the grouped parameter inputs into ParamStore objects.
            const params = [...paramsMap.entries()].reduce((map, inputs) => {
                if (
                    inputs[1].length === 1 &&
                    inputs[1][0].input.split(',').length > 1 &&
                    !isJsonObjectString(inputs[1][0].input) // not a JSON object because it's the expression in MathJSON
                ) {
                    const arr = inputs[1][0].input.split(',');
                    if (arr.some(input => !input.trim())) {
                        throw new Error(`Missing required parameter value for ${inputs[0]}`);
                    }
                    map.set(inputs[0], new ParamStoreArray(arr));
                } else if (inputs[1].length > 1) {
                    const arr = inputs[1].map(pi => pi.input);
                    if (arr.some(input => !input.trim())) {
                        throw new Error(`Missing required parameter value for ${inputs[0]}`);
                    }
                    map.set(inputs[0], new ParamStoreArray(arr));
                } else {
                    const input = inputs[1][0].input;
                    if (!input.trim()) {
                        throw new Error(`Missing required parameter value for ${inputs[0]}`);
                    }
                    map.set(inputs[0], new ParamStoreValue(input));
                }
                return map;
            }, new Map<string, ParamStore>());

            specs.push({
                paramDefId: constraintModel.paramDefId,
                indexInCG: constraintModel.indexInCG,
                optional: constraintModel.optional,
                tplStrLangId: constraintModel.tplStringId,
                params: Object.fromEntries(params),
            } as ParamSpecs);
            return specs;
        }, new Array<ParamSpecs>());

        await this.handleConstruct(docCtrl, construction, docGlobalId);
    }

    /**
     * Sends a construction request to the geometry gateway.
     * @param docGlobalId The global ID of the document.
     * @param construction The construction request data.
     * @param preview Whether this is a preview construction. Defaults to `false`.
     * @returns A promise that resolves to the ApplyConstructionResponse.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async construct(
        docGlobalId: DocumentId,
        construction: GeoElConstructionRequest,
        preview = false
    ): Promise<ApplyConstructionResponse> {
        return await constructExec(() =>
            this.editor.geoGateway.construct(
                docGlobalId,
                [
                    {
                        construction: construction,
                    },
                ],
                preview
            )
        );
    }

    /**
     * Processes preview elements by updating their references and displaying them.
     * This function performs the following steps:
     * 1. Clear existing preview
     * 2. Create mapping for relIndex to avoid conflicts
     * 3. Update all references in elements
     * 4. Categorize and add to preview queue
     * 5. Display all elements
     *
     * @param resOfPreview The preview elements to process
     * @param docCtrl The document controller
     * @returns Object containing processed vertices and shapes
     */
    private async processPreviewElements(
        resOfPreview: GeoRenderElement[],
        docCtrl: GeoDocCtrl
    ): Promise<{ vertices: GeoRenderElement[]; shapes: GeoRenderElement[] }> {
        // Step 1: Clear existing preview to prepare for new preview
        docCtrl.rendererCtrl.clearPreview();

        // Initialize arrays to categorize elements
        const vertices: GeoRenderElement[] = [];
        const shapes: GeoRenderElement[] = [];

        // Step 2: Create mapping from old relIndex to new relIndex
        // Use negative numbers multiplied by 1000 to avoid conflicts with existing elements
        const relIndexMapping = new Map<number, number>();
        for (const renderElement of resOfPreview) {
            const oldRelIndex = renderElement.relIndex;
            const newRelIndex = -oldRelIndex * 1000; // Create negative ID to distinguish from real elements
            relIndexMapping.set(oldRelIndex, newRelIndex);
        }

        // Step 3: Process each element and update all references
        for (const renderElement of resOfPreview) {
            // Update relIndex of current element
            const oldRelIndex = renderElement.relIndex;
            renderElement.relIndex = relIndexMapping.get(oldRelIndex)!;

            // Remove name and renderProp to ensure this is preview
            renderElement.name = undefined;
            delete renderElement.renderProp;

            // Update vertex references in vertexRelIdxes (if exists)
            if (renderElement.vertexRelIdxes) {
                renderElement.vertexRelIdxes = renderElement.vertexRelIdxes.map(
                    idx => relIndexMapping.get(idx as number) ?? idx
                );
            }

            // Helper function: Update single point references
            const updatePointReference = (property: string) => {
                if (property in renderElement && (renderElement as any)[property] !== NOT_SET_VALUE) {
                    (renderElement as any)[property] =
                        relIndexMapping.get((renderElement as any)[property] as number) ??
                        (renderElement as any)[property];
                }
            };

            // Helper function: Update array references
            const updateArrayReference = (property: string) => {
                if (property in renderElement && Array.isArray((renderElement as any)[property])) {
                    (renderElement as any)[property] = (renderElement as any)[property].map(
                        (idx: any) => relIndexMapping.get(idx as number) ?? idx
                    );
                }
            };

            // Update specific point references for different element types
            // Includes: start/end points, center, focal points, angles, etc.
            const pointProperties = [
                'startPointIdx',
                'endPointIdx',
                'centerIdx',
                'centerPointIdx',
                'f1Idx',
                'f2Idx',
                'vaIdx',
                'vbIdx',
                'anglePointIdx',
                'startVIdx',
                'endVIdx',
                'arcRelIdx',
            ];
            pointProperties.forEach(updatePointReference);

            // Update array references like faces, lineRelIdxes
            const arrayProperties = ['faces', 'lineRelIdxes'];
            arrayProperties.forEach(updateArrayReference);

            // Step 4: Categorize element and add to preview queue
            if (renderElement.type === 'RenderVertex') {
                // Element is vertex (point)
                vertices.push(renderElement);
                this.previewQueue.add(renderElement);
            } else {
                // Element is shape
                shapes.push(renderElement);
                this.previewQueue.add(renderElement);
            }
        }

        // Step 5: Flush preview queue to display all elements
        await this.previewQueue.flush(docCtrl);

        // Return only the last shape as it represents the final geometric object (e.g., circle)
        // while earlier shapes might be intermediate constructions (e.g., circleShape)
        const finalShape = shapes.length > 0 ? [shapes[shapes.length - 1]] : [];
        return { vertices, shapes: finalShape };
    }

    /**
     * Handles the construction of the shape by calling the {@link construct} method,
     * and then either adds the shape to the document or updates the preview.
     * @param docCtrl The document controller.
     * @param construction The construction data.
     * @param docGlobalId The global ID of the document.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleConstruct(
        docCtrl: GeoDocCtrl,
        construction: GeoElConstructionRequest,
        docGlobalId: DocumentId
    ) {
        const awarenessFeature = this.editor.isSupportFeature(FEATURE_AWARENESS) ? this.editor.awarenessFeature : null;
        /**
         * Callback function to perform the construction.
         */
        const cb = async (): Promise<ApplyConstructionResponse> => {
            // Do a preview construction first
            const constructResponsePreview = await constructExec(() => this.construct(docGlobalId, construction, true));

            // Sync the preview
            const resOfPreview = constructResponsePreview.render;

            // Process preview elements and display them
            const { vertices, shapes } = await this.processPreviewElements(resOfPreview, docCtrl);

            const requirePointNames = vertices.map(e => {
                e.name = undefined;
                return e as RenderVertex;
            });

            this.userConstraintSelection = null;
            // Extract existing point names from construction parameters
            const existingPointNames = construction.paramSpecs
                .filter(paramSpec => paramSpec.paramDefId === 'aPoint')
                .map(paramSpec => paramSpec.params['name'].value);

            // Create virtual points for existing named points to avoid naming conflicts
            const virtualPoints = existingPointNames.map((pointName, index) => {
                // Use negative IDs to avoid conflicts with actual elements
                const virtualPoint = pVertex(-(index + 1000), [0, 0]);
                virtualPoint.name = pointName;
                return virtualPoint;
            });

            const reqEleNames = [];
            const pointsToName = [...virtualPoints, ...requirePointNames];
            if (pointsToName.length > 0) {
                reqEleNames.push({
                    objName: 'Điểm',
                    originElement: pointsToName,
                    pickName: pickPointName,
                    namesToAvoid: [],
                });
            }
            const isUseLowercase = shapes.filter(s => isLowercaseNamingElementTypes(s.type)).length > 0;
            if (isUseLowercase) {
                reqEleNames.push({
                    objName: 'Hình',
                    originElement: shapes,
                    pickName: pickShapeName,
                    namesToAvoid: [],
                });
            }
            const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
            const eleNames = await requestElementNames(docCtrl, nt, reqEleNames);

            if (isUseLowercase) construction.name = eleNames[reqEleNames.length - 1][0];
            else construction.name = eleNames[0].join('');

            if (!construction.name) {
                this.resetState();
                return undefined;
            }

            // Do the actual construction
            return await this.construct(docGlobalId, construction);
        };

        const constructResponse = awarenessFeature
            ? await awarenessFeature.useAwareness(
                  docCtrl.viewport.id,
                  'Đang tạo',
                  buildDocumentAwarenessCmdOption(this.editor.awarenessConstructId, docCtrl),
                  cb
              )
            : await cb();

        if (!constructResponse) {
            syncEndPreviewModeCommand(docCtrl);
            return;
        } else docCtrl.rendererCtrl.clearPreview();

        // Sync the render commands
        syncRenderCommands(constructResponse.render, docCtrl);
        // Add the history item
        addHistoryItemFromConstructionResponse(docCtrl, constructResponse);
    }

    /**
     * Cancels the current constraint selection process, clearing any selected constraints.
     */
    cancelConstraints() {
        this.userConstraintSelection = null;
    }

    /**
     * Checks if the tool is ready to be used.
     * The tool is ready if there is an active viewport and a single focused document.
     * @returns `true` if the tool is ready, `false` otherwise.
     */
    isReady(): boolean {
        const activeVP = this.toolbar.viewport;
        const ctrl = this.editor.selectDelegator.getFocusedDocs(activeVP.id)?.length === 1;
        return activeVP && ctrl;
    }

    /**
     * Processes tool change events, such as when the user inputs a constraint or changes the object type/keyword.
     * @param event The GeoToolEventData.
     * @returns A promise that resolves to the processed GeoToolEventData.
     */
    protected override async processChangeToolEvent(event: GeoToolEventData): Promise<GeoToolEventData> {
        const changeEvent = event as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
        const toolState = event.state as InputCommandToolState;

        if (changeEvent.changes.has('userConstraintInput') && changeEvent.changes.get('userConstraintInput'))
            this.onCompleteConstraint(toolState.userConstraintInput);

        if (changeEvent.changes.has('tplKeyword')) {
            // fetch all constraint for new tpl keyword
            toolState.templates = await this.fetchTemplate(toolState.objType, toolState.tplKeyword);
            this.toolbar.update(this.toolType, toolState);
        }

        if (changeEvent.changes.has('objType')) {
            // reset elName and tplKeyword
            toolState.elName = undefined;
            toolState.tplKeyword = undefined;
            toolState.templates = [];

            // fetch all constraint for new obj type
            toolState.templates = await this.fetchTemplate(toolState.objType, '');
            this.toolbar.update(this.toolType, toolState);
        }

        return event;
    }
}

/**
 * Checks if a given string is a valid JSON object string.
 * @param input The string to check.
 * @returns `true` if the string is a valid JSON object (i.e., it can be parsed into an object or array), `false` otherwise.
 */
function isJsonObjectString(input: string): boolean {
    try {
        const parsed = JSON.parse(input);
        return typeof parsed === 'object' && parsed !== null;
    } catch {
        return false;
    }
}

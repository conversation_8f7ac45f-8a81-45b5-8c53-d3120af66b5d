import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    GeoElConstructionRequest,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
} from '../model';
import { GeometryToolType } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import {
    buildPerpendicularLine,
    buildPerpendicularLineSegment,
    buildPerpendicularLineSegmentWithIntersectLine,
    buildPerpendicularSegmentBetweenPoints,
    buildThroughPointPerpendicularWithCircle,
    buildThroughPointPerpendicularWithEllipse,
    buildThroughPointPerpendicularWithSector,
} from './util.construction';
import { BaseParallelPerpendicularTool } from './util.parallel.perpendicular.tool';

/**
 * Perpendicular Line Tool - Creates perpendicular lines using selector pattern with preview
 * <AUTHOR>
 */
export class CreatePerpendicularLineTool extends BaseParallelPerpendicularTool {
    readonly toolType: GeometryToolType = 'CreatePerpendicularLineTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    protected override calculateLinePreviewVector(docCtrl: GeoDocCtrl, baseLine: RenderLine): number[] {
        const orderedVector = baseLine.orderedVector(docCtrl.rendererCtrl);
        return [-orderedVector[1], orderedVector[0]];
    }

    protected buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest {
        return buildPerpendicularLine(lineName, baseLine.name, baseLine.elType, throughPoint.name);
    }

    protected buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): GeoElConstructionRequest {
        return buildPerpendicularLineSegment(
            combinedName,
            baseLine.name,
            baseLine.elType,
            throughPoint.name,
            scalingFactor
        );
    }

    protected buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest {
        return buildPerpendicularLineSegmentWithIntersectLine(
            combinedName,
            baseLine.name,
            baseLine.elType,
            intersectLine.name,
            intersectLine.elType,
            throughPoint.name
        );
    }

    protected getSimpleConstructionLabel(): string {
        return 'Tên đường thẳng vuông góc';
    }

    protected getComplexConstructionLabel(): string {
        return 'Tên đường thẳng vuông góc';
    }

    protected buildSegmentBetweenPointsConstruction(
        combinedName: string,
        baseLine: RenderLine,
        startPoint: RenderVertex,
        endPoint: RenderVertex
    ): GeoElConstructionRequest {
        return buildPerpendicularSegmentBetweenPoints(
            combinedName,
            startPoint.name,
            baseLine.name,
            baseLine.elType,
            endPoint.name
        );
    }

    protected buildCurvedElementConstruction(
        combinedName: string,
        baseLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        nthIntersection?: number
    ): any {
        switch (curvedElement.type) {
            case 'RenderCircle':
                return buildThroughPointPerpendicularWithCircle(
                    combinedName,
                    throughPoint.name,
                    baseLine,
                    curvedElement as RenderCircle,
                    nthIntersection
                );

            case 'RenderEllipse':
                return buildThroughPointPerpendicularWithEllipse(
                    combinedName,
                    throughPoint.name,
                    baseLine,
                    curvedElement as RenderEllipse,
                    nthIntersection
                );

            case 'RenderSector':
                return buildThroughPointPerpendicularWithSector(
                    combinedName,
                    throughPoint.name,
                    baseLine,
                    curvedElement as RenderSector,
                    nthIntersection
                );

            default:
                throw new Error(`Unsupported curved element type: ${(curvedElement as any).type}`);
        }
    }
}

import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    GeoElConstructionRequest,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
} from '../model';
import { GeometryToolType } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import {
    buildParallelLine,
    buildParallelLineSegment,
    buildParallelLineSegmentWithIntersectLine,
    buildParallelSegmentBetweenPoints,
    buildThroughPointParallelWithCircle,
    buildThroughPointParallelWithEllipse,
    buildThroughPointParallelWithSector,
} from './util.construction';
import { BaseParallelPerpendicularTool } from './util.parallel.perpendicular.tool';

/**
 * Parallel Line Tool - Creates parallel lines using selector pattern with preview
 * <AUTHOR>
 */
export class CreateParallelLineTool extends BaseParallelPerpendicularTool {
    readonly toolType: GeometryToolType = 'CreateParallelLineTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    protected override calculateLinePreviewVector(docCtrl: GeoDocCtrl, baseLine: RenderLine): number[] {
        return baseLine.orderedVector(docCtrl.rendererCtrl);
    }

    protected buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest {
        return buildParallelLine(lineName, baseLine.name, baseLine.elType, throughPoint.name);
    }

    protected buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): GeoElConstructionRequest {
        return buildParallelLineSegment(combinedName, baseLine.name, baseLine.elType, throughPoint.name, scalingFactor);
    }

    protected buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest {
        return buildParallelLineSegmentWithIntersectLine(
            combinedName,
            baseLine.name,
            baseLine.elType,
            intersectLine.name,
            intersectLine.elType,
            throughPoint.name
        );
    }

    protected getSimpleConstructionLabel(): string {
        return 'Tên đường thẳng song song';
    }

    protected getComplexConstructionLabel(): string {
        return 'Tên đường thẳng song song';
    }

    protected buildSegmentBetweenPointsConstruction(
        combinedName: string,
        baseLine: RenderLine,
        startPoint: RenderVertex,
        endPoint: RenderVertex
    ): GeoElConstructionRequest {
        return buildParallelSegmentBetweenPoints(
            combinedName,
            startPoint.name,
            baseLine.name,
            baseLine.elType,
            endPoint.name
        );
    }

    protected buildCurvedElementConstruction(
        combinedName: string,
        baseLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        nthIntersection?: number
    ): any {
        switch (curvedElement.type) {
            case 'RenderCircle':
                return buildThroughPointParallelWithCircle(
                    combinedName,
                    throughPoint.name,
                    baseLine,
                    curvedElement as RenderCircle,
                    nthIntersection
                );

            case 'RenderEllipse':
                return buildThroughPointParallelWithEllipse(
                    combinedName,
                    throughPoint.name,
                    baseLine,
                    curvedElement as RenderEllipse,
                    nthIntersection
                );

            case 'RenderSector':
                return buildThroughPointParallelWithSector(
                    combinedName,
                    throughPoint.name,
                    baseLine,
                    curvedElement as RenderSector,
                    nthIntersection
                );

            default:
                throw new Error(`Unsupported curved element type: ${(curvedElement as any).type}`);
        }
    }
}

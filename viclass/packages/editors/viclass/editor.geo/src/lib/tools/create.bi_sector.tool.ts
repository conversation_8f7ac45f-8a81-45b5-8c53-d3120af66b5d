import { point, vector } from '@flatten-js/core';
import { ErrorHandlerDecorator } from '@viclass/editor.core';
import { geoDefaultHandlerFn, GeoErr } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, RenderAngle, RenderRay, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vertex, vertexOnStroke } from '../selectors';
import { strk, vert } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    buildBisectorConstruction,
    buildBisectorSegmentAndIntersectionLineConstruction,
    buildBisectorSegmentConstruction,
} from './util.construction';
import { calculateLineLineIntersection } from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';

/**
 * Bisector Line Tool - Creates bisector lines from angles using selector DSL
 * <AUTHOR>
 */
export class CreateBisectorLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateBisectorLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedAngle: RenderAngle | undefined;
    bisectorVector: number[] | undefined;
    rayPreview: RenderRay | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedAngle = undefined;
        this.bisectorVector = undefined;
        this.rayPreview = undefined;
        super.resetState();
    }

    /**
     * Creates the selection logic for angle + optional endpoint selection.
     */
    private createSelLogic() {
        // First selector: select an angle directly
        const angleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Second selector: enhanced vertex selector with or logic for vertex on stroke
        const vertexSelector = or(
            [
                // Option 1: Select free vertex with projection
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectOnBisectorRay(previewEl, doc),
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (stroke, previewVertex, doc) =>
                        this.projectVertexOnStrokeToRayIntersection(stroke, previewVertex, doc),
                    refinedFilter: (_el: any) =>
                        ['RenderVector', 'RenderLine', 'RenderLineSegment', 'RenderRay'].includes(_el.type),
                }),
            ],
            {
                flatten: true,
            }
        );

        // Main selection logic: first select angle, then select vertex
        this.selLogic = then([angleSelector, vertexSelector], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [angle, endSelection] = selector.selected;
                this.performConstruction(doc, angle as RenderAngle, endSelection);
            },
        });
    }

    /**
     * Transform function to project any point onto the bisector ray
     */
    private projectOnBisectorRay(previewEl: RenderVertex, doc: GeoDocCtrl): RenderVertex {
        if (!this.selectedAngle || !this.bisectorVector || !this.rayPreview) return previewEl; // Return original if no bisector is available

        try {
            const angleCoords = this.selectedAngle.coord('root', doc.rendererCtrl);
            const projectedCoords = projectPointOntoLine(previewEl.coords, angleCoords, this.bisectorVector);

            if (projectedCoords) {
                // Ensure the projected point is in the forward direction of the ray
                const pointVector = [projectedCoords[0] - angleCoords[0], projectedCoords[1] - angleCoords[1]];
                const dotProduct = pointVector[0] * this.bisectorVector[0] + pointVector[1] * this.bisectorVector[1];

                if (dotProduct >= 0) {
                    // Valid projection on the ray (not behind the start point)
                    previewEl.coords[0] = projectedCoords[0];
                    previewEl.coords[1] = projectedCoords[1];
                    if (previewEl.coords.length > 2) previewEl.coords[2] = 0; // Z coordinate
                } else {
                    // Point projects behind the ray start, project to the ray start itself
                    previewEl.coords[0] = angleCoords[0];
                    previewEl.coords[1] = angleCoords[1];
                    if (previewEl.coords.length > 2) previewEl.coords[2] = 0;
                }
            }

            return previewEl;
        } catch (error) {
            console.warn('Error projecting point onto bisector ray:', error);
            return previewEl;
        }
    }

    /**
     * Transform function to project vertex on stroke to intersection with bisector ray
     */
    private projectVertexOnStrokeToRayIntersection(
        stroke: any,
        previewVertex: RenderVertex,
        doc: GeoDocCtrl
    ): RenderVertex {
        if (!this.rayPreview) {
            console.debug('Missing rayPreview for intersection projection');
            return previewVertex;
        }

        try {
            // Get ray start and direction from the actual rayPreview
            const rayStartCoords = this.rayPreview.coord('start', doc.rendererCtrl);
            const rayVector = this.rayPreview.orderedVector(doc.rendererCtrl);
            if (!rayStartCoords || !rayVector) {
                console.debug('Could not get ray coordinates or vector from rayPreview');
                return previewVertex;
            }

            let intersections: { x: number; y: number }[] = [];

            // Xác định loại stroke và gọi hàm intersection phù hợp từ util.intersections
            if (isElementLine(stroke)) {
                intersections = calculateLineLineIntersection(this.rayPreview, stroke, doc);
            } else {
                console.warn(`Unsupported stroke type for intersection: ${stroke.type}`);
            }

            if (intersections && intersections.length > 0) {
                // Chọn giao điểm gần previewVertex nhất
                const pv = previewVertex.coords;
                let minIdx = 0;
                let minDist = Number.POSITIVE_INFINITY;
                for (let i = 0; i < intersections.length; ++i) {
                    const pt = intersections[i];
                    const dist = Math.hypot(pt.x - pv[0], pt.y - pv[1]);
                    if (dist < minDist) {
                        minDist = dist;
                        minIdx = i;
                    }
                }
                const chosen = intersections[minIdx];
                previewVertex.coords[0] = chosen.x;
                previewVertex.coords[1] = chosen.y;
                if (previewVertex.coords.length > 2) previewVertex.coords[2] = 0;
            } else {
                // Không có giao điểm, giữ nguyên
                console.debug('No intersection found for stroke type:', stroke.type);
            }

            return previewVertex;
        } catch (error) {
            console.warn('Error projecting vertex on stroke to ray intersection:', error);
            return previewVertex;
        }
    }

    /**
     * Calculate bisector vector from two angle vectors using the RenderAngle API
     */
    private calculateBiSectorVectorOfAngle(angle: RenderAngle, ctrl: GeoDocCtrl): number[] {
        // Use the vector() method from RenderAngle to get direction vectors
        const startDirection = angle.vector('start', ctrl.rendererCtrl);
        const endDirection = angle.vector('end', ctrl.rendererCtrl);

        if (!startDirection || !endDirection) {
            throw new Error('Could not get direction vectors from angle');
        }

        // Create unit vectors using @flatten-js/core directly
        const vecA = vector(startDirection[0], startDirection[1]).normalize();
        const vecB = vector(endDirection[0], endDirection[1]).normalize();

        // Calculate angle directions based on line orientations
        const startVDir = angle.startVDir || 1;
        const endVDir = angle.endVDir || 1;

        // Apply direction multipliers
        const directedVecA = vecA.multiply(startVDir);
        const directedVecB = vecB.multiply(endVDir);

        // Calculate bisector as normalized sum
        const bisectorVec = directedVecA.add(directedVecB).normalize();

        return [bisectorVec.x, bisectorVec.y];
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);

        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (this.selLogic.selected && Array.isArray(this.selLogic.selected)) {
            const [angle, endSelection] = this.selLogic.selected;

            if (angle && !endSelection) {
                // First selection: angle selected, show ray preview and update bisector info
                this.selectedAngle = angle as RenderAngle;
                this.bisectorVector = this.calculateBiSectorVectorOfAngle(this.selectedAngle, ctrl);
                this.showBisectorRayPreview(ctrl, this.selectedAngle);
            }
        } else if (this.selLogic.selected && !Array.isArray(this.selLogic.selected)) {
            // Only angle selected (not array yet)
            const angle = this.selLogic.selected as RenderAngle;
            if (angle && angle.type === 'RenderAngle') {
                this.selectedAngle = angle;
                this.bisectorVector = this.calculateBiSectorVectorOfAngle(this.selectedAngle, ctrl);
                this.showBisectorRayPreview(ctrl, this.selectedAngle);
            }
        }

        this.pQ.flush(ctrl);
    }

    /**
     * Show ray preview of the bisector line using the RenderAngle coord() method
     */
    private showBisectorRayPreview(ctrl: GeoDocCtrl, angle: RenderAngle) {
        // Calculate bisector vector
        this.selectedAngle = angle;
        this.bisectorVector = this.calculateBiSectorVectorOfAngle(angle, ctrl);

        // Get the angle vertex point using the coord() method
        const angleCoords = angle.coord('root', ctrl.rendererCtrl);
        if (!angleCoords) return;

        // Create ray preview with normalized direction
        const startPoint = [angleCoords[0], angleCoords[1]];
        this.rayPreview = pLine(ctrl, -20, RenderRay, startPoint, undefined, this.bisectorVector) as RenderRay;

        // Add to preview queue
        this.pQ.add(this.rayPreview);
    }

    /**
     * Performs the bisector construction based on selected elements
     * Using the same pattern as other tools (assignNames + remoteConstruct)
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(ctrl: GeoDocCtrl, angle: RenderAngle, endElement?: any) {
        try {
            // Store selected angle and calculate bisector vector
            this.selectedAngle = angle;
            this.bisectorVector = this.calculateBiSectorVectorOfAngle(angle, ctrl);

            // Validate essential prerequisites
            if (!this.rayPreview) {
                console.error('rayPreview is not available for construction');
                this.resetState();
                return;
            }

            const pointEnd = vert(endElement);
            if (pointEnd.type !== 'RenderVertex') throw new Error('Invalid end vertex');

            // Get angle point coordinates using the coord() method
            const angleCoords = angle.coord('root', ctrl.rendererCtrl);
            if (!angleCoords) {
                this.resetState();
                return;
            }

            if (pointEnd.relIndex === angle.anglePointIdx) {
                // If vertex is the same as angle origin, use simple ray construction
                await assignNames(
                    ctrl,
                    [],
                    this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                    '',
                    'Tia phân giác',
                    this.rayPreview
                );

                const construction = buildBisectorConstruction(angle.name);
                construction.name = this.rayPreview.name;

                await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, 'tia phân giác');
            } else {
                // Bisector segment to point - assign names for both ray and endpoint
                const { pcs, points } = await assignNames(
                    ctrl,
                    [pointEnd],
                    this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                    'Tên điểm cuối',
                    'Tên tia phân giác'
                );

                if (!pcs || !points) {
                    this.resetState();
                    return;
                }

                pointEnd.name = points[0].name;

                const anglePoint = ctrl.rendererCtrl.elementAt(angle.anglePointIdx) as RenderVertex;
                if (!anglePoint) throw new GeoErr('Angle root point not found');

                // Calculate scaling factor - k should be the ratio of actual distance to unit vector
                // Since bisectorVector is normalized, we can use the distance directly
                const startPt = point(angleCoords[0], angleCoords[1]);
                const endPt = point(pointEnd.coords[0], pointEnd.coords[1]);

                let k = 0;
                if (startPt && endPt) {
                    // k is simply the distance from angle vertex to end point
                    k = startPt.distanceTo(endPt)[0];
                }

                // Create combined name for angle and end element
                const combinedName = `${anglePoint.name}${pointEnd.name}`;

                const stroke = strk(endElement);
                if (isElementLine(stroke)) {
                    const construction = buildBisectorSegmentAndIntersectionLineConstruction(
                        combinedName,
                        angle.name,
                        stroke.name,
                        stroke.elType
                    );
                    await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, 'tia phân giác');
                } else {
                    const construction = buildBisectorSegmentConstruction(combinedName, angle.name, k);
                    await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, 'tia phân giác');
                }
            }
        } catch (error) {
            console.error('Error in bisector construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }
}

import { ChangeToolEventData } from '@viclass/editor.core';
import { firstValue<PERSON>rom, Subject } from 'rxjs';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoRelType, NamingElementToolState, RequireNameForUser, UserInputResult, ValidationResult } from '../model';
import { GeometryToolType, GeoToolEventData } from '../model/geo.models';
import { GeometryTool } from './geo.tool';

/**
 * Tool for handling the naming of geometry elements
 * Extends the base GeometryTool class with NamingElementToolState type parameter
 */
export class NamingElementTool extends GeometryTool<NamingElementToolState> {
    // Tool type identifier
    readonly toolType: GeometryToolType = 'NamingElementTool';

    // Subject for handling the input result stream
    private _inputResult: Subject<UserInputResult>;

    // Validation function for checking input names
    private _validate: (idx: number, name: string, type: GeoRelType) => ValidationResult;

    private autoFillNames: () => void;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    /**
     * Requests user input for naming geometry elements
     * @param forObj - Description of the object being named
     * @param originPointNames - Original/existing point names
     * @param inputPointNames - New point names to be input
     * @param validate - Function to validate input names
     * @returns Promise resolving to a user input result
     */
    requireNameFromUser(
        reqireNameForUser: RequireNameForUser[],
        validate: (idx: number, name: string, type: GeoRelType) => ValidationResult,
        autoFillNames: () => void
    ): Promise<UserInputResult> {
        // Prevent multiple naming processes
        if (this._inputResult) throw Error('other naming process is progressing');

        this.autoFillNames = autoFillNames;
        // Setup new naming input process
        const nameInput = new Subject<UserInputResult>();
        this._inputResult = nameInput;
        this._validate = validate;

        // Reset and initialize tool state
        this.toolState.reset();
        this.toolState.requireName = true;
        this.toolState.requireNameForUsers = reqireNameForUser;
        // Update toolbar with new state
        this.toolbar.update('NamingElementTool', this.toolState);
        return firstValueFrom(nameInput);
    }

    /**
     * Processes tool state changes
     * @param event - Event data containing state changes
     * @returns Promise resolving to processed event data
     */
    protected override async processChangeToolEvent(event: GeoToolEventData): Promise<GeoToolEventData> {
        const changeEvent = event as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
        const toolState = event.state as NamingElementToolState;

        // Skip processing if no changes or notProcess flag is true
        if (
            !changeEvent.changes ||
            changeEvent.changes.size <= 0 ||
            (changeEvent.changes.has('notProcess') && changeEvent.changes.get('notProcess').currentValue === true)
        )
            return event;

        // Handle validation of input names
        if (changeEvent.changes.has('idxValidating') && changeEvent.changes.has('typeValidating')) {
            const idx = changeEvent.changes.get('idxValidating').currentValue;
            const type: GeoRelType = changeEvent.changes.get('typeValidating').currentValue;
            const ele = toolState.requireNameForUsers.find(ele => ele.type === type);
            toolState.validateResult[idx] = this._validate(idx, ele.inputElNames[idx], type);
            toolState.notProcess = true;
            this.toolbar.update('NamingElementTool', toolState);
        }

        // Handle completion of naming process
        else if (changeEvent.changes.has('result')) {
            const rs = changeEvent.changes.get('result').currentValue;

            if (rs.action === 'auto') {
                // Auto-fill: generate names automatically
                if (this.autoFillNames) this.autoFillNames();
                else throw Error('autoFillNames is not defined');

                // Validate all generated names
                toolState.requireNameForUsers.forEach((reqName, typeIndex) => {
                    reqName.inputElNames.forEach((name, idx) => {
                        // validate if name is not empty and not the same as the original name
                        if (name && name.trim() && !(reqName.originElNames[idx] == name)) {
                            toolState.validateResult[idx] = this._validate(idx, name, reqName.type);
                        }
                    });
                });

                toolState.notProcess = true;
                this.toolbar.update('NamingElementTool', toolState);
            } else {
                // Confirm/cancel: finalize naming process
                toolState.reset();
                this.toolbar.update('NamingElementTool', toolState);
                this._inputResult.next(rs);

                // Clean up resources
                this._inputResult = undefined;
                this._validate = undefined;
            }
        }

        return event;
    }
}

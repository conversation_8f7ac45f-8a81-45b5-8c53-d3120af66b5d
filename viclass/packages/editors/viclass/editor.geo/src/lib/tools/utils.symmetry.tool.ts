import {
    GeoElConstructionRequest,
    GeoRelType,
    GeoRenderElement,
    RenderCircleShape,
    RenderEllipseShape,
    RenderLineSegment,
    RenderPolygon,
    RenderRay,
    RenderSectorShape,
    RenderVector,
    RenderVertex,
} from '../model';
import { GeoObjectType } from '../model/geo.models';
import {
    pCircle,
    pCircleShape,
    pEllipse,
    pEllipseShape,
    pLine,
    pPolygon,
    pSector,
    pSectorShape,
    pVertex,
} from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { GeoRenderer } from '../renderer';
import { isElementLine, projectPointOntoLine } from './util.tool';

export function symmetricPoint(originalPoint: number[], middlePoint: number[]): number[] {
    if (!originalPoint) return [];
    return [2 * middlePoint[0] - originalPoint[0], 2 * middlePoint[1] - originalPoint[1]];
}

export function calculateSymmetricPointsThroughLine(
    startPointOfLine: number[],
    vector?: number[]
): (params: number[][]) => number[][] {
    return (originalPoints: number[][]) =>
        originalPoints.map(originalPoint => {
            const pointInLine = projectPointOntoLine(originalPoint, startPointOfLine, vector);
            return symmetricPoint(originalPoint, pointInLine);
        });
}

export function calculateSymmetricPointsThroughMiddlePoint(middlePoint: number[]): (params: number[][]) => number[][] {
    return (originalPoints: number[][]): number[][] =>
        originalPoints.map(originalPoint => {
            return symmetricPoint(originalPoint, middlePoint);
        });
}

export interface SymmetryResult {
    element: GeoRenderElement;
    relIndexes: number[];
}

export function createSymmetry(
    element: GeoRenderElement,
    ctrl: GeoDocCtrl,
    renderer: GeoRenderer,
    calculateSymmetricPoints: (params: number[][]) => number[][],
    opposite?: GeoRelType[]
): SymmetryResult | null {
    if (!element) return null;

    if (element.type === 'RenderVertex') {
        const pointPosition = (element as RenderVertex).coords;
        const symmetricPoint = calculateSymmetricPoints([pointPosition]);
        return createPreviewVertex(symmetricPoint[0]);
    }

    if (element.type === 'RenderPolygon') {
        const polygon = element as RenderPolygon;
        const pointPositions = polygon.faces.map(face => (renderer.elementAt(face) as RenderVertex).coords);
        const symmetricPolygon = calculateSymmetricPoints(pointPositions);
        return createPreviewPolygon(ctrl, symmetricPolygon);
    }

    if (isElementLine(element)) {
        const line = element as RenderLineSegment | RenderRay | RenderVector;
        const endPointOfLineStart = renderer.elementAt(line.endPointIdx) as RenderVertex;
        const startPointOfLineStart = renderer.elementAt(line.startPointIdx) as RenderVertex;
        const pointArray = [startPointOfLineStart.coords, endPointOfLineStart.coords];
        const symmetricPoints = calculateSymmetricPoints(pointArray);
        return createPreviewLine(ctrl, symmetricPoints, line.type);
    }

    if (element.type === 'RenderCircleShape') {
        const circle = element as RenderCircleShape;
        const centerPoint = renderer.elementAt(circle.centerPointIdx) as RenderVertex;
        const symmetricPoint = calculateSymmetricPoints([centerPoint.coords]);
        return createPreviewCircle(ctrl, symmetricPoint[0], circle.radius);
    }

    if (element.type === 'RenderEllipseShape') {
        const ellipse = element as RenderEllipseShape;
        const f1 = ellipse.coord('f1', renderer);
        const f2 = ellipse.coord('f2', renderer);
        const symmetricPoints = calculateSymmetricPoints([f1, f2]);
        return createPreviewEllipse(ctrl, symmetricPoints, ellipse.a, ellipse.b);
    }

    if (element.type === 'RenderSectorShape') {
        const circularSector = element as RenderSectorShape;
        const centerPoint = renderer.elementAt(circularSector.centerPointIdx) as RenderVertex;
        const startPoint = renderer.elementAt(circularSector.startPointIdx) as RenderVertex;
        const endPoint = renderer.elementAt(circularSector.endPointIdx) as RenderVertex;
        let pointArray = [centerPoint?.coords, startPoint?.coords, endPoint?.coords];

        if (isOpposite(opposite, element)) pointArray = [centerPoint?.coords, endPoint?.coords, startPoint?.coords];

        const symmetricPoints = calculateSymmetricPoints([...pointArray]);
        return createPreviewCircularSector(ctrl, symmetricPoints);
    }
    return null;
}

function isOpposite(opposite: GeoRelType[], element: GeoRenderElement) {
    return opposite && opposite.find(x => x === element.type);
}

function createPreviewVertex(coords: number[]): SymmetryResult {
    const element = pVertex(-10, coords);
    return { element, relIndexes: [-10] };
}

function createPreviewPolygon(ctrl: GeoDocCtrl, faces: number[][]): SymmetryResult {
    const vertices = faces.map(coords => {
        const index = -Math.floor(Math.random() * 1000) - 100;
        return pVertex(index, coords);
    });

    const polygonIndex = -19;
    const element = pPolygon(ctrl, polygonIndex, vertices, true);

    // Extract all relIndexes from refPEl for proper cleanup
    const refPElIndexes = element.pInfo.refPEl.map(el => el.relIndex);

    return { element, relIndexes: [polygonIndex, ...refPElIndexes] };
}

function createPreviewLine(ctrl: GeoDocCtrl, points: number[][], type: GeoRelType): SymmetryResult {
    const startVertex = pVertex(-25, points[0]);
    const endVertex = pVertex(-26, points[1]);

    // Determine the RenderLine type, default to RenderLineSegment
    let renderLineType: typeof RenderLineSegment | typeof RenderRay | typeof RenderVector;
    if (type === 'RenderRay') {
        renderLineType = RenderRay;
    } else if (type === 'RenderVector') {
        renderLineType = RenderVector;
    } else {
        renderLineType = RenderLineSegment;
    }

    const element = pLine(ctrl, -20, renderLineType, startVertex, endVertex) as
        | RenderLineSegment
        | RenderRay
        | RenderVector;

    // Extract all relIndexes from refPEl for proper cleanup
    const refPElIndexes = element.pInfo.refPEl.map(el => el.relIndex);

    return { element, relIndexes: [-20, ...refPElIndexes] };
}

function createPreviewCircle(ctrl: GeoDocCtrl, centerPoint: number[], radius: number): SymmetryResult {
    const centerVertex = pVertex(-30, centerPoint);
    const radiusPoint = [centerPoint[0] + radius, centerPoint[1]];
    const circle = pCircle(ctrl, -31, centerVertex, radiusPoint, radius);
    const element = pCircleShape(ctrl, -32, centerVertex, circle, radiusPoint, radius);

    // Extract all relIndexes from refPEl for proper cleanup
    const refPElIndexes = element.pInfo.refPEl.map(el => el.relIndex);

    return { element, relIndexes: [-32, ...refPElIndexes] };
}

function createPreviewEllipse(ctrl: GeoDocCtrl, points: number[][], a: number, b: number): SymmetryResult {
    const f1Vertex = pVertex(-33, points[0]);
    const f2Vertex = pVertex(-34, points[1]);
    const pointOnEllipse = [points[0][0] + a, points[0][1]];

    const ellipse = pEllipse(ctrl, -35, f1Vertex, f2Vertex, pointOnEllipse, a, b);
    const element = pEllipseShape(ctrl, -36, f1Vertex, f2Vertex, pointOnEllipse, ellipse, a, b);

    // Extract all relIndexes from refPEl for proper cleanup
    const refPElIndexes = element.pInfo.refPEl.map(el => el.relIndex);

    return { element, relIndexes: [-36, ...refPElIndexes] };
}

function createPreviewCircularSector(ctrl: GeoDocCtrl, points: number[][]): SymmetryResult {
    const centerVertex = pVertex(-38, points[0]);
    const startVertex = pVertex(-37, points[2]);
    const endVertex = pVertex(-39, points[1]);
    const sector = pSector(ctrl, -40, startVertex, centerVertex, endVertex);
    const element = pSectorShape(ctrl, -41, startVertex, centerVertex, endVertex, sector);

    // Extract all relIndexes from refPEl for proper cleanup
    const refPElIndexes = element.pInfo.refPEl.map(el => el.relIndex);

    return { element, relIndexes: [-41, ...refPElIndexes] };
}

// Common construction builders for symmetry operations
export type SymmetryType = 'line' | 'point';
export type GeometryElementType = 'Point' | 'Line' | 'Circle' | 'Ellipse' | 'CircularSector' | 'Polygon';

interface GeometryElementConfig {
    paramDefId: string;
    tplStrLangId: string;
    objectType: GeoObjectType;
    category: string;
}

const GEOMETRY_ELEMENT_CONFIGS: Record<GeometryElementType, GeometryElementConfig> = {
    Point: {
        paramDefId: 'aPoint',
        tplStrLangId: 'tpl-SymmetryPoint',
        objectType: 'Point',
        category: 'Point',
    },
    Line: {
        paramDefId: 'aLine',
        tplStrLangId: 'tpl-SymmetryLine',
        objectType: 'LineVi',
        category: 'Line',
    },
    Circle: {
        paramDefId: 'aCircle',
        tplStrLangId: 'tpl-SymmetryCircle',
        objectType: 'Circle',
        category: 'Circle',
    },
    Ellipse: {
        paramDefId: 'anEllipse',
        tplStrLangId: 'tpl-SymmetryEllipse',
        objectType: 'Ellipse',
        category: 'Ellipse',
    },
    CircularSector: {
        paramDefId: 'aCircularSector',
        tplStrLangId: 'tpl-SymmetryCircularSector',
        objectType: 'CircularSector',
        category: 'CircularSector',
    },
    Polygon: {
        paramDefId: 'aPolygon',
        tplStrLangId: 'tpl-SymmetryPolygon',
        objectType: 'Polygon',
        category: 'Polygon',
    },
};

function getLineTypeMapping(lineType: string): { objectType: GeoObjectType; category: string; paramDefId: string } {
    switch (lineType) {
        case 'VectorVi':
            return {
                objectType: 'VectorVi' as GeoObjectType,
                category: 'Vector',
                paramDefId: 'aVector',
            };
        case 'Ray':
            return {
                objectType: 'Ray' as GeoObjectType,
                category: 'Ray',
                paramDefId: 'aRay',
            };
        case 'LineSegment':
            return {
                objectType: 'LineSegment' as GeoObjectType,
                category: 'Line',
                paramDefId: 'aLineSegment',
            };
        case 'LineVi':
        default:
            return {
                objectType: 'LineVi' as GeoObjectType,
                category: 'Line',
                paramDefId: 'aLine',
            };
    }
}

export function buildSymmetryConstruction(
    elementName: string,
    symmetryElementName: string,
    symmetryType: SymmetryType,
    geometryType: GeometryElementType,
    lineType?: string
): GeoElConstructionRequest {
    const elementType = symmetryType === 'line' ? 'Element/SymmetryThroughLineEC' : 'Element/SymmetryThroughPointEC';
    const config = GEOMETRY_ELEMENT_CONFIGS[geometryType];

    // Use line type mapping for Line geometry, otherwise use default config
    const { objectType, category, paramDefId } =
        geometryType === 'Line' && lineType ? getLineTypeMapping(lineType) : config;

    const construction = new GeoElConstructionRequest(elementType, objectType, category);

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: symmetryType === 'line' ? 'aLine' : 'aPoint',
            optional: false,
            tplStrLangId: symmetryType === 'line' ? 'tpl-LineStart' : 'tpl-MiddlePoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: symmetryElementName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: paramDefId,
            optional: false,
            tplStrLangId: config.tplStrLangId,
            params: {
                name: {
                    type: 'singleValue',
                    value: elementName,
                },
            },
            // Only add dataTypes for point symmetry and line geometry
            ...(symmetryType === 'point' && geometryType === 'Line' && lineType
                ? {
                      dataTypes: {
                          name: lineType,
                      },
                  }
                : {}),
        },
    ];

    return construction;
}

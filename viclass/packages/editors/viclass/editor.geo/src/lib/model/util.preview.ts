import { point, Vector } from '@flatten-js/core';
import {
    GeoRenderElement,
    NOT_SET_VALUE,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
} from '.';
import { syncPreviewCommands } from '../cmd';
import { GeoDocCtrl } from '../objects';
import { GeoRenderer } from '../renderer';
import { calcEllipseFromVectors, calcEllipseGeometry } from './utils.ellipse';

function validatePreviewId(id: number) {
    if (id > 0) throw new Error('Preview Id should be smaller than zero');
}

export function idxOk(id?: number) {
    return id !== undefined && id !== null && id !== NOT_SET_VALUE;
}

// Utility functions for create preview
export function pVertex(id: number, pos: number[]): RenderVertex {
    validatePreviewId(id);

    const el: RenderVertex = Object.assign(new RenderVertex(), {
        relIndex: id,
        coords: pos,
        name: undefined,
        usable: true,
        valid: true,
    });

    return el;
}

function addRefPEl(el: GeoRenderElement, refPEl: GeoRenderElement[]): boolean {
    if (el.relIndex < 0) {
        refPEl.push(el);
        return true;
    }

    return false;
}

/**
 * Create a line with preview information from start / end points or their coordinates
 * If a vector is supplied, then end point should be undefined
 * @param ctrl
 * @param id
 * @param clsK
 * @param startPoint
 * @param endPoint
 * @param vector
 * @returns RenerLine
 */
export function pLine(
    ctrl: GeoDocCtrl,
    id: number,
    clsK: new (...args) => RenderLine,
    startPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[] | undefined,
    vector: number[] | undefined = undefined
): RenderLine {
    const linePartial: Partial<RenderLine> = {
        relIndex: id,
        name: undefined,
        startPointIdx: startPoint instanceof RenderVertex ? startPoint.relIndex : NOT_SET_VALUE,
        endPointIdx: endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
            sVCoords: undefined,
            eVCoords: undefined,
        },
    };
    const el: RenderLine = Object.assign(new clsK(), linePartial);

    // if the point is added to refPEL, then we don't need to set the coord, else possibly
    let needSCoord = startPoint instanceof RenderVertex ? !addRefPEl(startPoint, el.pInfo.refPEl) : true;
    let needECoord = endPoint instanceof RenderVertex ? !addRefPEl(endPoint, el.pInfo.refPEl) : true;

    // if also exist in list of existing elements, don't need to include coords separately
    if (needSCoord && idxOk(el.startPointIdx) && ctrl.rendererCtrl.elementAt(el.startPointIdx)) needSCoord = false;
    if (needECoord && idxOk(el.endPointIdx) && ctrl.rendererCtrl.elementAt(el.endPointIdx)) needECoord = false;

    // still need the coord?
    if (needSCoord) el.pInfo.sVCoords = startPoint instanceof RenderVertex ? startPoint.coords : startPoint;
    if (needECoord) el.pInfo.eVCoords = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;

    // set the vector from the points
    const sCoord = el.coord('start', ctrl.rendererCtrl);
    const eCoord = el.coord('end', ctrl.rendererCtrl);

    el.vector = vector ? vector : [eCoord[0] - sCoord[0], eCoord[1] - sCoord[1], 0];

    return el;
}

/**
 * Internal helper that builds any "circle-like" element (RenderCircle | RenderCircleShape)
 * – The only thing that can vary is the concrete constructor and extra fields (e.g. `arcRelIdx`).
 */
function buildCircleLike<T extends RenderCircle | RenderCircleShape>(
    ctor: new () => T,
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    radius: number | undefined = undefined,
    extra: Partial<T> = {} // allow caller to inject specialised props
): T {
    const relIdxs = [
        centerPoint instanceof RenderVertex ? centerPoint.relIndex : NOT_SET_VALUE,
        endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
    ];

    const base: Partial<T> = {
        relIndex: id,
        name: '',
        centerPointIdx: relIdxs[0],
        vertexRelIdxes: relIdxs,
        radius: radius,
        usable: true,
        valid: true,
        pInfo: { refPEl: [], cCoords: undefined },
        ...extra,
    };

    const el = Object.assign(new ctor(), base);

    // reference-point bookkeeping
    let needCoords = centerPoint instanceof RenderVertex ? !addRefPEl(centerPoint, el.pInfo.refPEl) : true;

    if (needCoords && idxOk(el.centerPointIdx) && ctrl.rendererCtrl.elementAt(el.centerPointIdx)) {
        needCoords = false;
    }

    if (needCoords) {
        el.pInfo.cCoords = centerPoint instanceof RenderVertex ? centerPoint.coords : centerPoint;
    }

    // radius from centre → end
    const c = el.coord('center', ctrl.rendererCtrl);
    if (radius != null) {
        el.radius = radius;
    } else {
        const e = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;
        el.radius = point(c[0], c[1]).distanceTo(point(e[0], e[1]))[0];
    }

    return el;
}

/** Original `pCircle` now just delegates to the helper. */
export function pCircle(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    endPoint?: RenderVertex | number[] | undefined,
    radius: number | undefined = undefined
): RenderCircle {
    return buildCircleLike(RenderCircle, ctrl, id, centerPoint, endPoint, radius);
}

/** Original `pCircleShape` with the only extra bit: `arcRelIdx`. */
export function pCircleShape(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    arc: RenderCircle | number,
    endPoint: RenderVertex | number[] = undefined,
    radius: number | undefined = undefined
): RenderCircleShape {
    const el = buildCircleLike(RenderCircleShape, ctrl, id, centerPoint, endPoint, radius, {
        arcRelIdx: arc instanceof RenderCircle ? arc.relIndex : arc,
    } as Partial<RenderCircleShape>);

    if (arc instanceof RenderCircle) addRefPEl(arc, el.pInfo.refPEl);

    return el;
}

/**
 *
 * @param ctrl
 * @param id
 * @param verts
 * @param withBoundary
 * @param lineType
 * @returns
 */
export function pPolygon(
    ctrl: GeoDocCtrl,
    id: number,
    verts: (RenderVertex | number[])[],
    withBoundary: boolean,
    lineType?: new (...args: any[]) => RenderLine,
    lineIdGenerator?: (edgeNo: number) => number
) {
    const polygon = new RenderPolygon();
    const polygonPartial: Partial<RenderPolygon> = {
        relIndex: id,
        name: '',
        usable: true,
        valid: true,
        faces: verts.map(v => (v instanceof RenderVertex ? v.relIndex : NOT_SET_VALUE)),
        pInfo: {
            refPEl: [],
            verts: new Map(),
        },
    };

    verts.forEach((v, i) => {
        // for each vert, we try to use it
        if (v instanceof RenderVertex) {
            const addedRef = addRefPEl(v, polygonPartial.pInfo.refPEl);

            if (!addedRef) polygonPartial.pInfo.verts.set(i, [...v.coords]);
        } else {
            polygonPartial.pInfo.verts.set(i, [...v]);
        }

        if (withBoundary && i < verts.length) {
            const j = (i + 1) % verts.length;
            const edge = pLine(
                ctrl,
                lineIdGenerator ? lineIdGenerator(i) : id - j - 5,
                lineType ? lineType : RenderLineSegment,
                verts[i],
                verts[j],
                undefined
            );
            addRefPEl(edge, polygonPartial.pInfo.refPEl);
        }
    });

    Object.assign(polygon, polygonPartial);

    return polygon;
}

function createSectorElement<T extends RenderSector | RenderSectorShape>(
    ctrl: GeoDocCtrl,
    id: number,
    startPoint: RenderVertex | number[],
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    ElementCtor: new () => T,
    radius: number | undefined = undefined,
    extra?: GeoRenderElement
): T {
    const partial: Partial<T> = {
        relIndex: id,
        name: '',
        usable: true,
        valid: true,
        startPointIdx: startPoint instanceof RenderVertex ? startPoint.relIndex : NOT_SET_VALUE,
        centerPointIdx: centerPoint instanceof RenderVertex ? centerPoint.relIndex : NOT_SET_VALUE,
        endPointIdx: endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
        pInfo: {
            refPEl: [],
            sCoords: undefined,
            cCoords: undefined,
            eCoords: undefined,
        },
    } as T;

    if (extra) partial.pInfo!.refPEl.push(extra);

    const el: T = Object.assign(new ElementCtor(), partial);

    const c = centerPoint instanceof RenderVertex ? centerPoint.coords : centerPoint;
    const s = startPoint instanceof RenderVertex ? startPoint.coords : startPoint;
    const e = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;

    const pC = point(c[0], c[1]);
    const pS = point(s[0], s[1]);

    let needSCoord = startPoint instanceof RenderVertex ? !addRefPEl(startPoint, el.pInfo.refPEl) : true;
    let needCCoord = centerPoint instanceof RenderVertex ? !addRefPEl(centerPoint, el.pInfo.refPEl) : true;
    let needECoord = endPoint instanceof RenderVertex ? !addRefPEl(endPoint, el.pInfo.refPEl) : true;

    if (needSCoord && idxOk(el.startPointIdx) && ctrl.rendererCtrl.elementAt(el.startPointIdx)) needSCoord = false;
    if (needCCoord && idxOk(el.centerPointIdx) && ctrl.rendererCtrl.elementAt(el.centerPointIdx)) needCCoord = false;
    if (needECoord && idxOk(el.endPointIdx) && ctrl.rendererCtrl.elementAt(el.endPointIdx)) needECoord = false;

    if (needSCoord) el.pInfo.sCoords = s;
    if (needCCoord) el.pInfo.cCoords = c;
    if (needECoord) el.pInfo.eCoords = e;

    // Radius calculation consistent with buildCircleLike pattern
    if (radius != null) {
        el.radius = radius;
    } else {
        // For sector, radius is typically from center to start point
        el.radius = pC.distanceTo(pS)[0];
    }

    return el;
}

export function pSectorShape(
    ctrl: GeoDocCtrl,
    id: number,
    startPoint: RenderVertex | number[],
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    extra?: GeoRenderElement,
    radius: number | undefined = undefined
) {
    return createSectorElement(ctrl, id, startPoint, centerPoint, endPoint, RenderSectorShape, radius, extra);
}

export function pSector(
    ctrl: GeoDocCtrl,
    id: number,
    startPoint: RenderVertex | number[],
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    radius: number | undefined = undefined
) {
    return createSectorElement(ctrl, id, startPoint, centerPoint, endPoint, RenderSector, radius);
}

/** Helper to build ellipse elements from center and vectors */
function buildEllipseLikeFromCenterVectors<T extends RenderEllipse | RenderEllipseShape>(
    ctor: new () => T,
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[], // Center point
    vectorAPoint: RenderVertex | number[], // Vector A endpoint
    vectorBPoint: RenderVertex | number[], // Vector B endpoint
    rotate: number | undefined = undefined, // Optional rotation angle override
    extra: Partial<T> = {} // allow caller to inject specialised props
): T {
    const relIdxs = [
        centerPoint instanceof RenderVertex ? centerPoint.relIndex : NOT_SET_VALUE,
        vectorAPoint instanceof RenderVertex ? vectorAPoint.relIndex : NOT_SET_VALUE,
        vectorBPoint instanceof RenderVertex ? vectorBPoint.relIndex : NOT_SET_VALUE,
    ];

    const base: Partial<T> = {
        relIndex: id,
        name: '',
        centerIdx: relIdxs[0],
        vaIdx: relIdxs[1],
        vbIdx: relIdxs[2],
        mode: 1, // CENTER_VECTORS mode
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
            f1: undefined,
            f2: undefined,
            vaCoords: undefined,
            vbCoords: undefined,
            center: undefined,
        },
        ...extra,
    };

    const el = Object.assign(new ctor(), base);

    // reference-point bookkeeping for center
    let needCenterCoords = centerPoint instanceof RenderVertex ? !addRefPEl(centerPoint, el.pInfo.refPEl) : true;
    if (needCenterCoords && idxOk(el.centerIdx) && ctrl.rendererCtrl.elementAt(el.centerIdx)) {
        needCenterCoords = false;
    }
    if (needCenterCoords) {
        el.pInfo.center = centerPoint instanceof RenderVertex ? centerPoint.coords : centerPoint;
    }

    // reference-point bookkeeping for vectorA
    let needVaCoords = vectorAPoint instanceof RenderVertex ? !addRefPEl(vectorAPoint, el.pInfo.refPEl) : true;
    if (needVaCoords && idxOk(el.vaIdx) && ctrl.rendererCtrl.elementAt(el.vaIdx)) {
        needVaCoords = false;
    }
    if (needVaCoords) {
        el.pInfo.vaCoords = vectorAPoint instanceof RenderVertex ? vectorAPoint.coords : vectorAPoint;
    }

    // reference-point bookkeeping for vectorB
    let needVbCoords = vectorBPoint instanceof RenderVertex ? !addRefPEl(vectorBPoint, el.pInfo.refPEl) : true;
    if (needVbCoords && idxOk(el.vbIdx) && ctrl.rendererCtrl.elementAt(el.vbIdx)) {
        needVbCoords = false;
    }
    if (needVbCoords) {
        el.pInfo.vbCoords = vectorBPoint instanceof RenderVertex ? vectorBPoint.coords : vectorBPoint;
    }

    // Calculate ellipse geometry from center and vectors
    const center = centerPoint instanceof RenderVertex ? centerPoint.coords : centerPoint;
    const vectorA = vectorAPoint instanceof RenderVertex ? vectorAPoint.coords : vectorAPoint;
    const vectorB = vectorBPoint instanceof RenderVertex ? vectorBPoint.coords : vectorBPoint;

    const geometry = calcEllipseFromVectors(center, vectorA, vectorB);

    el.a = geometry.a;
    el.b = geometry.b;
    el.rotate = rotate !== undefined ? rotate : geometry.rotation.angle;

    // Calculate focus points for backward compatibility
    el.f1Idx = NOT_SET_VALUE;
    el.f2Idx = NOT_SET_VALUE;

    return el;
}

/** Helper to build ellipse elements from focus points */
function buildEllipseLike<T extends RenderEllipse | RenderEllipseShape>(
    ctor: new () => T,
    ctrl: GeoDocCtrl,
    id: number,
    f1Point: RenderVertex | number[], // Focus 1
    f2Point: RenderVertex | number[], // Focus 2
    pointOnEllipse: RenderVertex | number[], // Point on ellipse to calculate a, b
    a: number | undefined = undefined,
    b: number | undefined = undefined,
    rotate: number | undefined = undefined, // Optional rotation angle override
    extra: Partial<T> = {} // allow caller to inject specialised props
): T {
    const relIdxs = [
        f1Point instanceof RenderVertex ? f1Point.relIndex : NOT_SET_VALUE,
        f2Point instanceof RenderVertex ? f2Point.relIndex : NOT_SET_VALUE,
    ];

    const base: Partial<T> = {
        relIndex: id,
        name: '',
        f1Idx: relIdxs[0],
        f2Idx: relIdxs[1],
        a: a,
        b: b,
        mode: 0, // FOCUS_POINTS mode
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
            f1: undefined,
            f2: undefined,
            vaCoords: undefined,
            vbCoords: undefined,
            center: undefined,
        },
        ...extra,
    };

    const el = Object.assign(new ctor(), base);

    // reference-point bookkeeping for f1
    let needF1Coords = f1Point instanceof RenderVertex ? !addRefPEl(f1Point, el.pInfo.refPEl) : true;
    if (needF1Coords && idxOk(el.f1Idx) && ctrl.rendererCtrl.elementAt(el.f1Idx)) {
        needF1Coords = false;
    }
    if (needF1Coords) {
        el.pInfo.f1 = f1Point instanceof RenderVertex ? f1Point.coords : f1Point;
    }

    // reference-point bookkeeping for f2
    let needF2Coords = f2Point instanceof RenderVertex ? !addRefPEl(f2Point, el.pInfo.refPEl) : true;
    if (needF2Coords && idxOk(el.f2Idx) && ctrl.rendererCtrl.elementAt(el.f2Idx)) {
        needF2Coords = false;
    }
    if (needF2Coords) {
        el.pInfo.f2 = f2Point instanceof RenderVertex ? f2Point.coords : f2Point;
    }

    // Calculate center point from foci
    const f1 = f1Point instanceof RenderVertex ? f1Point.coords : f1Point;
    const f2 = f2Point instanceof RenderVertex ? f2Point.coords : f2Point;
    const cx = (f1[0] + f2[0]) / 2;
    const cy = (f1[1] + f2[1]) / 2;

    // Only calculate geometry if we need missing parameters
    const needGeometry = a === undefined || b === undefined || rotate === undefined;
    let geometry;

    if (needGeometry) {
        const p3 = pointOnEllipse instanceof RenderVertex ? pointOnEllipse.coords : pointOnEllipse;
        geometry = calcEllipseGeometry(f1, f2, p3);
    }

    // Use provided values or fallback to calculated geometry
    el.a = a !== undefined ? a : geometry.a;
    el.b = b !== undefined ? b : geometry.b;
    el.rotate = rotate !== undefined ? rotate : geometry.rotation.angle;

    // Calculate vector A (major axis vector) and vector B (minor axis vector)
    const cosAngle = Math.cos(el.rotate);
    const sinAngle = Math.sin(el.rotate);

    // Vector A: from center to end of major axis
    const vaCoords = [cx + el.a * cosAngle, cy + el.a * sinAngle];
    // Vector B: from center to end of minor axis (perpendicular to major axis)
    const vbCoords = [cx - el.b * sinAngle, cy + el.b * cosAngle];

    // Add vector coordinates and center to pInfo
    if (el.pInfo) {
        el.pInfo.vaCoords = vaCoords;
        el.pInfo.vbCoords = vbCoords;
        el.pInfo.center = [cx, cy];
    }

    return el;
}

/** Create preview ellipse from center and vectors (arc only) */
export function pEllipseFromCenterVectors(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    vectorAPoint: RenderVertex | number[],
    vectorBPoint: RenderVertex | number[],
    rotate: number | undefined = undefined
): RenderEllipse {
    return buildEllipseLikeFromCenterVectors(RenderEllipse, ctrl, id, centerPoint, vectorAPoint, vectorBPoint, rotate);
}

/** Create preview ellipse shape from center and vectors (filled area) */
export function pEllipseShapeFromCenterVectors(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    vectorAPoint: RenderVertex | number[],
    vectorBPoint: RenderVertex | number[],
    arc: RenderEllipse | number,
    rotate: number | undefined = undefined
): RenderEllipseShape {
    const el = buildEllipseLikeFromCenterVectors(
        RenderEllipseShape,
        ctrl,
        id,
        centerPoint,
        vectorAPoint,
        vectorBPoint,
        rotate,
        {
            arcRelIdx: arc instanceof RenderEllipse ? arc.relIndex : arc,
        } as Partial<RenderEllipseShape>
    );

    if (arc instanceof RenderEllipse) addRefPEl(arc, el.pInfo.refPEl);

    return el;
}

/** Create preview ellipse (arc only) */
export function pEllipse(
    ctrl: GeoDocCtrl,
    id: number,
    f1Point: RenderVertex | number[],
    f2Point: RenderVertex | number[],
    pointOnEllipse: RenderVertex | number[],
    a: number | undefined = undefined,
    b: number | undefined = undefined,
    rotate: number | undefined = undefined
): RenderEllipse {
    return buildEllipseLike(RenderEllipse, ctrl, id, f1Point, f2Point, pointOnEllipse, a, b, rotate);
}

/** Create preview ellipse shape (filled area) */
export function pEllipseShape(
    ctrl: GeoDocCtrl,
    id: number,
    f1Point: RenderVertex | number[],
    f2Point: RenderVertex | number[],
    pointOnEllipse: RenderVertex | number[],
    arc: RenderEllipse | number,
    a: number | undefined = undefined,
    b: number | undefined = undefined,
    rotate: number | undefined = undefined
): RenderEllipseShape {
    const el = buildEllipseLike(RenderEllipseShape, ctrl, id, f1Point, f2Point, pointOnEllipse, a, b, rotate, {
        arcRelIdx: arc instanceof RenderEllipse ? arc.relIndex : arc,
    } as Partial<RenderEllipseShape>);

    if (arc instanceof RenderEllipse) addRefPEl(arc, el.pInfo.refPEl);

    return el;
}

/** Get reference element by index */
/**
 * Create a preview RenderAngle from two lines or three points, synchronized with other preview functions.
 * @param ctrl
 * @param id
 * @param root - Root point (vertex) of the angle (flatten Point, RenderVertex, or [x, y])
 * @param startV - Starting vector of the angle (vector, RenderLine, or two points)
 * @param endV - Ending vector of the angle (vector, RenderLine, or two points)
 * @param startVDir - Direction for start vector (1 or -1), default 1
 * @param endVDir - Direction for end vector (1 or -1), default 1
 * @returns RenderAngle
 */
export function pAngle(
    ctrl: GeoDocCtrl,
    id: number,
    root: RenderVertex | number[],
    startV: RenderLine | number[],
    endV: RenderLine | number[],
    startVDir: 1 | -1 = 1,
    endVDir: 1 | -1 = 1
): RenderAngle {
    validatePreviewId(id);

    const anglePartial: Partial<RenderAngle> = {
        relIndex: id,
        name: '',
        anglePointIdx: root instanceof RenderVertex ? root.relIndex : NOT_SET_VALUE,
        startVIdx: startV instanceof RenderLine ? startV.relIndex : NOT_SET_VALUE,
        endVIdx: endV instanceof RenderLine ? endV.relIndex : NOT_SET_VALUE,
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
        },
    };
    const el = Object.assign(new RenderAngle(), anglePartial);

    let needCCoord = root instanceof RenderVertex ? !addRefPEl(root, el.pInfo.refPEl) : true;
    if (needCCoord && idxOk(el.anglePointIdx) && refElFromIdx(el.anglePointIdx, el, ctrl.rendererCtrl))
        needCCoord = false;
    if (needCCoord) el.pInfo.cCoords = root instanceof RenderVertex ? root.coords : root;

    let needSVector = startV instanceof RenderLine ? !addRefPEl(startV, el.pInfo.refPEl) : true;
    if (needSVector && idxOk(el.startVIdx) && refElFromIdx(el.startVIdx, el, ctrl.rendererCtrl)) needSVector = false;
    if (needSVector)
        el.pInfo.startV =
            startV instanceof RenderLine ? startV.orderedVector(ctrl.rendererCtrl).map(x => x * startVDir) : startV;
    else el.startVDir = startVDir;

    let needEVector = endV instanceof RenderLine ? !addRefPEl(endV, el.pInfo.refPEl) : true;
    if (needEVector && idxOk(el.endVIdx) && refElFromIdx(el.endVIdx, el, ctrl.rendererCtrl)) needEVector = false;
    if (needEVector)
        el.pInfo.endV = endV instanceof RenderLine ? endV.orderedVector(ctrl.rendererCtrl).map(x => x * endVDir) : endV;
    else el.endVDir = endVDir;

    return el;
}

/**
 * Get reference element from index. This will first check the the renderer
 * if the element is there, then use it, if not it check the reference elements
 * included as part of the preview.
 * @param idx
 * @param host
 * @param renderer
 */
export function refElFromIdx(idx: number, host: GeoRenderElement, renderer: GeoRenderer): GeoRenderElement | undefined {
    const el = renderer.elementAt(idx);

    if (el) return el;

    const pel = renderer.previewElAt(idx);
    if (pel) return pel;

    const refEl = host.pInfo?.refPEl.find(re => re.relIndex == idx);
    if (refEl) return refEl;

    return undefined;
}

/**
 * Utility class to ensure only necessary preview is synchronized
 */
export class PreviewQueue {
    private queue: GeoRenderElement[] = [];

    add(...elements: GeoRenderElement[]): void {
        elements.forEach(element => {
            if (element) {
                // Ensure no duplicates by relIndex
                if (!this.queue.find(el => el.relIndex === element.relIndex)) {
                    this.queue.push(element);
                }
            }
        });
    }

    async flush(docCtrl: GeoDocCtrl): Promise<void> {
        if (!docCtrl || this.queue.length === 0) {
            this.queue = []; // Clear queue if no docCtrl or empty
            return;
        }

        const elementsToSync = new Map<number, GeoRenderElement>();
        this.queue.forEach(el => elementsToSync.set(el.relIndex, el));

        this.queue.forEach(el => {
            if (el.pInfo && el.pInfo.refPEl) {
                el.pInfo.refPEl.forEach(refEl => {
                    elementsToSync.delete(refEl.relIndex);
                });
            }
        });

        for (const el of Array.from(elementsToSync.values())) {
            await syncPreviewCommands(el, docCtrl);
        }
        this.queue = [];
    }
}

// Hàm tính góc giữa hai vector theo CCW, trả về giá trị từ 0 đến 2*PI
export function angleCCW(v1: Vector, v2: Vector): number {
    const dot = v1.dot(v2);
    const cross = v1.x * v2.y - v1.y * v2.x;
    let angle = Math.atan2(cross, dot);
    if (angle < 0) angle += 2 * Math.PI;
    return angle;
}

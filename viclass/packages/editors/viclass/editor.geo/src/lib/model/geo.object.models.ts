import { BehaviorSubject } from 'rxjs';
import { mapGetOrSet, renderType } from '../tools/util.tool';
import {
    FillType,
    GeoRelType,
    GeoRenderElement,
    RenderAngle,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderLineSegment,
    RenderRay,
    RenderSector,
    RenderVector,
    RenderVertex,
    StrokeType,
} from './render.elements.model';

/**
 * Defines the possible render types for geometric objects.
 */
export type GeoRenderType =
    | 'RenderVertex'
    | 'RenderLine'
    | 'RenderSector'
    | 'RenderEllipse'
    | 'RenderCircle'
    | 'RenderShape'
    | 'RenderAngle';

/**
 * Manages a collection of geometric render elements (`GeoRenderElement`).
 * It provides methods for adding, removing, querying, and managing the state (e.g., highlighting, selection)
 * of these elements, along with caching mechanisms for performance.
 */
export class GeoObjCollection {
    /**
     * Main store for all geometric render elements, keyed by their `relIndex`.
     */
    protected readonly _elements: Map<number, GeoRenderElement> = new Map();
    /**
     * Cache for elements that are potential candidates for selection.
     */
    private readonly cachedPotentialSelection: Map<number, GeoRenderElement> = new Map();
    /**
     * Cache for stroke-based (non-shape) elements, grouped by their `GeoRelType`.
     */
    private readonly cachedStrokeElements: Map<GeoRelType, Map<number, StrokeType>> = new Map();
    /**
     * Cache for shape-based elements.
     */
    private readonly cachedShapeElements: Map<number, FillType> = new Map();
    /**
     * Cache for highlighted stroke-based elements, grouped by their `GeoRelType`.
     */
    private readonly cachedHighlightStrokeElements: Map<GeoRelType, Map<number, StrokeType>> = new Map();
    /**
     * Cache for highlighted shape-based elements.
     */
    private readonly cachedHighlightShapeElements: Map<number, FillType> = new Map();
    /**
     * BehaviorSubject to track and notify observers of collection updates.
     */
    private readonly collectionVersion = new BehaviorSubject<number>(0);
    /**
     * Observable that emits when the collection is updated.
     */
    readonly onCollectionUpdated$ = this.collectionVersion.asObservable();

    /**
     * Cache for selectable elements, grouped by their `GeoRenderType`.
     */
    protected readonly cachedSelectableElements: Map<GeoRenderType, Map<number, GeoRenderElement>> = new Map();
    /**
     * Cache for unselectable elements, grouped by their `GeoRenderType`.
     */
    protected readonly cachedUnselectableElements: Map<GeoRenderType, Map<number, GeoRenderElement>> = new Map();

    constructor() {}

    /**
     * Notifies observers that the collection has been modified by incrementing the version.
     */
    private notifyCollectionChanges() {
        this.collectionVersion.next(this.collectionVersion.value + 1);
    }

    /**
     * Gets the total number of elements in the collection.
     * @returns The number of elements.
     */
    get size(): number {
        return this._elements.size;
    }

    /**
     * Gets an array of all element `relIndex` keys in the collection.
     * @returns An array of element indices.
     */
    get elementIdxes(): number[] {
        return Array.from(this._elements.keys());
    }

    /**
     * Checks if an element with the given `relIndex` exists in the collection.
     * @param relIdx - The `relIndex` of the element to check.
     * @returns `true` if the element exists, `false` otherwise.
     */
    has(relIdx: number): boolean {
        return this._elements.has(relIdx);
    }

    /**
     * Gets all usable, valid, and not deleted elements from the collection.
     * @returns An array of `GeoRenderElement` objects.
     */
    get usableElements(): GeoRenderElement[] {
        return Array.from(this._elements.values()).filter(rel => rel.deleted != true && rel.usable && rel.valid);
    }

    /**
     * Adds elements to the potential selection cache if they are valid, usable, and not hidden.
     * @param elIndexArr - An array of `relIndex` values for elements to consider for potential selection.
     */
    addPotentialSelection(elIndexArr: number[]) {
        elIndexArr
            .map(idx => this.elementAt(idx))
            .filter(rel => rel && rel.deleted != true && rel.usable && rel.valid && !rel.renderProp.hidden)
            .forEach(rel => {
                this.cachedPotentialSelection.set(rel.relIndex, rel);
            });
    }

    /**
     * Removes elements from the potential selection cache.
     * @param elIndexArr - An array of `relIndex` values for elements to remove from potential selection.
     */
    removePotentialSelection(elIndexArr: number[]) {
        elIndexArr.forEach(idx => this.cachedPotentialSelection.delete(idx));
    }

    /**
     * Gets all elements currently in the potential selection cache.
     * @returns An array of `GeoRenderElement` objects.
     */
    get potentialSelection(): GeoRenderElement[] {
        return Array.from(this.cachedPotentialSelection.values());
    }

    /**
     * Adds a geometric object to the collection and manages various caching mechanisms.
     *
     * @param rel - The geometric object to add to the collection
     */
    addRenderElement(rel: GeoRenderElement) {
        this._elements.set(rel.relIndex, rel);

        // Condition for an element to be considered active and renderable
        const isActiveAndRenderable = rel.deleted !== true && rel.usable && rel.valid && !rel.renderProp?.hidden; // if render prop is not set, considered it is not hidden

        if (isActiveAndRenderable) {
            // Handle shapes and non-shapes differently for caching
            if (renderType(rel.type) === 'RenderShape') {
                this.cachedShapeElements.set(rel.relIndex, rel as FillType);
                // Ensure it's not in stroke cache if it was miscategorized before
                this.cachedStrokeElements.forEach(objMap => objMap.delete(rel.relIndex));
            } else {
                // Add object to type-specific stroke cache collection
                mapGetOrSet(this.cachedStrokeElements, rel.type, new Map()).set(rel.relIndex, rel);
                // Ensure it's not in shape cache
                this.cachedShapeElements.delete(rel.relIndex);

                // Remove any stale entries with same relIndex from other stroke type collections
                // This handles cases where an element's type might change.
                this.cachedStrokeElements.forEach((objMap, objType) => {
                    if (objType !== rel.type) objMap.delete(rel.relIndex);
                });
            }
        } else {
            // If object is invalid or not renderable, remove it from all primary render caches
            this.cachedShapeElements.delete(rel.relIndex);
            this.cachedStrokeElements.get(rel.type)?.delete(rel.relIndex); // Only try to delete if the type map exists
            // Also remove from highlight caches
            this.cachedHighlightStrokeElements.get(rel.type)?.delete(rel.relIndex);
            this.cachedHighlightShapeElements.delete(rel.relIndex);
            // And from selectable/unselectable caches
            this.cachedSelectableElements.get(renderType(rel.type))?.delete(rel.relIndex);
            this.cachedUnselectableElements.get(renderType(rel.type))?.delete(rel.relIndex);
        }

        // Update selectable/unselectable caches
        if (isActiveAndRenderable) {
            if (!rel.unselectable) {
                mapGetOrSet(this.cachedSelectableElements, renderType(rel.type), new Map()).set(rel.relIndex, rel);
                this.cachedUnselectableElements.get(renderType(rel.type))?.delete(rel.relIndex); // Remove from unselectable if now selectable
            } else {
                mapGetOrSet(this.cachedUnselectableElements, renderType(rel.type), new Map()).set(rel.relIndex, rel);
                this.cachedSelectableElements.get(renderType(rel.type))?.delete(rel.relIndex); // Remove from selectable if now unselectable
            }
        } else {
            // If not active or renderable, remove from both selectable and unselectable caches
            // The previous block already handles this for `isActiveAndRenderable = false`,
            // but this specifically targets elements that might have been active and then became inactive.
            this.cachedSelectableElements.get(renderType(rel.type))?.delete(rel.relIndex);
            this.cachedUnselectableElements.get(renderType(rel.type))?.delete(rel.relIndex);
        }

        // Notify listeners that collection has changed
        this.notifyCollectionChanges();
    }

    /**
     * Clears all elements and caches from the collection.
     */
    clear() {
        this._elements.clear();
        this.cachedPotentialSelection.clear();
        this.cachedStrokeElements.clear();
        this.cachedShapeElements.clear();
        this.cachedHighlightStrokeElements.clear();
        this.cachedHighlightShapeElements.clear();
        this.cachedUnselectableElements.clear();
        this.cachedSelectableElements.clear();
        this.notifyCollectionChanges();
    }

    /**
     * Clear all unusable objects. This typically happens when an object does not need to be kept anymore.
     * Ex: After undo, an element might be marked as unusable. If a new element is then added,
     * the unusable element is no longer needed for redoing.
     * @returns An array of `relIndex` values of the cleared unusable objects.
     */
    clearUnusableObject(): number[] {
        const unusableIndexes: number[] = [];
        this._elements.forEach((rel, relIndex) => {
            if (!rel.usable) {
                unusableIndexes.push(relIndex);
            }
        });

        if (unusableIndexes.length > 0) {
            unusableIndexes.forEach(idx => {
                const rel = this.elementAt(idx); // Get the element once
                if (rel) {
                    // Ensure element exists before trying to delete
                    this._elements.delete(idx);
                    this.cachedPotentialSelection.delete(idx);
                    this.cachedShapeElements.delete(idx);
                    this.cachedHighlightShapeElements.delete(idx);

                    const rType = renderType(rel.type);
                    this.cachedStrokeElements.get(rel.type)?.delete(idx);
                    this.cachedHighlightStrokeElements.get(rel.type)?.delete(idx);
                    this.cachedSelectableElements.get(rType)?.delete(idx);
                    this.cachedUnselectableElements.get(rType)?.delete(idx);
                }
            });
            this.notifyCollectionChanges();
            return unusableIndexes;
        }
        return [];
    }

    /**
     * Adds specified elements to their respective highlight caches.
     * Only valid, usable, and non-hidden elements are highlighted.
     * @param relIndexArr - An array of `relIndex` values for elements to highlight.
     */
    highlight(relIndexArr: number[]) {
        relIndexArr
            .map(idx => this.elementAt(idx))
            .filter(rel => rel && rel.deleted != true && rel.usable && rel.valid && !rel.renderProp.hidden)
            .forEach(rel => {
                if (renderType(rel.type) === 'RenderShape') {
                    this.cachedHighlightShapeElements.set(rel.relIndex, rel as FillType);
                } else {
                    mapGetOrSet(this.cachedHighlightStrokeElements, rel.type, new Map()).set(rel.relIndex, rel);
                }
            });
        this.notifyCollectionChanges();
    }

    /**
     * Removes specified elements from their respective highlight caches.
     * @param relIndexArr - An array of `relIndex` values for elements to remove from highlight.
     */
    removeHighlight(relIndexArr: number[]) {
        relIndexArr
            .map(idx => this.elementAt(idx))
            .filter(rel => !!rel) // Ensure element exists
            .forEach(rel => {
                if (renderType(rel.type) === 'RenderShape') {
                    this.cachedHighlightShapeElements.delete(rel.relIndex);
                } else {
                    this.cachedHighlightStrokeElements.get(rel.type)?.delete(rel.relIndex);
                }
            });
        this.notifyCollectionChanges();
    }

    removeByIds(relIndexes: number[]) {
        relIndexes
            .map(idx => this.elementAt(idx))
            .filter(rel => !!rel) // Ensure element exists
            .forEach(rel => {
                if (renderType(rel.type) === 'RenderShape') {
                    this.cachedHighlightShapeElements.delete(rel.relIndex);
                    this.cachedShapeElements.delete(rel.relIndex);
                } else {
                    mapGetOrSet(this.cachedHighlightStrokeElements, rel.type, new Map()).delete(rel.relIndex);
                    mapGetOrSet(this.cachedStrokeElements, rel.type, new Map()).delete(rel.relIndex);
                }
                this._elements.delete(rel.relIndex);
                this.cachedPotentialSelection.delete(rel.relIndex);
                this.cachedSelectableElements.get(renderType(rel.type))?.delete(rel.relIndex);
                this.cachedUnselectableElements.get(renderType(rel.type))?.delete(rel.relIndex);
            });

        this.notifyCollectionChanges();
    }

    /**
     * Gets all elements currently in any highlight cache.
     * @returns An array of `GeoRenderElement` objects that are highlighted.
     */
    highlightingElements(): GeoRenderElement[] {
        const highlighting: GeoRenderElement[] = [];
        this.cachedHighlightStrokeElements.forEach(m => {
            m.forEach(obj => {
                highlighting.push(obj);
            });
        });
        this.cachedHighlightShapeElements.forEach(obj => {
            highlighting.push(obj);
        });
        return highlighting;
    }

    /**
     * Retrieves an element from the collection by its `relIndex`.
     * @param relIdx - The `relIndex` of the element.
     * @returns The `GeoRenderElement` if found, otherwise `undefined`.
     */
    elementAt(relIdx: number): GeoRenderElement | undefined {
        return this._elements.get(relIdx);
    }

    /**
     * Retrieves stroke-based elements of a specific type.
     * @template T - The specific type of `GeoRenderElement` expected.
     * @param type - The `GeoRelType` of the stroke elements to retrieve.
     * @param highlight - If `true`, retrieves from highlight cache; otherwise, from the main stroke cache. Defaults to `false`.
     * @returns An array of elements of type `T`.
     */
    strokeElements<T extends GeoRenderElement>(type: GeoRelType, highlight: boolean = false): T[] {
        const cache = highlight ? this.cachedHighlightStrokeElements : this.cachedStrokeElements;
        const elementMap = cache.get(type) ?? new Map<number, GeoRenderElement>();
        return Array.from(elementMap.values()) as T[];
    }

    /**
     * Retrieves shape-based elements.
     * @param highlight - If `true`, retrieves from highlight cache; otherwise, from the main shape cache. Defaults to `false`.
     * @returns An array of `GeoRenderElement` objects.
     */
    shapes(highlight: boolean = false): FillType[] {
        const cache = highlight ? this.cachedHighlightShapeElements : this.cachedShapeElements;
        return Array.from(cache.values());
    }

    /**
     * Retrieves all line segment elements.
     * @param highlight - If `true`, retrieves highlighted line segments. Defaults to `false`.
     * @returns An array of `RenderLineSegment` objects.
     */
    lineSegments(highlight: boolean = false): RenderLineSegment[] {
        return this.strokeElements('RenderLineSegment', highlight);
    }

    /**
     * Retrieves all vector elements.
     * @param highlight - If `true`, retrieves highlighted vectors. Defaults to `false`.
     * @returns An array of `RenderVector` objects.
     */
    vectors(highlight: boolean = false): RenderVector[] {
        return this.strokeElements('RenderVector', highlight);
    }

    /**
     * Retrieves all ray elements.
     * @param highlight - If `true`, retrieves highlighted rays. Defaults to `false`.
     * @returns An array of `RenderRay` objects.
     */
    rays(highlight: boolean = false): RenderRay[] {
        return this.strokeElements('RenderRay', highlight);
    }

    /**
     * Retrieves all line elements.
     * @param highlight - If `true`, retrieves highlighted lines. Defaults to `false`.
     * @returns An array of `RenderLine` objects.
     */
    lines(highlight: boolean = false): RenderLine[] {
        return this.strokeElements('RenderLine', highlight);
    }

    /**
     * Retrieves all angle elements.
     * @param highlight - If `true`, retrieves highlighted angles. Defaults to `false`.
     * @returns An array of `RenderAngle` objects.
     */
    angles(highlight: boolean = false): RenderAngle[] {
        return this.strokeElements('RenderAngle', highlight);
    }

    /**
     * Retrieves all vertex elements.
     * @param highlight - If `true`, retrieves highlighted vertexes. Defaults to `false`.
     * @returns An array of `RenderVertex` objects.
     */
    vertexes(highlight: boolean = false): RenderVertex[] {
        return this.strokeElements('RenderVertex', highlight);
    }

    /**
     * Retrieves all sector elements.
     * @param highlight - If `true`, retrieves highlighted sectors. Defaults to `false`.
     * @returns An array of `RenderSector` objects.
     */
    sectors(highlight: boolean = false): RenderSector[] {
        return this.strokeElements('RenderSector', highlight);
    }

    /**
     * Retrieves all ellipse elements.
     * @param highlight - If `true`, retrieves highlighted ellipses. Defaults to `false`.
     * @returns An array of `RenderEllipse` objects.
     */
    ellipses(highlight: boolean = false): RenderEllipse[] {
        return this.strokeElements('RenderEllipse', highlight);
    }

    /**
     * Retrieves all circle elements.
     * @param highlight - If `true`, retrieves highlighted circles. Defaults to `false`.
     * @returns An array of `RenderCircle` objects.
     */
    circles(highlight: boolean = false): RenderCircle[] {
        return this.strokeElements('RenderCircle', highlight);
    }

    /**
     * Retrieves selectable elements of a specific render type.
     * @param type - The `GeoRenderType` of elements to retrieve.
     * @returns An array of `GeoRenderElement` objects that are selectable.
     */
    selectableElements(type: GeoRenderType): GeoRenderElement[] {
        return Array.from((this.cachedSelectableElements.get(type) ?? new Map()).values());
    }

    /**
     * Retrieves unselectable elements of a specific render type.
     * @param type - The `GeoRenderType` of elements to retrieve.
     * @returns An array of `GeoRenderElement` objects that are unselectable.
     */
    unselectableElements(type: GeoRenderType): GeoRenderElement[] {
        return Array.from((this.cachedUnselectableElements.get(type) ?? new Map()).values());
    }
}

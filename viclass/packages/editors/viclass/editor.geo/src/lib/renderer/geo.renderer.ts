import {
    BoardViewportManager,
    DefaultEventData,
    DefaultEventEmitter,
    GraphicLayerCtrl,
    Position,
    ScreenPosition,
    VEventListener,
} from '@viclass/editor.core';
import { Subscription } from 'rxjs';
import { syncUpdateDocStateCommand } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import {
    DocRenderProp,
    GeoObjCollection,
    GeoRenderElement,
    RenderAngle,
    RenderLineSegment,
    RenderVertex,
} from '../model';
import { GeoLayer } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';

/**
 * Abstract base class for rendering geometric objects on a canvas.
 * It handles the common logic for managing geometric elements, viewport interactions,
 * coordinate transformations, and rendering lifecycle.
 */
export abstract class GeoRenderer {
    /** Emitter for element change events. */
    private readonly onElementChange: DefaultEventEmitter<DefaultEventData<string, any>> = new DefaultEventEmitter();

    /** The HTML canvas element used for rendering. */
    protected readonly canvas: HTMLCanvasElement;
    /** The rendering context (2D or WebGL). */
    protected readonly context: CanvasRenderingContext2D | WebGLRenderingContext;
    /** Stores the target zoom level during a zoom operation. */
    private zoomingToLevel: number;
    /** Represents the state of the geographic layer. */
    readonly layerState: GeoLayer;
    /** Flag indicating if the collection of geometric objects has been updated. */
    protected isCollectionUpdated: boolean = false;

    /** Collection of original geometric objects. */
    protected readonly originObjects: GeoObjCollection = new GeoObjCollection();
    /** Collection of preview geometric objects (e.g., during object creation). */
    protected readonly previewObjects: GeoObjCollection = new GeoObjCollection();

    /** Manages the viewport state (zoom, pan). */
    protected readonly viewport: BoardViewportManager;

    /** Subscription to document render property changes. */
    private readonly docRenderPropSub: Subscription;
    /** Subscription to updates in the original object collection. */
    private readonly isOriginCollectionUpdatedSub: Subscription;
    /** Subscription to updates in the preview object collection. */
    private readonly isPreviewCollectionUpdatedSub: Subscription;

    /**
     * Creates an instance of GeoRenderer.
     * @param docCtrl The controller for the geometry document.
     * @param layer The graphic layer controller.
     */
    constructor(
        public readonly docCtrl: GeoDocCtrl,
        public readonly layer: GraphicLayerCtrl
    ) {
        this.viewport = docCtrl.viewport as BoardViewportManager;
        this.layerState = layer.state as GeoLayer;
        this.canvas = layer.canvas;
        // Set a unique ID for the canvas element for easier debugging and identification.
        layer.nativeEl.setAttribute('id', `geo_${this.docCtrl.state.id}_${this.layerState.id}`);
        this.clearState();

        this.docRenderPropSub = docCtrl.docRenderProp$.subscribe((_docRenderProp: DocRenderProp) =>
            this.onContentVisiblePossiblyChanged()
        );

        this.isOriginCollectionUpdatedSub = this.originObjects.onCollectionUpdated$.subscribe(() => {
            this.isCollectionUpdated = true;
            this.onContentVisiblePossiblyChanged();
        });

        this.isPreviewCollectionUpdatedSub = this.previewObjects.onCollectionUpdated$.subscribe(() => {
            this.isCollectionUpdated = true;
            this.onContentVisiblePossiblyChanged();
        });
    }

    abstract scheduleRender();

    /** Gets the current zoom level of the document. */
    get zoomLevel(): number {
        return this.docCtrl.state.docRenderProp.scale;
    }

    /** Gets the screen unit, representing the size of one geometric unit on the screen. */
    get geoUnit(): number {
        return this.docCtrl.state.docRenderProp.screenUnit;
    }

    /** Gets the current look-at position (center of the viewport) in geometric coordinates. */
    get currLookAt(): Position {
        return {
            x: this.docCtrl.state.docRenderProp.translation[0],
            y: this.docCtrl.state.docRenderProp.translation[1],
            z: this.docCtrl.state.docRenderProp.translation[2],
        };
    }

    /** Gets the effective zoom unit, combining geoUnit and zoomLevel. */
    get zoomUnit(): number {
        return this.geoUnit * this.zoomLevel;
    }

    /** Gets the width of the renderable area in layer coordinates. */
    get width(): number {
        if (this.docCtrl.isBoundedView()) return this.layerState.boundary.width;
        return this.viewport.viewportWidth();
    }

    /** Gets the height of the renderable area in layer coordinates. */
    get height(): number {
        if (this.docCtrl.isBoundedView()) return this.layerState.boundary.height;
        return this.viewport.viewportHeight();
    }

    /** Gets the left boundary of the renderable area in geometric coordinates. */
    get left(): number {
        if (this.docCtrl.isBoundedView()) return -this.width / 2;
        return -this.width / 2 + this.viewport.currentLookAt.x;
    }

    /** Gets the right boundary of the renderable area in geometric coordinates. */
    get right(): number {
        if (this.docCtrl.isBoundedView()) return this.width / 2;
        return this.width / 2 + this.viewport.currentLookAt.x;
    }

    /** Gets the bottom boundary of the renderable area in geometric coordinates. */
    get bottom(): number {
        if (this.docCtrl.isBoundedView()) return -this.height / 2;
        return -this.height / 2 + this.viewport.currentLookAt.y;
    }

    /** Gets the top boundary of the renderable area in geometric coordinates. */
    get top(): number {
        if (this.docCtrl.isBoundedView()) return this.height / 2;
        return this.height / 2 + this.viewport.currentLookAt.y;
    }

    get previewing(): boolean {
        return this.previewObjects.size > 0;
    }

    /** Gets all usable elements from the original object collection. */
    get usableElements(): GeoRenderElement[] {
        return this.originObjects.usableElements;
    }

    /** Gets all elements that are potential selections from both original and preview collections. */
    get potentialSelection(): GeoRenderElement[] {
        return [...this.originObjects.potentialSelection, ...this.previewObjects.potentialSelection];
    }

    /**
     * Abstract method to clear the rendering board (canvas).
     * Must be implemented by subclasses.
     */
    abstract clearBoard();

    /** Clears unusable objects from both original and preview collections. */
    clearUnusableObject() {
        const o = this.originObjects.clearUnusableObject();
        const p = this.previewObjects.clearUnusableObject();
    }

    /** Clears all objects from both original and preview collections. */
    private clearState() {
        this.originObjects.clear();
        this.previewObjects.clear();
    }

    /**
     * Registers a listener for element change events.
     * @param l The event listener to register.
     */
    registerElementChange(l: VEventListener<DefaultEventData<string, GeoRenderElement>>) {
        this.onElementChange.registerListener(l);
    }

    /**
     * Unregisters a listener for element change events.
     * @param l The event listener to unregister.
     */
    unregisterElementChange(l: VEventListener<DefaultEventData<string, GeoRenderElement>>) {
        this.onElementChange.unregisterListener(l);
    }

    /** Gets all currently highlighted elements from the original object collection. */
    highlightingElements(): GeoRenderElement[] {
        return this.originObjects.highlightingElements();
    }

    /** Clears all objects from the preview collection. */
    clearPreview() {
        this.previewObjects.clear();
    }

    private onContentVisiblePossiblyChanged(): void {
        const editor = this.docCtrl.editor as GeometryEditor;
        editor.notifyContentVisibilityChange(this.docCtrl);
        this.isCollectionUpdated = true;
        this.onElementChange.emit(new DefaultEventData('elementChange', ''));
    }

    /**
     * Retrieves a geometric element by its relative index from either
     * the original or preview collection.
     * @template T The type of the geometric element.
     * @param relIndex The relative index of the element.
     * @returns The geometric element, or undefined if not found.
     */
    elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined {
        return (this.originObjects.elementAt(relIndex) || this.previewObjects.elementAt(relIndex)) as T | undefined;
    }

    previewElAt<T extends GeoRenderElement>(relIndex: number): T | undefined {
        return this.previewObjects.elementAt(relIndex) as T;
    }

    realElAt<T extends GeoRenderElement>(relIndex: number): T {
        return this.originObjects.elementAt(relIndex) as T;
    }

    setFlag(id: number, flag: Partial<Pick<GeoRenderElement, 'usable' | 'valid' | 'deleted' | 'unselectable'>>) {
        const obj = this.elementAt(id);
        if (obj) Object.assign(obj, flag);
        else return;
        // remove and readd to ensure caches are updated
        if (id >= 0) {
            this.originObjects.removeByIds([id]);
            this.addActualElement(obj);
        } else {
            this.previewObjects.removeByIds([id]);
            this.addPreviewElement(obj);
        }
    }

    /**
     * Gets selectable point (vertex) elements.
     * @param preview If true, returns elements from the preview collection; otherwise, from the original collection.
     * @returns An array of point elements.
     */
    pointElements(preview: boolean = false): GeoRenderElement[] {
        return preview
            ? [...this.previewObjects.selectableElements('RenderVertex')]
            : [...this.originObjects.selectableElements('RenderVertex')];
    }

    /**
     * Gets selectable line elements.
     * @param preview If true, returns elements from the preview collection; otherwise, from the original collection.
     * @returns An array of line elements.
     */
    lineElements(preview: boolean = false): GeoRenderElement[] {
        return preview
            ? [...this.previewObjects.selectableElements('RenderLine')]
            : [...this.originObjects.selectableElements('RenderLine')];
    }

    /**
     * Gets selectable sector elements.
     * @param preview If true, returns elements from the preview collection; otherwise, from the original collection.
     * @returns An array of sector elements.
     */
    sectorElements(preview: boolean = false): GeoRenderElement[] {
        return preview
            ? [...this.previewObjects.selectableElements('RenderSector')]
            : [...this.originObjects.selectableElements('RenderSector')];
    }

    /**
     * Gets selectable ellipse elements.
     * @param preview If true, returns elements from the preview collection; otherwise, from the original collection.
     * @returns An array of ellipse elements.
     */
    ellipseElements(preview: boolean = false): GeoRenderElement[] {
        return preview
            ? [...this.previewObjects.selectableElements('RenderEllipse')]
            : [...this.originObjects.selectableElements('RenderEllipse')];
    }

    /**
     * Gets selectable circle elements.
     * @param preview If true, returns elements from the preview collection; otherwise, from the original collection.
     * @returns An array of circle elements.
     */
    circleElements(preview: boolean = false): GeoRenderElement[] {
        return preview
            ? [...this.previewObjects.selectableElements('RenderCircle')]
            : [...this.originObjects.selectableElements('RenderCircle')];
    }

    /**
     * Gets selectable shape elements.
     * @param preview If true, returns elements from the preview collection; otherwise, from the original collection.
     * @returns An array of shape elements.
     */
    shapeElements(preview: boolean = false): GeoRenderElement[] {
        return preview
            ? [...this.previewObjects.selectableElements('RenderShape')]
            : [...this.originObjects.selectableElements('RenderShape')];
    }

    /**
     * Gets angle elements.
     * Angles from the original collection are sorted by degree and then by space from arc to corner.
     * @param preview If true, returns elements from the preview collection; otherwise, from the original collection.
     * @returns An array of angle elements.
     */
    angleElements(preview: boolean = false): GeoRenderElement[] {
        return preview
            ? [...this.previewObjects.selectableElements('RenderAngle')]
            : [...this.originObjects.selectableElements('RenderAngle')].sort((a: RenderAngle, b: RenderAngle) => {
                  const aAngle = a.renderProp;
                  const bAngle = b.renderProp;

                  if (a.degree < b.degree) return 1;
                  if (a.degree > b.degree) return -1;

                  return aAngle.spaceFromArcToCorner < bAngle.spaceFromArcToCorner ? 1 : -1;
              });
    }

    /**
     * Adds a geometric element to the appropriate collection (original or preview).
     * @param element The geometric element to add.
     */
    addActualElement(element: GeoRenderElement) {
        this.originObjects.addRenderElement(element);
    }

    addPreviewElement(element: GeoRenderElement) {
        this.previewObjects.addRenderElement(element);
    }

    removePreviewByIds(previewEls: (GeoRenderElement | number)[]) {
        const ids = previewEls.map(preview => (typeof preview === 'number' ? preview : preview.relIndex));
        this.previewObjects.removeByIds(ids);
        this.onElementChange.emit(new DefaultEventData('elementChange', previewEls));
    }

    /**
     * Converts a length from layer coordinates to geometric coordinates.
     * @param l The length in layer coordinates.
     * @returns The length in geometric coordinates.
     */
    layerToGeoLength(l: number): number {
        return l / this.zoomUnit;
    }

    /**
     * Converts a length from geometric coordinates to layer coordinates.
     * @param l The length in geometric coordinates.
     * @returns The length in layer coordinates.
     */
    geoToLayerLength(l: number): number {
        return l * this.zoomUnit;
    }

    /**
     * Converts coordinates from geometric space to layer (screen) space.
     * @param geoCoord An array [x, y] representing geometric coordinates.
     * @returns A ScreenPosition object {x, y} representing layer coordinates.
     */
    geoToLayerCoord(geoCoord: number[]): ScreenPosition {
        return {
            x: this.geoToLayerX(geoCoord[0]),
            y: this.geoToLayerY(geoCoord[1]),
        };
    }

    /**
     * Converts a position from geometric space to layer (screen) space.
     * @param pos A Position object {x, y, z} representing geometric coordinates.
     * @returns A ScreenPosition object {x, y} representing layer coordinates.
     */
    geoToLayerPos(pos: Position): ScreenPosition {
        return {
            x: this.geoToLayerX(pos.x),
            y: this.geoToLayerY(pos.y),
        };
    }

    /**
     * Converts a position from layer (screen) space to geometric space.
     * @param layerPos A ScreenPosition object {x, y} representing layer coordinates.
     * @returns A Position object {x, y} representing geometric coordinates (z is implicitly 0).
     */
    layerToGeoPos(layerPos: ScreenPosition): Position {
        return {
            x: this.layerToGeoX(layerPos.x),
            y: this.layerToGeoY(layerPos.y),
        };
    }

    /**
     * Converts an x-coordinate from layer space to geometric space.
     * @param x The x-coordinate in layer space.
     * @returns The x-coordinate in geometric space.
     */
    layerToGeoX(x: number): number {
        return x / this.zoomUnit + this.currLookAt.x;
    }

    /**
     * Converts a y-coordinate from layer space to geometric space.
     * Note: Y-axis is typically inverted between screen and geometric contexts.
     * @param y The y-coordinate in layer space.
     * @returns The y-coordinate in geometric space.
     */
    layerToGeoY(y: number): number {
        return y / this.zoomUnit - this.currLookAt.y;
    }

    /**
     * Converts an x-coordinate from geometric space to layer space.
     * @param x The x-coordinate in geometric space.
     * @returns The x-coordinate in layer space.
     */
    geoToLayerX(x: number): number {
        return (x - this.currLookAt.x) * this.zoomUnit;
    }

    /**
     * Converts a y-coordinate from geometric space to layer space.
     * Note: Y-axis is typically inverted between screen and geometric contexts.
     * @param y The y-coordinate in geometric space.
     * @returns The y-coordinate in layer space.
     */
    geoToLayerY(y: number): number {
        return (y + this.currLookAt.y) * this.zoomUnit;
    }

    /**
     * Abstract method to perform the actual rendering of geometric objects.
     * Must be implemented by subclasses.
     */
    abstract render();

    /**
     * Cleans up resources when the renderer is removed.
     * This includes clearing objects, the board, and unsubscribing from observables.
     */
    onRemoved(): void {
        this.clearUnusableObject();
        this.clearState();
        this.clearBoard();
        this.layer.onRemoved();
        this.docRenderPropSub?.unsubscribe();
        this.isOriginCollectionUpdatedSub?.unsubscribe();
        this.isPreviewCollectionUpdatedSub?.unsubscribe();
    }

    /**
     * Highlights elements in the original object collection.
     * @param elIndexArr An array of relative indices of elements to highlight.
     */
    highlight(elIndexArr: number[]) {
        this.originObjects.highlight(elIndexArr);
    }

    /**
     * Removes highlighting from elements in the original object collection.
     * @param elIndexArr An array of relative indices of elements to remove highlight from.
     */
    removeHighlight(elIndexArr: number[]) {
        this.originObjects.removeHighlight(elIndexArr);
    }

    /**
     * Adds elements to the potential selection list in both original and preview collections.
     * @param elIndexArr An array of relative indices of elements to add.
     */
    addPotentialSelection(elIndexArr: number[]) {
        this.originObjects.addPotentialSelection(elIndexArr);
        this.previewObjects.addPotentialSelection(elIndexArr);
    }

    /**
     * Removes elements from the potential selection list in both original and preview collections.
     * @param elIndexArr An array of relative indices of elements to remove.
     */
    removePotentialSelection(elIndexArr: number[]) {
        this.originObjects.removePotentialSelection(elIndexArr);
        this.previewObjects.removePotentialSelection(elIndexArr);
    }

    /**
     * Zooms the view to a specified level, optionally around a zoom root point.
     * @param level The target zoom level.
     * @param zoomRoot The point (in geometric coordinates) to zoom around. If undefined, zooms around the current center.
     */
    zoom(level: number, zoomRoot?: Position) {
        let lx = this.currLookAt.x;
        let ly = this.currLookAt.y;
        if (zoomRoot) {
            // Calculate the new look-at position to keep the zoomRoot point stationary on the screen.
            lx = zoomRoot.x - ((zoomRoot.x - lx) * this.zoomUnit) / (level * this.geoUnit);
            ly = ((ly + zoomRoot.y) * this.zoomUnit) / (level * this.geoUnit) - zoomRoot.y;
        }

        this.zoomingToLevel = level; // Store the target zoom level

        this.lookAt(lx, ly); // Apply the new look-at position (and zoom level if changed)

        syncUpdateDocStateCommand(this.docCtrl); // Synchronize the document state
    }

    /**
     * Sets the center of the viewport to the specified geometric coordinates.
     * Also applies any pending zoom level change.
     * @param xInGeoCoord The x-coordinate in geometric space.
     * @param yInGeoCoord The y-coordinate in geometric space.
     */
    lookAt(xInGeoCoord: number, yInGeoCoord: number) {
        const docRenderProp = this.docCtrl.state.docRenderProp;
        docRenderProp.translation[0] = xInGeoCoord;
        docRenderProp.translation[1] = yInGeoCoord;

        // If a zoom operation was initiated and the level is different, apply it.
        if (this.zoomingToLevel && this.zoomingToLevel != docRenderProp.scale) {
            docRenderProp.scale = this.zoomingToLevel;
            delete this.zoomingToLevel; // Clear the pending zoom level
        }

        this.docCtrl.rendererCtrl.render(); // Trigger a re-render
    }

    /**
     * Calculates the length of a line segment in geometric (model) coordinates.
     * @param line The RenderLineSegment object.
     * @returns The length of the line segment, or 0 if start or end point is not found.
     */
    getGeoLineLength(line: RenderLineSegment): number {
        const startPoint: RenderVertex = this.elementAt(line.startPointIdx);
        const endPoint: RenderVertex = this.elementAt(line.endPointIdx);
        if (!startPoint || !endPoint) return 0;

        // Calculate distance using the Pythagorean theorem on geometric coordinates.
        return Math.sqrt(
            Math.pow(startPoint.coords[0] - endPoint.coords[0], 2) +
                Math.pow(startPoint.coords[1] - endPoint.coords[1], 2)
        );
    }

    /**
     * Determines the shorter side length of an angle in geometric (model) coordinates.
     * An angle is defined by up to two line segments.
     * @param renderAngle The RenderAngle object.
     * @returns The length of the shorter side, or undefined if one or both line segments are missing.
     */
    getGeoMinLengthOfAngleSide(renderAngle: RenderAngle): number | undefined {
        // Retrieve line segments from the angle definition using their relative indices.
        const startLineSegment = renderAngle.lineRelIdxes?.[0]
            ? (this.elementAt(renderAngle.lineRelIdxes[0]) as RenderLineSegment)
            : undefined;
        const endLineSegment = renderAngle.lineRelIdxes?.[1]
            ? (this.elementAt(renderAngle.lineRelIdxes[1]) as RenderLineSegment)
            : undefined;

        // If either line segment is missing, the minimum length cannot be determined.
        if (!startLineSegment || !endLineSegment) return undefined;

        // Compute lengths of both sides in geometric coordinates and return the smaller one.
        return Math.min(this.getGeoLineLength(startLineSegment), this.getGeoLineLength(endLineSegment));
    }
}

import { BoundaryDelegator } from '@viclass/editor.coordinator/classroom';
import {
    AwarenessFeature,
    BoardViewportManager,
    BoundaryRectangle,
    BoundedGraphicLayerCtrl,
    CmdChannel,
    ContentVisibilityCheckFeature,
    CRUDChangeResult,
    CRUDDelegator,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocumentEditor,
    DocumentId,
    EditorBase,
    EditorCoordinator,
    EditorId,
    EditorType,
    ErrorHandlerDecorator,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FEATURE_AWARENESS,
    FEATURE_CONTENT_VISIBILITY,
    FEATURE_CRD_DOC,
    FEATURE_CRUD,
    FEATURE_HISTORY,
    FEATURE_INTERNAL_SELECTION,
    FEATURE_PAN,
    FEATURE_REMOVE,
    FEATURE_ROB,
    FEATURE_SELECTION,
    FEATURE_ZOOM,
    GraphicLayerCtrl,
    HasSelectionFeature,
    HistoryFeature,
    initRandomAwarenessId,
    InsertDocCtrlDelegator,
    LayerOptions,
    LoadingContext,
    LocatableEvent,
    OperationMode,
    PanFeature,
    reliableCmdMeta,
    reliableSaveCmdMeta,
    ROBFeature,
    SelectContext,
    SelectDelegator,
    SelectionFeature,
    SupportContentVisibilityCheckFeature,
    SupportInternalSelectFeature,
    SupportRemoveFeature,
    SupportSelectFeature,
    ThrottleCombinator,
    ToolBar,
    UnboundedGraphicLayerCtrl,
    VDocLayerCtrl,
    ViewportId,
    ViewportManager,
    ZoomFeature,
} from '@viclass/editor.core';
import { CmdTypeProto, GeoKind } from '@viclass/proto/editor.geo';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import {
    convertDocRenderPropToProto,
    convertSettingPropertiesToProto,
    deserializer,
    GeometryCmdProcessor,
    HighlightElementsCmd,
    PotentialSelectionElementsCmd,
    RemoveHighlightElementsCmd,
    RemovePotentialSelectionElementsCmd,
    syncRenderCommands,
    UpdateDocStateCmd,
    UpdateElementsPropCmd,
} from './cmd';
import { geoDefaultHandlerFn } from './error-handler';
import { GeoGateway } from './geo.gateway';
import { GeometryToolBar } from './geo.toolbar';
import {
    DocRenderPropHistoryItem,
    ElementPropHistoryItem,
    GeoHistoryItem,
    ReconstructionHistoryItem,
    RenameElementHistoryItem,
    RenderElementHistoryItem,
} from './history';
import {
    _angleGeoRenderProp,
    _circleGeoRenderProp,
    _circleShapeGeoRenderProp,
    _ellipseGeoRenderProp,
    _ellipseShapeGeoRenderProp,
    _lineGeoRenderProp,
    _lineSegmentGeoRenderProp,
    _pointGeoRenderProp,
    _polygonGeoRenderProp,
    _sectorGeoRenderProp,
    _sectorShapeGeoRenderProp,
    FetchDocResponse,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
} from './model';
import {
    GeoDoc,
    GeoDocInitData,
    GeoEditorConfig,
    GeoLayer,
    GeometryToolType,
    GeoObjectType,
    GeoObjectTypeValue,
} from './model/geo.models';
import { GeoDocCtrl, GeoHitContextDetails, GeoSelectHitContext } from './objects';
import { Geo2dRenderer } from './renderer';
import {
    CreateAngleByThreePointsTool,
    CreateAngleTool,
    CreateCircleTool,
    CreateEllipseTool,
    CreateEquilateralTriangleTool,
    CreateIsoscelesRightTriangleTool,
    CreateIsoscelesTriangleTool,
    CreateLineSegmentTool,
    CreateLineTool,
    CreateParallelogramTool,
    CreatePointTool,
    CreatePolygonTool,
    CreateRayTool,
    CreateRectangleTool,
    CreateRegularPolygonTool,
    CreateRhombusTool,
    CreateRightTriangleTool,
    CreateSectorTool,
    CreateSemicircleTool,
    CreateSquareTool,
    CreateTrapezoidTool,
    CreateTriangleTool,
    CreateVectorTool,
    GeoCreateDocumentTool,
    InputCommandTool,
    IntersectionPointTool,
    ListElementTool,
    MiddlePointTool,
    NamingElementTool,
    PointOnObjectTool,
    RenameElementTool,
    UpdatePropTool,
} from './tools';
import { CreateBisectorLineTool } from './tools/create.bi_sector.tool';
import { CreateSymmetricThroughLineTool, CreateSymmetricThroughPointTool } from './tools/create.symmetry.tool';
import { GeoPanTool } from './tools/geo.pan.tool';
import { GeoZoomTool } from './tools/geo.zoom.tool';
import { MoveElementTool } from './tools/move.element.tool';
import { CreateParallelLineTool } from './tools/parallel.line.tool';
import { CreatePerpendicularLineTool } from './tools/perpendicular.line.tool';
import { addRemoveElementHistoryItem, defaultDocRenderProp } from './tools/util.tool';

export class GeometryEditor
    extends EditorBase<GeoDocCtrl>
    implements
        DocumentEditor,
        SupportInternalSelectFeature,
        SupportContentVisibilityCheckFeature,
        SupportRemoveFeature,
        HasSelectionFeature
{
    private readonly _editorType: EditorType;
    private readonly _id: EditorId;
    private readonly _cmdChannel: CmdChannel;
    private readonly _cmdProcessor: GeometryCmdProcessor;
    private readonly _geoGateway: GeoGateway;

    readonly supportFeature: Map<string, any> = new Map();

    selectionFeature: SelectionFeature;
    panFeature: PanFeature;
    zoomFeature: ZoomFeature;
    awarenessFeature: AwarenessFeature;
    private historyFeature: HistoryFeature;
    private robFeature: ROBFeature;

    readonly selectDelegator = new SelectDelegator<GeoDocCtrl>(this);

    readonly insertDocDelegator = new InsertDocCtrlDelegator<GeoDocCtrl, GeoDoc>(
        this,
        (vp, state) => new GeoDocCtrl(this, state, vp as BoardViewportManager)
    );

    private readonly objTypeStr: Map<GeoObjectType, string> = new Map();

    filterElementFunc: (el: GeoRenderElement) => boolean;

    crdFeature: DocCRDFeature;
    private contentVisibilityFeature: ContentVisibilityCheckFeature;

    get geoGateway(): GeoGateway {
        return this._geoGateway;
    }

    get id(): number {
        return this._id;
    }

    get editorType(): EditorType {
        return this._editorType;
    }

    get cmdChannel(): CmdChannel {
        return this._cmdChannel;
    }

    get coordinator(): EditorCoordinator {
        return this._coordinator;
    }

    // TODO: here
    private readonly _operationMode: OperationMode;

    get operationMode(): OperationMode {
        return this._operationMode;
    }

    readonly awarenessConstructId = initRandomAwarenessId();

    static readonly cmdChannelThrottle = 300;

    disableJsHighlight?: boolean;

    readonly boundaryDelegator: BoundaryDelegator = new BoundaryDelegator(this);

    constructor(
        readonly geoEditorConf: GeoEditorConfig,
        private readonly _coordinator: EditorCoordinator
    ) {
        super(geoEditorConf);

        this._id = geoEditorConf.id;
        this._editorType = geoEditorConf.editorType;
        this._geoGateway = new GeoGateway(geoEditorConf.apiUri);
        this._operationMode = geoEditorConf.operationMode || OperationMode.CLOUD;

        this._cmdProcessor = new GeometryCmdProcessor(this);
        this._cmdChannel = _coordinator.registerCmdChannel(this._id);
        this._cmdChannel.registerCmdListener(this._cmdProcessor);
        this._cmdChannel.registerDeserializer(deserializer);
        this._cmdChannel.registerCombinator(FCCmdTypeProto.PREVIEW_BOUNDARY, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(
            FCCmdTypeProto.UPDATE_BOUNDARY,
            new ThrottleCombinator(GeometryEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(CmdTypeProto.PREVIEW_LINE, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(CmdTypeProto.PREVIEW_POLYGON, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(CmdTypeProto.PREVIEW_ELLIPSE_SHAPE, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(CmdTypeProto.PREVIEW_CIRCLE_SHAPE, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(CmdTypeProto.PREVIEW_CIRCULAR_SECTOR_SHAPE, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(CmdTypeProto.PREVIEW_ANGLE, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(
            CmdTypeProto.UPDATE_DOC_STATE,
            new ThrottleCombinator(GeometryEditor.cmdChannelThrottle)
        );

        this.supportFeature.set(FEATURE_HISTORY, this);
        this.supportFeature.set(FEATURE_SELECTION, this.selectDelegator);
        this.supportFeature.set(FEATURE_INTERNAL_SELECTION, this);
        this.supportFeature.set(FEATURE_ROB, this);
        this.supportFeature.set(FEATURE_REMOVE, this);
        this.supportFeature.set(
            FEATURE_CRUD,
            new CRUDDelegator(
                this,
                this.cmdChannel,
                this.regMan,
                { doc: this.docReg, layer: this.layerReg },
                this.generateInitDocData.bind(this)
            )
        );
        this.supportFeature.set(FEATURE_CONTENT_VISIBILITY, this);
        this.supportFeature.set(FEATURE_AWARENESS, this);
        this.resetFilterElementFunc();
    }

    getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined {
        return undefined;
    }

    async initialize(): Promise<void> {
        // nothing to do
    }

    getSelectionFeature(): SelectionFeature {
        return this.selectionFeature;
    }

    async duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>> {
        return await this.geoGateway.duplicateDoc(docGlobalIds);
    }

    resetFilterElementFunc() {
        this.filterElementFunc = el => true;
    }

    checkHitInternal(
        layer: VDocLayerCtrl,
        event: LocatableEvent<any>,
        useRelaxedHitPrecision = false,
        preview = false
    ): GeoSelectHitContext {
        if (!layer || !layer.doc || !(layer.doc instanceof GeoDocCtrl)) return undefined;
        return (layer.doc as GeoDocCtrl).checkHit(
            event,
            layer as BoundedGraphicLayerCtrl,
            useRelaxedHitPrecision,
            preview
        );
    }

    highlight(ctx: GeoSelectHitContext) {
        if (!ctx) return;
        const elIndexArr = [ctx.hitDetails.el.relIndex];
        const els = ctx.doc.selectedElements;
        if (els && els.length >= 0) {
            for (const el of els) {
                if (elIndexArr.indexOf(el.relIndex) === -1) elIndexArr.push(el.relIndex);
            }
        }
        const layerCtrl = ctx.doc.layers[0];
        const meta = reliableCmdMeta(
            ctx.doc.viewport,
            ctx.doc.state.id,
            ctx.doc.state.id,
            CmdTypeProto.HIGHLIGHT_ELEMENTS
        );
        const cmd = new HighlightElementsCmd(meta);
        cmd.setState(layerCtrl.state.id, elIndexArr);
        this.cmdChannel.receive(cmd);
    }

    potentialSelection(ctx: GeoSelectHitContext) {
        if (!ctx) return;
        const elIndexArr = [ctx.hitDetails.el.relIndex];
        const layerCtrl = ctx.doc.layers[0];
        const meta = reliableCmdMeta(
            ctx.doc.viewport,
            ctx.doc.state.id,
            ctx.doc.state.id,
            CmdTypeProto.POTENTIAL_SELECTION_ELEMENTS
        );
        const cmd = new PotentialSelectionElementsCmd(meta);
        cmd.setState(layerCtrl.state.id, elIndexArr);
        this.cmdChannel.receive(cmd);
    }

    removePotentialSelection(ctx: GeoSelectHitContext) {
        if (!ctx) return;
        const hitDetailsArr: GeoHitContextDetails[] = ctx.hitDetails ? [ctx.hitDetails] : [];
        const elIndexArr = hitDetailsArr.map(d => d.el.relIndex);
        const layerCtrl = ctx.doc.layers[0];
        const meta = reliableCmdMeta(
            ctx.doc.viewport,
            ctx.doc.state.id,
            ctx.doc.state.id,
            CmdTypeProto.REMOVE_POTENTIAL_SELECTION_ELEMENTS
        );
        const cmd = new RemovePotentialSelectionElementsCmd(meta);
        cmd.setState(layerCtrl.state.id, elIndexArr);
        this.cmdChannel.receive(cmd);
    }

    removeHighlight(ctx: GeoSelectHitContext) {
        if (!ctx) return;
        const hitDetailsArr: GeoHitContextDetails[] = ctx.hitDetails ? [ctx.hitDetails] : [];
        let elIndexArr = hitDetailsArr.map(d => d.el.relIndex);
        const els = ctx.doc.selectedElements?.map(e => e.relIndex);
        if (els && els.length >= 0) elIndexArr = elIndexArr.filter(idx => !els.includes(idx));
        const layerCtrl = ctx.doc.layers[0];
        const meta = reliableCmdMeta(
            ctx.doc.viewport,
            ctx.doc.state.id,
            ctx.doc.state.id,
            CmdTypeProto.REMOVE_HIGHLIGHT_ELEMENTS
        );
        const cmd = new RemoveHighlightElementsCmd(meta);
        cmd.setState(layerCtrl.state.id, elIndexArr);
        this.cmdChannel.receive(cmd);
    }

    /**
     * Selects a geometric element in the document
     * @param ctx The selection hit context containing document and element info
     * @param multiple Whether to allow multiple selection (e.g. with Ctrl key)
     */
    selectElement(ctx: GeoSelectHitContext, multiple = false) {
        const details = ctx.hitDetails as GeoHitContextDetails;
        if (!details) return;

        const el = details.el;
        const depEls = new Map<number, GeoRenderElement>();

        // Collect dependent elements based on element type
        switch (el.type) {
            case 'RenderCircleShape': {
                const curve = el as RenderCircleShape;
                this.collectDependentElements(
                    ctx.doc,
                    [...(curve.vertexRelIdxes || []), curve.arcRelIdx, curve.centerPointIdx],
                    depEls
                );
                break;
            }
            case 'RenderEllipseShape': {
                const ellipse = el as RenderEllipseShape;
                this.collectDependentElements(
                    ctx.doc,
                    [...(ellipse.vertexRelIdxes || []), ellipse.arcRelIdx, ellipse.f1Idx, ellipse.f2Idx],
                    depEls
                );
                break;
            }
            case 'RenderSectorShape': {
                const sector = el as RenderSectorShape;
                this.collectDependentElements(
                    ctx.doc,
                    [
                        ...(sector.vertexRelIdxes || []),
                        sector.arcRelIdx,
                        sector.centerPointIdx,
                        sector.endPointIdx,
                        sector.startPointIdx,
                    ],
                    depEls
                );
                this.collectLineElements(ctx.doc, sector.lineRelIdxes, depEls);
                break;
            }
            case 'RenderPolygon': {
                const polygon = el as RenderPolygon;
                this.collectDependentElements(ctx.doc, [...(polygon.vertexRelIdxes || [])], depEls);
                this.collectLineElements(ctx.doc, polygon.lineRelIdxes, depEls);
                break;
            }
            case 'RenderAngle': {
                const angle = el as RenderAngle;
                this.collectDependentElements(
                    ctx.doc,
                    [...(angle.vertexRelIdxes || []), angle.anglePointIdx, angle.centerAngle],
                    depEls
                );
                this.collectLineElements(ctx.doc, angle.lineRelIdxes, depEls);
                break;
            }
            case 'RenderLine':
            case 'RenderLineSegment':
            case 'RenderVector':
            case 'RenderRay':
                const l = el as RenderLine;
                this.collectDependentElements(ctx.doc, [...(l.vertexRelIdxes || [])], depEls);
                break;
        }

        // Get current selection
        const selectedEls = ctx.doc.selectedElements.reduce((acc, el) => {
            if (el) acc.set(el.relIndex, el);
            return acc;
        }, new Map<number, GeoRenderElement>());

        // Update selection based on multiple flag
        if (multiple) {
            if (!selectedEls.has(el.relIndex)) {
                // Add element and dependencies
                selectedEls.set(el.relIndex, el);
                depEls.forEach((depEl, idx) => selectedEls.set(depEl.relIndex, depEl));
            } else {
                // Remove element and dependencies
                selectedEls.delete(el.relIndex);
                depEls.forEach((depEl, idx) => selectedEls.delete(depEl.relIndex));
            }
        } else {
            // Replace selection
            selectedEls.clear();
            selectedEls.set(el.relIndex, el);
            depEls.forEach((depEl, idx) => selectedEls.set(depEl.relIndex, depEl));
        }

        // Update document and send highlight command
        ctx.doc.updateSelectedElements(Array.from(selectedEls.values()));
        this.highlightElements(ctx.doc, Array.from(selectedEls.keys()));
    }

    private collectDependentElements(doc: GeoDocCtrl, indices: number[], result: Map<number, GeoRenderElement>) {
        indices.forEach((idx, mapIdx) => {
            const el = doc.rendererCtrl.elementAt(idx);
            if (el) result.set(el.relIndex, el);
        });
    }

    private collectLineElements(
        doc: GeoDocCtrl,
        lineIndices: number[] | undefined,
        result: Map<number, GeoRenderElement>
    ) {
        if (!lineIndices) return;
        lineIndices.forEach(idx => {
            const el = doc.rendererCtrl.elementAt(idx) as RenderLine;
            if (el) {
                result.set(el.relIndex, el);
                el.vertexRelIdxes.forEach(vIdx => {
                    const vertex = doc.rendererCtrl.elementAt(vIdx);
                    if (vertex) result.set(vertex.relIndex, vertex);
                });
            }
        });
    }

    private highlightElements(doc: GeoDocCtrl, indices: number[]) {
        const layerCtrl = doc.layers[0];
        const meta = reliableCmdMeta(doc.viewport, doc.state.id, doc.state.id, CmdTypeProto.HIGHLIGHT_ELEMENTS);
        const cmd = new HighlightElementsCmd(meta);
        cmd.setState(layerCtrl.state.id, indices);
        this.cmdChannel.receive(cmd);
    }

    removeSelectedElement(ctx: GeoSelectHitContext) {
        // Create a map of selected elements from the document's selected elements
        const selectedEls = ctx.doc.selectedElements.reduce((acc, el) => {
            if (el) acc.set(el.relIndex, el);
            return acc;
        }, new Map<number, GeoRenderElement>());

        // If no elements are selected, exit early
        if (selectedEls.size === 0) return;

        const details = ctx.hitDetails as GeoHitContextDetails;

        // Collect dependent elements based on the type of the selected element
        const deps = new Map<number, GeoRenderElement>();
        switch (details.el.type) {
            case 'RenderCircleShape': {
                const curve = details.el as RenderCircleShape;
                this.collectDependentElements(
                    ctx.doc,
                    [...(curve.vertexRelIdxes || []), curve.arcRelIdx, curve.centerPointIdx],
                    deps
                );
                break;
            }
            case 'RenderEllipseShape': {
                const ellipse = details.el as RenderEllipseShape;
                this.collectDependentElements(
                    ctx.doc,
                    [...(ellipse.vertexRelIdxes || []), ellipse.arcRelIdx, ellipse.f1Idx, ellipse.f2Idx],
                    deps
                );
                break;
            }
            case 'RenderSectorShape': {
                const sector = details.el as RenderSectorShape;
                this.collectDependentElements(
                    ctx.doc,
                    [
                        ...(sector.vertexRelIdxes || []),
                        sector.arcRelIdx,
                        sector.centerPointIdx,
                        sector.endPointIdx,
                        sector.startPointIdx,
                    ],
                    deps
                );
                this.collectLineElements(ctx.doc, sector.lineRelIdxes, deps);
                break;
            }
            case 'RenderPolygon': {
                const polygon = details.el as RenderPolygon;
                this.collectDependentElements(ctx.doc, [...(polygon.vertexRelIdxes || [])], deps);
                this.collectLineElements(ctx.doc, polygon.lineRelIdxes, deps);
                break;
            }
            case 'RenderAngle': {
                const angle = details.el as RenderAngle;
                this.collectDependentElements(
                    ctx.doc,
                    [...(angle.vertexRelIdxes || []), angle.anglePointIdx, angle.centerAngle],
                    deps
                );
                this.collectLineElements(ctx.doc, angle.lineRelIdxes, deps);
                break;
            }
            case 'RenderLine':
            case 'RenderLineSegment':
            case 'RenderVector':
            case 'RenderRay':
                const l = details.el as RenderLine;
                this.collectDependentElements(ctx.doc, [...(l.vertexRelIdxes || [])], deps);
                break;
        }

        // Remove the selected element and its dependencies from the selection
        const excludeIndices = new Set([details.el.relIndex, ...Array.from(deps.keys())]);
        Array.from(excludeIndices.keys()).forEach(key => selectedEls.delete(key));

        // Update the document with the filtered elements
        ctx.doc.updateSelectedElements(Array.from(selectedEls.values()));

        // Create and send a command to remove highlight from the element
        const elIndexArr = [details.el.relIndex, ...Array.from(deps.keys())];
        const layerCtrl = ctx.doc.layers[0];

        const meta = reliableCmdMeta(
            ctx.doc.viewport,
            ctx.doc.state.id,
            ctx.doc.state.id,
            CmdTypeProto.REMOVE_HIGHLIGHT_ELEMENTS
        );
        const cmd = new RemoveHighlightElementsCmd(meta);
        cmd.setState(layerCtrl.state.id, elIndexArr);

        this.cmdChannel.receive(cmd);
    }

    featureSupporter<T>(featureKey: string): T {
        const f = this.supportFeature.get(featureKey);
        if (f) return f as unknown as T;
        throw new Error(`Geo Editor doesn't support feature ${featureKey}`);
    }

    initCreateDocTool(viewport: ViewportManager) {
        const createToolType: GeometryToolType = 'CreateDocumentTool';
        const createTool = this.crdFeature.generateCRDToolForViewport(
            viewport.id,
            createToolType,
            this,
            this.cmdChannel,
            this.robFeature,
            this.getSelectionFeature()
        );

        return createTool;
    }

    createToolbar(): ToolBar<any, any> {
        const toolBar = new GeometryToolBar(this, this.objTypeStr);

        toolBar.addTool('MoveElementTool', new MoveElementTool(this, toolBar)); // here
        toolBar.addTool('InputCommandTool', new InputCommandTool(this, toolBar)); // here
        toolBar.addTool('UpdatePropTool', new UpdatePropTool(this, toolBar));
        toolBar.addTool('RenameElementTool', new RenameElementTool(this, toolBar));
        toolBar.addTool('NamingElementTool', new NamingElementTool(this, toolBar));
        toolBar.addTool('ListElementTool', new ListElementTool(this, toolBar));

        toolBar.addTool('CreatePointTool', new CreatePointTool(this, toolBar));
        toolBar.addTool('PointOnObjectTool', new PointOnObjectTool(this, toolBar));
        toolBar.addTool('IntersectionPointTool', new IntersectionPointTool(this, toolBar));
        toolBar.addTool('MiddlePointTool', new MiddlePointTool(this, toolBar));

        toolBar.addTool('CreateVectorTool', new CreateVectorTool(this, toolBar));
        toolBar.addTool('CreateRayTool', new CreateRayTool(this, toolBar));
        toolBar.addTool('CreateLineSegmentTool', new CreateLineSegmentTool(this, toolBar));
        toolBar.addTool('CreateLineTool', new CreateLineTool(this, toolBar));

        toolBar.addTool('CreateCircleTool', new CreateCircleTool(this, toolBar));
        toolBar.addTool('CreateEllipseTool', new CreateEllipseTool(this, toolBar));
        toolBar.addTool('CreateSemicircleTool', new CreateSemicircleTool(this, toolBar));
        toolBar.addTool('CreateSectorTool', new CreateSectorTool(this, toolBar));

        toolBar.addTool('CreateTriangleTool', new CreateTriangleTool(this, toolBar));
        toolBar.addTool('CreateEquilateralTriangleTool', new CreateEquilateralTriangleTool(this, toolBar));
        toolBar.addTool('CreateIsoscelesRightTriangleTool', new CreateIsoscelesRightTriangleTool(this, toolBar));
        toolBar.addTool('CreateIsoscelesTriangleTool', new CreateIsoscelesTriangleTool(this, toolBar));
        toolBar.addTool('CreateRightTriangleTool', new CreateRightTriangleTool(this, toolBar));

        toolBar.addTool('CreatePolygonTool', new CreatePolygonTool(this, toolBar));
        toolBar.addTool('CreateRegularPolygonTool', new CreateRegularPolygonTool(this, toolBar));
        toolBar.addTool('CreateSquareTool', new CreateSquareTool(this, toolBar));
        toolBar.addTool('CreateRectangleTool', new CreateRectangleTool(this, toolBar));
        toolBar.addTool('CreateParallelogramTool', new CreateParallelogramTool(this, toolBar));
        toolBar.addTool('CreateRhombusTool', new CreateRhombusTool(this, toolBar));
        toolBar.addTool('CreateTrapezoidTool', new CreateTrapezoidTool(this, toolBar));

        toolBar.addTool('CreateAngleTool', new CreateAngleTool(this, toolBar));
        toolBar.addTool('CreateAngleByThreePointsTool', new CreateAngleByThreePointsTool(this, toolBar));
        toolBar.addTool('CreateSymmetricThroughLineTool', new CreateSymmetricThroughLineTool(this, toolBar));
        toolBar.addTool('CreateSymmetricThroughPointTool', new CreateSymmetricThroughPointTool(this, toolBar));
        toolBar.addTool('CreatePerpendicularLineTool', new CreatePerpendicularLineTool(this, toolBar));
        toolBar.addTool('CreateParallelLineTool', new CreateParallelLineTool(this, toolBar));
        toolBar.addTool('CreateBisectorLineTool', new CreateBisectorLineTool(this, toolBar));

        if (this.geoEditorConf.docViewMode === 'bounded') {
            toolBar.addTool('CreateDocumentTool', new GeoCreateDocumentTool(this, toolBar));
        }

        if (this.geoEditorConf.docViewMode !== 'full-viewport') {
            const geoPanTool = new GeoPanTool(this, toolBar, this.panFeature);
            toolBar.addTool('GeoPanTool', geoPanTool);

            const geoZoomTool = new GeoZoomTool(this, toolBar, this.zoomFeature);
            toolBar.addTool('GeoZoomTool', geoZoomTool);
        }

        return toolBar;
    }

    isSupportFeature(featureKey: string): boolean {
        if ([FEATURE_CRD_DOC, FEATURE_ZOOM, FEATURE_PAN, FEATURE_AWARENESS].includes(featureKey)) return true;

        return this.supportFeature.has(featureKey);
    }

    onFeatureInitialization(featureKey: string, feature: any): Promise<void> {
        switch (featureKey) {
            case FEATURE_SELECTION: {
                this.selectionFeature = feature as SelectionFeature;
                this.boundaryDelegator.setSelectionFeature(this.selectionFeature);
                break;
            }
            case FEATURE_HISTORY: {
                this.historyFeature = feature as HistoryFeature;
                break;
            }
            case FEATURE_ROB: {
                this.robFeature = feature as ROBFeature;
                break;
            }
            case FEATURE_CRD_DOC: {
                this.crdFeature = feature as DocCRDFeature;
                this.crdFeature.registerEditor(this, this.cmdChannel);
                break;
            }
            case FEATURE_CONTENT_VISIBILITY: {
                this.contentVisibilityFeature = feature as ContentVisibilityCheckFeature;
                break;
            }
            case FEATURE_PAN: {
                this.panFeature = feature as PanFeature;
                break;
            }
            case FEATURE_ZOOM: {
                this.zoomFeature = feature as ZoomFeature;
                break;
            }
            case FEATURE_AWARENESS: {
                this.awarenessFeature = feature as AwarenessFeature;
                break;
            }
            default: {
                break;
            }
        }

        return Promise.resolve(null);
    }

    addHistoryItem(item: GeoHistoryItem) {
        if (!this.historyFeature) return; // if history feature is not initialize, we simply ignore

        const vm = item.vm;
        const manager = this.historyFeature.getHistoryManager(vm.id);
        const docRegistry = this.regDelegator.getDocReg(vm.id);

        const docCtrl = docRegistry.getEntity(item.docId);
        docCtrl?.clearUnusableObject();

        manager.push(item);
    }

    async undo(item: GeoHistoryItem) {
        const vm = item.vm;
        const docCtrl = this.regDelegator.getDocReg(vm.id)?.getEntity(item.docId);

        switch (item.type) {
            case 'paste-doc':
                throw new Error('Not yet implemented');

            case 'rename-element': {
                const hisEl = item as RenameElementHistoryItem;
                const renders = hisEl.renamed.map(m => {
                    const rel = docCtrl.rendererCtrl.elementAt(m.relIdx);
                    rel.name = m.oldName;
                    return rel;
                });
                await this.geoGateway.updateElsState(docCtrl.state.globalId, {
                    renameEls: hisEl.renamed.map(m => {
                        const newm = structuredClone(m);
                        newm.oldName = m.newName;
                        newm.newName = m.oldName;
                        return newm;
                    }),
                });
                await syncRenderCommands(renders, docCtrl);
                break;
            }

            case 'render-elements': {
                const item1 = item as RenderElementHistoryItem;

                for (const e of item1.renderEls) e.usable = false;

                await this.geoGateway.updateElsState(docCtrl.state.globalId, {
                    unusableEls: {
                        elIdxes: item1.elIdxes,
                        relIdxes: item1.renderEls.map(i => i.relIndex),
                        ctIdxes: item1.ctIndexes,
                    },
                    renameEls: item1.renameItems.map(m => {
                        const newm = structuredClone(m);
                        newm.oldName = m.newName;
                        newm.newName = m.oldName;
                        return newm;
                    }),
                });
                syncRenderCommands(item1.renderEls, docCtrl);
                break;
            }
            case 'remove-elements': {
                const item1 = item as RenderElementHistoryItem;

                for (const e of item1.renderEls) e.deleted = undefined;

                await this.geoGateway.updateElsState(docCtrl.state.globalId, {
                    undeletedEls: {
                        elIdxes: item1.elIdxes,
                        relIdxes: item1.renderEls.map(i => i.relIndex),
                    },
                });
                syncRenderCommands(item1.renderEls, docCtrl);
                break;
            }
            case 'reconstruction': {
                const item1 = item as ReconstructionHistoryItem;
                const req = new GeoElConstructionRequest(
                    item1.ctId,
                    item1.elType,
                    item1.cgName,
                    item1.name,
                    item1.oldParamSpecs
                );
                const c = await this.geoGateway.reconstruct(docCtrl.state.globalId, item1.ctIdx, req);
                this.clearSelectedElInDoc(docCtrl);
                await syncRenderCommands(c.render, docCtrl);
                break;
            }
            case 'doc-render-prop': {
                const item1 = item as DocRenderPropHistoryItem;
                const meta = reliableSaveCmdMeta(
                    vm, // viewport manager
                    docCtrl.state,
                    docCtrl.state.id, // versionable (doc local id)
                    docCtrl.state.id, // targetId (doc local id)
                    CmdTypeProto.UPDATE_DOC_STATE, // cmdType
                    true // isUndoOrRedo
                );
                const cmd = new UpdateDocStateCmd(meta);
                cmd.setState(docCtrl.state.globalId, convertDocRenderPropToProto(item1.beforeState));
                await this.cmdChannel.receive(cmd);
                break;
            }
            case 'element-prop': {
                const item1 = item as ElementPropHistoryItem;
                for (const el of item1.beforeStates) {
                    const meta = reliableSaveCmdMeta(
                        vm,
                        docCtrl.state,
                        docCtrl.state.id,
                        docCtrl.state.id,
                        CmdTypeProto.UPDATE_ELS_PROP,
                        true
                    );
                    const cmd = new UpdateElementsPropCmd(meta);
                    cmd.state.setRelIndexList([el.relIndex]);
                    cmd.state.setElRenderProps(convertSettingPropertiesToProto(el.props));
                    await this.cmdChannel.receive(cmd);
                }
                break;
            }
            default:
                break;
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    async redo(item: GeoHistoryItem) {
        const vm = item.vm;
        const docCtrl = this.regDelegator.getDocReg(vm.id)?.getEntity(item.docId);

        switch (item.type) {
            case 'paste-doc':
                throw new Error('Not yet implemented');

            case 'rename-element': {
                const hisEl = item as RenameElementHistoryItem;
                const renders = hisEl.renamed.map(m => {
                    const rel = docCtrl.rendererCtrl.elementAt(m.relIdx);
                    rel.name = m.newName;
                    return rel;
                });
                await this.geoGateway.updateElsState(docCtrl.state.globalId, {
                    renameEls: hisEl.renamed,
                });
                await syncRenderCommands(renders, docCtrl);
                break;
            }

            case 'render-elements': {
                const item1 = item as RenderElementHistoryItem;
                const docCtrl = this.regDelegator.getDocReg(vm.id)?.getEntity(item.docId);

                for (const e of item1.renderEls) e.usable = true;

                await this.geoGateway.updateElsState(docCtrl.state.globalId, {
                    usableEls: {
                        elIdxes: item1.elIdxes,
                        relIdxes: item1.renderEls.map(i => i.relIndex),
                        ctIdxes: item1.ctIndexes,
                    },
                    renameEls: item1.renameItems,
                });
                syncRenderCommands(item1.renderEls, docCtrl);
                break;
            }

            case 'remove-elements': {
                const item1 = item as RenderElementHistoryItem;
                const docCtrl = this.regDelegator.getDocReg(vm.id)?.getEntity(item.docId);

                for (const e of item1.renderEls) e.deleted = true;

                await this.geoGateway.updateElsState(docCtrl.state.globalId, {
                    deletedEls: {
                        elIdxes: item1.elIdxes,
                        relIdxes: item1.renderEls.map(i => i.relIndex),
                    },
                });
                syncRenderCommands(item1.renderEls, docCtrl);
                break;
            }
            case 'reconstruction': {
                const item1 = item as ReconstructionHistoryItem;
                const req = new GeoElConstructionRequest(
                    item1.ctId,
                    item1.elType,
                    item1.cgName,
                    item1.name,
                    item1.newParamSpecs
                );
                const c = await this.geoGateway.reconstruct(docCtrl.state.globalId, item1.ctIdx, req);
                await syncRenderCommands(c.render, docCtrl);
                break;
            }
            case 'doc-render-prop': {
                const item1 = item as DocRenderPropHistoryItem;
                const meta = reliableCmdMeta(
                    vm, // viewport manager
                    docCtrl.state.id, // versionable
                    docCtrl.state.id, // targetId
                    CmdTypeProto.UPDATE_DOC_STATE, // cmdType
                    true // isUndoOrRedo
                );
                const cmd = new UpdateDocStateCmd(meta);
                cmd.setState(docCtrl.state.globalId, convertDocRenderPropToProto(item1.afterState));
                await this.cmdChannel.receive(cmd);
                break;
            }
            case 'element-prop': {
                const item1 = item as ElementPropHistoryItem;
                const meta = reliableCmdMeta(
                    vm,
                    docCtrl.state.id,
                    docCtrl.state.id,
                    CmdTypeProto.UPDATE_ELS_PROP,
                    true // isUndoOrRedo
                );
                const cmd = new UpdateElementsPropCmd(meta);
                cmd.state.setRelIndexList(item1.afterStates.relIndexList);
                cmd.state.setElRenderProps(convertSettingPropertiesToProto(item1.afterStates.props));
                await this.cmdChannel.receive(cmd);
                break;
            }
            default:
                break;
        }
    }

    clearHistory(viewportId: ViewportId) {
        this.historyFeature?.clear(this, viewportId);
    }

    async loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByGlobalId(
            this.cmdChannel.channelCode,
            globalId,
            'json'
        );
        this.createDocumentCtrlFromResponseData(response, loadingContext.vm as BoardViewportManager, loadingContext);
    }

    async loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByLocalId(
            this.cmdChannel.channelCode,
            localId,
            'json'
        );
        this.createDocumentCtrlFromResponseData(
            response,
            loadingContext.vm as BoardViewportManager,
            loadingContext,
            localId
        );
    }

    async reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        await this.loadDocumentByLocalId(localId, loadingContext);
    }

    requestLayer(viewport: ViewportManager, add: boolean, options: LayerOptions): GraphicLayerCtrl {
        if (this.geoEditorConf.docViewMode === 'full-viewport')
            return viewport.requestLayer(UnboundedGraphicLayerCtrl, add, options) as GraphicLayerCtrl;
        return viewport.requestLayer(BoundedGraphicLayerCtrl, add, options) as GraphicLayerCtrl;
    }

    getLayerBoundaryFromCtrl(layerCtrl: GraphicLayerCtrl): BoundaryRectangle {
        if (layerCtrl instanceof UnboundedGraphicLayerCtrl) return { start: { x: 0, y: 0 }, end: { x: 0, y: 0 } };
        if (layerCtrl instanceof BoundedGraphicLayerCtrl) return layerCtrl.boundary;
        throw new Error('Layer Ctrl of Undefined type');
    }

    /**
     *
     * @param preparedChanges
     * @param cmd
     * @param layerCmd
     */
    private async generateInitDocData(
        preparedChanges: CRUDChangeResult,
        cmd: FCInsertDocCmd,
        layerCmd: FCInsertLayerCmd
    ) {
        const bd = layerCmd.state.getBoundary();

        const initData: GeoDocInitData = {
            kind: GeoKind.UNKNOWN,
            numDim: this.geoEditorConf.numDim,
            boundary: bd ? fcConvertProtoToBoundary(bd) : undefined,
            docRenderProp: defaultDocRenderProp(),
        };

        switch (this.geoEditorConf.numDim) {
            case 2: {
                initData.kind = GeoKind.GEO2D;
                break;
            }
            case 3: {
                initData.kind = GeoKind.GEO3D;
                break;
            }
            default:
                break;
        }

        const encoder = new TextEncoder();
        const bytes = encoder.encode(JSON.stringify(initData));
        cmd.state.setInitdata(bytes);
    }

    /**
     * Creates a `GeoDocCtrl` instance from the response data received from the server.
     * This method initializes the document controller, assigns it to the appropriate registry,
     * and attaches the necessary renderer and layer controller.
     *
     * @param response The response data containing document properties and rendering information.
     * @param vm The viewport manager handling the document's display and interaction.
     * @param loadingContext (Optional) Context for managing loading states, including z-index tracking.
     * @param localId (Optional) A local identifier for the document. If not provided, it will be generated.
     * @returns The created `GeoDocCtrl` instance.
     */
    private createDocumentCtrlFromResponseData(
        response: FetchDocResponse,
        vm: BoardViewportManager,
        loadingContext?: LoadingContext,
        localId?: DocLocalId
    ): GeoDocCtrl {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vm.id);

        // in scenarios where documents are not loaded by localId but only
        // global id is provided, we generate a local id for usage, it doesn't matter anyway
        if (!localId) localId = docRegistry.getAndIncrementId();

        const kind = response.numDim === 2 ? GeoKind.GEO2D : GeoKind.GEO3D;
        const geoDoc = new GeoDoc(
            response.docId,
            localId,
            kind,
            response.docRenderProp,
            response.docDefaultElRenderProp
        );
        const layerId = 1; // only one layer per document, so layer id is always 1
        const initialBoundary = {
            start: {
                x: -response.docRenderProp.canvasWidth / 2,
                y: response.docRenderProp.canvasHeight / 2,
            },
            end: {
                x: response.docRenderProp.canvasWidth / 2,
                y: -response.docRenderProp.canvasHeight / 2,
            },
            width: response.docRenderProp.canvasWidth,
            height: response.docRenderProp.canvasHeight,
        };

        /**
         * Request the layer used by the document controller from the viewport
         * Provide what we know about the layer. The actual positioning of the layer
         * on the board is determined by the viewport.
         *
         * In the case of the classroom, the viewport manager will get the position from the
         * doc mapping, in other case, the layer will be created in a default position with
         * the provided width / height
         */
        const layerCtrl = this.requestLayer(vm, true, {
            boundary: initialBoundary,
            docLocalId: localId,
            docGlobalId: response.docId,
            viewport: vm,
            editor: this,
            state: new GeoLayer(layerId, initialBoundary, loadingContext.zIndexes[layerId]), // initially, the layer state doesn't have a boundary
        });

        // the state of the layer is initialized with the actual boundary of the layer, this is because the actual position of the layer is
        // not known when loaded but might be set when the layer ctrl is put on the board
        const layerState = layerCtrl.state as GeoLayer;
        layerState.boundary = this.getLayerBoundaryFromCtrl(layerCtrl);

        // let's start creating the doc controller
        const geoDocCtrl = new GeoDocCtrl(this, geoDoc, vm);

        docRegistry.addEntity(geoDoc.id, geoDocCtrl);

        // link the layer with the doc controller
        layerCtrl.doc = geoDocCtrl;

        const rendererCtrl = kind === GeoKind.GEO2D ? new Geo2dRenderer(layerCtrl, geoDocCtrl) : undefined;
        geoDocCtrl.attachRenderer(rendererCtrl);

        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vm.id, localId);
        layerRegistry.addEntity(layerId, rendererCtrl);

        // add geo render elements
        for (const rd of response.render) {
            switch (rd.type) {
                case 'RenderVertex': {
                    const rel = Object.assign(new RenderVertex(), rd);
                    rel.renderProp = Object.assign(_pointGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }

                case 'RenderVector':
                case 'RenderRay':
                case 'RenderLine': {
                    const rel = Object.assign(new RenderLine(), rd);
                    rel.renderProp = Object.assign(_lineGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }

                case 'RenderLineSegment': {
                    const rel = Object.assign(new RenderLineSegment(), rd);
                    rel.renderProp = Object.assign(_lineSegmentGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }

                case 'RenderPolygon': {
                    const rel = Object.assign(new RenderPolygon(), rd);
                    rel.renderProp = Object.assign(_polygonGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }

                case 'RenderCircleShape': {
                    const rel = Object.assign(new RenderCircleShape(), rd);
                    rel.renderProp = Object.assign(_circleShapeGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }
                case 'RenderEllipseShape': {
                    const rel = Object.assign(new RenderEllipseShape(), rd);
                    rel.renderProp = Object.assign(_ellipseShapeGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }
                case 'RenderSectorShape': {
                    const rel = Object.assign(new RenderSectorShape(), rd);
                    rel.renderProp = Object.assign(_sectorShapeGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }
                case 'RenderSector': {
                    const rel = Object.assign(new RenderSector(), rd);
                    rel.renderProp = Object.assign(_sectorGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }
                case 'RenderEllipse': {
                    const rel = Object.assign(new RenderEllipse(), rd);
                    rel.renderProp = Object.assign(_ellipseGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }
                case 'RenderCircle': {
                    const rel = Object.assign(new RenderCircle(), rd);
                    rel.renderProp = Object.assign(_circleGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }
                case 'RenderAngle': {
                    const rel = Object.assign(new RenderAngle(), rd);
                    rel.renderProp = Object.assign(_angleGeoRenderProp(), rd.renderProp);
                    rendererCtrl.addActualElement(rel);
                    break;
                }
                default:
                    break;
            }
        }

        // render all elements
        layerCtrl.renderer(vm, layerCtrl);

        return geoDocCtrl;
    }

    async start() {
        await this._cmdProcessor.start();
        await this.cmdChannel.start();
        await this.loadObjTypesInLang();
    }

    private async loadObjTypesInLang() {
        const textIds = GeoObjectTypeValue.filter(option => Number.isNaN(Number(option)));
        try {
            const res = await this.geoGateway.nameByLang(textIds);
            if (res) {
                for (const en of res.entries()) {
                    this.objTypeStr.set(en[0], en[1]);
                }
            }
        } catch (error) {
            console.error('Failed to load object type in specific lang: ', error);
        }
    }
    /**
     * remove document internally, meaning just remove doc without sync cmd
     *
     * @param vpId
     * @param docId
     */
    async internalRemoveDoc(vpId: ViewportId, docId: DocLocalId) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vpId);
        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vpId, docId);
        const docCtrl = docRegistry.getEntity(docId);
        if (docCtrl) {
            await this.selectDelegator.doBlurDocCtrl(docCtrl);
            const context: SelectContext = {
                doc: docCtrl,
                supporter: this.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION),
                selectDetails: {},
            };

            this.selectionFeature.onDeselect(context);
            docRegistry.removeEntity(docId);
            layerRegistry.removeEntity(docCtrl.layers[0].state.id);
            this.selectDelegator.blurDocCtrls([docCtrl]);
            docCtrl.onRemove();
        }
    }

    async remove(vpId: ViewportId, isCutting?: boolean): Promise<boolean> {
        // won't handle custom remove renderEl for cutting as we don't support copy and paste renderEl yet
        if (isCutting) return false;

        const focusDocCtrls = this.selectDelegator.getFocusedDocs(vpId);
        if (!focusDocCtrls || focusDocCtrls.length !== 1) return false;

        const docCtrl = focusDocCtrls[0];
        let els = docCtrl.selectedElements;
        const _relIdxes = els.map(rel => rel.relIndex);

        if (_relIdxes?.length < 1) return false;

        const { elIdxes, relIdxes } = await this.geoGateway.removeRenderElements(docCtrl.state.globalId, _relIdxes);

        els = els.filter(el => !relIdxes.includes(el.relIndex));

        docCtrl.updateSelectedElements(els);

        const renderElements = relIdxes.map(relIdx => {
            const rel = docCtrl.rendererCtrl.elementAt(relIdx);
            rel.deleted = true;
            return rel;
        });

        syncRenderCommands(renderElements, docCtrl);

        addRemoveElementHistoryItem(docCtrl, elIdxes, renderElements);

        return true;
    }

    clearSelectedElInDoc(doc: GeoDocCtrl) {
        const els: GeoRenderElement[] = doc.selectedElements;
        if (els && els.length > 0) {
            const meta = reliableCmdMeta(
                doc.viewport,
                doc.state.id,
                doc.state.id,
                CmdTypeProto.REMOVE_HIGHLIGHT_ELEMENTS
            );
            const cmd = new RemoveHighlightElementsCmd(meta);
            cmd.setState(
                doc.layers[0].state.id,
                els.map(e => e.relIndex)
            );
            this.cmdChannel.receive(cmd);
            doc.updateSelectedElements([]);
        }
    }

    isContentVisible(docCtrl: GeoDocCtrl): boolean {
        return (docCtrl?.rendererCtrl as Geo2dRenderer).isContentVisible();
    }

    notifyContentVisibilityChange(docCtrl: GeoDocCtrl) {
        this.contentVisibilityFeature?.onContentVisibilityChange(docCtrl);
    }
}

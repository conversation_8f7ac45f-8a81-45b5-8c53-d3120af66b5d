import { DocLocalId, DocMappingLDEData, HistoryItem, SupportFeatureHistory, ViewportId } from '@viclass/editor.core';

import { ComposerDoc } from '../model';

export type HistoryItemType = 'paste-doc' | 'render-elements' | 'composer-editing' | 'subviewport-editing';

export class ComposerHistoryItem implements HistoryItem {
    constructor(supporter: SupportFeatureHistory) {
        this.supporter = supporter;
    }
    supporter: SupportFeatureHistory;
    type: HistoryItemType;

    viewportId: ViewportId;
    docId: DocLocalId;

    // document state
    docState?: ComposerDoc;

    docsMappingLDEData?: DocMappingLDEData[];
}

/**
 * Wrapper around a history item of a sub-viewport inside composer document
 */
export class ComposerSubViewportHistoryItem extends ComposerHistoryItem {
    constructor(
        supporter: SupportFeatureHistory,
        public originalItem: HistoryItem
    ) {
        super(supporter);
        this.type = 'subviewport-editing';
    }
}

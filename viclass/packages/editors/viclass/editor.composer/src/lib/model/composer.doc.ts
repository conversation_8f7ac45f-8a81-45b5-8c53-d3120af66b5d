import {
    BoundaryRectangle,
    DocLocalId,
    DocumentId,
    EditorType,
    KeyboardEventData,
    LayerId,
    MouseEventData,
    NativeEventTarget,
    VDoc,
    VDocLayer,
} from '@viclass/editor.core';

export type ComposerMouseEvent = MouseEventData<NativeEventTarget<any>>;
export type ComposerKeyboardEvent = KeyboardEventData<NativeEventTarget<any>>;

export class ComposerDoc implements VDoc {
    layer: ComposerLayer;
    content: string;
    version = 0;
    globalId: DocumentId;
    viewportElClass: string;

    constructor(
        public id: DocLocalId,
        globalId: DocumentId,
        viewportElClass: string,
        content: string,
        version?: number
    ) {
        this.globalId = globalId; // set the global id
        this.viewportElClass = viewportElClass;
        this.content = content;
        if (version) this.version = version;
    }

    getLayers(): VDocLayer[] {
        return [this.layer as VDocLayer];
    }
    setLayers(layers: VDocLayer[]) {
        this.layer = layers[0] as ComposerLayer;
    }

    addLayer(layer: VDocLayer) {
        this.layer = layer as ComposerLayer;
    }
}

export class ComposerLayer implements VDocLayer {
    zindex: number;

    constructor(
        public id: LayerId,
        public docId: DocLocalId,
        public boundary?: BoundaryRectangle
    ) {}
    getBoundary(): BoundaryRectangle {
        return this.boundary;
    }
    getZindex() {
        return this.zindex;
    }
}

export type SubViewportInfo = {
    viewportId: string;
    globalId: DocumentId;
    localId: DocLocalId;
    editorType: EditorType;
};

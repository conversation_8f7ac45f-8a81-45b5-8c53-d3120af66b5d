import { DocLocalId, DocumentId, EditorBackendConnector } from '@viclass/editor.core';
import axios, { ResponseType } from 'axios';

/**
 * A backend connector used by composer coordinator. It is used only by the composer coordinator and allows loading by local id
 * of the document within the composer document.
 */
export class ComposerCoordBackendConnector implements EditorBackendConnector {
    constructor(
        private apiUrl: string,
        private globalId: DocumentId,
        private localId: DocLocalId
    ) {}

    /**
     * Because the loading is direct, we use the global id stored in the backend connector to load
     * @param channelCode
     * @param localId
     * @param responseType
     * @returns
     */
    loadDocumentByLocalId(channelCode: number, localId: DocLocalId, responseType: ResponseType): Promise<any> {
        if (localId != this.localId) throw new Error('Inconsistency in local id of document');
        return this.loadDocumentByGlobalId(channelCode, this.globalId, responseType);
    }

    reloadDocumentByLocalId(channelCode: number, localId: DocLocalId, responseType: ResponseType): Promise<any> {
        throw new Error('Method not implemented.');
    }

    /**
     * by convention, all API for loading document is at /document/{globalId}
     */
    loadDocumentByGlobalId(channelCode: number, globalId: DocumentId, responseType: ResponseType): Promise<any> {
        return axios
            .get(`${this.apiUrl}/document/fetch`, {
                params: {
                    globalId: globalId,
                },
                responseType: responseType,
            })
            .then(
                resp => resp.data,
                err => {
                    throw err;
                }
            );
    }
}

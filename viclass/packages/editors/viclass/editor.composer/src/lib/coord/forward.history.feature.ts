import { ComposerForwardHistoryManager } from './forward.history.manager';
import { ComposerDocCtrl } from '../docs/composer.doc.ctrl';
import { ComposerEditor } from '../composer.editor';
import { HistoryFeature, HistoryManager } from '@viclass/editor.core';
import { ComposerEditorCoordinator } from './composer.coordinator';

/**
 * Special history feature for composer coordinator, which will forward the history of sub editors
 * to the history of the main composer editor by always return the HistoryManager of main editor
 */
export class ComposerForwardHistoryFeature extends HistoryFeature {
    get composerCoord(): ComposerEditorCoordinator {
        return this.coord as ComposerEditorCoordinator;
    }

    constructor(
        coord: ComposerEditorCoordinator,
        private weditor: ComposerEditor
    ) {
        super(coord);
    }

    override getHistoryManager(vpId: string): HistoryManager {
        const docCtrl = this.getComposerDocCtrl(vpId);
        if (!docCtrl) throw Error(`Don't have composer doc ctrl of viewport ${vpId}`);

        const rootVmId = docCtrl.viewport.id;
        let manager = this.historyManagers.get(rootVmId);
        if (!manager) {
            const targetHistoryManager = this.weditor.historyFeature.getHistoryManager(rootVmId);

            // create a dummy manager that will forward the history item to the main vp HistoryManager
            manager = new ComposerForwardHistoryManager(targetHistoryManager, this.weditor);
            this.historyManagers.set(rootVmId, manager);
        }

        return manager;
    }

    private getComposerDocCtrl(vpId: string): ComposerDocCtrl {
        return this.composerCoord.containingDoc.get(vpId);
    }
}

/**
 * Configuration of the composer editor extends the basic configuration and provides additionally lookups so that
 * the composer editor can act like a coordinator and load its own instances of editors.
 *
 * Composer editor doesn't create the UI of its internal editor, instead, the application which host the composer editor
 * needs to create the editor UI and supply it to the composer editor through its API.
 */

import { EditorConfig, EditorCoordinatorConfig, EditorLookup } from '@viclass/editor.core';

export type EditorViewportType = 'board' | 'inline';
export interface EditorViewportConfig {
    vpType: EditorViewportType;
    // if not set, default to 100%
    defaultWidth?: string;
    // if not set, default to 250px
    defaultHeight?: string;
}

export interface ComposerEditorCoordinatorConfig extends EditorCoordinatorConfig {
    syncRouting: boolean;
    edLookups: EditorLookup[];
    // a map from editor type to the viewport type to be used for that editor
    viewport: {
        [key: string]: EditorViewportConfig;
    };
    // element class which is attached to the viewport element
    viewportElClass: string;
}

export interface ComposerEditorConfig extends EditorConfig {
    composerCoordConf: ComposerEditorCoordinatorConfig;
    composerTheme: string;
}

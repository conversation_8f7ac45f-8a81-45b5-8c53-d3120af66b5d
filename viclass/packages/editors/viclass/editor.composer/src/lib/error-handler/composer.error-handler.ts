import {
    CriticalDocument<PERSON>IErr,
    <PERSON><PERSON>rr,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rror<PERSON><PERSON>lerFn,
    NonCriticalDocumentAPIErr,
    ViErrEventData,
    ViErrEventEmitter,
} from '@viclass/editor.core';
import { ComposerErr } from './error';

export class ComposerErrorHandler extends ErrorHandler {
    constructor(emitter?: ViErrEventEmitter) {
        super(emitter);
    }

    override handle(e: ViErrEventData): ViErrEventData | null {
        if (
            e.state instanceof ComposerErr ||
            e.state instanceof CriticalErr ||
            e.state instanceof CriticalDocumentAPIErr ||
            e.state instanceof NonCriticalDocumentAPIErr
        ) {
            this.emit(e);
            return null;
        }

        return e;
    }
}

export const availableHandlers: {
    composerDefault?: ComposerErrorHandler;
} = {};

export const composerDefaultHandlerFn: ErrorHandlerFn = (ctx: any) => {
    if (availableHandlers.composerDefault) return availableHandlers.composerDefault;
    throw new Error('Error handler not yet defined.');
};

/**
 * A method to initialize the relevant error handlers of the modules with the emitter for listening
 * to the module's error events. This emitter can be passed in from the application level
 *
 * NOTE TO DEVELOPERS: a more sophisticated method is pass in option object instead of the emitter directly
 * @param emitter
 */
export function initErrorHandlers(emitter: ViErrEventEmitter) {
    // initialize / create all the error emitter
    availableHandlers.composerDefault = new ComposerErrorHandler(emitter);
}

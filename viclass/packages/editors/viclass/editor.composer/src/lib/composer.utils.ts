import { DocLocalId, ViewportId } from '@viclass/editor.core';
import { $getRoot } from 'lexical';

export function composerDocReg(viewportId: ViewportId) {
    return `composerEditor/composerDoc/${viewportId}`;
}

export function composerLayerReg(viewportId: ViewportId, docId: DocLocalId) {
    return `composerEditor/composerLayer/${viewportId}/${docId}`;
}

export function composerObjectReg(viewportId: ViewportId, docId: DocLocalId) {
    return `composerEditor/composerObject/${viewportId}/${docId}`;
}

export function loadCSS(url: string, rootDoc?: Document) {
    rootDoc = rootDoc || document;

    // Check if the CSS has already been loaded
    const existingStylesheet = Array.from(rootDoc.styleSheets).find(sheet => sheet.href === url);
    if (!existingStylesheet) {
        // Create a <link> tag
        const link = rootDoc.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;

        // Append the <link> tag to the document head
        rootDoc.head.appendChild(link);

        // Listen for the load event
        link.addEventListener('load', () => {
            console.log('CSS loaded:', url);
        });
    } else {
        console.log('CSS already loaded:', url);
    }
}

export function removeClassesWithPrefix(elem: HTMLElement | SVGElement, prefix: string): void {
    Array.from(elem.classList).forEach(cls => {
        if (cls.startsWith(prefix)) {
            elem.classList.remove(cls);
        }
    });
}

export class AutoExpiringCache<K, V> {
    private cache = new Map<K, { value: V; expiresAt: number | null }>();
    private timers = new Map<K, number>();
    private onExpireCallback: (key: K, value: V) => void;

    constructor(onExpireCallback: (key: K, value: V) => void) {
        this.onExpireCallback = onExpireCallback;
    }

    /**
     * Set a value in the cache with optional expiration.
     * @param key - The key to store the value under.
     * @param value - The value to store.
     * @param ttl - Time-to-live in milliseconds (optional).
     */
    set(key: K, value: V, ttl?: number): void {
        // Clear existing timer for the key, if any
        this.clearTimer(key);

        const expiresAt = ttl ? Date.now() + ttl : null;
        this.cache.set(key, { value, expiresAt });

        if (ttl) {
            const timer = setTimeout(() => {
                this.expireKey(key);
            }, ttl);
            this.timers.set(key, timer as any);
        }
    }

    /**
     * Get a value from the cache.
     * @param key - The key to retrieve.
     * @returns The stored value or undefined if not found/expired.
     */
    get(key: K): V | undefined {
        const entry = this.cache.get(key);

        if (!entry) return undefined;

        if (entry.expiresAt && entry.expiresAt <= Date.now()) {
            this.expireKey(key);
            return undefined;
        }

        return entry.value;
    }

    /**
     * Remove a key from the cache.
     * @param key - The key to remove.
     */
    delete(key: K): void {
        this.clearTimer(key);
        this.cache.delete(key);
    }

    /**
     * Clear the entire cache.
     */
    clear(): void {
        for (const key of this.timers.keys()) {
            this.clearTimer(key);
        }
        this.cache.clear();
    }

    /**
     * Check if a key exists in the cache.
     * @param key - The key to check.
     * @returns True if the key exists and is not expired, false otherwise.
     */
    has(key: K): boolean {
        return this.get(key) !== undefined;
    }

    /**
     * Handle expiration of a key.
     * @param key - The key to expire.
     */
    private expireKey(key: K): void {
        const entry = this.cache.get(key);
        if (entry) {
            this.onExpireCallback(key, entry.value);
            this.delete(key);
        }
    }

    /**
     * Clear the expiration timer for a key.
     * @param key - The key whose timer should be cleared.
     */
    private clearTimer(key: K): void {
        const timer = this.timers.get(key);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(key);
        }
    }
}

/**
 * Splits a Uint8Array into chunks of specified size.
 * @param input - The Uint8Array to split.
 * @param chunkSize - The size of each chunk.
 * @returns An array of Uint8Array chunks.
 */
export function splitUint8Array(input: Uint8Array, chunkSize: number): Uint8Array[] {
    const chunks: Uint8Array[] = [];
    for (let i = 0; i < input.length; i += chunkSize) {
        chunks.push(input.subarray(i, i + chunkSize));
    }
    return chunks;
}

/**
 * Reconnects an array of Uint8Array chunks into a single Uint8Array.
 * @param chunks - An array of Uint8Array chunks.
 * @returns A single Uint8Array combining all chunks.
 */
export function reconnectUint8Array(chunks: Uint8Array[]): Uint8Array {
    const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;

    for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
    }

    return result;
}

/**
 * Generates a random uint32 number (1 to 4,294,967,295).
 * @returns A random uint32 number (never 0).
 */
export function randomUint32(): number {
    return Math.floor(Math.random() * (2 ** 32 - 1)) + 1;
}

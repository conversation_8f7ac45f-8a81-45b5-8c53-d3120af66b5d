import {
    <PERSON>T<PERSON><PERSON>,
    CoordinatorEvent,
    CoordinatorEventType,
    CursorManager,
    CursorMonitor,
    defaultCursor,
    DefaultToolBar,
    EditorEventManager,
    EditorFocusCES,
    EventToViewportMode,
    Tool,
    ToolBar,
    ToolEventData,
    ToolEventListener,
    UserInputHandler,
    VEventListener,
    ViewportDisableCES,
    ViewportEnabledCES,
    ViewportFocusInCES,
    ViewportFocusOutCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { ComposerEditorCoordinator } from './coord/composer.coordinator';
import { SubEditorManagerTool } from './tools';

export const LOCAL_CURSOR = 'local_cursor';

/**
 * Handlers and Global Handlers from tools, toolbar needs to be attached and detached
 * at the correct moment. This class listens to the events occurred from the coordinator activities
 * and decide which handlers to be attached.
 *
 * This handler attachment manager is designed to work with multiple concurrent viewport on the same page
 * It uses a single event manager and when a viewport is focused in, the handlers of the toolbars of that viewport
 * will be attached. The toolbar is retrieved from the coordinator.
 *
 * - editor focus
 * - editor blur
 * - tool-focus
 * - tool-blur
 * - viewport-enable
 * - viewport-disable
 * - viewport-focusin
 * - viewport-focusout
 */
export class HandlerAttachmentManager {
    timeout: any;

    toolListener: ToolEventListener<ToolBar<any, any>, any>;

    editorToolbar?: ToolBar<any, any> = undefined;
    active = false;
    focusTool: ToolEventData<ToolBar<any, any>, any>[] = [];
    cursorManager: CursorManager;
    cursorMonitor: CursorMonitor;
    private vm: ViewportManager;
    private externalMonitored: HTMLElement[] = [];

    constructor(
        private readonly coordinator: ComposerEditorCoordinator,
        private readonly subEditorManagerTool: SubEditorManagerTool,
        private readonly commonToolbar: CommonToolbar
    ) {
        coordinator.coordEventEmitter.registerListener(new this.CoordinatorListener(this));
        this.toolListener = new this.ToolListener(this);
    }

    additionalMonitoredElements(els: HTMLElement[]) {
        this.externalMonitored = this.externalMonitored.concat(els);

        // if there are an active viewport currently, notify its event manager to capture events for these additional elements
        if (this.active) for (const el of els) this.vm.eventManager.captureEventsFor(el);
    }

    private handleViewportModeChange(vpMode: ViewportMode) {
        if (!vpMode) return;

        if (vpMode === 'Disabled') {
            this.commonToolbar.disable();
            this.editorToolbar?.disable();
        } else {
            this.commonToolbar.enable();
            this.editorToolbar?.enable();
        }

        [this.commonToolbar, this.editorToolbar].forEach(tb => {
            (tb as DefaultToolBar<any, any>)?.onViewportModeChanged(vpMode);
        });
    }

    private CoordinatorListener = class implements VEventListener<CoordinatorEvent> {
        constructor(private m: HandlerAttachmentManager) {}

        async onEvent(eventData: CoordinatorEvent): Promise<CoordinatorEvent> {
            if (!this.m.active) return eventData;

            // check if the event of the internal viewport belongs to this handler attachment manager
            const docCtrl = this.m.coordinator.containingDoc.get(eventData.state?.vmId);
            const parentVm = this.m.subEditorManagerTool.toolbar.viewport;

            // each viewport where the composer editor operate
            // the composer editor creates a handler attachment manager for the composer documents in the viewport
            if (docCtrl?.viewport != parentVm) return eventData;

            switch (eventData.eventType as CoordinatorEventType) {
                case 'editor-focus':
                case 'editor-blur': {
                    const eS = eventData.state as EditorFocusCES;

                    // when an editor is focused, we listen to its tool event
                    const tb = this.m.subEditorManagerTool.toolState.getEditorToolbar(eS.editor.editorType);
                    if (!tb) return eventData;

                    if (eventData.eventType == 'editor-focus') {
                        tb.registerToolListener(this.m.toolListener);
                        if (tb.curTool) await tb.blur(tb.curTool);
                        this.m.editorToolbar = tb;
                    } else {
                        if (tb.curTool) await tb.blur(tb.curTool);
                        tb.unregisterToolListener(this.m.toolListener);

                        this.m.resetAll(this.m.vm);

                        delete this.m.vm;
                        delete this.m.editorToolbar;
                    }

                    break;
                }

                case 'viewport-focusin': {
                    this.m.commonToolbar.registerToolListener(this.m.toolListener);
                    const eS = eventData.state as ViewportFocusInCES;
                    const vm = this.m.coordinator.getViewportManager(eS.vmId);

                    // clean up any previous vm and cursor manager
                    if (this.m.vm === vm) {
                        this.m.cursorMonitor?.destroy();
                        this.m.cursorManager?.destroy();
                    }

                    // cursor manager
                    this.m.vm = vm;
                    this.m.cursorManager = new CursorManager(this.m.vm);
                    this.m.cursorManager.add(LOCAL_CURSOR);
                    this.m.cursorMonitor = new CursorMonitor(
                        this.m.vm,
                        this.m.cursorManager,
                        LOCAL_CURSOR,
                        defaultCursor
                    );

                    break;
                }
                case 'viewport-focusout': {
                    this.m.commonToolbar.unregisterToolListener(this.m.toolListener);
                    const eS = eventData.state as ViewportFocusOutCES;
                    if (eS.vmId === this.m.vm?.id) {
                        this.m.cursorMonitor.destroy();
                        this.m.cursorManager.destroy();

                        delete this.m.cursorManager;
                        delete this.m.cursorMonitor;
                        delete this.m.vm;
                    }

                    break;
                }

                case 'viewport-interactive-mode':
                case 'viewport-view-mode':
                case 'viewport-edit-mode': {
                    const veS = eventData.state as ViewportEnabledCES;

                    if (this.m.externalMonitored.length > 0)
                        for (const el of this.m.externalMonitored) {
                            const vm = this.m.coordinator.getViewportManager(veS.vmId);
                            vm.eventManager.captureEventsFor(el);
                        }

                    this.m.handleViewportModeChange(EventToViewportMode[eventData.eventType]);
                    break;
                }
                case 'viewport-disabled': {
                    const veS = eventData.state as ViewportDisableCES;
                    const vm = this.m.coordinator.getViewportManager(veS.vmId);
                    if (veS.vmId !== this.m.vm.id) return eventData;

                    this.m.cursorManager.hideCursor(LOCAL_CURSOR);

                    // if additional elements being monitored, the event manager of the disabled viewport shouldn't capture anymore
                    if (this.m.externalMonitored.length > 0)
                        for (const el of this.m.externalMonitored) vm.eventManager.uncaptureEventsFor(el);
                    this.m.handleViewportModeChange(EventToViewportMode[eventData.eventType]);
                    break;
                }
                default: // default, we break out and don't register handler
                    return eventData;
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    };
    /**
     * Handling attachment when tool focus / blur occurs
     */
    private ToolListener = class implements ToolEventListener<ToolBar<any, any>, any> {
        processingFocus: boolean = false;

        constructor(private m: HandlerAttachmentManager) {}

        isAncestorOf(t: ToolBar<any, Tool>, check: Tool): boolean {
            let result = false;
            if (check.childToolbar) {
                if (check.childToolbar == t) result = true;
                else {
                    for (const tool of check.childToolbar.tools.values()) {
                        if (tool.childToolbar && this.isAncestorOf(t, tool)) {
                            result = true;
                            break;
                        }
                    }
                }
            }

            return result;
        }

        async onEvent(
            eventData: ToolEventData<ToolBar<any, any>, any>
        ): Promise<ToolEventData<ToolBar<any, any>, any>> {
            if (eventData.eventType == 'change') return eventData;

            const t = eventData.source.getTool(eventData.toolType) as Tool;
            const s = eventData.source;

            // ignore if the source has no viewport (toolbar already detached)
            if (!s.viewport) return eventData;

            // if the tool sending the event is not from the viewport this HAM is in charge of.
            if (s.viewport != this.m.vm) return eventData;

            switch (eventData.eventType) {
                case 'focus': {
                    // if (t.toolbar.isDisabled() || t.toolbar.isToolDisable(t.toolType)) return eventData

                    const tobeBlur = [].concat(this.m.focusTool).reverse();
                    const remaining = [];

                    this.processingFocus = true;
                    for (const e of tobeBlur) {
                        // only blur tools which are not the ancestor of the focused tool
                        const tobeBlurTool = e.source.getTool(e.toolType);

                        if (this.isAncestorOf(s, tobeBlurTool)) remaining.unshift(e);
                        else e.source.blur(e.toolType);
                    }

                    this.processingFocus = false;
                    this.m.focusTool = [...remaining, eventData]; // focusTool now only contains the newly focused tool or its ancestor tool

                    if (t.childToolbar) t.childToolbar.registerToolListener(this.m.toolListener);
                    break;
                }
                case 'transient-focus': {
                    // if (t.toolbar.isDisabled() || t.toolbar.isToolDisable(t.toolType)) return eventData

                    // if the focus is transient, we don't blur existing tool
                    // so that when the transient focused tool is blurred, we can revert back to the existing tool in the correct order.
                    this.m.focusTool.push(eventData);
                    if (t.childToolbar) t.childToolbar.registerToolListener(this.m.toolListener);
                    break;
                }
                case 'blur':
                case 'transient-blur': {
                    if (t.childToolbar) t.childToolbar.unregisterToolListener(this.m.toolListener);

                    // if a tool is blur, we remove it from the focus tool and re-register the handler
                    this.m.focusTool = this.m.focusTool.filter(
                        e => e.source != eventData.source || e.toolType != eventData.toolType
                    );

                    break;
                }
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    };

    private resetAll(vm: ViewportManager) {
        if (vm) {
            vm.eventManager.resetKeyboardHandling();
            vm.eventManager.resetMouseHandling();
            vm.eventManager.resetPointerHandling();
        }
    }

    private registerHandler() {
        delete this.timeout;

        if (!this.vm) return; // when there is no viewport, there's nothing to handle

        this.resetAll(this.vm);

        const em = this.vm.eventManager;
        if (!em) return;

        const focusTools: UserInputHandler[] = this.focusTool.map(e => e.source.getTool(e.toolType));
        const handlerStack: UserInputHandler[] = [this.commonToolbar, this.editorToolbar, ...focusTools];

        // register all handling, except for the global ones
        for (const uih of handlerStack) {
            if (uih) {
                if (uih.mouseHandler)
                    for (const mh of uih.mouseHandling) if (!mh.global) em.registerMouseHandling(mh, uih.mouseHandler);

                if (uih.pointerHandler)
                    for (const mh of uih.pointerHandling)
                        if (!mh.global) em.registerPointerHandling(mh, uih.pointerHandler);

                if (uih.keyboardHandler)
                    for (const kh of uih.keyboardHandling)
                        if (!kh.global) em.registerKeyboardHandling(kh, uih.keyboardHandler);
            }
        }

        if (this.commonToolbar) this.registerGlobal(em, this.commonToolbar); // register all global handling of the coordinator toolbar
        if (this.editorToolbar) this.registerGlobal(em, this.editorToolbar); // if there is a focus editor, register all global handling of the tools of that editor

        // update the tool stack in cursor manager so that it knows if focused handler has been changed
        this.cursorMonitor?.updateToolStack(handlerStack.map(h => h?.pointerHandler).filter(h => h != undefined));
    }

    private registerGlobal(em: EditorEventManager, toolbar: ToolBar<any, Tool>) {
        if (toolbar.isDisabled()) return;
        // All global handling of the all tools within a toolbar
        toolbar.tools.forEach((tool, type) => {
            if (tool.mouseHandler)
                for (const mh of tool.mouseHandling) if (mh.global) em.registerMouseHandling(mh, tool.mouseHandler);

            if (tool.pointerHandler)
                for (const mh of tool.pointerHandling)
                    if (mh.global) em.registerPointerHandling(mh, tool.pointerHandler);

            if (tool.keyboardHandler) {
                for (const kh of tool.keyboardHandling) {
                    if (kh.global) em.registerKeyboardHandling(kh, tool.keyboardHandler);
                }
            }
        });
    }
}

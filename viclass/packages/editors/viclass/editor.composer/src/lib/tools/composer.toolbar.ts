import {
    <PERSON><PERSON><PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    KeyboardEventData,
    KeyboardEventListener,
    MouseEventData,
    MouseEventListener,
    NativeEventTarget,
    newCursor,
    PointerEventListener,
    ToolState,
    VEventListener,
    ViewportDisableCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { ComposerEditor } from '../composer.editor';
import { ComposerDocEvent, ComposerDocFocusedES } from '../composer.models';
import { ComposerDocCtrl } from '../docs/composer.doc.ctrl';
import { ComposerSettingsTool } from './composer.settings.tool';
import { ComposerTool } from './composer.tool';
import { ComposerToolType } from './models';

export class ComposerToolBar extends DefaultToolBar<ComposerToolType, ComposerTool<ToolState, any>> {
    override keyboardHandler: KeyboardEventListener<NativeEventTarget<any>>;
    override mouseHandler: MouseEventListener<NativeEventTarget<any>>;
    override pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    composerDocEventListener: VEventListener<ComposerDocEvent>;

    constructor(private editor: ComposerEditor) {
        super(editor.coordinator);
        this.composerDocEventListener = new this.ComposerDocEventListener(this);

        this.keyboardHandler = new this._keyboardHandler(this);
        this.mouseHandler = new this._mouseHandler();
        this.mouseHandling = [];
    }

    public override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    switch (type) {
                        case 'SubEditorManagerTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                        }
                    }
                    break;
                }
                case 'ViewMode': {
                    switch (type) {
                        case 'SubEditorManagerTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                        }
                    }
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    override attachViewport(viewport: ViewportManager): void {
        super.attachViewport(viewport);
        this.editor.toolbars.set(viewport.id, this);
        this.editor.selectDelegator.registerDocEventListener(this.composerDocEventListener);
    }

    override detachViewport(viewport: ViewportManager): void {
        super.detachViewport(viewport);
        if (this.composerDocEventListener) {
            this.editor.selectDelegator.unregisterDocEventListener(this.composerDocEventListener);
        }
        this.editor.toolbars.delete(viewport.id);
    }

    protected override generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: ComposerToolBar) {}

            onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                const eType = eventData.eventType as CoordinatorEventType;
                const eventState = eventData.state;

                if (!this.toolbar.viewport || eventState.vmId != this.toolbar.viewport.id) return eventData;

                switch (eType) {
                    case 'viewport-disabled': {
                        const evs = eventState as ViewportDisableCES;
                        this.toolbar.editor.selectDelegator.onViewportDisabled(eventData.state.vmId, evs);
                        break;
                    }
                    case 'viewport-removed': {
                        this.toolbar.editor.selectDelegator.onViewportRemoved(eventData.state.vmId);
                        break;
                    }
                }

                return eventData;
            }
        })(this);
    }

    override addTool(toolType: ComposerToolType, tool: ComposerTool<ToolState, any>): void {
        super.addTool(toolType, tool);
        tool.registerToolbar(this);
    }

    private ComposerDocEventListener = class implements VEventListener<ComposerDocEvent> {
        constructor(private toolbar: ComposerToolBar) {}

        onEvent(eventData: ComposerDocEvent): ComposerDocEvent | Promise<ComposerDocEvent> {
            if (this.toolbar.isDisabled()) return eventData;

            if (!this.toolbar.viewport || eventData.state.vm.id != this.toolbar.viewport.id) return eventData;

            switch (eventData.eventType) {
                case 'doc-focused': {
                    const { vm, docCtrl } = eventData.state as ComposerDocFocusedES;
                    vm.eventManager.captureAllKeyboardEvent(docCtrl.root, this.toolbar.keyboardHandler);

                    if (!this.toolbar.isToolDisable('ComposerSettingsTool')) {
                        const tool = this.toolbar.getTool('ComposerSettingsTool') as ComposerSettingsTool;
                        tool.loadSettings();
                    }

                    this.toolbar.blur('CreateComposerDocumentTool');
                    break;
                }
                case 'doc-unfocused': {
                    const { vm, docCtrl } = eventData.state as ComposerDocFocusedES;
                    vm.eventManager.unCaptureAllKeyboardEvent(docCtrl.root);

                    if (!this.toolbar.isToolDisable('ComposerSettingsTool')) {
                        const tool = this.toolbar.getTool('ComposerSettingsTool') as ComposerSettingsTool;
                        tool.loadSettings();
                    }

                    docCtrl.unselect();
                    break;
                }
            }

            return eventData;
        }
    };

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private toolbar: ComposerToolBar) {}

        onEvent(
            event: KeyboardEventData<NativeEventTarget<any>>
        ): KeyboardEventData<NativeEventTarget<any>> | Promise<KeyboardEventData<NativeEventTarget<any>>> {
            if (this.toolbar.isDisabled()) return event;

            const vm = this.toolbar.viewport;
            if (vm) {
                const focusedDocCtrls: ComposerDocCtrl[] =
                    this.toolbar.editor.selectDelegator.getFocusedDocs(vm.id) || [];
                if (focusedDocCtrls.length === 1 && focusedDocCtrls[0]) {
                    return focusedDocCtrls[0].keyboardListener.onEvent(event);
                }
            }

            return event;
        }
    };

    private _mouseHandler = class implements MouseEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject([newCursor('auto', 10, '#fff', 0, 'system')]);

        onEvent(event: MouseEventData<NativeEventTarget<any>>) {
            return event;
        }
    };
}

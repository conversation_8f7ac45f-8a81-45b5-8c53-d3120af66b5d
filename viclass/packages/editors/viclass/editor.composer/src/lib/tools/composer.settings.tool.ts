import { reliableSaveCmdMeta, ToolState } from '@viclass/editor.core';
import { ComposerCmdTypeProto } from '@viclass/proto/editor.composer';
import { UpdateDocSettingsCmd } from '../cmds/composer.cmd';
import { DEFAULT_SETTINGS, ComposerDocSettings } from '../model';
import { ComposerEditor } from '../composer.editor';
import { ComposerToolType } from './models';
import { ComposerTool } from './composer.tool';

export interface ComposerSettingsToolState extends ToolState, ComposerDocSettings {}

const DEFAULT_TOOLSTATE: ComposerSettingsToolState = {
    ...DEFAULT_SETTINGS,
};

export class ComposerSettingsTool extends ComposerTool<ComposerSettingsToolState, any> {
    override toolType: ComposerToolType = 'ComposerSettingsTool';
    override toolState: ComposerSettingsToolState = { ...DEFAULT_TOOLSTATE };

    constructor(editor: ComposerEditor) {
        super(editor);
    }

    loadSettings(): void {
        const state = { ...this.toolState };

        const docCtrls = this.getFocusedComposerDocCtrls();

        const valueSet = new Set<any>();
        docCtrls.forEach(docCtrl => valueSet.add(docCtrl.settings.padding));
        if (valueSet.size === 1) {
            state.padding = valueSet.values().next().value;
        }

        this.changeToolState(state);
    }

    updateSetting(field: string, value: any) {
        const docCtrls = this.getFocusedComposerDocCtrls();
        if (!docCtrls?.length) return;

        // apply local docCtrls settings
        docCtrls.forEach(docCtrl => {
            docCtrl.applySettings({ ...docCtrl.settings, [field]: value });
        });

        // sync settings
        const meta = reliableSaveCmdMeta(
            this.toolbar.viewport,
            docCtrls[0].state,
            docCtrls[0].state.id,
            docCtrls[0].state.id,
            ComposerCmdTypeProto.UPDATE_DOC_SETTINGS
        );

        const updateCmd = new UpdateDocSettingsCmd(meta);
        docCtrls.forEach(docCtrl => {
            updateCmd.addDocSettings(docCtrl.state.id, docCtrl.state.globalId, docCtrl.settings);
        });
        this.editor.sendCommand(updateCmd);

        const state = { ...this.toolState, [field]: value };
        this.changeToolState(state);
    }

    private changeToolState(newState: ComposerSettingsToolState) {
        this.toolState = newState;
        this.toolbar.update('ComposerSettingsTool', this.toolState);
    }
}

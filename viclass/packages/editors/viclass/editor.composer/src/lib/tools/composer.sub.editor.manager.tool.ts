import {
    <PERSON><PERSON><PERSON><PERSON>,
    CoordinatorE<PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    EditorAddedCES,
    EditorType,
    FEATURE_SELECTION,
    SelectContext,
    SelectTool,
    SupportSelectFeature,
    ToolBar,
    ToolEventData,
    ToolEventListener,
    ToolState,
    VEventListener,
} from '@viclass/editor.core';
import { ComposerEditorCoordinator } from '../coord/composer.coordinator';
import { HandlerAttachmentManager } from '../handler.attachment.manager';
import { ComposerTool, ComposerToolType } from '../tools';
import { ComposerEditor } from '../composer.editor';

export class SubEditorManagerToolState implements ToolState {
    private subToolbars = new Map<EditorType, DefaultToolBar<any, any>>();
    currentEditor: EditorType;

    hasEditor(edType: EditorType) {
        return this.subToolbars.has(edType);
    }

    remove(edType: EditorType) {
        this.subToolbars.delete(edType);
    }

    existingEditors(): IterableIterator<EditorType> {
        return this.subToolbars.keys();
    }

    setEditorToolbar(edType: EditorType, tb: DefaultToolBar<any, any>) {
        this.subToolbars.set(edType, tb);
    }

    getEditorToolbar(edType: EditorType): DefaultToolBar<any, any> {
        return this.subToolbars.get(edType);
    }
}

/**
 * This tool initializes all the toolbar of the editors used by the composer editor.
 * The composer editor tool UI will base on this toolbars to create the necessary UI for the sub editor and connect to them
 */
export class SubEditorManagerTool extends ComposerTool<ToolState, any> {
    override readonly toolType: ComposerToolType = 'SubEditorManagerTool';
    override toolState = new SubEditorManagerToolState();

    private readonly subCoordEventListener: VEventListener<CoordinatorEvent>;
    public readonly commonToolbar: CommonToolbar;

    // for each internal viewport, if its internal document is selected, we store the select context here
    private curSelectContext = new Map<string, SelectContext>();

    readonly ham: HandlerAttachmentManager;

    constructor(
        override editor: ComposerEditor,
        private composerCoord: ComposerEditorCoordinator
    ) {
        super(editor);
        this.subCoordEventListener = new this.SubCoordEventListener(this);
        composerCoord.registerCoordEventListener(this.subCoordEventListener);
        this.refreshToolbarList();
        this.commonToolbar = composerCoord.createToolbar() as CommonToolbar;
        this.ham = new HandlerAttachmentManager(this.composerCoord, this, this.commonToolbar);
    }

    private SubCoordEventListener = class implements VEventListener<CoordinatorEvent> {
        constructor(private _p: SubEditorManagerTool) {}

        async onEvent(eventData: CoordinatorEvent): Promise<CoordinatorEvent> {
            if (eventData.eventType === 'editor-added') {
                this._p.refreshToolbarList();
                const editor = (eventData.state as EditorAddedCES).editor;
                await editor.start(); // if the sub editor was added after the composer editor has started, we have to start it here
                return eventData;
            }

            if (eventData.eventType === 'editor-removed') {
                this._p.refreshToolbarList();
                return eventData;
            }

            if (this._p.toolbar.isDisabled() || this._p.toolbar.isToolDisable(this._p.toolType)) return eventData;

            switch (eventData.eventType as CoordinatorEventType) {
                case 'viewport-focusin': {
                    const containingDoc = this._p.composerCoord.containingDoc.get(eventData.state.vmId);
                    // we not process this event if the sub viewport being focus is not inside a parent viewport of the tool
                    if (!containingDoc || this._p.toolbar.viewport != containingDoc.viewport) return eventData;

                    const vm = this._p.composerCoord.getViewportManager(eventData.state.vmId);

                    const el = vm.rootEl;
                    const edType = el.dataset['edType'] as EditorType;
                    el.focus(); // focus on the editor

                    const edToolbar = this._p.toolState.getEditorToolbar(edType as EditorType);
                    edToolbar.attachViewport(vm);
                    edToolbar.registerToolListener(this._p.toolListener);
                    this._p.commonToolbar.attachViewport(vm);

                    this._p.composerCoord.focusEditor(edType as EditorType, vm);
                    const switchModePromise = this._p.composerCoord.switchViewportMode(
                        vm.id,
                        containingDoc.viewport.mode
                    );

                    // wait for the viewport mode to be switch before select doc
                    // so that the toolbar is ready to handle the incoming event from selection (tb enable/disable based on vp mode)
                    // ! Must use `then` instead of `await` because the `viewport-focusin` event's promise need to be resolved first
                    switchModePromise.then(() => {
                        const editor = this._p.composerCoord.editorByType(edType);
                        if (editor.isSupportFeature(FEATURE_SELECTION)) {
                            // add select tool into selection feature for the viewport first
                            // so that the unselection can be done later
                            this._p.composerCoord.selectionFeature.addSelectTool(
                                vm.id,
                                this._p.commonToolbar.getTool('select') as SelectTool
                            );
                            const supporter = editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION);
                            const docCtrl = editor.findDocumentByGlobalId(vm.id, el.dataset['globalId']);
                            this._p.curSelectContext.set(vm.id, supporter.selectDoc(docCtrl));
                        }

                        this._p.showEditorToolBar(edType as EditorType);
                    });
                    break;
                }
                case 'viewport-focusout': {
                    const containingDoc = this._p.composerCoord.containingDoc.get(eventData.state.vmId);
                    // we not process this event if the sub viewport being focus is not inside a parent viewport of the tool
                    if (!containingDoc || this._p.toolbar.viewport != containingDoc.viewport) return eventData;

                    const vm = this._p.composerCoord.getViewportManager(eventData.state.vmId);

                    const el = vm.rootEl;
                    const edType = el.dataset['edType'] as EditorType;
                    const editor = this._p.composerCoord.editorByType(edType);

                    this._p.hideAllEditorToolBar();

                    if (editor.isSupportFeature(FEATURE_SELECTION)) {
                        const supporter = editor.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION);
                        const selectContext = this._p.curSelectContext.get(vm.id);

                        if (selectContext) {
                            supporter.deselectDoc(selectContext.doc);
                            this._p.curSelectContext.delete(vm.id);
                        }
                        // clean up
                        this._p.composerCoord.selectionFeature.removeSelectTool(vm.id);
                    }

                    const edToolbar = this._p.toolState.getEditorToolbar(edType as EditorType);
                    edToolbar.detachViewport(vm);
                    edToolbar.unregisterToolListener(this._p.toolListener);
                    this._p.commonToolbar.detachViewport(vm);

                    this._p.composerCoord.blurEditor(edType as EditorType, vm);
                    this._p.composerCoord.switchViewportMode(vm.id, 'Disabled');
                    break;
                }
                case 'viewport-removed': {
                    this._p.hideAllEditorToolBar();
                    break;
                }
            }
            return eventData;
        }
    };

    /**
     * Show the toolbar of the sub editor
     * @param edType the editor type of the sub toolbar to show
     */
    showEditorToolBar(edType: EditorType) {
        this.toolState.currentEditor = edType;
        this.toolbar.update(this.toolType, this.toolState);
    }

    /**
     * Hide all the sub editor toolbar so we can switch to the composer toolbar
     */
    hideAllEditorToolBar() {
        delete this.toolState.currentEditor;
        this.toolbar.update(this.toolType, this.toolState);
    }

    override onDisable(): void {
        super.onDisable();
        this.ham.active = false;
    }

    override onEnable(): void {
        super.onEnable();
        this.ham.active = true;
    }

    /**
     * Override the onBlur method to clear the focus on all tools of the sub-editor toolbar
     */
    override onBlur(): void {
        super.onBlur();
        if (this.toolState.currentEditor) {
            this.toolState.getEditorToolbar(this.toolState.currentEditor)?.clearAllFocus();
        }
    }

    // create / remove toolbars for all the sub editor inside the composer coordinator
    private refreshToolbarList() {
        let change = false;

        const subEditors = this.composerCoord.editors;

        for (const et of subEditors.keys()) {
            if (!this.toolState.hasEditor(et)) {
                change = true;
                const ed = subEditors.get(et)!!;
                const etb = ed.createToolbar();
                this.toolState.setEditorToolbar(et, etb as DefaultToolBar<any, any>);
            }
        }

        for (const et of this.toolState.existingEditors()) {
            if (subEditors.has(et)) continue;
            else {
                change = true;
                this.toolState.remove(et);
            }
        }

        // this method could be called at the construction time when toolbar has not yet been set
        // need to check if the toolbar is there before update state
        if (change && this.toolbar) {
            this.toolbar.update(this.toolType, this.toolState);
        }
    }

    /**
     * Listen to tool event from the child editor toolbar.
     * Currently use to focus on the SubEditorManagerTool when any child editor tool is focused.
     * So the other tool of the root coord (i.g. select, pan tool) can be blurred
     */
    private readonly toolListener = new (class implements ToolEventListener<ToolBar<any, any>, any> {
        constructor(private m: SubEditorManagerTool) {}

        onEvent(eventData: ToolEventData<ToolBar<any, any>, any>): ToolEventData<ToolBar<any, any>, any> {
            if (this.m.toolbar.isDisabled() || this.m.toolbar.isToolDisable(this.m.toolType)) return eventData;

            if (eventData.eventType === 'focus') {
                this.m.toolbar.focus(this.m.toolType);
            }

            return eventData;
        }
    })(this);
}

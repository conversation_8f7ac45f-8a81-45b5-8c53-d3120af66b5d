import {
    CR<PERSON><PERSON><PERSON>,
    <PERSON>ursor,
    KeyboardEventListener,
    KeyboardHandlingItem,
    NativeEventTarget,
    newCursor,
    PointerEventData,
    PointerEventListener,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerType<PERSON>en,
    pointerTypePenMouse,
    ToolState,
    UIPointerEventData,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { ComposerKeyboardEvent } from '../model';
import { ComposerEditor } from '../composer.editor';
import { ComposerToolType } from './models';
import { ComposerTool } from './composer.tool';

export class ComposerCreateDocumentTool extends ComposerTool<ToolState, any> {
    readonly toolType: ComposerToolType = 'CreateComposerDocumentTool';
    private delegator: CRDTool;

    isCreatingDoc$ = new BehaviorSubject<boolean>(false);

    constructor(editor: ComposerEditor) {
        super(editor);

        this.registerPointerHandling(
            { event: 'pointerdown', button: 0, pointerTypes: pointerType<PERSON>enMouse },
            { event: 'pointerup', button: 0, pointerTypes: pointerTypePenMouse },
            { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', pointerTypes: pointerTypePen, numPointer: 1 },

            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 }
        );

        this.registerKeyboardHandling(
            new (class extends KeyboardHandlingItem {
                override global = false;
            })(['esc'])
        );
    }

    pointerHandler = new (class implements PointerEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject<Cursor[] | undefined>([newCursor('crosshair', 0, '', 0, 'system')]);

        constructor(public tool: ComposerCreateDocumentTool) {}

        onEvent(event: PointerEventData<any>): PointerEventData<any> {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            if ('nativeEvent' in event) return this.tool.handlePointerEvent(event);
            else return event;
        }
    })(this);

    override keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public tool: ComposerCreateDocumentTool) {}

        onEvent(event: ComposerKeyboardEvent): ComposerKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleKeyboardEvent(event);
        }
    })(this);

    get toolState(): any {
        return {};
    }

    override onAttachViewport(): void {
        this.delegator = this.editor.initCreateDocTool(this.toolbar.viewport);
        this.delegator.toolbar = this.toolbar;
        this.delegator.isCreatingDoc$ = this.isCreatingDoc$;
    }

    override onDetachViewport(): void {
        this.delegator.destroy(this.toolbar.viewport);
    }

    override onBlur(): void {
        this.delegator?.resetState();
    }

    override onDisable(): void {
        this.delegator?.resetState();
    }

    override onFocus(): void {
        this.delegator?.onFocusCreateDocTool();
    }

    handlePointerEvent(event: UIPointerEventData<any>): UIPointerEventData<any> {
        return this.delegator.handlePointerEvent(event);
    }

    override handleKeyboardEvent(event: ComposerKeyboardEvent): ComposerKeyboardEvent {
        return this.delegator.handleKeyboardEvent(event);
    }
}

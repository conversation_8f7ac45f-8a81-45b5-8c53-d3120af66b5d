import {
    DefaultVDocObjCtrl,
    DocLocalId,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventListener,
    MouseHandlingItem,
    NativeEventTarget,
    PointerHandlingItem,
    Tool,
    ToolBar,
    ToolState,
    UserInputHandlerType,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { ComposerDocCtrl } from '../docs/composer.doc.ctrl';
import { ComposerKeyboardEvent } from '../model';
import { ComposerEditor } from '../composer.editor';
import { composerDocReg } from '../composer.utils';
import { ComposerToolType } from './models';
import { ComposerToolBar } from './composer.toolbar';

export abstract class ComposerTool<TState extends ToolState, OCtrl extends DefaultVDocObjCtrl<any, any>>
    implements Tool
{
    readonly type: UserInputHandlerType = 'Tool';
    childToolbar?: ToolBar<any, Tool>;
    abstract readonly toolType: ComposerToolType;
    abstract readonly toolState: TState;
    readonly mouseHandling: MouseHandlingItem[] = [];
    readonly keyboardHandling: KeyboardHandlingItem[] = [];
    readonly pointerHandling: PointerHandlingItem[] = [];

    toolbar: ComposerToolBar;

    constructor(public editor: ComposerEditor) {}

    mouseHandler: MouseEventListener<NativeEventTarget<any>>;
    readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public tool: ComposerTool<ToolState, any>) {}

        onEvent(event: ComposerKeyboardEvent): ComposerKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleKeyboardEvent(event);
        }
    })(this);

    protected getComposerDoc(vm: ViewportManager, localId: DocLocalId): ComposerDocCtrl {
        return this.editor.regMan.registry<ComposerDocCtrl>(composerDocReg(vm.id))?.getEntity(localId);
    }

    registerMouseHandling(...handling: MouseHandlingItem[]) {
        this.mouseHandling.push(...handling);
    }

    registerKeyboardHandling(...handling: KeyboardHandlingItem[]) {
        this.keyboardHandling.push(...handling);
    }

    registerPointerHandling(...handling: PointerHandlingItem[]) {
        this.pointerHandling.push(...handling);
    }

    registerToolbar(toolbar: ComposerToolBar) {
        this.toolbar = toolbar;
    }

    onBlur() {}
    onFocus() {
        // refocus on the lexical doc to continue typing
        const focusedDocs = this.getFocusedComposerDocCtrls();
        if (focusedDocs.length === 1) {
            console.warn('TODO: implement focus event on composer doc');
            // focusedDocs[0].composerLib.lexical.focus();
        }
    }

    onDisable() {}
    onEnable() {}

    onAttachViewport() {}

    onDetachViewport() {}

    focusAble(vpId: ViewportId): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;

        return this.editor.selectDelegator.getFocusedDocs(vpId)?.length == 1 && true;
    }

    protected getFocusedComposerDocCtrls(): ComposerDocCtrl[] {
        const vm = this.toolbar.viewport;

        if (!vm) {
            throw new Error(
                'The toolbar of the composer editor has not been attached to any viewport. Cannot insert document!'
            );
        }

        return this.editor.selectDelegator.getFocusedDocs(vm.id);
    }

    protected executeInFocusedDocCtrl(cb: (doc: ComposerDocCtrl) => any) {
        const docCtrls = this.getFocusedComposerDocCtrls();
        if (!docCtrls?.length) return;

        cb(docCtrls[0]);
        console.warn('TODO: implement re-focus on composer doc');
        // docCtrls[0].composerLib.lexical.focus();
    }

    handleKeyboardEvent(event: ComposerKeyboardEvent): ComposerKeyboardEvent {
        return event;
    }
}

import { EditorType, isCtrlOrMeta, KeyboardHandlingItem, ToolState } from '@viclass/editor.core';
import { ComposerKeyboardEvent } from '../model';
import { ComposerEditor } from '../composer.editor';
import { ComposerToolType } from './models';
import { ComposerTool } from './composer.tool';

const SUPPORTED_EDITORS: EditorType[] = ['FreeDrawingEditor', 'GeometryEditor', 'MathEditor', 'MathGraphEditor'];

/**
 * This tool allows insertion of document of configured editors into a composer document.
 *
 * The list of available editors whose documents can be inserted are added when the composer editor is created.
 */
export class DocInsertTool extends ComposerTool<ToolState, any> {
    readonly toolType: ComposerToolType = 'InsertionEditorTool';
    toolState: ToolState;

    constructor(editor: ComposerEditor) {
        super(editor);

        ['q', 'Q', ...SUPPORTED_EDITORS.map((_, idx) => String(idx + 1))].forEach(key =>
            this.registerKeyboardHandling(
                new (class extends KeyboardHandlingItem {
                    override global = true;
                    override event = 'keydown';
                })(['ctrl', key])
            )
        );
    }

    insert(editorType: EditorType) {
        const vm = this.toolbar.viewport;

        if (!vm) {
            throw new Error(
                'The toolbar of the composer editor has not been attached to any viewport. Cannot insert document!'
            );
        }

        const docs = this.editor.selectDelegator.getFocusedDocs(vm.id);

        if (docs.length != 1) {
            return;
        }

        // create a command to create internal viewport and create necessary document
        docs[0].processInsertViewportEvent(editorType); // insert viewport at current location
    }

    override handleKeyboardEvent(event: ComposerKeyboardEvent): ComposerKeyboardEvent {
        const isActive = this.toolbar.isToolActive('InsertionEditorTool');
        if (isCtrlOrMeta(event.nativeEvent) && event.nativeEvent.code === 'KeyQ') {
            if (isActive) {
                this.toolbar.blur('InsertionEditorTool');
            } else {
                this.toolbar.focus('InsertionEditorTool');
            }
            event.nativeEvent.preventDefault();
            event.continue = false;
            return event;
        }

        if (isActive && isCtrlOrMeta(event.nativeEvent)) {
            const editorType = SUPPORTED_EDITORS[parseInt(event.nativeEvent.key) - 1];
            if (editorType) {
                this.insert(editorType);
                this.toolbar.blur('InsertionEditorTool');

                event.nativeEvent.preventDefault();
                event.continue = false;
            }
            return event;
        }

        return event;
    }
}

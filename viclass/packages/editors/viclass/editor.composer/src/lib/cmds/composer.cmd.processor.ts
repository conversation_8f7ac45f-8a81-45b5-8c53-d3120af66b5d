import {
    AbstractCommand,
    CmdMeta,
    CmdOriginType,
    CmdProcessor,
    DOMElementLayerCtrl,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    ViewportManager,
} from '@viclass/editor.core';
import { ComposerCmdTypeProto } from '@viclass/proto/editor.composer';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { ComposerEditor } from '../composer.editor';
import { ComposerDocInitData } from '../composer.models';
import { composerDocReg, composerLayerReg } from '../composer.utils';
import { ComposerDocCtrl } from '../docs/composer.doc.ctrl';
import { ComposerDoc, ComposerDocSettings, ComposerLayer } from '../model';
import { UpdateDocSettingsCmd, WrappingSubEditorCmd } from './composer.cmd';
import { ComposerWrappingCmdGateway } from './composer.wrapping.cmd.gateway';

export class ComposerCmdProcessor extends CmdProcessor {
    constructor(private editor: ComposerEditor) {
        super();
    }

    async processCmd(
        cmd: AbstractCommand<ComposerCmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<ComposerCmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case FCCmdTypeProto.INSERT_DOC: {
                return this.processInsertDocCmd(cmd as FCInsertDocCmd);
            }
            case FCCmdTypeProto.PREVIEW_BOUNDARY: {
                this.processNewDocBoundaryCmd(cmd as FCPreviewBoundaryCmd);
                break;
            }
            case FCCmdTypeProto.INSERT_LAYER: {
                this.processInsertLayerCmd(cmd as FCInsertLayerCmd);
                break;
            }
            case FCCmdTypeProto.REMOVE_DOC: {
                await this.processRemoveDocCmd(cmd as FCRemoveDocCmd);
                break;
            }
            case FCCmdTypeProto.RELOAD_DOC: {
                return this.processReloadCmd(cmd as FCReloadDocCmd);
            }
            case FCCmdTypeProto.UPDATE_BOUNDARY: {
                return this.processUpdateBoundaryCmd(cmd as FCUpdateDocCmd);
            }

            case ComposerCmdTypeProto.WRAPPING_SUB_EDITOR_CMD: {
                this.processWrappingCmd(cmd as WrappingSubEditorCmd);
                break;
            }
            case ComposerCmdTypeProto.UPDATE_DOC_SETTINGS: {
                this.processUpdateDocSettings(cmd as UpdateDocSettingsCmd);
                break;
            }
            default:
                break;
        }
        return cmd;
    }

    /**
     * Process update composer document settings
     */
    processUpdateDocSettings(cmd: UpdateDocSettingsCmd) {
        const vm = cmd.meta.viewport;
        const docRegistry = this.editor.regMan.registry<ComposerDocCtrl>(composerDocReg(vm.id));

        cmd.state.getSettingsList().forEach(settingObj => {
            const localId = settingObj.getDocLocalId();
            const docCtrl = docRegistry.getEntity(localId);

            const settingJSON = settingObj.getSettingJson();
            const settings = JSON.parse(settingJSON) as ComposerDocSettings;

            docCtrl.applySettings(settings);
        });

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Process the wrapped command from the sub editor
     */
    processWrappingCmd(cmd: WrappingSubEditorCmd) {
        // only processing remote wrapping command, because local one
        // is not supposed to be processed since it is produced by local editor
        // whose processor already process the underlying command
        if (cmd.meta.origin === CmdOriginType.remote) {
            const underlying = cmd.state.getUnderlying_asU8();
            (this.editor.composerCoord.cmdGateway as ComposerWrappingCmdGateway).receive(underlying);
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private processNewDocBoundaryCmd(cmd: FCPreviewBoundaryCmd): FCPreviewBoundaryCmd {
        const result = this.editor.crdFeature.processDocCreationPreviewCmd(cmd, this.editor);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return result;
    }

    private async processInsertDocCmd(cmd: FCInsertDocCmd) {
        const initData = cmd.state.getInitdata() as Uint8Array;
        const decoder = new TextDecoder();
        const initObj: ComposerDocInitData = JSON.parse(decoder.decode(initData));

        if (cmd.meta.origin === CmdOriginType.remote && (!cmd.state.getGlobalId() || !initObj.viewportElClass))
            throw new Error('State data for new composer document is not complete!');

        const doc = new ComposerDoc(
            cmd.meta.targetId,
            cmd.state.getGlobalId(),
            initObj.viewportElClass,
            initObj.content
        );

        this.editor.insertDocDelegator.insertDocCtrl(cmd.meta.viewport, doc);

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);

        return cmd;
    }

    private processInsertLayerCmd(cmd: FCInsertLayerCmd) {
        const layerId = cmd.meta.targetId;
        const docId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;
        const zindex = cmd.state.getZIndex();

        const docRegistry = this.editor.regMan.registry<ComposerDocCtrl>(composerDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(docId);

        const layerState = new ComposerLayer(layerId, docId, fcConvertProtoToBoundary(cmd.state.getBoundary()));

        const layerCtrl = docCtrl.viewport.requestLayer(DOMElementLayerCtrl, true, {
            boundary: layerState.boundary,
            docLocalId: docId,
            docGlobalId: docCtrl.state.globalId,
            viewport: docCtrl.viewport,
            editor: this.editor,
            state: layerState,
            domElType: 'div',
        }) as DOMElementLayerCtrl;

        layerCtrl.doc = docCtrl;
        layerCtrl.zindex = zindex;
        docCtrl.addLayer(layerCtrl);
        docCtrl.initializeLayerContent();

        const layerRegistry = this.editor.regMan.registry<DOMElementLayerCtrl>(
            composerLayerReg(cmd.meta.viewport.id, docId)
        );
        layerRegistry.addEntity(layerId, layerCtrl);

        this.editor.insertDocDelegator.notifyDocCreation(vm, docId);

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private async processRemoveDocCmd(cmd: FCRemoveDocCmd) {
        const l = cmd.state.getIdsList();
        for (const id of l) {
            await this.editor.internalRemoveDoc(cmd.meta.viewport.id, id.getLocalId());
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    async processReloadCmd(cmd: FCReloadDocCmd): Promise<FCReloadDocCmd> {
        const promises = cmd.state
            .getLocalidList()
            .map(localId => this.editor.crdFeature.reloadLocalDoc(this.editor, cmd.meta.viewport.id, localId));
        await Promise.all(promises);

        this.editor.insertDocDelegator.notifyDocCreation(cmd.meta.viewport, ...cmd.state.getLocalidList());
        return cmd;
    }

    private async processUpdateBoundaryCmd(cmd: FCUpdateDocCmd) {
        this.editor.boundaryDelegator.processUpdateBoundaryCmd(cmd, ComposerEditor.cmdChannelThrottle);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return cmd;
    }

    isLocalCmd(cmd: AbstractCommand<any>) {
        return cmd.meta.origin === CmdOriginType.local;
    }

    private clearCurrentViewportHistoryIfRemoteCmd(meta: CmdMeta) {
        if (meta.origin === CmdOriginType.remote) this.editor.clearHistory(meta.viewport.id);
    }
}

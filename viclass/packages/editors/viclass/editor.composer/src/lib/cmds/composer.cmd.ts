import {
    AbstractCommand,
    BoundaryRectangle,
    Cmd,
    CmdMeta,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    Position,
    Rectangle,
} from '@viclass/editor.core';
import {
    ComposerCmdTypeProto,
    ComposerPositionProto,
    DocSettingsProto,
    InsertDocCmdProto,
    InsertLayerCmdProto,
    InsertViewportCmdProto,
    NewDocBoundaryCmdProto,
    UpdateDocSettingsCmdProto,
    UpdateInternalDocMappingCmdProto,
    UpdateViewportViewStateCmdProto,
    WrappingSubEditorCmdProto,
} from '@viclass/proto/editor.composer';
import { ProtoMessage, ProtoMessageStatic } from '@viclass/proto/editor.core';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { ComposerDocSettings } from '../model';
import { buildBoundaryProto } from './composer.cmd.utils';

export class AbstractProtoCmd<T extends ProtoMessage> extends AbstractCommand<ComposerCmdTypeProto> {
    override state: T;

    constructor(
        meta: CmdMeta,
        private stateClass: ProtoMessageStatic<T>
    ) {
        super(meta, meta.cmdType);
        this.state = new this.stateClass();
    }

    override serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    override deserialize(buf: Uint8Array): T {
        return this.stateClass.deserializeBinary(buf);
    }
}
export class InsertDocCmd extends AbstractProtoCmd<InsertDocCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, InsertDocCmdProto);
    }

    setState(boundary: Rectangle, globalId: string, viewportElClass: string, content: string) {
        this.state
            .setBoundary(buildBoundaryProto(boundary))
            .setViewportElClass(viewportElClass)
            .setContent(content)
            .setGlobalId(globalId);
    }

    setGlobalId(globalId: string) {
        this.state.setGlobalId(globalId);
    }

    setViewportElClass(c: string) {
        this.state.setViewportElClass(c);
    }

    setContent(content: string) {
        this.state.setContent(content);
    }
}

export class InsertLayerCmd extends AbstractProtoCmd<InsertLayerCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, InsertLayerCmdProto);
    }

    setState(boundary: BoundaryRectangle, zIndex: number) {
        this.state.setBoundary(buildBoundaryProto(boundary)).setZIndex(zIndex);
    }
}

export class NewDocBoundaryCmd extends AbstractProtoCmd<NewDocBoundaryCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, NewDocBoundaryCmdProto);
    }

    setState(boundary: Rectangle) {
        this.state.setBoundary(buildBoundaryProto(boundary));
    }
}

export class UpdateDocSettingsCmd extends AbstractProtoCmd<UpdateDocSettingsCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, UpdateDocSettingsCmdProto);
    }

    addDocSettings(localId: number, globalId: string, settings: ComposerDocSettings) {
        const settingProto = new DocSettingsProto()
            .setDocLocalId(localId)
            .setDocGlobalId(globalId)
            .setSettingJson(JSON.stringify(settings));
        this.state.addSettings(settingProto);
    }
}

export class InputInsertViewport extends AbstractProtoCmd<InsertViewportCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, InsertViewportCmdProto);
    }
}

export class WrappingSubEditorCmd extends AbstractProtoCmd<WrappingSubEditorCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, WrappingSubEditorCmdProto);
    }
}

export class UpdateInternalDocMappingCmd extends AbstractProtoCmd<UpdateInternalDocMappingCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, UpdateInternalDocMappingCmdProto);
    }
}

export class UpdateInternalViewportViewStateCmd extends AbstractProtoCmd<UpdateViewportViewStateCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, UpdateViewportViewStateCmdProto);
    }

    setState(vmId: string, lookAt: Position, zoom: number) {
        this.state
            .setLookAt(new ComposerPositionProto().setX(lookAt.x).setY(lookAt.y))
            .setZoom(zoom)
            .setInternalVmId(vmId);
    }
}

export function deserializer(meta: CmdMeta, stateData: Uint8Array): Cmd<FCCmdTypeProto | ComposerCmdTypeProto> {
    let cmd: Cmd<FCCmdTypeProto | ComposerCmdTypeProto>;
    const cmdType = meta.cmdType as FCCmdTypeProto | ComposerCmdTypeProto;

    switch (cmdType) {
        case FCCmdTypeProto.INSERT_DOC: {
            cmd = new FCInsertDocCmd(meta);
            break;
        }
        case FCCmdTypeProto.REMOVE_DOC: {
            cmd = new FCRemoveDocCmd(meta);
            break;
        }
        case FCCmdTypeProto.INSERT_LAYER: {
            cmd = new FCInsertLayerCmd(meta);
            break;
        }
        case FCCmdTypeProto.PREVIEW_BOUNDARY: {
            cmd = new FCPreviewBoundaryCmd(meta);
            break;
        }

        case FCCmdTypeProto.RELOAD_DOC: {
            cmd = new FCReloadDocCmd(meta);
            break;
        }

        case FCCmdTypeProto.UPDATE_BOUNDARY: {
            cmd = new FCUpdateDocCmd(meta);
            break;
        }

        // case ComposerCmdTypeProto.INPUT_INSERT_VIEWPORT: {
        //     cmd = new InputInsertViewport(meta);
        //     break;
        // }

        case ComposerCmdTypeProto.WRAPPING_SUB_EDITOR_CMD: {
            cmd = new WrappingSubEditorCmd(meta);
            break;
        }

        case ComposerCmdTypeProto.UPDATE_INTERNAL_DOC_MAPPING: {
            cmd = new UpdateInternalDocMappingCmd(meta);
            break;
        }

        case ComposerCmdTypeProto.UPDATE_VIEWPORT_VIEW_STATE: {
            cmd = new UpdateInternalViewportViewStateCmd(meta);
            break;
        }

        case ComposerCmdTypeProto.UPDATE_DOC_SETTINGS: {
            cmd = new UpdateDocSettingsCmd(meta);
            break;
        }

        default:
            throw new Error(`invalid cmd type: ${cmdType}`);
    }

    const state = cmd.deserialize(stateData);
    cmd.state = state;

    return cmd;
}

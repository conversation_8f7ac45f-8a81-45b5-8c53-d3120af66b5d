import {
    bytesToStrId,
    Cmd,
    CmdChannel,
    CmdGateway,
    CmdMeta,
    cmdMeta,
    CmdOriginType,
    delayPromise,
    isLocalContentDocId,
    reliableCmdMeta,
    strIdToBytes,
    ViewportManager,
} from '@viclass/editor.core';
import { ComposerCmdChannel } from './composer.cmd.channel';
import { CmdMetaProto } from '@viclass/proto/editor.core';
import { ComposerCmdTypeProto } from '@viclass/proto/editor.composer';
import { WrappingSubEditorCmd } from './composer.cmd';
import { DirectBackendDataSaver } from '@viclass/editor.coordinator/common';
import { ComposerEditorCoordinator } from '../coord/composer.coordinator';

export class ComposerWrappingCmdGateway implements CmdGateway {
    constructor(
        private parentChannel: CmdChannel,
        private composerCoord: ComposerEditorCoordinator,
        private dataSaver: DirectBackendDataSaver
    ) {}

    private allowEmission: boolean = false;
    private cmdQueue: Uint8Array[] = [];
    private viewportCmdQueue: Map<string, { ready: boolean; receiveQueue: [CmdChannel, Cmd<any>][] }> = new Map();
    private channels: { [channelCode: number]: CmdChannel } = {};
    private idCache: { [id: string]: Uint8Array } = {};
    private stopped = true;

    async initialize(): Promise<void> {}

    async start() {
        if (!this.stopped) {
            console.log('cmd gateways started already, cannot start again');
            return;
        }
        // console.log('Started cmd gateways');
        this.stopped = false;
        this.allowEmission = true;
        this.doStart();
    }

    private doStart() {
        const fn = async () => {
            while (!this.stopped) {
                if (!this.allowEmission) {
                    await delayPromise(10);
                    continue;
                }
                // apply all the command
                const cmd = this.cmdQueue.shift();
                if (!cmd) {
                    await delayPromise(10);
                    continue;
                }
                this.proceedReceivedCmd(cmd);
            }
        };
        fn();
    }

    async pause() {
        this.allowEmission = false;
    }

    async resume() {
        this.allowEmission = true;
    }

    async stop() {
        this.allowEmission = false;
        this.stopped = true;
    }

    registerChannel(channelCode: number): CmdChannel {
        if (!this.channels[channelCode]) this.channels[channelCode] = new ComposerCmdChannel(channelCode, this);

        return this.channels[channelCode];
    }

    /**
     * Register the viewport to gateway when its readies to receive cmd
     * The cmd that received before the viewport ready will be cached
     * When the viewport is registered, all cached cmd will be sent to editor through channel
     * @param vpId
     */
    registerViewport(vpId: string) {
        if (!this.viewportCmdQueue.has(vpId)) this.viewportCmdQueue.set(vpId, { ready: false, receiveQueue: [] });

        const vm = this.composerCoord.getViewportManager(vpId);
        const vp = this.viewportCmdQueue.get(vpId);
        vp.ready = true;
        while (vp.receiveQueue.length > 0) {
            const cmd = vp.receiveQueue.shift();

            if (!cmd[1].meta.viewport) {
                cmd[1].meta.viewport = vm; // fill the missing ViewportManager
            }
            cmd[0].receive(cmd[1]);
        }
    }

    unregisterViewport(vpId: string) {
        if (!this.viewportCmdQueue.has(vpId)) return;

        const vp = this.viewportCmdQueue.get(vpId);
        vp.ready = false;
        while (vp.receiveQueue.length > 0) {
            const cmd = vp.receiveQueue.shift();
            cmd[0].receive(cmd[1]);
        }

        this.viewportCmdQueue.delete(vpId);
    }

    sync(cmd: Cmd<any>) {
        try {
            // serialize meta data
            const msgMeta = new CmdMetaProto()
                .setChannelCode(cmd.meta.channelCode)
                .setCmdType(cmd.meta.cmdType)
                .setTargetId(cmd.meta.targetId);

            const vm = cmd.meta.viewport as ViewportManager; // we know that this command gateway is used within classroom context
            const cid: string = vm.id;
            if (!this.idCache[cid]) this.idCache[cid] = strIdToBytes(cid);

            if (cmd.meta.versionable != undefined) msgMeta.setVersionable(cmd.meta.versionable);
            if (cmd.meta.sequence != undefined) msgMeta.setSequence(cmd.meta.sequence);

            const metaArrBuf = msgMeta.serializeBinary();
            const stateArrBuf = cmd.serialize();
            const final = new Uint8Array(
                this.idCache[cid].byteLength + 1 + metaArrBuf.byteLength + 1 + stateArrBuf.byteLength
            );

            let index = 0;
            final.set([this.idCache[cid].byteLength], index++);
            final.set(this.idCache[cid], index);
            index += this.idCache[cid].byteLength;
            const cmdStartIndex = index;
            final.set([metaArrBuf.byteLength], index++);
            final.set(metaArrBuf, index);
            index += metaArrBuf.byteLength;
            final.set(stateArrBuf, index);

            // TODO: putting commands to be saved into a queue and then submit as batch, editor backend should support
            // receiving a list of commands and then gradually processing them
            const cmdToSave = final.slice(cmdStartIndex);
            const globalId = this.composerCoord.getGlobalId(vm);
            const localId = this.composerCoord.getLocalId(vm);
            const parentVP = this.composerCoord.getParentViewport(vm);
            if (!isLocalContentDocId(globalId)) {
                // save to editor backend only if this is not local content doc
                this.dataSaver.saveData(cmd.meta, cmdToSave, cmd.meta.channelCode, globalId);
            }

            // wrap the original command inside a composer command
            let wrapMeta: CmdMeta;
            if (cmd.meta.reliable)
                wrapMeta = reliableCmdMeta(parentVP, localId, localId, ComposerCmdTypeProto.WRAPPING_SUB_EDITOR_CMD);
            // wrap command needs not be saved!
            else wrapMeta = cmdMeta(parentVP, localId, ComposerCmdTypeProto.WRAPPING_SUB_EDITOR_CMD);

            const wrapCmd = new WrappingSubEditorCmd(wrapMeta);
            wrapCmd.state.setUnderlying(final);
            this.parentChannel.receive(wrapCmd);
        } catch (err) {
            console.error('sync cmd failed... ', err);
            throw err;
        }
    }

    receive(cmd: Uint8Array) {
        if (this.allowEmission && this.cmdQueue.length == 0) {
            this.proceedReceivedCmd(cmd);
        } else {
            this.cmdQueue.push(cmd);
        }
    }

    proceedReceivedCmd(cmd: Uint8Array) {
        let index = 0;
        const cidLength = cmd[index++];
        const cidBytes = cmd.slice(index, index + cidLength);

        const cid = bytesToStrId(cidBytes);

        index += cidLength;
        const metaLength = cmd[index++];
        const metaArrBuf = cmd.slice(index, index + metaLength);
        index += metaLength;
        const stateData = cmd.slice(index);

        const metaProto = CmdMetaProto.deserializeBinary(metaArrBuf);

        const vm = this.composerCoord.getViewportManager(cid);

        const cmdMeta: CmdMeta = {
            channelCode: metaProto.getChannelCode(),
            targetId: metaProto.getTargetId(),
            cmdType: metaProto.getCmdType(),
            origin: CmdOriginType.remote,
            reliable: true, // not very important to the receiving end
            notSync: true, // no need further syncing
            sequence: metaProto.getSequence(),
            versionable: metaProto.getVersionable(),
            viewport: vm,
        };

        const channel = this.channels[cmdMeta.channelCode];

        const viewport = this.viewportCmdQueue.get(cid);
        if (viewport && viewport.ready && viewport.receiveQueue.length <= 0) {
            channel?.receive(channel.deserialize(cmdMeta, stateData));
        } else {
            if (!viewport)
                this.viewportCmdQueue.set(cid, {
                    ready: false,
                    receiveQueue: [],
                });
            if (channel)
                this.viewportCmdQueue.get(cid).receiveQueue.push([channel, channel.deserialize(cmdMeta, stateData)]);
        }
    }
}

import { BoundaryRectangle, Position } from '@viclass/editor.core';
import { ComposerBoundaryProto, ComposerPositionProto } from '@viclass/proto/editor.composer';

export function buildBoundaryProto(boundary: BoundaryRectangle): ComposerBoundaryProto {
    return new ComposerBoundaryProto()
        .setStart(new ComposerPositionProto().setX(boundary.start.x).setY(boundary.start.y))
        .setEnd(new ComposerPositionProto().setX(boundary.end.x).setY(boundary.end.y));
}

export function convertProtoToBoundary(boundary: ComposerBoundaryProto): BoundaryRectangle {
    const start = {
        x: boundary.getStart().getX(),
        y: boundary.getStart().getY(),
    };
    const end = { x: boundary.getEnd().getX(), y: boundary.getEnd().getY() };
    return {
        start: start,
        end: end,
        width: Math.abs(end.x - start.x),
        height: Math.abs(end.y - start.y),
    };
}

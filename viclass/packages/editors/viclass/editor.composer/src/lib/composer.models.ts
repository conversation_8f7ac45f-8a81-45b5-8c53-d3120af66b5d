import { DocFocusedES, FocusDocEvent } from '@viclass/editor.core';
import { ComposerDocCtrl } from './docs/composer.doc.ctrl';

export type ComposerDocFocusedES = DocFocusedES<ComposerDocCtrl>;

export type ComposerDocEvent = FocusDocEvent<ComposerDocCtrl>;

/**
 * Interface for the tool that need to listen to the internal lexical doc context
 * Ex: ComposerContextTool, ComposerTableTool,... that need to listen to any changes in the composer doc
 */
export interface ComposerToolDocListener {
    onDocAttached(docCtrl?: ComposerDocCtrl): void;
    onDocDetached(docCtrl?: ComposerDocCtrl): void;
}

export type ComposerDocInitData = {
    viewportElClass: string;
    content: string;
};

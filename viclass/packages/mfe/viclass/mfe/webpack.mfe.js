const abs = require('../../../webpack/helpers');
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const shared = require('../../../webpack/webpack.mf.wpshare');
const share = shared.share;

const projectFolder = 'packages/mfe/viclass/mfe';

module.exports = {
    experiments: {
        outputModule: true,
    },
    optimization: {
        splitChunks: {
            cacheGroups: {
                rxjsCommon: {
                    test: /[\\/]node_modules[\\/](rxjs)/,
                    name: 'vendors_rxjs',
                    chunks: 'all',
                },
                angularCDK: {
                    test: /[\\/]node_modules[\\/]@angular[\\/]cdk/,
                    name: 'vendors_angular_cdk',
                    chunks: 'all',
                },
                angularMaterial: {
                    test: /[\\/]node_modules[\\/]@angular[\\/]material/,
                    name: 'vendors_angular_material',
                    chunks: 'all',
                },
                polyfills: {
                    test: /[\\/]node_modules[\\/]tslib/,
                    name: 'polyfill_tslib',
                    chunks: 'all',
                },
                angularAnimations: {
                    test: /[\\/]node_modules[\\/]@angular[\\/]animations/,
                    name: 'vendors_angular_animations',
                    chunks: 'all',
                },
                lexical: {
                    test: /[\\/]node_modules[\\/]@lexical/,
                    name: 'vendors_lexical',
                    chunks: 'all',
                },
                yjs: {
                    test: /[\\/]node_modules[\\/]yjs/,
                    name: 'vendors_yjs',
                    chunks: 'all',
                },
                cortexJs: {
                    test: /[\\/]node_modules[\\/]@cortexjs/,
                    name: 'vendors_cortex_js',
                    chunks: 'all',
                },
            },
        },
    },
    output: {
        filename: '[name].[contenthash:9].js',
        chunkFilename: '[id].[contenthash:9].js',
        clean: true,
    },
    plugins: [
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'coordinator.docloader',
            filename: 'coordinator.docloader.js',
            exposes: {
                './coordinator.docloader': abs(`${projectFolder}/src/coordinator.docloader.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareInternal'],
                ...share('@viclass/editor.coordinator/docloader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.coordinator/common', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'coordinator.embed',
            filename: 'coordinator.embed.js',
            exposes: {
                './coordinator.embed': abs(`${projectFolder}/src/coordinator.embed.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareInternal'],
                ...share('@viclass/editor.coordinator/embed', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.coordinator/common', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editor.freedrawing',
            filename: 'editor.freedrawing.js',
            exposes: {
                './editor.freedrawing': abs(`${projectFolder}/src/editor.freedrawing.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareProto'],
                ...share('@viclass/editor.freedrawing', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.freedrawing',
            filename: 'editorui.freedrawing.js',
            exposes: {
                './editorui.freedrawing': abs(`${projectFolder}/src/editorui.freedrawing.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareProto'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.freedrawing', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editor.geo',
            filename: 'editor.geo.js',
            exposes: {
                './editor.geo': abs(`${projectFolder}/src/editor.geo.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareProto'],
                ...share('perfect-freehand'),
                ...share('@viclass/editor.geo', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.geo',
            filename: 'editorui.geo.js',
            exposes: {
                './editorui.geo': abs(`${projectFolder}/src/editorui.geo.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareMathlive'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.geo', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editor.word',
            filename: 'editor.word.js',
            exposes: {
                './editor.word': abs(`${projectFolder}/src/editor.word.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareProto'],
                ...shared['shareLexical'],
                ...shared['shareYjs'],
                ...share('@viclass/editor.word', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.coordinator/common', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.word',
            filename: 'editorui.word.js',
            exposes: {
                './editorui.word': abs(`${projectFolder}/src/editorui.word.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.word', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editor.composer',
            filename: 'editor.composer.js',
            exposes: {
                './editor.composer': abs(`${projectFolder}/src/editor.composer.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareProto'],
                ...share('@viclass/editor.composer', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.coordinator/common', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.composer',
            filename: 'editorui.composer.js',
            exposes: {
                './editorui.composer': abs(`${projectFolder}/src/editorui.composer.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.composer', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editor.math',
            filename: 'editor.math.js',
            exposes: {
                './editor.math': abs(`${projectFolder}/src/editor.math.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareProto'],
                ...shared['shareMathlive'],
                ...shared['shareComputeEngine'],
                ...share('@viclass/editor.math', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.math',
            filename: 'editorui.math.js',
            exposes: {
                './editorui.math': abs(`${projectFolder}/src/editorui.math.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.math', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editor.magh',
            filename: 'editor.magh.js',
            exposes: {
                './editor.magh': abs(`${projectFolder}/src/editor.magh.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareProto'],
                ...shared['shareMathlive'],
                ...shared['shareComputeEngine'],
                ...share('@viclass/editor.magh', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.magh',
            filename: 'editorui.magh.js',
            exposes: {
                './editorui.magh': abs(`${projectFolder}/src/editorui.magh.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...shared['shareMathlive'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.magh', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.commontools',
            filename: 'editorui.commontools.js',
            exposes: {
                './editorui.commontools': abs(`${projectFolder}/src/editorui.commontools.ts`),
                './editorui.zoomtools': abs(`${projectFolder}/src/editorui.zoomtools.ts`),
                './editorui.embedtools': abs(`${projectFolder}/src/editorui.embedtools.ts`),
                './editorui.contextmenutool': abs(`${projectFolder}/src/editorui.contextmenutool.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.core', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.classroomtools',
            filename: 'editorui.classroomtools.js',
            exposes: {
                './editorui.classroomtools': abs(`${projectFolder}/src/editorui.classroomtools.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
                ...share('@viclass/editor.core', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
        new ModuleFederationPlugin({
            runtime: false,
            library: { type: 'module' },
            name: 'editorui.loader.webcomp',
            filename: 'editorui.loader.webcomp.js',
            exposes: {
                './editorui.loader.webcomp': abs(`${projectFolder}/src/editorui.loader.webcomp.ts`),
            },
            shared: {
                ...shared['shareCommon'],
                ...shared['shareAngular'],
                ...shared['shareInternal'],
                ...share('@viclass/editorui.loader', {
                    requiredVersion: '*',
                    strictVersion: false,
                }),
            },
        }),
    ],
};

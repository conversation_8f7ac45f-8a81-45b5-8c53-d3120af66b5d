const editorConfSpecsMap = {
    'docloader-coordinator': {
        item: 'docloader-coordinator',
        impl: true,
    },
    'editor-ui-base-style': {
        item: 'editor-ui-base-style',
        ui: true,
    },
    'viclass-cursor-style': {
        item: 'viclass-cursor-style',
        ui: true,
    },
    'viclass-embed-style': {
        item: 'viclass-embed-style',
        ui: true,
    },
    'viclass-math-static-style': {
        item: 'viclass-math-static-style',
        ui: true,
    },
    'editorui-loader-webcom': {
        item: 'editorui-loader-webcom',
        ui: true,
    },
    'embed-tools-editor': {
        item: 'embed-tools-editor',
        ui: true,
    },
    'free-drawing-editor': {
        item: 'free-drawing-editor',
        impl: {
            useCase: 'embed',
        },
        ui: true,
    },
    'geometry-editor': {
        item: 'geometry-editor',
        impl: {
            useCase: 'embed',
        },
        ui: true,
    },
    'math-editor': {
        item: 'math-editor',
        impl: {
            useCase: 'embed',
        },
        ui: true,
    },
    'magh-editor': {
        item: 'magh-editor',
        impl: {
            useCase: 'embed',
        },
        ui: true,
    },
    'word-editor': {
        item: 'word-editor',
        impl: {
            settings: {
                embedded: ['free-drawing-editor', 'geometry-editor', 'math-editor', 'magh-editor'],
            },
            useCase: 'embed',
        },
        ui: {
            settings: {
                embedded: ['free-drawing-editor', 'geometry-editor', 'math-editor', 'magh-editor'],
            },
        },
    },
    'composer-editor': {
        item: 'composer-editor',
        impl: {
            settings: {
                embedded: ['free-drawing-editor', 'geometry-editor', 'math-editor', 'magh-editor', 'word-editor'],
            },
            useCase: 'embed',
        },
        ui: {
            settings: {
                embedded: ['free-drawing-editor', 'geometry-editor', 'math-editor', 'magh-editor', 'word-editor'],
            },
        },
    },
};

const editorConfSpecs = new Map<string, any>(Object.entries(editorConfSpecsMap));

export default editorConfSpecs;

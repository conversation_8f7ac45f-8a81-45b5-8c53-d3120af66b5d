/// <reference types="../typings" />

import type { MFEConfResponse } from '@viclass/config.server';
import type { WrappingBoardViewportManager } from '@viclass/editor.coordinator/common';
import type {
    BoardType,
    DocLoaderCoordinator,
    DocLoaderCoordinatorConfig,
    DocLoadingConfig,
} from '@viclass/editor.coordinator/docloader';
import {
    loadThemeModules,
    type CommonToolType,
    type EditorLookup,
    type EditorType,
    type ModuleLookup,
    type ModuleLookupWithSettings,
    type PanEventData,
    type VEventListener,
    type ViewportManager,
    type ZoomEventData,
} from '@viclass/editor.core';
import { EditorUILoaderComponent, EditorUILoaderEvent, EditorUILookup } from '@viclass/editorui.loader';
import { BehaviorSubject, ReplaySubject, filter, firstValueFrom, map } from 'rxjs';
import editorConfSpecs from './environments/module.conf.spec';
import { RestGateway } from './rest.gateway';

require('./mfe.runtime');

export namespace ViDocLoader {
    const editorList: Map<string, string> = new Map(
        Object.entries({
            GeometryEditor: 'geometry-editor',
            FreeDrawingEditor: 'free-drawing-editor',
            WordEditor: 'word-editor',
            MathEditor: 'math-editor',
            MathGraphEditor: 'magh-editor',
            ComposerEditor: 'composer-editor',
        })
    );

    let idIncrement = 0;

    const loadedModule: Map<string, BehaviorSubject<any>> = new Map();

    const appliedStyleSheets: CSSStyleSheet[] = [];

    const mfeConf$ = new ReplaySubject<MFEConfResponse>(1);

    // editor lookups
    const edConf$ = mfeConf$
        .pipe(
            map(resp => {
                return resp.descs.filter(v => editorList.has(v.item)).map(desc => desc.impl as EditorLookup);
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(ls => {
            ls.map(l => {
                loadedModule.get(editorList.get(l.editorType)).next(l);
            });
        });

    const embedUIConf$ = mfeConf$
        .pipe(
            map(resp => {
                return resp.descs.find(v => v.item == 'EmbedToolsEditor')?.ui as EditorUILookup;
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(l => {
            loadedModule.get('embed-tools-editor').next(l);
        });

    const editorUiBaseStyleModuleConf$ = mfeConf$
        .pipe(
            map(resp => {
                return resp.descs.find(v => v.item == 'EditorUIBaseStyle')?.ui as ModuleLookup;
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(l => {
            loadedModule.get('editor-ui-base-style').next(l);
        });

    const viclassCursorStyleModuleConf$ = mfeConf$
        .pipe(
            map(resp => {
                return resp.descs.find(v => v.item == 'ViclassCursorStyle')?.ui as ModuleLookup;
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(l => {
            loadedModule.get('viclass-cursor-style').next(l);
        });

    const viclassEmbedStyleModuleConf$ = mfeConf$
        .pipe(
            map(resp => {
                return resp.descs.find(v => v.item == 'ViclassEmbedStyle')?.ui as ModuleLookup;
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(l => {
            loadedModule.get('viclass-embed-style').next(l);
        });

    const viclassMathStaticStyleModuleConf$ = mfeConf$
        .pipe(
            map(resp => {
                return resp.descs.find(v => v.item == 'ViclassMathStaticStyle')?.ui as ModuleLookup;
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(l => {
            loadedModule.get('viclass-math-static-style').next(l);
        });

    // coordinator look up with settings
    const editoruiLoaderWebcompModuleLookup$ = mfeConf$
        .pipe(
            map(resp => {
                const descs = resp.descs.filter(v => v.item == 'EditorUiLoaderWebComp');
                return descs[0]?.impl as ModuleLookup;
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(l => {
            loadedModule.get('editorui-loader-webcom').next(l);
        });

    // coordinator look up with settings
    const coordModuleLookup$ = mfeConf$
        .pipe(
            map(resp => {
                const descs = resp.descs.filter(v => v.item == 'DocLoaderCoordinator');
                return descs[0]?.impl as ModuleLookupWithSettings;
            })
        )
        .pipe(filter(e => e != undefined))
        .subscribe(l => {
            loadedModule.get('docloader-coordinator').next(l);
        });

    const coordinator$: ReplaySubject<DocLoaderCoordinator> = new ReplaySubject(1);

    const uiLoaders: Map<string, EditorUILoaderComponent> = new Map();

    const rest: RestGateway = new RestGateway();

    async function checkAndLoadModule(moduleName: string[]): Promise<void> {
        try {
            const notLoadedName = moduleName.filter(n => !loadedModule.has(n));
            const specs = notLoadedName.map(n => editorConfSpecs.get(n));

            if (specs.length <= 0) return;
            notLoadedName.forEach(n =>
                loadedModule.set(
                    n,
                    new BehaviorSubject<any>(undefined).pipe(filter(e => e != undefined)) as BehaviorSubject<any>
                )
            );

            await rest.getEditorLookup(specs).then(v => {
                mfeConf$.next(v.data);
            });

            await Promise.all(moduleName.map(n => firstValueFrom(loadedModule.get(n))));
        } catch (error) {
            console.error('error in checkAndLoadModule', error);
        }
    }

    /**
     * configuration might be optional, in that case, use default configuration
     */
    export async function initialize(): Promise<boolean> {
        await checkAndLoadModule(['docloader-coordinator', 'viclass-embed-style']);

        const coordModuleLookup = await firstValueFrom(loadedModule.get('docloader-coordinator'));

        const coordModule = await viclass.mfeRT.loadRemoteModule(coordModuleLookup.lookup as any);

        const ctor = coordModule.DocLoaderCoordinator;

        // create the coordinator
        const partialConfig = coordModuleLookup.settings as Partial<DocLoaderCoordinatorConfig>;

        const coordConfig: DocLoaderCoordinatorConfig = {
            id: 'vi-docloader-coord',
            editorTypeMapping: partialConfig.editorTypeMapping,
            edLookups: [],
        };

        const coord = new ctor(coordConfig);

        await coord.initialize();
        await coord.start();

        coordinator$.next(coord);

        await checkAndLoadModule(['viclass-embed-style']);

        const viclassThemeCursor = await firstValueFrom(loadedModule.get('viclass-embed-style'));

        await loadThemeModules(viclassThemeCursor);

        return true;
    }

    export async function loadWebcomp(): Promise<boolean> {
        await checkAndLoadModule(['editorui-loader-webcom']);

        const webCompModuleLookup = await firstValueFrom(loadedModule.get('editorui-loader-webcom'));

        await viclass.mfeRT.loadRemoteModule(webCompModuleLookup as any).then(() => {
            console.log('Editor ui loader module loaded!');
        });

        return true;
    }

    /**
     * render all elements that match a default class name
     * @param option
     */
    export async function renderAll(option?: RenderAllOption): Promise<string[]> {
        const vpContainerEls = document.querySelectorAll<HTMLElement>(option?.vpSelector || '.vi-embed-viewport');

        return Promise.all(Array.from(vpContainerEls).map(vpContainer => renderEl(vpContainer, option || {})));
    }

    export async function renderEl(vpContainerEl: HTMLElement, option: RenderOption = {}): Promise<string> {
        const data = vpContainerEl.dataset;

        if (option.width) vpContainerEl.style.width = option.width + 'px';
        if (option.height) vpContainerEl.style.height = option.height + 'px';

        const vpEl = document.createElement('div');
        vpEl.id = data['viVpId'] || `vi-vp-id-${idIncrement++}`;
        vpEl.style.width = '100%';
        vpEl.style.height = '100%';
        vpEl.style.position = 'relative';
        vpEl.style.zIndex = '0';

        vpContainerEl.style.position = 'relative';
        vpContainerEl.append(vpEl);
        vpContainerEl.classList.add('vi-theme');

        const coord = await firstValueFrom(coordinator$);

        const boardType = (option?.vpType || data['viVpType']) as BoardType;

        const vm = coord.createViewport(vpEl, {
            bType: boardType,
            viewportBoundingEl: vpContainerEl,
        });

        let tools = data['viTools']?.split(',')?.map(t => t.trim());

        if (boardType == 'board') {
            tools = tools || option.tools || ['pan', 'zoom'];

            const lookAtX = option.lookAtX || data['viLookAtX'];
            const lookAtY = option.lookAtY || data['viLookAtY'];
            const zoomLevel = option.zoom || data['viZoom'];

            const _vm = vm as WrappingBoardViewportManager;
            if (lookAtX != undefined && lookAtY != undefined)
                _vm.centerAt({
                    x: Number(lookAtX) || 0,
                    y: Number(lookAtY) || 0,
                });
            if (zoomLevel != undefined) _vm.zoom(Number(zoomLevel) || 1);
        }

        if (option.showTool == true || data['viShowTool'] == 'true') {
            const embedToolbarEl = document.createElement('editor-ui');
            embedToolbarEl.style.position = 'absolute';
            embedToolbarEl.style.position = 'none';
            embedToolbarEl.setAttribute('v-align', data['viToolVAlign'] || option.toolVAlign || 'bottom');
            embedToolbarEl.setAttribute('h-align', data['viToolHAlign'] || option.toolHAlign || 'left');
            embedToolbarEl.setAttribute('direction', data['viToolDirection'] || option.toolDirection || 'ltr');

            vpContainerEl.append(embedToolbarEl);

            embedToolbarEl.addEventListener('loaderEvent', event =>
                onEditorUILoaderEvent('EmbedToolsEditor', vm, (event as any).detail)
            );

            await loadWebcomp();
        }

        const docs = vpContainerEl.querySelectorAll<HTMLElement>(option.docSelector || '.vi-embed-doc');

        const docConf: Partial<DocLoadingConfig>[] = [];

        docs.forEach(docEl => {
            const data = docEl.dataset;
            docConf.push({
                edType: option.edType || (data['viEdType'] as EditorType),
                gId: data['viDocId'],
            });
        });

        for (const dataKey in data) {
            if (dataKey.startsWith('viDocId')) {
                const edType = dataKey.replace('viDocId', 'viEdType');
                docConf.push({
                    edType: (data[edType] || option.edType || data['viEdType']) as EditorType,
                    gId: data[dataKey],
                });
            }
        }

        const edTypes = docConf.map(c => editorList.get(c.edType)).filter(edt => edt != undefined);

        await checkAndLoadModule(edTypes);

        for (const e of edTypes) {
            const edModule: EditorLookup = await firstValueFrom(loadedModule.get(e));
            if (!coord.editorByType(edModule.editorType)) {
                await coord.addEditor(edModule);
            }
        }

        for (const docConfElement of docConf) {
            await coord.loadDocOnViewport(vm, docConfElement);
        }

        coord.switchViewportMode(vm.id, 'ViewMode');

        return vm.id;
    }

    async function onEditorUILoaderEvent(edType: EditorType, vm: ViewportManager, event: EditorUILoaderEvent) {
        const coord = await firstValueFrom(coordinator$);
        switch (event.eventType) {
            case 'loader-initialized': {
                const uiLoader = event.source;
                await checkAndLoadModule(['editor-ui-base-style', 'embed-tools-editor']);
                const baseThemeConf = await firstValueFrom(loadedModule.get('editor-ui-base-style'));
                await uiLoader.loadBaseTheme(baseThemeConf);
                uiLoaders.set(`${vm.id}_${edType}`, uiLoader);

                switch (edType) {
                    case 'EmbedToolsEditor': {
                        const embedUIConf = await firstValueFrom(loadedModule.get('embed-tools-editor'));
                        await uiLoader.createUI([embedUIConf]);
                        break;
                    }
                    default:
                        break;
                }
                break;
            }
            case 'ui-loaded': {
                const ed = event.state;
                const uiLoader = uiLoaders.get(`${vm.id}_${ed}`);
                const loadedUI = uiLoader.getUI(ed as EditorType);

                switch (event.state) {
                    case 'EmbedToolsEditor':
                    case 'CommonToolsEditor':
                    case 'ZoomToolsEditor': {
                        const tb = coord.getCommonToolbar(vm.id);
                        loadedUI.connectToolbar(tb);
                        uiLoader.showUI(event.state);
                        break;
                    }
                    default:
                        break;
                }
                break;
            }
            case 'all-ui-loaded': {
                break;
            }
        }
    }

    /**
     * Register an event listener for viewport events.
     * @returns a function to remove the event listener or null if failed or the event type is not supported
     */
    export async function addViewportEventListener(
        vpId: string,
        vpType: BoardType,
        eventType: ViewportEventType,
        handler: (event: ViewportEvent) => void | Promise<void>
    ): Promise<RemoveEventListenerFunc | null> {
        // the current events (pan/zoom) are not supported for inline
        if (vpType === 'inline') return null;

        const coord = await firstValueFrom(coordinator$);
        if (!coord) return null;

        const vm = coord.getViewportManager(vpId) as WrappingBoardViewportManager;
        if (!vm) return null;

        switch (eventType) {
            case 'viewport-pan': {
                const panHandler: VEventListener<PanEventData> = {
                    onEvent: (event: PanEventData) => {
                        handler({
                            eventType: 'viewport-pan',
                            viewportId: vpId,
                            state: { ...event.state.pos },
                        });
                        return event;
                    },
                };
                vm.panEventEmitter().registerListener(panHandler);
                return () => vm.panEventEmitter().unregisterListener(panHandler);
            }
            case 'viewport-zoom': {
                const zoomHandler: VEventListener<ZoomEventData> = {
                    onEvent: (event: ZoomEventData) => {
                        handler({
                            eventType: 'viewport-zoom',
                            viewportId: vpId,
                            state: event.state.zoomLevel,
                        });
                        return event;
                    },
                };
                vm.zoomEventEmitter().registerListener(zoomHandler);
                return () => vm.zoomEventEmitter().unregisterListener(zoomHandler);
            }
            default:
                return null;
        }
    }

    export type RenderOption = {
        vpType?: BoardType;
        lookAtX?: number;
        lookAtY?: number;
        zoom?: number;
        width?: number;
        height?: number;
        /**
         * The element selector. All element with this selector will be
         * checked for potential documents data to be rendered.
         */
        docSelector?: string;
        edType?: EditorType;
        showTool?: boolean;
        tools?: CommonToolType[];
        toolVAlign?: string; // v-align toolbar
        toolHAlign?: string; // h-align toolbar
        toolDirection?: string; // toolbar direction
    };

    export type RenderAllOption = RenderOption & {
        /**
         * The element selector. All element with this selector will be
         * checked for potential documents to be rendered.
         */
        vpSelector?: string;
    };

    export type ViewportEventType = 'viewport-pan' | 'viewport-zoom';

    export interface IViewportEvent<ViewportEventType, S> {
        eventType: ViewportEventType;
        viewportId: string;
        state: S;
    }

    export type ViewportEvent =
        | IViewportEvent<'viewport-pan', { x: number; y: number }>
        | IViewportEvent<'viewport-zoom', number>;

    export type RemoveEventListenerFunc = () => void;
}

// manually export to global scope
if (window) {
    window['viclass'] = window['viclass'] || ({} as any);
    window['viclass']['ViDocLoader'] = ViDocLoader;
}

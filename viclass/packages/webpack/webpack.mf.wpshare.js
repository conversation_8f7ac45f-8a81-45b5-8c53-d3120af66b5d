// common sharing for workspace libraries
const wpdeps = require('../../package.json').dependencies;
const { share } = require('@angular-architects/module-federation/webpack');

function vshare(name, options) {
    if (!options) options = {};

    //options['shareKey'] = name
    //options['shareScope'] = "viclass"

    const keys = Object.keys(options);

    if (!keys.includes('strictVersion')) options['strictVersion'] = false;
    if (!keys.includes('requiredVersion') && wpdeps[name]) options['requiredVersion'] = wpdeps[name];
    if (!keys.includes('singleton')) options['singleton'] = true;

    s = {};
    s[name] = options;
    return s;
}

module.exports = {
    shareAngular: share({
        ...vshare('@angular/common', { requiredVersion: 'auto' }),
        ...vshare('@angular/common/http', { requiredVersion: 'auto' }),
        ...vshare('@angular/core', { requiredVersion: 'auto' }),
        ...vshare('@angular/cdk/overlay', { requiredVersion: 'auto' }),
        ...vshare('@angular/cdk/bidi', { requiredVersion: 'auto' }),
        ...vshare('@angular/cdk/portal', { requiredVersion: 'auto' }),
        ...vshare('@angular/cdk/scrolling', { requiredVersion: 'auto' }),
        ...vshare('@angular/cdk/clipboard', { requiredVersion: 'auto' }),
        ...vshare('@angular/cdk/platform', { requiredVersion: 'auto' }),
        ...vshare('@angular/cdk/drag-drop', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/core', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/autocomplete', {
            requiredVersion: 'auto',
        }),
        ...vshare('@angular/material/dialog', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/menu', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/button', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/select', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/slider', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/badge', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/input', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/datepicker', { requiredVersion: 'auto' }),
        ...vshare('@angular/material/material-moment-adapter', {
            requiredVersion: 'auto',
        }),
        ...vshare('@angular/forms', { requiredVersion: 'auto' }),
        ...vshare('@angular/platform-browser/animations', {
            requiredVersion: 'auto',
        }),
        ...vshare('@angular/platform-browser', { requiredVersion: 'auto' }),
        ...vshare('@angular/platform-browser-dynamic', {
            requiredVersion: 'auto',
        }),
    }),
    shareProto: {
        ...vshare('google-protobuf'),
    },
    shareCommon: {
        ...vshare('rxjs'),
        ...vshare('jquery'),
        ...vshare('lodash'),
        ...vshare('Axios'), // should not use ...vshare('axios'), cause problem when load module
        ...vshare('zone.js'),
    },
    shareInternal: {
        ...vshare('@viclass/editor.core', {
            requiredVersion: '*',
            strictVersion: false,
        }),
    },
    shareLexical: {
        ...vshare('lexical', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/clipboard', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/code', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/file', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/hashtag', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/headless', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/history', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/link', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/list', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/mark', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/overflow', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/plain-text', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/rich-text', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/selection', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/table', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/text', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/utils', { requiredVersion: '^0.33.1' }),
        ...vshare('@lexical/yjs', { requiredVersion: '^0.33.1' }),
    },
    shareYjs: {
        ...vshare('yjs', { requiredVersion: '^13.6.15' }),
        ...vshare('y-protocols', { requiredVersion: '^1.0.6' }),
    },
    shareMathlive: {
        ...vshare('lib-mathlive', { requiredVersion: '0.0.1' }),
    },
    shareComputeEngine: {
        // separated mathlive and compute-engine as other editors might need compute-engine but not mathlive
        ...vshare('@cortex-js/compute-engine', { requiredVersion: '^0.28.0' }),
    },
    shareGraphic: {
        ...vshare('pixi.js', { requiredVersion: '^8.0.0' }),
    },
    share: vshare,
};

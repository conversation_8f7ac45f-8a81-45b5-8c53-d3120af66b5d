import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Injector, NgModule } from '@angular/core';
import { EditorUILoaderComponent } from './editoruiloader.component';
import { UIHolderComponent } from './uiholder/uiholder.component';

const LOADER_TAG = 'editor-ui';

@NgModule({
    declarations: [EditorUILoaderComponent, UIHolderComponent],
    imports: [CommonModule, OverlayModule],
    exports: [EditorUILoaderComponent],
})
export class EditorUILoaderModule {
    constructor(private injector: Injector) {}
}

import { Platform } from '@angular/cdk/platform';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    Output,
    SimpleChanges,
    ViewChild,
    ViewContainerRef,
} from '@angular/core';
import { EditorType, loadThemeModules, ModuleLookup, processStyleSheet, VEventData } from '@viclass/editor.core';
import { BehaviorSubject, firstValueFrom, Subject, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { CustomOverlayContainer } from './custom.overlay.container';
import { EditorUILoaderService } from './editoruiloader.service';
import { EditorUIComponent, EditorUILookup, IEditorUILoaderComponent } from './editoruilookup';
import { HolderEvent, UIHolderComponent } from './uiholder/uiholder.component';

export type EditorUILoaderEventType = 'loader-initialized' | 'ui-loaded' | 'all-ui-loaded';
type _EUILE<T, S> = T extends EditorUILoaderEventType ? VEventData<T, EditorUILoaderComponent, S> : never;

export type LoaderInitializedData = Exclude<_EUILE<'loader-initialized', any>, 'state'>;
export type UILoadedData = Exclude<_EUILE<'ui-loaded', string>, 'processedHandlers'>;
export type AllUILoadedData = Exclude<_EUILE<'all-ui-loaded', any>, 'state'>;

export type EditorUILoaderEvent = LoaderInitializedData | UILoadedData | AllUILoadedData;

@Component({
    selector: 'editor-ui-group',
    templateUrl: './editoruiloader.component.html',
    providers: [],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditorUILoaderComponent implements AfterViewInit, OnChanges, IEditorUILoaderComponent, OnDestroy {
    lookups$ = new BehaviorSubject<EditorUILookup[]>([]);
    pinned: string[] = [];
    loadCompleted = new Subject<string>();
    components = new Map<EditorType, UIHolderComponent>();
    overlayContainer: CustomOverlayContainer;
    resizeObserver: ResizeObserver;

    isScrollToStart: boolean = true;
    isScrollToEnd: boolean = true;
    checkShowingScrollArrowWithThis: any;
    lookupSub: Subscription;

    shownUI: EditorType | undefined = undefined;

    @Input() lookups: string;
    @Input() uiDisplay: string;
    @Input() vAlign: 'top' | 'center' | 'bottom';
    @Input() hAlign: 'left' | 'center' | 'right';
    @Input() direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    @Output() loaderEvent: EventEmitter<EditorUILoaderEvent> = new EventEmitter();

    @ViewChild('toolbarGroup') toolbarGroup: ElementRef;

    constructor(
        private viewRef: ViewContainerRef,
        private loader: EditorUILoaderService,
        platform: Platform,
        private cdr: ChangeDetectorRef
    ) {
        this.overlayContainer = new CustomOverlayContainer(platform);
        this.checkShowingScrollArrowWithThis = this.checkShowingScrollArrow.bind(this);
    }

    ngAfterViewInit(): void {
        const host = this.getRoot();
        this.overlayContainer.setRoot(host);

        this.loaderEvent.emit({
            eventType: 'loader-initialized',
            source: this,
        });

        // tracking resize and scroll to rerender toolbar
        host.addEventListener('scroll', this.checkShowingScrollArrowWithThis);
        window.addEventListener('resize', this.checkShowingScrollArrowWithThis);

        this.lookupSub = this.lookups$.subscribe(v => {
            if (v.length > 0)
                requestAnimationFrame(() => {
                    const toolbarGroup = this.toolbarGroup?.nativeElement;
                    if (toolbarGroup) {
                        this.resizeObserver = new ResizeObserver(this.checkShowingScrollArrowWithThis);
                        this.resizeObserver.observe(toolbarGroup);

                        this.lookupSub.unsubscribe();
                    }
                });
        });
    }

    ngOnDestroy(): void {
        // remove tracking resize and scroll to rerender toolbar
        this.getRoot().removeEventListener('scrollend', this.checkShowingScrollArrowWithThis);
        window.removeEventListener('resize', this.checkShowingScrollArrowWithThis);
        this.resizeObserver?.disconnect();

        this.lookupSub?.unsubscribe();
    }

    get isVertical() {
        return ['ttb', 'btt'].includes(this.direction);
    }

    checkShowingScrollArrow() {
        const element = this.getRoot();

        if (this.isVertical) {
            const scrollStart = element.scrollTop;
            const scrollEnd = scrollStart + element.offsetHeight;

            this.isScrollToStart = scrollStart === 0;
            this.isScrollToEnd = scrollEnd >= element.scrollHeight;
        } else {
            const scrollLeft = element.scrollLeft;
            const scrollRight = scrollLeft + element.offsetWidth;

            this.isScrollToStart = scrollLeft === 0;
            this.isScrollToEnd = scrollRight >= element.scrollWidth;
        }

        this.cdr.markForCheck();
    }

    async ngOnChanges(changes: SimpleChanges): Promise<void> {
        if (changes['vAlign'] || changes['hAlign']) setTimeout(() => this.setupAlignmentForHost());

        if (changes['lookups']) {
            setTimeout(
                () => this.createUI(JSON.parse(changes['lookups'].currentValue) as unknown as EditorUILookup[]),
                0
            );
        }

        if (changes['uiDisplay']) {
            setTimeout(() => this.switchTo(changes['uiDisplay'].currentValue), 0);
        }
    }

    getRoot(): HTMLElement | undefined {
        return this.viewRef.element.nativeElement;
    }

    private setupAlignmentForHost() {
        const host = this.getRoot();
        const style = {
            top: 'auto',
            bottom: 'auto',
            left: 'auto',
            right: 'auto',
        };

        if (this.vAlign == 'center') {
            style['top'] = '0px';
            style['bottom'] = '0px';
            style['margin-top'] = 'auto';
            style['margin-bottom'] = 'auto';
        } else if (this.vAlign) style[this.vAlign] = '0px';

        if (this.hAlign == 'center') {
            style['left'] = '0px';
            style['right'] = '0px';
            style['margin-left'] = 'auto';
            style['margin-right'] = 'auto';
        } else if (this.hAlign) style[this.hAlign] = '0px';

        Object.assign(host.style, style);
    }

    pinUI(edType: EditorType) {
        this.pinned.push(edType);

        this.showUI(edType);

        this.cdr.detectChanges();
    }

    isPinned(edType: EditorType) {
        return this.pinned.includes(edType);
    }

    lookupItems(): EditorUILookup[] {
        return this.allPinned().concat(this.allNotPinned());
    }

    allPinned(): EditorUILookup[] {
        return this.lookups$.getValue().filter(v => this.isPinned(v.editorType));
    }

    allNotPinned(): EditorUILookup[] {
        return this.lookups$.getValue().filter(v => !this.isPinned(v.editorType));
    }

    unpinUI(edType: EditorType) {
        this.pinned = this.pinned.filter(value => value != edType);
        this.cdr.detectChanges();
    }

    async loadBaseTheme(theme: ModuleLookup[] | ModuleLookup) {
        await loadThemeModules(theme, this.viewRef.element?.nativeElement?.shadowRoot || document);
    }

    createUI(lookups: EditorUILookup[]): Promise<string> {
        this.components.clear();

        this.lookups$.next(lookups);

        this.cdr.detectChanges();

        return firstValueFrom(this.loadCompleted.pipe(take(1)));
    }

    onHolderEvent(e: HolderEvent, lookup: EditorUILookup, comp: UIHolderComponent) {
        switch (e.eventType) {
            case 'loaded':
                this.onUICompLoaded(lookup, comp);

                if (e.styleSheet) {
                    processStyleSheet(e.styleSheet, this.viewRef.element?.nativeElement?.shadowRoot || document);
                }

                break;
            case 'destroyed':
                break;
        }

        this.cdr.detectChanges();
    }

    onUICompLoaded(lookup: EditorUILookup, comp: UIHolderComponent) {
        this.components.set(lookup.editorType, comp);

        this.loaderEvent.emit({
            eventType: 'ui-loaded',
            source: this,
            state: lookup.editorType,
            processedHandlers: undefined,
        });

        if (this.components.size == this.lookups$.getValue().length) {
            this.loadCompleted.next('');

            this.loaderEvent.emit({
                eventType: 'all-ui-loaded',
                source: this,
            }); // when everything loaded
        }
    }

    switchTo(editorType: EditorType) {
        if (!editorType || !this.components.has(editorType)) {
            this.hideAll();
            return;
        }

        if (this.shownUI == editorType || this.pinned.includes(editorType)) return;

        for (const c of this.components) {
            if (this.isPinned(c[0])) continue;
            else c[1].editorUI.hideUI();
        }

        this.showUI(editorType);

        this.cdr.detectChanges();
    }

    showUI(editorType: EditorType) {
        const comp = this.components.get(editorType) as UIHolderComponent;
        if (comp) {
            comp.editorUI.showUI();
            this.shownUI = editorType;
        }
    }

    hideUI(editorType: EditorType) {
        const comp = this.components.get(editorType) as UIHolderComponent;
        if (comp) {
            comp.editorUI.hideUI();
            if (this.shownUI == editorType) this.shownUI = undefined;
        }
    }

    hideAll() {
        for (const c of this.components) {
            if (this.isPinned(c[0])) continue;
            else c[1].editorUI.hideUI();
        }
        this.shownUI = undefined;
    }

    getUI(editorType: EditorType): EditorUIComponent {
        if (!this.components.has(editorType)) throw new Error(`Editor UI for ${editorType} has not been loaded!`);
        return this.components.get(editorType).editorUI;
    }

    scrollStart() {
        if (this.isVertical) {
            this.getRoot().scrollTop -= 100;
        } else {
            this.getRoot().scrollLeft -= 100;
        }
    }

    scrollEnd() {
        if (this.isVertical) {
            this.getRoot().scrollTop += 100;
        } else {
            this.getRoot().scrollLeft += 100;
        }
    }
}

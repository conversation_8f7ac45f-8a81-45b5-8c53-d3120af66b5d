<ng-template #namingInputPortal cdkPortal>
    <div
        class="naming-input-overlay shadow-SH1 overflow-hidden"
        cdkDrag
        cdkDragRootElement=".viewport-root-el"
        cdkDragBoundary=".cdk-overlay-container"
        role="dialog"
        aria-labelledby="naming-input-header"
        (submit)="$event.preventDefault()">
        <div
            class="naming-input-overlay__header input-header cursor-move text-BW4 bg-BW1 justify-end items-center gap-[10px] w-full h-auto p-[5px] leading-[20px] flex max-xs:cursor-default"
            cdkDragHandle
            id="naming-input-header">
            <button
                type="button"
                (click)="onCancel()"
                class="naming-input-overlay__close-button p-[5px] w-[50px] text-center"
                aria-label="Đóng">
                <span class="vcon-general vcon_delete align-middle text-BW7" aria-hidden="true"></span>
            </button>
        </div>
        <div class="naming-input-overlay__content p-3">
            <div *ngFor="let rn of state?.requireNameForUsers || []" class="w-100">
                <label [for]="'name-input-' + (getError?.idx ?? 0)" class="block text-sm font-medium text-gray-700 mb-1"
                    >Tên {{ rn?.objType }} <span class="require text-red-500">*</span></label
                >
                <div
                    class="naming-input-overlay__input-group flex flex-row justify-center items-center space-x-1 mt-1 mb-3">
                    <span
                        #nameInput
                        *ngFor="let name of rn?.originElNames || []; let i = index; trackBy: trackByNameIndex"
                        class="naming-input__field block min-w-[2.5rem] max-w-full h-10 text-xl text-center border border-gray-300 rounded-md shadow-sm transition-colors duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 whitespace-nowrap overflow-hidden text-ellipsis p-1"
                        [id]="'name-input-' + i"
                        [class.bg-gray-100]="name && name.length > 0"
                        [class.text-gray-500]="name && name.length > 0"
                        [class.cursor-default]="name && name.length > 0"
                        [class.border-red-500]="hasError && getError?.idx === i"
                        [class.focus:ring-red-500]="hasError && getError?.idx === i"
                        [class.focus:border-red-500]="hasError && getError?.idx === i"
                        [attr.aria-invalid]="hasError && getError?.idx === i"
                        [attr.aria-describedby]="hasError && getError?.idx === i ? 'naming-error-message' : null"
                        (focus)="onFocus(i)"
                        (input)="onNamingChange($event, i, rn.type)"
                        [innerHTML]="name || rn?.suggestedNames[i]"
                        (keyup)="onKeyup($event)"
                        (keydown)="onKeydown($event, i, rn.type)"
                        (beforeinput)="onBeforeInput($event)"
                        (submit)="onSubmit()"
                        [contentEditable]="!name || name.length < 1"
                        style="
                            display: inline-block;
                            width: auto;
                            min-width: 2.5rem;
                            max-width: 100%;
                            white-space: nowrap;
                        "></span>
                </div>
            </div>

            <!-- Error Message - Improved Styling -->
            <div
                *ngIf="hasError && getError"
                id="naming-error-message"
                class="naming-error p-2.5 mb-3 rounded-md bg-red-50 border border-red-200"
                role="alert">
                <div class="flex items-center">
                    <span class="vcon vcon-geometry vcon_error text-red-500 mr-1.5 text-lg" aria-hidden="true"></span>
                    <span class="text-sm text-red-700">{{ getError.message }}</span>
                </div>
            </div>

            <!-- Actions - Adjusted for image feedback -->
            <div class="naming-input-overlay__actions flex flex-col sm:flex-row sm:justify-end sm:gap-3 pt-[10px]">
                <button
                    type="button"
                    class="btn vi-btn vi-btn-small vi-btn-outline w-full sm:w-auto mb-2 sm:mb-0 order-2 sm:order-1 whitespace-nowrap px-4 py-1.5 rounded-full shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-1"
                    (click)="onAuto()">
                    Tên tự động
                </button>
                <button
                    type="submit"
                    class="btn vi-btn vi-btn-small vi-btn-focus w-full sm:w-auto order-1 sm:order-2 whitespace-nowrap px-4 py-1.5 rounded-full shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-60 disabled:cursor-not-allowed"
                    (click)="onSubmit()"
                    [disabled]="hasError">
                    Xác nhận
                </button>
            </div>
        </div>
    </div>
</ng-template>

import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy } from '@angular/core';
import { GeometryToolBar, GeometryToolType, SectorToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for display and edit the clockwise/counterclockwise mode for CreateSectorTool
 */
@Component({
    selector: 'tb-sector-cwccw',
    templateUrl: './sector-cwccw.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SectorCWCCWComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;

    public cwccw$ = new BehaviorSubject<boolean>(true);

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setCWCCW(value: boolean) {
        const ts = this.toolbar.toolState(this.tooltype) as SectorToolState;
        ts.clockwise = value;
        this.toolbar.update(this.tooltype, ts);
    }

    get cwccw(): boolean {
        const ts = this.toolbar.toolState(this.tooltype) as SectorToolState;
        return ts.clockwise;
    }

    updateInputFromToolState() {
        this.cwccw$.next(this.cwccw);
    }
}

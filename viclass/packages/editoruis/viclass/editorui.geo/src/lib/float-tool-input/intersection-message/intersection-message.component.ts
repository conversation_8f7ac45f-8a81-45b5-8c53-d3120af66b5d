import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { GeometryToolBar, GeometryToolType, IntersectionPointToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for displaying current selection message for IntersectionPointTool
 * Shows step-by-step instructions based on current selection state
 */
@Component({
    selector: 'tb-intersection-message',
    templateUrl: './intersection-message.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IntersectionMessageComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;

    public currentMessage$ = new BehaviorSubject<string>('');

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, this.changeDetectorRef);
        this.currentMessage$.next(this.getInitialMessage());
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    get currentMessage(): string {
        const toolState = this.toolbar.toolState(this.tooltype) as IntersectionPointToolState;

        if (toolState.selectedCount === 1) return 'Chọn đường thẳng, đường tròn hoặc hình thứ hai để tìm giao điểm';
        else if (toolState.selectedCount >= 2) return 'Chọn giao điểm mong muốn từ các giao điểm có sẵn';

        return this.getInitialMessage();
    }

    private getInitialMessage(): string {
        return 'Chọn đường thẳng, đường tròn hoặc hình đầu tiên';
    }

    updateInputFromToolState() {
        this.currentMessage$.next(this.currentMessage);
    }
}

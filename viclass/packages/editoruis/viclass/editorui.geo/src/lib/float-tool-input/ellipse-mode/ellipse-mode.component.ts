import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { EllipseToolState, GeometryToolBar, GeometryToolType } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for display and edit the drawing mode for CreateEllipseTool
 */
@Component({
    selector: 'tb-ellipse-mode',
    templateUrl: './ellipse-mode.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EllipseModeComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;

    public mode$ = new BehaviorSubject<number>(0);

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setMode(value: number) {
        const ts = this.toolbar.toolState(this.tooltype) as EllipseToolState;
        ts.mode = value;
        this.toolbar.update(this.tooltype, ts);
    }

    get mode(): number {
        const ts = this.toolbar.toolState(this.tooltype) as EllipseToolState;
        return ts.mode;
    }

    updateInputFromToolState() {
        this.mode$.next(this.mode);
    }
}

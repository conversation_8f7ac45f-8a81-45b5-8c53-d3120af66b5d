import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy } from '@angular/core';
import { GeometryToolBar, GeometryToolType, ParallelPerpendicularPointToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for displaying current selection message for both CreateParallelLineTool and CreatePerpendicularLineTool
 * Shows step-by-step instructions based on current selection state
 */
@Component({
    selector: 'tb-parallel-perpendicular-message',
    templateUrl: './parallel-perpendicular-message.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParallelPerpendicularMessageComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;

    public currentMessage$ = new BehaviorSubject<string>('');

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, this.changeDetectorRef);
        this.currentMessage$.next(this.getInitialMessage());
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    get currentMessage(): string {
        const toolState = this.toolbar.toolState(this.tooltype) as ParallelPerpendicularPointToolState;

        if (toolState.selectedCount === 1) return this.getSelectPointMessage();
        else if (toolState.selectedCount >= 2) return this.getSelectFinalPositionMessage();

        return this.getInitialMessage();
    }

    private getInitialMessage(): string {
        if (this.tooltype === 'CreateParallelLineTool') {
            return 'Chọn đường thẳng gốc để tạo đường song song';
        } else if (this.tooltype === 'CreatePerpendicularLineTool') {
            return 'Chọn đường thẳng gốc để tạo đường vuông góc';
        }
        return '';
    }

    private getSelectPointMessage(): string {
        if (this.tooltype === 'CreateParallelLineTool') {
            return 'Chọn điểm mà đường song song sẽ đi qua';
        } else if (this.tooltype === 'CreatePerpendicularLineTool') {
            return 'Chọn điểm mà đường vuông góc sẽ đi qua';
        }
        return '';
    }

    private getSelectFinalPositionMessage(): string {
        if (this.tooltype === 'CreateParallelLineTool') {
            return 'Chọn vị trí cuối hoặc giao điểm với một đường để hoàn thành đường song song';
        } else if (this.tooltype === 'CreatePerpendicularLineTool') {
            return 'Chọn vị trí cuối hoặc giao điểm với một đường để hoàn thành đường vuông góc';
        }
        return '';
    }

    updateInputFromToolState() {
        this.currentMessage$.next(this.currentMessage);
    }
}

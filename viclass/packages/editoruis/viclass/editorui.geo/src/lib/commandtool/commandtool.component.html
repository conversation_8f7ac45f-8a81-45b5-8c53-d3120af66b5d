<div class="overflow-visible relative w-full h-full sm:!w-0 sm:!h-0">
    <div
        *ngIf="!isHiddenForNaming"
        class="command-ui relative h-full items-end overflow-hidden flex gap-[10px] top-0 left-0 max-xs:!transform-none sm:absolute sm:!overflow-visible sm:h-auto sm:command-ui-md-top sm:command-ui-md-left">
        <div
            class="input-container bg-BW4 relative items-center flex flex-col sm:rounded-[35px] sm:overflow-hidden"
            cdkDrag
            cdkDragBoundary=".cdk-overlay-container"
            [cdkDragDisabled]="!(isDraggable$ | async)">
            <div
                class="input-header cursor-move text-BW4 bg-BW1 justify-end items-center gap-[10px] w-full h-auto p-[5px] leading-[20px] flex max-xs:cursor-default"
                cdkDragHandle>
                <button (click)="onNoClick()" class="p-[5px] w-[50px] text-center">
                    <span class="vcon-general vcon_delete align-middle text-BW7"></span>
                </button>
            </div>
            <div class="input-body w-full h-full p-[10px] flex gap-[0.5rem] overflow-hidden pb-[10px] bg-white">
                <div>
                    <span class="font-bold size-[24px] w-full">TẠO {{ tool.viName.toUpperCase() }}</span>
                </div>
                <div class="flex relative w-full">
                    <div
                        class="command-input-group flex flex-row py-[5px] px-[10px] rounded-[12px] items-center gap-[5px] w-full border border-BW3">
                        <img src="/assets/img/search-icon.svg" alt="Tìm kiếm" class="w-4 h-4" />
                        <input
                            class="w-full text-[1rem] text-sm"
                            style="padding-top: 2px; padding-bottom: 2px"
                            type="text"
                            placeholder="Ràng buộc / tham số"
                            *ngIf="!selectingConstraintTpl"
                            #searchTemplateInput
                            (focus)="onFocusSearchTemplate()"
                            (keyup)="onKeyupSearchTemplate($event)"
                            [disabled]="disabledSearchTemplate()" />
                        <div
                            *ngIf="selectingConstraintTpl"
                            class="input-constraint w-full d-flex flex-row gap-[.25rem]">
                            <div
                                class="constraint-params-input d-flex align-items-center me-auto items-center text-[0.9rem] overflow-hidden whitespace-nowrap bg-white"
                                contenteditable="true"
                                (keyup)="onKeyUpInputConstraint($event)">
                                <ng-container *ngFor="let piece of pieces; let idx = index">
                                    <ng-container [ngSwitch]="piece.type">
                                        <span *ngSwitchCase="'text'" class="me-1" contenteditable="false">
                                            {{ piece.content }}
                                        </span>
                                        <ng-container *ngSwitchCase="'input'">
                                            <span class="heighlight" contenteditable="false">
                                                {{ piece.content[0] }}
                                            </span>
                                            <div class="input-container-1 inline-block relative">
                                                <lib-param-input
                                                    [paramValue]="piece.input"
                                                    (paramInput)="onParamInput(idx, $event)"
                                                    [possiblePoints]="possiblePointsNames"
                                                    [focusAfterInit]="idx === getFirstInputIdx()"
                                                    [hasError]="!!piece.error"></lib-param-input>
                                            </div>
                                            <span class="heighlight me-1" contenteditable="false">{{
                                                piece.content[piece.content.length - 1]
                                            }}</span>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                            </div>
                            <button
                                class="btn vi-btn vi-btn-normal vi-btn-focus"
                                (click)="completeConstraintInput()"
                                [disabled]="!isFulfill()">
                                <span class="vcon vcon-geometry vcon_general_yes"></span>
                            </button>
                            <button
                                class="btn mr-[-5px] vi-btn vi-btn-normal vi-btn-focus"
                                (click)="cancelConstraintInput()">
                                <span class="vcon vcon-geometry vcon_delete"></span>
                            </button>
                        </div>
                    </div>
                    <div
                        *ngIf="selectingConstraintTpl"
                        class="text-red-500 text-center text-[10px] absolute bottom-[-25px] left-0 right-0">
                        {{ getFirstParamError() }}
                    </div>
                </div>

                <div
                    class="command-input-row command-input-col group-constraint grid gap-2 grow overflow-y-auto grid-cols-2 max-xs:!flex max-xs:!flex-col sm-max-md:!flex sm-max-md:!flex-col">
                    <div
                        class="selected-constraint-row flex justify-center items-center"
                        *ngIf="!selectedConstraints || selectedConstraints?.length === 0">
                        <div>
                            <div class="m-0 justify-center text-center hidden lg:!block max-h-xs:!hidden">
                                <img class="inline-block" src="assets/img/dont-have-any-doc.svg" />
                            </div>
                            <span class="">Chưa có ràng buộc</span>
                        </div>
                    </div>
                    <div class="selected-constraint-row" *ngIf="selectedConstraints?.length > 0">
                        <div
                            *ngFor="let sc of selectedConstraints; let i = index"
                            class="selected-constraint rounded-[10px] p-[5px] text-[12px] mb-[5px] mr-[5px] w-max max-w-full justify-between inline-block text-BW1 bg-white border border-BW3">
                            <lib-constrain-renderer
                                [constrainsHtml]="transformLatexTemplateFulfill(sc.templateFulfill)">
                            </lib-constrain-renderer>
                            <span
                                class="vcon vcon-general vcon_delete"
                                (click)="removeCompletedConstraint($event, i)"></span>
                        </div>
                    </div>
                    <p
                        class="text-red-500 text-center text-[12px] w-full mt-[16px]"
                        *ngIf="(errorMessage$ | async)?.length">
                        {{ errorMessage$ | async }}
                    </p>
                    <div class="list-tpl-container overflow-hidden h-full w-full text-[13px]">
                        <div
                            class="list-tpl-body list-tpl overflow-y-auto overflow-x-hidden w-full max-w-[100vw] h-full flex flex-col"
                            [spinner]="inputCommandTool.isFetchingTemplates$"
                            [spinnerStyle]="'margin: auto; width: 1.75rem; height: 1.75rem;'">
                            <div
                                *ngFor="let tpl of templates(); let i = index"
                                class="tpl-item break-words px-2 rounded-[6px] leading-5 py-2"
                                tabindex="0"
                                #tplElList
                                (click)="onSelectTemplate(tpl)"
                                (keyup)="onKeyupTemplateItem($event, i)"
                                [innerHtml]="transformTemplateFulfill(tpl | heighlight)"></div>
                        </div>
                    </div>
                </div>

                <div class="command-input-row py-[10px]">
                    <button
                        class="btn vi-btn vi-btn-normal vi-btn-focus"
                        (click)="submitConstraints()"
                        [spinner]="isSubmitConstruction$"
                        [disabled]="
                            !enableSubmitAndCancelConstraintsBtn() ||
                            requiredTemplates().length ||
                            (isSubmitConstruction$ | async)
                        ">
                        <span>Tạo đối tượng</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

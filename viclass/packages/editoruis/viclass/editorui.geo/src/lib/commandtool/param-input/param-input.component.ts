import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    Inject,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { ComputeEngine } from '@cortex-js/compute-engine';
import { MathfieldElement } from 'lib-mathlive';
import { GEOMETRY_UI_SETTINGS, GeometryUiSettings } from '../../geometrytools.models';
import { setupEngine, setupMathfield, setupVirtualKeyboard } from './setup-engine';

export type ParamInputEvent = {
    latex: string;
    mathjson: string;
    finished: boolean;
};

@Component({
    standalone: true,
    imports: [CommonModule],
    selector: 'lib-param-input',
    templateUrl: './param-input.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParamInputComponent implements OnInit, AfterViewInit, OnDestroy {
    static engine = new ComputeEngine();

    @ViewChild('mathRoot', { read: ElementRef })
    mathRoot: ElementRef<HTMLDivElement>;
    mathfield: MathfieldElement;
    private teardown?: () => void;

    @Input() paramValue: string = '';
    @Input() possiblePoints: string[] = [];

    @Input() focusAfterInit = false;
    @Input() hasError = false;

    @Output() paramInput = new EventEmitter<ParamInputEvent>();

    constructor(@Inject(GEOMETRY_UI_SETTINGS) private settings: GeometryUiSettings) {}

    ngOnInit(): void {
        const fontsUri = `${this.settings.mathFieldAssetsRoot}/fonts`;
        const soundsUri = `${this.settings.mathFieldAssetsRoot}/sounds`;
        MathfieldElement.fontsDirectory = fontsUri;
        MathfieldElement.soundsDirectory = soundsUri;
        // @ts-ignore: missing properties expected
        MathfieldElement.computeEngine = ParamInputComponent.engine;

        setupEngine(ParamInputComponent.engine, new Set(this.possiblePoints));
    }

    ngAfterViewInit(): void {
        const param = this.paramValue;
        this.mathfield = new MathfieldElement();
        this.mathfield.setValue(param);
        this.mathRoot.nativeElement.appendChild(this.mathfield);
        setupMathfield(this.mathfield);
        setupVirtualKeyboard();

        const emitInputUpdate = (finished: boolean) => {
            setupEngine(ParamInputComponent.engine, new Set(this.possiblePoints), this.mathfield.value);

            this.paramInput.emit({
                latex: this.mathfield.getValue('latex-expanded'),
                mathjson: this.mathfield.expression.json,
                finished: finished,
            });
        };

        this.mathfield.menuItems = [];
        const inputListener = () => emitInputUpdate(false);
        const changeListener = () => emitInputUpdate(true);
        this.mathfield.addEventListener('input', inputListener);
        this.mathfield.addEventListener('change', changeListener);

        this.teardown = () => {
            this.mathfield.removeEventListener('input', inputListener);
            this.mathfield.removeEventListener('change', changeListener);
        };

        if (this.focusAfterInit) this.mathfield.focus();
    }

    ngOnDestroy(): void {
        if (this.teardown) this.teardown();
        this.mathfield?.disconnectedCallback();
        this.mathfield?.remove();
        delete this.mathfield;
    }
}

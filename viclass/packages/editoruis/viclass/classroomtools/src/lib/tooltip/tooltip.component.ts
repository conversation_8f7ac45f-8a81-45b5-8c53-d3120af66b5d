import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, Input, OnDestroy, OnInit } from '@angular/core';
import { Tooltip, TooltipInterface } from 'flowbite';
import { v4 as uuidv4 } from 'uuid';

@Component({
    selector: 'tooltip-classroom',
    templateUrl: './tooltip.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule],
})
export class TooltipComponent implements OnInit, OnDestroy {
    private _tooltip: TooltipInterface;

    @Input() toolTipFor: HTMLElement;
    @Input() tooltipContent: string;

    constructor(private _elementRef: ElementRef) {}

    ngOnInit(): void {
        const $tooltipEl: HTMLElement = this._elementRef.nativeElement.childNodes[0];
        const $triggerEl: HTMLElement = this.toolTipFor;

        this._tooltip = new Tooltip($tooltipEl as HTMLElement, $triggerEl as HTMLElement, undefined, { id: uuidv4() });
    }

    ngOnDestroy() {
        this._tooltip.destroyAndRemoveInstance();
        delete this._tooltip;
    }
}

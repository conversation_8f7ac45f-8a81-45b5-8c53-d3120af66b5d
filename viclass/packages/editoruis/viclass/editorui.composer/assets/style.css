@font-face {
    font-family: 'viclass-composer';
    src:
        url('fonts/viclass-composer.ttf?16wv7v') format('truetype'),
        url('fonts/viclass-composer.woff?16wv7v') format('woff'),
        url('fonts/viclass-composer.svg?16wv7v#viclass-composer') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.vcon-composer {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'viclass-composer' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vcon_brush-outline_no-color .path1:before {
    content: '\e013';
    color: rgb(255, 0, 46);
}
.vcon_brush-outline_no-color .path2:before {
    content: '\e014';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_brush-outline_no-color .path3:before {
    content: '\e015';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_brush-outline_no-color .path4:before {
    content: '\e016';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_brush-outline .path1:before {
    content: '\e010';
    color: rgb(0, 0, 0);
}
.vcon_brush-outline .path2:before {
    content: '\e011';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_brush-outline .path3:before {
    content: '\e012';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_line-style:before {
    content: '\e952b';
}
.vcon_more-tools:before {
    content: '\e003';
}
.vcon_replace:before {
    content: '\e564';
}
.vcon_heading:before {
    content: '\e560';
}
.vcon_page-bar_ad:before {
    content: '\e938';
}
.vcon_text-bold:before {
    content: '\e030';
}
.vcon_text-italic:before {
    content: '\e029';
}
.vcon_text-underline:before {
    content: '\e031';
}
.vcon_general_yes:before {
    content: '\e904';
}
.vcon_delete:before {
    content: '\e903a';
}
.vcon_empty:before {
    content: '\e006';
    color: #ff002e;
}
.vcon_text_color1 .path1:before {
    content: '\e953';
    color: rgb(0, 0, 0);
}
.vcon_text_color1 .path2:before {
    content: '\e957';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path3:before {
    content: '\e958';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path4:before {
    content: '\e959';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path5:before {
    content: '\e95a';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path6:before {
    content: '\e95b';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path7:before {
    content: '\e95c';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path8:before {
    content: '\e95d';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path9:before {
    content: '\e95e';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_color1 .path10:before {
    content: '\e95f';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text_highlight-color1 .path1:before {
    content: '\e954';
    color: rgb(0, 0, 0);
}
.vcon_text_highlight-color1 .path2:before {
    content: '\e955';
    margin-left: -1em;
    color: rgb(0, 0, 0);
}
.vcon_text_highlight-color1 .path3:before {
    content: '\e956';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path4:before {
    content: '\e960';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path5:before {
    content: '\e961';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path6:before {
    content: '\e962';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path7:before {
    content: '\e963';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path8:before {
    content: '\e964';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path9:before {
    content: '\e965';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path10:before {
    content: '\e966';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_text_highlight-color1 .path11:before {
    content: '\e967';
    margin-left: -1em;
    color: rgb(0, 250, 55);
}
.vcon_color-list .path1:before {
    content: '\e018';
    color: rgb(18, 20, 20);
}
.vcon_color-list .path2:before {
    content: '\e019';
    margin-left: -1em;
    color: rgb(255, 255, 255);
}
.vcon_color-list_no-color .path1:before {
    content: '\e020';
    color: rgb(18, 20, 20);
}
.vcon_color-list_no-color .path2:before {
    content: '\e021';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_color-list_no-color .path3:before {
    content: '\e022';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_color-list_no-color .path4:before {
    content: '\e023';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_color-list_no-color .path5:before {
    content: '\e024';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_insert:before {
    content: '\e033';
}
.vcon_bullet_none:before {
    content: '\e034';
}
.vcon_bullet_point:before {
    content: '\e035';
}
.vcon_bullet_pinwheel:before {
    content: '\e036';
}
.vcon_bullet_plus:before {
    content: '\e037';
}
.vcon_bullet_star:before {
    content: '\e038';
}
.vcon_bullet_lowercase:before {
    content: '\e039';
}
.vcon_bullet_upercase:before {
    content: '\e041';
}
.vcon_bullet_number:before {
    content: '\e042';
}
.vcon_bullet_roman-number:before {
    content: '\e043';
}
.vcon_table:before {
    content: '\e044';
}
.vcon_table-action:before {
    content: '\e055';
}
.vcon_table_delete-table:before {
    content: '\e047';
}
.vcon_table_split:before {
    content: '\e045';
}
.vcon_table_merge:before {
    content: '\e046';
}
.vcon_table_edit-color:before {
    content: '\e607';
}
.vcon_table_auto-layout:before {
    content: '\e608';
}
.vcon_table_outline_none:before {
    content: '\e609';
    color: #a4adb4;
}
.vcon_table_outline_grid .path1:before {
    content: '\e610';
    color: rgb(164, 173, 180);
}
.vcon_table_outline_grid .path2:before {
    content: '\e611';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_table_outline_outer .path1:before {
    content: '\e612';
    color: rgb(18, 20, 20);
}
.vcon_table_outline_outer .path2:before {
    content: '\e613';
    margin-left: -1em;
    color: rgb(164, 173, 180);
}
.vcon_table_outline_full:before {
    content: '\e614';
}
.vcon_table_outline_top .path1:before {
    content: '\e615';
    color: rgb(164, 173, 180);
}
.vcon_table_outline_top .path2:before {
    content: '\e616';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_table_outline_right .path1:before {
    content: '\e617';
    color: rgb(164, 173, 180);
}
.vcon_table_outline_right .path2:before {
    content: '\e618';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_table_outline_bottom .path1:before {
    content: '\e619';
    color: rgb(164, 173, 180);
}
.vcon_table_outline_bottom .path2:before {
    content: '\e61a';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_table_outline_left .path1:before {
    content: '\e620';
    color: rgb(164, 173, 180);
}
.vcon_table_outline_left .path2:before {
    content: '\e621';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_table_column-left-ad:before {
    content: '\e048';
}
.vcon_table_column-right-ad:before {
    content: '\e049';
}
.vcon_table_row-above-ad:before {
    content: '\e050';
}
.vcon_table_row-below-ad:before {
    content: '\e051';
}
.vcon_delete-column:before {
    content: '\e052';
}
.vcon_delete-row:before {
    content: '\e054';
}
.vcon_2column-layout1:before {
    content: '\e053';
}
.vcon_2column-layout2:before {
    content: '\e060';
}
.vcon_3column-layout1:before {
    content: '\e061';
}
.vcon_3column-layout2:before {
    content: '\e062';
}
.vcon_4column-layout:before {
    content: '\e063';
}
.vcon_text-align_left:before {
    content: '\e056';
}
.vcon_text-align_center:before {
    content: '\e057';
}
.vcon_text-align_right:before {
    content: '\e058';
}
.vcon_text-align_justify:before {
    content: '\e059';
}
.vcon_text-align_top:before {
    content: '\e585';
}
.vcon_text-align_middle:before {
    content: '\e586';
}
.vcon_text-align_bottom:before {
    content: '\e587';
}
.vcon_font-style:before {
    content: '\e040';
}

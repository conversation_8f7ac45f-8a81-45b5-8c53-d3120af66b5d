<div class="v-toolbar composer-toolbar with-sub" *ngIf="isShowing() && !isShowingSubEditorUI()">
    <div class="v-tool-group">
        <ng-template [ngIf]="createTool && !(createTool.isCreatingDoc$ | async)" [ngIfElse]="disableSpinner">
            <button
                class="v-tool-btn"
                placement="bottom"
                *ngIf="hasTool('CreateComposerDocumentTool')"
                [ngClass]="{ active: isToolActive('CreateComposerDocumentTool') }"
                (click)="switchTool('CreateComposerDocumentTool')"
                [disabled]="!isToolEnable('CreateComposerDocumentTool')">
                <span class="vcon vcon-composer vcon_page-bar_ad"></span>
            </button>
        </ng-template>

        <button
            #settingBtnEl
            class="v-tool-btn"
            placement="bottom"
            [ngClass]="{
                active: isToolActive('ComposerSettingsTool'),
            }"
            [disabled]="!isToolEnable('ComposerSettingsTool')"
            (click)="switchTool('ComposerSettingsTool')">
            <span class="vcon-general vcon_sidebar-setting"></span>
        </button>
        <tooltip-composer [toolTipFor]="settingBtnEl" [tooltipContent]="'Cài đặt'"></tooltip-composer>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayDisableClose]="true"
            [cdkConnectedOverlayOrigin]="settingBtnEl"
            [cdkConnectedOverlayOpen]="isToolActive('ComposerSettingsTool')"
            [cdkConnectedOverlayPositions]="settingsSubMenuPositions">
            <div class="v-toolbar">
                <div class="v-tool-group shadow-SH1 !w-auto bg-BW7">
                    <div class="setting-tool-popup shadow-SH1 p-3">
                        <setting-tool
                            [settings]="settings$ | async"
                            (onClose)="switchTool('ComposerSettingsTool')"
                            (onChange)="onSettingFieldChange($event)"></setting-tool>
                    </div>
                </div>
            </div>
        </ng-template>
    </div>
    <span class="v-toolbar composer-toolbar-gutter ng-star-inserted"></span>
</div>

<div *ngIf="isShowing()">
    <editor-ui-group
        class="no-pad"
        *ngIf="isSubEditorReady"
        [vAlign]="vAlign"
        [hAlign]="hAlign"
        [direction]="direction"
        (loaderEvent)="onSubEditorUILoaderEvent($event)"
        #subEditorToolbar></editor-ui-group>
</div>

<ng-template #disableSpinner>
    <button class="v-tool-btn" disabled="true" *ngIf="createTool !== undefined">
        <span class="vcon vcon-common vcon_mini-spinner"></span>
    </button>
</ng-template>

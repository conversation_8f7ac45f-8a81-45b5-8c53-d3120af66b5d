import { ComponentRef, createNgModule, Injector, ViewContainerRef } from '@angular/core';
import { EditorUIComponent } from '@viclass/editorui.loader';
import { COMPOSER_UI_SETTINGS, ComposerToolsModule } from './composertools.module';
import { ComposertoolsComponent } from './composertools.component';
import { ComposerUISettings } from './composertools.models';

export function factory(
    viewContainerRef: ViewContainerRef,
    injector: Injector,
    settings?: ComposerUISettings
): Promise<ComponentRef<EditorUIComponent>> {
    const module = createNgModule(ComposerToolsModule, injector);

    if (settings) {
        const moduleSettings = module.injector.get(COMPOSER_UI_SETTINGS);
        moduleSettings.subEditorUILookups = settings.subEditorUILookups;
        moduleSettings.iconClasses = settings.iconClasses;
        moduleSettings.subEditorUIBaseTheme = settings.subEditorUIBaseTheme;
    }

    return new Promise((rs, rj) => {
        rs(
            module.injector.runInContext(() =>
                viewContainerRef.createComponent(ComposertoolsComponent, {
                    injector: injector,
                    ngModuleRef: module,
                })
            )
        );
    });
}

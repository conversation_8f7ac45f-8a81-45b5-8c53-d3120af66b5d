import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { ComposerSettingsToolState } from '@viclass/editor.composer';
import { SettingFieldChangeEmitterData } from './setting-tool.models';

@Component({
    selector: 'setting-tool',
    templateUrl: './setting-tool.component.html',
    styleUrls: ['./setting-tool.component.sass'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolComponent {
    @Input() settings: ComposerSettingsToolState;

    @Output() onClose = new EventEmitter<boolean>();
    @Output() onChange = new EventEmitter<SettingFieldChangeEmitterData>();

    onFieldChange(data: SettingFieldChangeEmitterData) {
        this.onChange.emit(data);
    }

    onInputChange(e: Event, field: string) {
        const target = e.target as HTMLInputElement;
        this.onFieldChange({ field, value: target.value });
    }
}

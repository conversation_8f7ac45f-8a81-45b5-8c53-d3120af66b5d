import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnInit,
    Output,
    SimpleChanges,
} from '@angular/core';
import _ from 'lodash';
import { SettingFieldChangeEmitterData, SettingFieldValue } from '../setting-tool.models';

@Component({
    selector: 'lib-setting-tool-adjust-number',
    templateUrl: './setting-tool-adjust-number.component.html',
    styleUrls: ['./setting-tool-adjust-number.component.sass'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolAdjustNumberComponent implements OnInit, OnChanges {
    @Input() label?: string;
    @Input() field: string;
    @Input() value: SettingFieldValue;
    @Input() suffix?: string;
    @Input() min?: number;
    @Input() max?: number;
    @Input() disabled?: boolean;
    @Input() step: number = 1;

    @Output() onChange: EventEmitter<SettingFieldChangeEmitterData>;

    _value: number = 0;
    _isMixed: boolean = false;

    pressTimeout?: number;

    constructor() {
        this.onChange = new EventEmitter();
    }

    ngOnInit(): void {
        this._value = this.value?.value ?? this.min ?? 0;
        this._isMixed = this.value?.isMixed ?? false;
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['value']) {
            const newVal = changes['value'].currentValue?.value;
            this._value = newVal;
        }
    }

    changeValue(increaseValue: number, startPress = true) {
        let newVal = this._value + increaseValue;

        // validate new value
        if (_.isFinite(this.min) && newVal < this.min) {
            newVal = this.min;
        } else if (_.isFinite(this.max) && newVal > this.max) {
            newVal = this.max;
        }

        const isChanged = this._value !== newVal;
        this._value = newVal;
        this._isMixed = false;

        if (isChanged) {
            this.onChange.emit({ field: this.field, value: newVal });
            this.pressTimeout = setTimeout(() => this.changeValue(increaseValue, false), startPress ? 500 : 100) as any;
        }
    }

    cancelPointerPress() {
        if (this.pressTimeout) {
            clearTimeout(this.pressTimeout);
            this.pressTimeout = undefined;
        }
    }
}

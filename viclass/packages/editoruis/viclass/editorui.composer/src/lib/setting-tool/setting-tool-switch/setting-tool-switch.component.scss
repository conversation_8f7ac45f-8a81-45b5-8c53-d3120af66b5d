mat-slide-toggle.my-slide {
    --mdc-switch-selected-handle-color: rgb(var(--P1));
    --mdc-switch-selected-pressed-handle-color: rgb(var(--P1));
    --mdc-switch-selected-pressed-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-hover-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-hover-handle-color: rgb(var(--P1));
    --mdc-switch-selected-focus-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-focus-handle-color: rgb(var(--P1));
    --mdc-switch-selected-track-color: rgb(var(--P1));
    --mdc-switch-selected-pressed-track-color: rgb(var(--P1));
    --mdc-switch-selected-hover-track-color: rgb(var(--P1));
    --mdc-switch-selected-focus-track-color: rgb(var(--P1));
}

mat-slide-toggle.my-slide-mixed {
    --mdc-switch-unselected-handle-color: rgb(var(--P1));
    --mdc-switch-unselected-pressed-handle-color: rgb(var(--P1));
    --mdc-switch-unselected-pressed-state-layer-color: rgb(var(--P1));
    --mdc-switch-unselected-hover-state-layer-color: rgb(var(--P1));
    --mdc-switch-unselected-hover-handle-color: rgb(var(--P1));
    --mdc-switch-unselected-focus-state-layer-color: rgb(var(--P1));
    --mdc-switch-unselected-focus-handle-color: rgb(var(--P1));
    --mdc-switch-unselected-track-color: rgb(var(--P1));
    --mdc-switch-unselected-pressed-track-color: rgb(var(--P1));
    --mdc-switch-unselected-hover-track-color: rgb(var(--P1));
    --mdc-switch-unselected-focus-track-color: rgb(var(--P1));
}

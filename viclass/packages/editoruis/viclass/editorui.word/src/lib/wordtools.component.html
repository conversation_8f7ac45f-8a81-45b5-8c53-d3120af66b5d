<div class="v-toolbar word-toolbar with-sub" *ngIf="isShowing() && !isShowingSubEditorUI()">
    <div class="v-tool-group">
        <ng-template [ngIf]="createTool && !(createTool.isCreatingDoc$ | async)" [ngIfElse]="disableSpinner">
            <button
                class="v-tool-btn"
                placement="bottom"
                *ngIf="hasTool('CreateWordDocumentTool')"
                [ngClass]="{ active: isToolActive('CreateWordDocumentTool') }"
                (click)="switchTool('CreateWordDocumentTool')"
                [disabled]="!isToolEnable('CreateWordDocumentTool')">
                <span class="vcon vcon-word vcon_page-bar_ad"></span>
            </button>
        </ng-template>

        <button
            #settingBtnEl
            class="v-tool-btn"
            placement="bottom"
            [ngClass]="{
                active: isToolActive('WordSettingsTool'),
            }"
            [disabled]="!isToolEnable('WordSettingsTool')"
            (click)="switchTool('WordSettingsTool')">
            <span class="vcon-general vcon_sidebar-setting"></span>
        </button>
        <tooltip-word [toolTipFor]="settingBtnEl" [tooltipContent]="'Cài đặt'"></tooltip-word>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayDisableClose]="true"
            [cdkConnectedOverlayOrigin]="settingBtnEl"
            [cdkConnectedOverlayOpen]="isToolActive('WordSettingsTool')"
            [cdkConnectedOverlayPositions]="settingsSubMenuPositions">
            <div class="v-toolbar">
                <div class="v-tool-group shadow-SH1 !w-auto bg-BW7">
                    <div class="setting-tool-popup shadow-SH1 p-3">
                        <setting-tool
                            [settings]="settings$ | async"
                            (onClose)="switchTool('WordSettingsTool')"
                            (onChange)="onSettingFieldChange($event)"></setting-tool>
                    </div>
                </div>
            </div>
        </ng-template>

        <div class="v-tool-separation"></div>

        <button
            #insertBtnEl
            class="v-tool-btn"
            placement="bottom"
            [ngClass]="{ active: (focusInsertSubEditor$ | async) || isToolActive('InsertionEditorTool') }"
            [disabled]="!isToolEnable('InsertionEditorTool')"
            (pointerenter)="onMouseEnter('InsertSubEditor')"
            (pointerleave)="onMouseLeave('InsertSubEditor', $event)"
            (click)="switchTool('InsertionEditorTool')">
            <span class="vcon vcon-word vcon_insert"></span>
        </button>
        <tooltip-word [toolTipFor]="insertBtnEl" [tooltipContent]="'Chèn (Ctrl+Q)'"></tooltip-word>

        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="insertBtnEl"
            [cdkConnectedOverlayOpen]="(focusInsertSubEditor$ | async) || isToolActive('InsertionEditorTool')"
            [cdkConnectedOverlayPositions]="subMenuPositions">
            <div
                class="v-toolbar word-toolbar"
                (pointerenter)="onMouseEnter('InsertSubEditor')"
                (pointerleave)="onMouseLeave('InsertSubEditor', $event)">
                <div class="v-tool-group">
                    <button
                        #subInsertBtnEl
                        *ngFor="let t of subEditors(); let idx = index"
                        class="v-tool-btn"
                        (mousedown)="insertDocument(t, $event)">
                        <span class="vcon vcon-common {{ iconClass(t) }}"></span>
                        <tooltip-word
                            [toolTipFor]="subInsertBtnEl"
                            [tooltipContent]="tooltipLabel(t) + ' (Ctrl+Q,Ctrl+' + (idx + 1) + ')'"></tooltip-word>
                    </button>
                </div>
            </div>
        </ng-template>

        <div class="v-tool-separation"></div>

        <button
            #createTableBtnEl
            class="v-tool-btn"
            placement="bottom"
            [disabled]="!isToolEnable('WordTableTool')"
            (pointerenter)="onMouseEnter('CreateTable')"
            (pointerleave)="onMouseLeave('CreateTable', $event)">
            <span class="vcon vcon-word vcon_table"></span>
        </button>
        <tooltip-word [toolTipFor]="createTableBtnEl" [tooltipContent]="'Tạo bảng mới'"></tooltip-word>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="createTableBtnEl"
            [cdkConnectedOverlayOpen]="focusCreateTable$ | async"
            [cdkConnectedOverlayPositions]="headingSubMenuPositions">
            <tb-table-size-selector
                (pointerenter)="onMouseEnter('CreateTable')"
                (pointerleave)="onMouseLeave('CreateTable', $event)"
                (selected)="insertTable($event)"></tb-table-size-selector>
        </ng-template>

        <!-- Table Layout Edit Button -->
        <button
            #tableLayoutEditEl
            class="v-tool-btn"
            placement="bottom"
            [ngClass]="{
                active: showTableLayoutEditOverlay$ | async,
            }"
            [disabled]="!(tableContext$ | async)?.isTableEditing && !(tableContext$ | async)?.isTableSelection"
            (click)="toggleTableLayoutEditOverlay()">
            <span class="vcon vcon-word vcon_table-action"></span>
        </button>
        <tooltip-word [toolTipFor]="tableLayoutEditEl" [tooltipContent]="'Chỉnh sửa bố cục bảng'"></tooltip-word>

        <!-- Table Layout Edit Overlay -->
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="tableLayoutEditEl"
            [cdkConnectedOverlayOpen]="showTableLayoutEditOverlay$ | async"
            [cdkConnectedOverlayPositions]="tableSubMenuPositions">
            <div class="v-toolbar">
                <div class="v-tool-group shadow-SH1 !w-auto bg-BW7">
                    <lib-table-layout-edit-tool
                        [tableContext]="tableContext$ | async"
                        [tableTool]="tableTool"
                        (onClose)="closeTableLayoutEditOverlay()"></lib-table-layout-edit-tool>
                </div>
            </div>
        </ng-template>

        <!-- Cell Edit Button -->
        <button
            #tableCellEditEl
            class="v-tool-btn"
            placement="bottom"
            [ngClass]="{
                active: showTableCellEditOverlay$ | async,
            }"
            [disabled]="!(tableContext$ | async)?.isTableEditing && !(tableContext$ | async)?.isTableSelection"
            (click)="toggleTableCellEditOverlay()">
            <span class="vcon vcon-word vcon_table_edit-color"></span>
        </button>
        <tooltip-word [toolTipFor]="tableCellEditEl" [tooltipContent]="'Chỉnh sửa ô'"></tooltip-word>

        <!-- Cell Edit Overlay -->
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="tableCellEditEl"
            [cdkConnectedOverlayOpen]="showTableCellEditOverlay$ | async"
            [cdkConnectedOverlayPositions]="tableSubMenuPositions">
            <div class="v-toolbar">
                <div class="v-tool-group shadow-SH1 !w-auto bg-BW7">
                    <lib-table-cell-edit-tool
                        [tableContext]="tableContext$ | async"
                        [tableTool]="tableTool"
                        (onClose)="closeTableCellEditOverlay()"></lib-table-cell-edit-tool>
                </div>
            </div>
        </ng-template>

        <div class="v-tool-separation"></div>

        <tb-toolbar-button
            [data]="layoutTypeBtn"
            [subMenuPositions]="subMenuPositions"
            [allowCheck]="isToolEnable"
            (btnClicked)="onInsertLayout($event)"></tb-toolbar-button>

        <div class="v-tool-separation"></div>

        <button
            #headingBtnEl
            class="v-tool-btn"
            placement="bottom"
            [disabled]="!isToolEnable('TextStylingTool')"
            (pointerenter)="onMouseEnter('Heading')"
            (pointerleave)="onMouseLeave('Heading', $event)">
            <span class="vcon vcon-word vcon_heading"></span>
        </button>
        <tooltip-word [toolTipFor]="headingBtnEl" [tooltipContent]="'Heading'"></tooltip-word>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="headingBtnEl"
            [cdkConnectedOverlayOpen]="focusHeading$ | async"
            [cdkConnectedOverlayPositions]="headingSubMenuPositions">
            <div
                [class]="'v-toolbar word-toolbar heading-wrapper-tb__' + (focusedDocIdCtx$ | async)"
                (pointerenter)="onMouseEnter('Heading')"
                (pointerleave)="onMouseLeave('Heading', $event)">
                <div class="v-tool-group flex !flex-row !w-auto bg-BW7 overflow-hidden">
                    <div class="flex-1">
                        <div
                            *ngFor="let heading of headingList"
                            [class]="
                                'hover:bg-P3 p-[10px] text-[14px] cursor-pointer heading-wrapper-item HeadingWrapper HeadingWrapper__' +
                                heading.type
                            "
                            [ngClass]="{
                                '!bg-P2': heading.type === (blockType$ | async),
                            }">
                            {{ heading.label }}

                            <div class="heading-wrapper-overlays flex align-items-center hover:bg-P3 overflow-hidden">
                                <div
                                    (click)="headingWrapperTool.wrapHeading(heading.type)"
                                    class="p-[10px] pr-0 overflow-hidden text-clip whitespace-nowrap flex-shrink">
                                    {{ heading.label }}
                                </div>
                                <div
                                    class="p-[10px] flex-grow flex-shrink-0 justify-self-end flex align-items-center justify-end gap-[5px]">
                                    <button title="Áp dụng" (click)="headingWrapperTool.wrapHeading(heading.type)">
                                        <span class="vcon vcon-word vcon_general_yes !text-BW1"></span>
                                    </button>
                                    <button
                                        title="Cập nhật thay thế"
                                        (click)="replaceHeadingWrapperStyle(heading.type)">
                                        <span class="vcon vcon-word vcon_replace !text-BW1"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-[10px] flex align-items-center">
                            <button
                                type="button"
                                class="vi-btn vi-btn-normal vi-btn-outline"
                                (click)="headingWrapperTool.resetHeadingStyles()">
                                <span class="vcon vcon-general vcon_general_reset"></span>
                                Đặt lại kiểu
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </ng-template>

        <button
            #fontBtnEl
            class="v-tool-btn"
            placement="bottom"
            [disabled]="!isToolEnable('TextStylingTool')"
            (pointerenter)="onMouseEnter('Font')"
            (pointerleave)="onMouseLeave('Font', $event)">
            <span class="vcon vcon-word vcon_font-style"></span>
        </button>
        <tooltip-word [toolTipFor]="fontBtnEl" [tooltipContent]="'Font'"></tooltip-word>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="fontBtnEl"
            [cdkConnectedOverlayOpen]="focusFont$ | async"
            [cdkConnectedOverlayPositions]="fontSubMenuPositions">
            <div
                class="v-toolbar word-toolbar"
                (pointerenter)="onMouseEnter('Font')"
                (pointerleave)="onMouseLeave('Font', $event)">
                <div class="v-tool-group flex !flex-row !w-auto bg-BW7 overflow-hidden">
                    <div class="flex-1">
                        <div
                            *ngFor="let font of fontFamilies"
                            [ngStyle]="{
                                'font-family': font,
                            }"
                            class="hover:bg-P3 p-[10px] text-[14px] cursor-pointer"
                            [ngClass]="{
                                'bg-P2': font === (fontFamilyCtx$ | async),
                            }"
                            (click)="onFontFamilyChange(font)">
                            {{ font }}
                        </div>
                    </div>
                    <div class="v-tool-group slider">
                        <tb-width-slider
                            [value]="fontSizeCtx$ | async"
                            [minValue]="12"
                            [maxValue]="100"
                            [vertical]="isVertical"
                            [height]="isVertical ? 80 : undefined"
                            (valueChange)="onFontSizeChange($event)">
                        </tb-width-slider>
                    </div>
                </div>
            </div>
        </ng-template>

        <button
            class="v-tool-btn relative"
            placement="bottom"
            #styleFormatBtnEl
            [disabled]="!isToolEnable('TextStylingTool') && !isToolEnable('FormatTextTool')"
            (pointerenter)="onMouseEnter('StyleFormat')"
            (pointerleave)="onMouseLeave('StyleFormat', $event)">
            <span class="vcon vcon-word vcon_text_color1">
                <span class="path1"></span>
                <span class="path5" [style]="'--color-text--:' + (textColorCtx$ | async)"></span>
            </span>
            <span class="vcon vcon-word vcon_more-tools"></span>
        </button>
        <tooltip-word [toolTipFor]="styleFormatBtnEl" [tooltipContent]="'Màu và định dạng'"></tooltip-word>

        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="styleFormatBtnEl"
            [cdkConnectedOverlayOpen]="focusStyleNFormat$ | async"
            [cdkConnectedOverlayPositions]="subMenuPositions">
            <div
                class="v-toolbar word-toolbar"
                (pointerenter)="onMouseEnter('StyleFormat')"
                (pointerleave)="onMouseLeave('StyleFormat', $event)">
                <div class="v-tool-group">
                    <button
                        class="v-tool-btn relative"
                        placement="bottom"
                        #colorBtnEl
                        [disabled]="!isToolEnable('TextStylingTool')"
                        (pointerenter)="onMouseEnter('StyleFormat.TextColor')"
                        (pointerleave)="onMouseLeave('StyleFormat.TextColor', $event)">
                        <span class="vcon vcon-word vcon_text_color1">
                            <span class="path1"></span>
                            <span class="path5" [style]="'--color-text--:' + (textColorCtx$ | async)"></span>
                        </span>
                        <span class="vcon vcon-word vcon_more-tools"></span>
                    </button>
                    <tooltip-word [toolTipFor]="colorBtnEl" [tooltipContent]="'Màu chữ'"></tooltip-word>

                    <ng-template
                        cdkConnectedOverlay
                        cdkConnectedOverlayPush
                        [cdkConnectedOverlayOrigin]="colorBtnEl"
                        [cdkConnectedOverlayOpen]="focusTextColor$ | async"
                        [cdkConnectedOverlayPositions]="subMenuPositions">
                        <div
                            class="v-toolbar word-toolbar"
                            (pointerenter)="onMouseEnter('StyleFormat.TextColor')"
                            (pointerleave)="onMouseLeave('StyleFormat.TextColor', $event)">
                            <div class="v-tool-group">
                                <button
                                    *ngFor="let color of colorList"
                                    class="v-tool-btn"
                                    [ngClass]="{
                                        active: color === (textColorCtx$ | async),
                                    }"
                                    (click)="onSelectTextColor(color)">
                                    <span class="vcon vcon-word vcon_color-list">
                                        <span class="path1"></span>
                                        <span class="path2" [style]="'--color--:' + color"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </ng-template>

                    <button
                        #highlightBtnEl
                        class="v-tool-btn relative"
                        placement="bottom"
                        [disabled]="!isToolEnable('TextStylingTool')"
                        (pointerenter)="onMouseEnter('StyleFormat.HighlightColor')"
                        (pointerleave)="onMouseLeave('StyleFormat.HighlightColor', $event)">
                        <span class="vcon vcon-word vcon_text_highlight-color1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path11" [style]="'--color-highlight--:' + (highlightColorCtx$ | async)"></span>
                        </span>
                        <span class="vcon vcon-word vcon_more-tools"></span>
                    </button>
                    <tooltip-word [toolTipFor]="highlightBtnEl" [tooltipContent]="'Làm nổi bật'"></tooltip-word>

                    <ng-template
                        cdkConnectedOverlay
                        cdkConnectedOverlayPush
                        [cdkConnectedOverlayOrigin]="highlightBtnEl"
                        [cdkConnectedOverlayOpen]="focusHighlightColor$ | async"
                        [cdkConnectedOverlayPositions]="subMenuPositions">
                        <div
                            class="v-toolbar word-toolbar"
                            (pointerenter)="onMouseEnter('StyleFormat.HighlightColor')"
                            (pointerleave)="onMouseLeave('StyleFormat.HighlightColor', $event)">
                            <div class="v-tool-group">
                                <button
                                    class="v-tool-btn"
                                    [ngClass]="{
                                        active: (highlightColorCtx$ | async) === 'transparent',
                                    }"
                                    (click)="onSelectHighlightColor()">
                                    <span class="vcon vcon-word vcon_color-list_no-color"
                                        ><span class="path1"></span><span class="path2"></span
                                        ><span class="path3"></span><span class="path4"></span
                                        ><span class="path5"></span
                                    ></span>
                                </button>
                                <button
                                    *ngFor="let color of highlightColorList"
                                    class="v-tool-btn"
                                    [ngClass]="{
                                        active: color === (highlightColorCtx$ | async),
                                    }"
                                    (click)="onSelectHighlightColor(color)">
                                    <span class="vcon vcon-word vcon_color-list">
                                        <span class="path1"></span>
                                        <span class="path2" [style]="'--color--:' + color"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </ng-template>

                    <div class="v-tool-separation"></div>

                    <button
                        #boldBtnEl
                        class="v-tool-btn"
                        placement="bottom"
                        [disabled]="!isToolEnable('FormatTextTool')"
                        [ngClass]="{ active: (wordContext$ | async)?.isBold }"
                        (click)="formatTextTool.bold()">
                        <span class="vcon vcon-word vcon_text-bold"></span>
                    </button>
                    <tooltip-word [toolTipFor]="boldBtnEl" [tooltipContent]="'Làm đậm'"></tooltip-word>

                    <button
                        #italicBtnEl
                        class="v-tool-btn"
                        placement="bottom"
                        [disabled]="!isToolEnable('FormatTextTool')"
                        [ngClass]="{ active: (wordContext$ | async)?.isItalic }"
                        (click)="formatTextTool.italic()">
                        <span class="vcon vcon-word vcon_text-italic"></span>
                    </button>
                    <tooltip-word [toolTipFor]="italicBtnEl" [tooltipContent]="'In nghiêng'"></tooltip-word>

                    <button
                        #underlineBtnEl
                        class="v-tool-btn"
                        placement="bottom"
                        [disabled]="!isToolEnable('FormatTextTool')"
                        [ngClass]="{ active: (wordContext$ | async)?.isUnderline }"
                        (click)="formatTextTool.underline()">
                        <span class="vcon vcon-word vcon_text-underline"></span>
                    </button>
                    <tooltip-word [toolTipFor]="underlineBtnEl" [tooltipContent]="'Gạch chân'"></tooltip-word>
                </div>
            </div>
        </ng-template>

        <div class="v-tool-separation"></div>

        <tb-toolbar-button
            [data]="alignmentBtn"
            [subMenuPositions]="subMenuPositions"
            [isToolActive]="isToolActive"
            [allowCheck]="isToolEnable"
            (btnClicked)="onAlignmentBtnClick($event)"></tb-toolbar-button>

        <button
            #listBtnEl
            class="v-tool-btn relative"
            placement="bottom"
            [disabled]="!isToolEnable('ListTool')"
            (pointerenter)="onMouseEnter('List')"
            (pointerleave)="onMouseLeave('List', $event)"
            [ngClass]="{ active: ['bullet', 'number'].includes((wordContext$ | async)?.blockType) }">
            <span class="vcon vcon-word vcon_bullet_point"></span>
            <span class="vcon vcon-word vcon_more-tools"></span>
        </button>
        <tooltip-word [toolTipFor]="listBtnEl" [tooltipContent]="'Danh sách'"></tooltip-word>

        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="listBtnEl"
            [cdkConnectedOverlayOpen]="focusList$ | async"
            [cdkConnectedOverlayPositions]="listSubMenuPositions">
            <div
                class="v-toolbar word-toolbar"
                (pointerenter)="onMouseEnter('List')"
                (pointerleave)="onMouseLeave('List', $event)">
                <div class="v-tool-group">
                    <button
                        *ngFor="let listType of listBtnChildren"
                        class="v-tool-btn"
                        [ngClass]="{
                            active:
                                listType.param === (wordContext$ | async).list?.listStyleType ||
                                (!(wordContext$ | async).list && listType.param === 'none'),
                        }"
                        (click)="onListBtnClick(listType)">
                        <span [ngClass]="listType.iconClasses"></span>
                    </button>
                </div>
            </div>
        </ng-template>
    </div>
    <span class="v-toolbar word-toolbar-gutter ng-star-inserted"></span>
</div>

<div class="v-toolbar word-toolbar with-sub" *ngIf="isShowing() && isShowingSubEditorUI()">
    <div class="v-tool-group">
        <tb-toolbar-button
            [data]="alignmentBtn"
            [subMenuPositions]="subMenuPositions"
            [isToolActive]="isToolActive"
            [allowCheck]="isToolEnable"
            (btnClicked)="onAlignmentBtnClick($event)"></tb-toolbar-button>
    </div>
</div>

<div *ngIf="isShowing()">
    <editor-ui-group
        class="no-pad"
        *ngIf="isSubEditorReady"
        [vAlign]="vAlign"
        [hAlign]="hAlign"
        [direction]="direction"
        (loaderEvent)="onSubEditorUILoaderEvent($event)"
        #subEditorToolbar></editor-ui-group>
</div>

<ng-template #disableSpinner>
    <button class="v-tool-btn" disabled="true" *ngIf="createTool !== undefined">
        <span class="vcon vcon-common vcon_mini-spinner"></span>
    </button>
</ng-template>

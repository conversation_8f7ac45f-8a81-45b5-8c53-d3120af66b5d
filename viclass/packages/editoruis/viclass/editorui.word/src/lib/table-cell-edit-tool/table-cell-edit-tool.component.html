<div class="cell-edit-tool w-[300px] p-3 bg-white rounded-lg">
    <div class="flex gap-1 items-center">
        <span class="vcon vcon-general vcon_document_geometry text-P1"></span>
        <span class="text-[12px] text-P1">NỀN / ĐƯỜNG VIỀN</span>
        <div class="flex-grow"></div>
        <button (click)="onClose.emit()">
            <span class="vcon vcon-general vcon_delete text-BW1 text-[13px]"></span>
        </button>
    </div>

    <div class="bg-P1 h-[1px] my-3"></div>

    <!-- Cell Background Section -->
    <div class="flex flex-col gap-4 mb-4">
        <div>
            <p class="font-bold text-sm">Nền</p>
            <div class="bg-BW1 h-[2px] mt-2"></div>
        </div>
        <div class="grid grid-cols-7 gap-3 py-2 px-[5px] pl-2">
            <div
                class="border-black rounded-full w-[20px] h-[20px] border-[1px] cursor-pointer flex items-center justify-center"
                [ngClass]="{
                    'selected-color': cellBackgroundColor === 'transparent',
                    'opacity-50 !cursor-not-allowed pointer-events-none': false,
                }"
                (click)="onSelectCellColor('transparent')">
                <span class="vcon vcon-word vcon_color-list_no-color text-xs">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                    <span class="path5"></span>
                </span>
            </div>
            <div
                *ngFor="let color of tableCellBgColorList"
                class="border-black rounded-full w-[20px] h-[20px] border-[1px] cursor-pointer flex items-center justify-center"
                [ngClass]="{
                    'selected-color': color === cellBackgroundColor,
                    'opacity-50 !cursor-not-allowed pointer-events-none': false,
                }"
                [ngStyle]="{ 'background-color': color }"
                (click)="onSelectCellColor(color)">
                <span class="vcon vcon-word vcon_color-list">
                    <span class="path1"></span>
                    <span class="path2" [style]="'--color--:' + color"></span>
                </span>
            </div>
        </div>
    </div>
    <!-- Border Settings Row -->
    <div class="flex flex-col gap-4 mb-4">
        <div>
            <p class="font-bold text-sm">Đường viền</p>
            <div class="bg-BW1 h-[2px] mt-2"></div>
        </div>

        <!-- Border Type -->
        <div class="flex flex-col">
            <label class="text-sm mb-1">Kiểu viền</label>
            <div class="flex gap-1 items-center justify-between pl-2">
                <label class="!w-8 !h-8 flex items-center justify-center cursor-pointer gap-2">
                    <input
                        type="radio"
                        name="borderType"
                        [checked]="cellBorderType === 'hidden'"
                        (change)="onCellBorderTypeChange('hidden')" />
                    <span class="vcon vcon-word vcon_color-list_no-color text-xs">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                        <span class="path4"></span>
                        <span class="path5"></span>
                    </span>
                </label>
                <label
                    *ngFor="let type of borderTypes"
                    class="!w-8 !h-8 flex items-center justify-center cursor-pointer gap-2">
                    <input
                        type="radio"
                        name="borderType"
                        class="cursor-pointer"
                        [checked]="type === cellBorderType"
                        (change)="onCellBorderTypeChange(type)" />
                    <span class="!h-[16px] flex items-center">
                        <div
                            class="!h-[1px] w-[15px]"
                            [ngStyle]="{ 'border-top': (type === 'double' ? 3 : 2) + 'px ' + type + ' black' }"></div>
                    </span>
                </label>
            </div>
        </div>

        <!-- Border Width -->
        <lib-setting-tool-adjust-number
            label="Độ dày"
            field="cellBorderWidth"
            [value]="{ value: cellBorderWidth }"
            [min]="cellBorderType === 'double' ? 3 : 1"
            [max]="20"
            suffix="px"
            [disabled]="cellBorderType === 'hidden'"
            (onChange)="onCellBorderWidthChange($event.value)">
        </lib-setting-tool-adjust-number>

        <!-- Border Color -->
        <div class="flex flex-col justify-between">
            <lib-setting-tool-colors
                label="Màu viền"
                field="cellBorderColor"
                [colorList]="borderColorList"
                [value]="{ value: cellBorderColor }"
                [maxWidth]="290"
                [disabled]="cellBorderType === 'hidden'"
                (onChange)="onCellBorderColorChange($event.value)">
            </lib-setting-tool-colors>
        </div>
    </div>

    <!-- Border Application Modes -->
    <div class="mb-4">
        <label class="text-sm text-gray-600 mb-2 block">Áp dụng cho</label>
        <div class="grid grid-cols-4 gap-2">
            <button
                *ngFor="let mode of borderApplicationModes"
                class="v-tool-btn [&:not(.active)]:!bg-BW7 hover:!bg-P2 shadow-SH1"
                #applyBtnEl
                (click)="onBorderApplicationModeSelect(mode.value)">
                <span class="vcon vcon-word {{ mode.icon }}">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </span>
                <tooltip-word [toolTipFor]="applyBtnEl" [tooltipContent]="mode.label"></tooltip-word>
            </button>
        </div>
    </div>
</div>

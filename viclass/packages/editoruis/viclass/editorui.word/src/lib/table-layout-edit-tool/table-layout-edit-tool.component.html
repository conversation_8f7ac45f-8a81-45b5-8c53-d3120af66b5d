<div class="table-layout-edit-tool w-[300px] p-3 bg-white rounded-lg">
    <div class="flex gap-1 items-center">
        <span class="vcon vcon-general vcon_document_geometry text-P1"></span>
        <span class="text-[12px] text-P1">BẢNG</span>
        <div class="flex-grow"></div>
        <button (click)="onClose.emit()">
            <span class="vcon vcon-general vcon_delete text-BW1 text-[13px]"></span>
        </button>
    </div>

    <div class="bg-P1 h-[1px] my-3"></div>

    <!-- Vertical Alignment Section -->
    <div class="flex flex-col gap-4 mb-4">
        <div>
            <p class="font-bold text-sm">Canh dòng</p>
            <div class="bg-BW1 h-[2px] mt-2"></div>
        </div>
        <div class="grid grid-cols-4 gap-2">
            <button
                *ngFor="let btn of tableCellVerticalAlignBtns"
                class="v-tool-btn [&:not(.active)]:!bg-BW7 hover:!bg-P2 shadow-SH1"
                [ngClass]="{ active: isVerticalCellActive(btn.name) }"
                [disabled]="!isTableActionEnable(btn.name)"
                #alignBtnEl
                (click)="onTableActionBtnClick(btn)">
                <span [class]="btn.iconClasses">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </span>
                <tooltip-word [toolTipFor]="alignBtnEl" [tooltipContent]="btn.label"></tooltip-word>
            </button>
        </div>
    </div>

    <!-- Merge/Split Section -->
    <div class="flex flex-col gap-4 mb-4">
        <div>
            <p class="font-bold text-sm">Gộp / tách ô</p>
            <div class="bg-BW1 h-[2px] mt-2"></div>
        </div>
        <div class="grid grid-cols-4 gap-2">
            <button
                *ngFor="let btn of tableMergeSplitBtn"
                class="v-tool-btn [&:not(.active)]:!bg-BW7 hover:!bg-P2 shadow-SH1"
                [disabled]="!isTableActionEnable(btn.name) || !isActionAllowed(btn.name)"
                #mergeBtnEl
                (click)="onTableActionBtnClick(btn)">
                <span [class]="btn.iconClasses">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </span>
                <tooltip-word [toolTipFor]="mergeBtnEl" [tooltipContent]="btn.label"></tooltip-word>
            </button>
        </div>
    </div>

    <!-- Distribute Columns/Rows Evenly -->
    <div class="flex flex-col gap-4 mb-4">
        <div>
            <p class="font-bold text-sm">Giãn cột / dòng tự động</p>
            <div class="bg-BW1 h-[2px] mt-2"></div>
        </div>
        <div class="grid grid-cols-4 gap-2">
            <button
                *ngFor="let btn of tableDistributeBtn"
                class="v-tool-btn [&:not(.active)]:!bg-BW7 hover:!bg-P2 shadow-SH1"
                [disabled]="!isTableActionEnable(btn.name) || !isActionAllowed(btn.name)"
                #mergeBtnEl
                (click)="onTableActionBtnClick(btn)">
                <span [class]="btn.iconClasses">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </span>
                <tooltip-word [toolTipFor]="mergeBtnEl" [tooltipContent]="btn.label"></tooltip-word>
            </button>
        </div>
    </div>

    <!-- Insert/Delete Section -->
    <div class="flex flex-col gap-4 mb-4">
        <div>
            <p class="font-bold text-sm">Thêm / xóa cột / dòng</p>
            <div class="bg-BW1 h-[2px] mt-2"></div>
        </div>
        <div class="grid grid-cols-4 gap-2">
            <button
                *ngFor="let btn of tableInsertDeleteBtns"
                class="v-tool-btn [&:not(.active)]:!bg-BW7 hover:!bg-P2 shadow-SH1"
                [disabled]="!isTableActionEnable(btn.name)"
                #insertDeleteBtnEl
                (click)="onTableActionBtnClick(btn)">
                <span [class]="btn.iconClasses">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </span>
                <tooltip-word [toolTipFor]="insertDeleteBtnEl" [tooltipContent]="btn.label"></tooltip-word>
            </button>
        </div>
    </div>
</div>

<div>
    <span *ngIf="label?.length" class="text-BW1 text-sm">{{ label }}</span>
    <div
        class="flex gap-3 flex-wrap py-2 px-[5px]"
        [ngStyle]="{
            gap: '1.25rem',
            maxWidth: maxWidth + 'px',
        }">
        <div
            *ngFor="let color of colorList"
            [ngStyle]="{
                'background-color': color,
            }"
            [ngClass]="{
                'selected-color': value?.value === color,
                'border-black rounded-full w-[20px] h-[20px] border-[1px] cursor-pointer': true,
                'opacity-50 !cursor-not-allowed pointer-events-none': disabled,
            }"
            (click)="changeValue(color)"></div>
    </div>
</div>

import { EditorType, ModuleLookup } from '@viclass/editor.core';
import { BorderStyleData, WordTableTool, WordToolBar, WordToolType } from '@viclass/editor.word';
import { EditorUILookup } from '@viclass/editorui.loader';

export interface CommunicationEvent<T> {
    source: any;
    eventType: T;
    eventData: any;
}

export type WordEditorControllerEvent = 'switch-tool';

export class WordTools {
    toolbar: WordToolBar;

    constructor(toolBar: WordToolBar) {
        this.toolbar = toolBar;
    }

    activeTool(): WordToolType {
        return this.toolbar.activeTool?.toolType;
    }
}

export interface WordUISettings {
    subEditorUILookups: EditorUILookup[];
    subEditorUIBaseTheme: ModuleLookup;
    iconClasses: Partial<{ [key in EditorType]: string }>;
}

export type ButtonData<T> = {
    name: T;
    iconClasses: string;
    param?: string;
    onClick?: (btnName: T) => void;
    label?: string;
    children?: ButtonData<T>[];
};

/**
 * Configuration for table border dialog
 */
export type TableBorderDialogConfig = {
    /**
     * The border style of current table selection
     */
    borderStyle?: BorderStyleData;
    /**
     * The update table tool
     */
    tableTool: WordTableTool;
};

export type TableActions =
    | 'InsertRowAbove'
    | 'InsertRowBelow'
    | 'InsertColumnLeft'
    | 'InsertColumnRight'
    | 'DeleteRow'
    | 'DeleteColumn'
    | 'DeleteTable'
    | 'MergeCells'
    | 'SplitCell'
    | 'VerticalAlignTop'
    | 'VerticalAlignMiddle'
    | 'VerticalAlignBottom'
    | 'DistributeRowsEvenly'
    | 'DistributeColumnsEvenly';

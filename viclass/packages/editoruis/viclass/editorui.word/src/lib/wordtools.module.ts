import { DragDropModule } from '@angular/cdk/drag-drop';
import { Overlay, OverlayContainer, OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { InjectionToken, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EditorUILoaderComponent, EditorUILoaderModule } from '@viclass/editorui.loader';
import {
    SettingToolAdjustNumberComponent,
    SettingToolColorsComponent,
    SettingToolComponent,
    SettingToolSwitchComponent,
} from './setting-tool';
import { TableCellEditToolComponent } from './table-cell-edit-tool/table-cell-edit-tool.component';
import { TableLayoutEditToolComponent } from './table-layout-edit-tool/table-layout-edit-tool.component';
import { TableSizeSelectorComponent } from './table-size-selector/table-size-selector.component';
import { ToolbarButtonComponent } from './toolbar-button/toolbar-button.component';
import { TooltipComponent } from './tooltip/tooltip.component';
import { WidthSliderComponent } from './width-slider/width-slider.component';
import { WordtoolsComponent } from './wordtools.component';
import { WordUISettings } from './wordtools.models';

export const WORD_UI_SETTINGS = new InjectionToken<WordUISettings>('wordui.settings');

const uiSettings: WordUISettings = {
    subEditorUILookups: [],
    subEditorUIBaseTheme: undefined,
    iconClasses: undefined,
};

@NgModule({
    declarations: [
        WordtoolsComponent,
        ToolbarButtonComponent,
        SettingToolColorsComponent,
        SettingToolSwitchComponent,
        SettingToolAdjustNumberComponent,
        SettingToolComponent,
        TableCellEditToolComponent,
        TableLayoutEditToolComponent,
    ],
    imports: [
        CommonModule,
        OverlayModule,
        MatAutocompleteModule,
        MatSlideToggleModule,
        ReactiveFormsModule,
        FormsModule,
        DragDropModule,
        EditorUILoaderModule,
        TooltipComponent,
        TableSizeSelectorComponent,
        MatDialogModule,
        WidthSliderComponent,
    ],
    exports: [WordtoolsComponent, TableCellEditToolComponent, TableLayoutEditToolComponent],
    providers: [
        {
            provide: WORD_UI_SETTINGS,
            useValue: uiSettings,
        },
        {
            provide: OverlayContainer,
            useFactory: (comp: EditorUILoaderComponent) => {
                return comp.overlayContainer;
            },
            deps: [EditorUILoaderComponent],
        },
        { provide: Overlay, useClass: Overlay },
    ],
})
export class WordToolsModule {}

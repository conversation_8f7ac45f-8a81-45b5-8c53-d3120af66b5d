import {
    CommonToolState,
    EraserTool,
    EraserToolState,
    FreedrawingToolBar,
    LineV2Tool,
    LineV2ToolState,
    OvalTool,
    PenTool,
    RectTool,
} from '@viclass/editor.freedrawing';

export interface CommunicationEvent<T> {
    source: any;
    eventType: T;
    eventData: any;
}

// export type WhiteboardControllerEvent = 'init-editor-coordinator'
export type FreeDrawingEditorControllerEvent = 'switch-tool';

export class FreedrawingTools {
    fdToolbar: FreedrawingToolBar;

    rectTool: RectTool;
    lineV2Tool: LineV2Tool;
    ovalTool: OvalTool;
    penTool: PenTool;
    eraserTool: EraserTool;

    commonToolState: CommonToolState;
    eraserToolState: EraserToolState;
    lineV2ToolState: LineV2ToolState;

    constructor(toolBar: FreedrawingToolBar) {
        this.fdToolbar = toolBar;

        this.rectTool = toolBar.getTool('RectangleTool') as RectTool;
        this.lineV2Tool = toolBar.getTool('LineV2Tool') as LineV2Tool;
        this.ovalTool = toolBar.getTool('OvalTool') as OvalTool;
        this.penTool = toolBar.getTool('PencilTool') as PenTool;
        this.eraserTool = toolBar.getTool('EraserTool') as EraserTool;

        this.commonToolState = toolBar.toolState('CommonPropertiesTool');
        this.eraserToolState = toolBar.toolState('EraserPropertiesTool');
        this.lineV2ToolState = toolBar.toolState('LineV2Tool');
    }
}

export type ButtonData<T> = {
    name: T;
    iconClasses: string;
    onClick?: (btnName: T) => void;
    label?: string;
    children?: ButtonData<T>[];
};

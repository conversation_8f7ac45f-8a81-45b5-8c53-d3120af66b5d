import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { ButtonData } from './freedrawingtools.models';
import { FreedrawingToolType } from '@viclass/editor.freedrawing';

@Injectable({
    providedIn: 'root',
})
export class FredToolbarBtnStateService {
    /**
     * Keep track of the path that the mouse is hovering over
     */
    public readonly focusPath$ = new BehaviorSubject<string[]>([]);
    private _pinnedPath: string = '';
    private leaveTimeoutHandle?: number;

    constructor() {}

    enterPath(path: string) {
        this.clearLastLeaveTimeout();
        // new paths should contains only the parent paths and the newly entered path
        const newPaths = this.focusPath$.value.filter(old => path.startsWith(old) && old !== path);
        newPaths.push(path);
        this.focusPath$.next(newPaths);
    }

    pinPath(path: string) {
        this._pinnedPath = path;
    }

    leavePath(path: string, event?: PointerEvent) {
        this.clearLastLeaveTimeout();

        this.leaveTimeoutHandle = window.setTimeout(
            () => {
                this.focusPath$.next(this.focusPath$.value.filter(p => !p.startsWith(path)));
                this.leaveTimeoutHandle = undefined;
            },
            this.pointerLeaveTimeoutMs(path, event)
        );
    }

    clearPath() {
        this.focusPath$.next([]);
        this._pinnedPath = '';
    }

    isFocusPath(path: string): Observable<boolean> {
        return this.focusPath$.pipe(map(currentPaths => currentPaths.some(p => p.startsWith(path))));
    }

    private pointerLeaveTimeoutMs(path: string, event?: PointerEvent): number {
        return this._pinnedPath && path.startsWith(this._pinnedPath)
            ? 15_000
            : event?.pointerType === 'touch'
              ? 6000
              : 3000;
    }

    private clearLastLeaveTimeout() {
        if (this.leaveTimeoutHandle) window.clearTimeout(this.leaveTimeoutHandle);
    }

    /*
     * Add a global click listener to clear the focus path when clicking outside the toolbar
     */
    addGlobalClickHandler(): void {
        // Do this in capture phase to ensure it runs before any other click handlers
        document.addEventListener('click', this.onDocumentClick, true);
    }

    /*
     * Remove the global click listener
     */
    removeGlobalClickHandler(): void {
        document.removeEventListener('click', this.onDocumentClick, true);
    }

    /**
     * Handle document click to clear the focus path if clicked outside the toolbar
     */
    private onDocumentClick = (event: MouseEvent): void => {
        if (this.focusPath$.value.length === 0) return;

        const target = event.target as HTMLElement;
        if (!target?.matches('.fred-toolbar *')) this.clearPath();
    };

    getShapeToolsInitialData(): ButtonData<FreedrawingToolType>[] {
        return [
            {
                name: 'PencilTool',
                iconClasses: 'vcon vcon-freedrawing vcon_pen',
                label: 'Bút (Ctrl+Q)',
            },
            {
                name: 'LineV2Tool',
                iconClasses: 'vcon vcon-freedrawing vcon_line',
                children: [
                    {
                        name: 'LineV2Tool',
                        iconClasses: 'vcon vcon-freedrawing vcon_line',
                        label: 'Đường thẳng (Ctrl+R)',
                    },
                    {
                        name: 'RectangleTool',
                        iconClasses: 'vcon vcon-freedrawing vcon_shape_square',
                        label: 'Hình vuông/Hình chữ nhật (Ctrl+G)',
                    },
                    {
                        name: 'OvalTool',
                        iconClasses: 'vcon vcon-freedrawing vcon_shape_circle',
                        label: 'Hình tròn/Hình oval (Ctrl+Y)',
                    },
                    {
                        name: 'TriangleTool',
                        iconClasses: 'vcon vcon-freedrawing vcon_shape_triangle',
                        label: 'Hình tam giác (Ctrl+U)',
                    },
                    {
                        name: 'HexagonTool',
                        iconClasses: 'vcon vcon-freedrawing vcon_shape_polygon',
                        label: 'Hình lục giác (Ctrl+I)',
                    },
                ],
            },
            {
                name: 'EraserTool',
                iconClasses: 'vcon vcon-freedrawing vcon_eraser',
                label: 'Gôm (Ctrl+E)',
            },
        ];
    }
}

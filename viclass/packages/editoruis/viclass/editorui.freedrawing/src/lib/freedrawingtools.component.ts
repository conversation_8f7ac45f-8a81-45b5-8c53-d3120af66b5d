import { ConnectionPositionPair, HorizontalConnectionPos, VerticalConnectionPos } from '@angular/cdk/overlay';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { DefaultToolBar, ToolEventListener } from '@viclass/editor.core';
import { FreedrawingToolBar, FreedrawingToolEventData, FreedrawingToolType } from '@viclass/editor.freedrawing';
import { EditorUIComponent, EditorUILoaderComponent } from '@viclass/editorui.loader';
import { BehaviorSubject, Observable, ReplaySubject, Subscription } from 'rxjs';
import { FredToolbarBtnStateService } from './fredtoolbar-btn-state.service';
import {
    ButtonData,
    CommunicationEvent,
    FreeDrawingEditorControllerEvent,
    FreedrawingTools,
} from './freedrawingtools.models';

/**
 *
 * <AUTHOR>
 */
@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'tb-freedrawingtools',
    templateUrl: './freedrawingtools.component.html',
    styleUrls: ['../../assets/freed.style.sass'],
})
export class FreedrawingtoolsComponent implements AfterViewInit, OnInit, EditorUIComponent {
    show = false;
    private readonly freTools$: BehaviorSubject<FreedrawingTools> = new BehaviorSubject(null);
    private readonly subscription: Subscription;

    fontSizeName: string = 'Regular';

    shapeSubMenuOpened: boolean = false;
    fillColorSubMenuOpened: boolean = false;
    strokeColorSubMenuOpened: boolean = false;
    lineWidthSubMenuOpened: boolean = false;

    focusFillColor$: Observable<boolean>;
    focusBorderColor$: Observable<boolean>;
    focusLineWidth$: Observable<boolean>;

    private vAlign: 'top' | 'center' | 'bottom';
    private hAlign: 'left' | 'center' | 'right';
    private direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    private uiLoader: EditorUILoaderComponent;

    private notifier: ReplaySubject<CommunicationEvent<FreeDrawingEditorControllerEvent>>;

    fredToolsBtnData: ButtonData<FreedrawingToolType>[];

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        public btnStateSv: FredToolbarBtnStateService
    ) {
        this.notifier = new ReplaySubject<CommunicationEvent<FreeDrawingEditorControllerEvent>>();
        this.subscription = this.notifier.subscribe(event => this.handleEvents(event));

        this.fredToolsBtnData = btnStateSv.getShapeToolsInitialData();

        this.isToolEnable = this.isToolEnable.bind(this);
        this.isToolActive = this.isToolActive.bind(this);

        this.focusFillColor$ = this.btnStateSv.isFocusPath('FillColor');
        this.focusBorderColor$ = this.btnStateSv.isFocusPath('BorderColor');
        this.focusLineWidth$ = this.btnStateSv.isFocusPath('LineWidth');
    }

    ngOnInit() {
        this.freTools$.subscribe(tools => {
            if (!tools) return;
            tools.fdToolbar.registerToolListener(FreedrawingtoolsComponent.FreedrawingToolListener(this));
        });
        this.btnStateSv.addGlobalClickHandler();
    }

    ngAfterViewInit(): void {}

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
        this.notifier?.unsubscribe();
        this.btnStateSv.removeGlobalClickHandler();
    }

    private get freedrawingTools(): FreedrawingTools {
        return this.freTools$.getValue();
    }

    get toolBar(): FreedrawingToolBar {
        return this.freedrawingTools?.fdToolbar;
    }

    get isHorizontal(): boolean {
        return this.direction == 'ltr' || this.direction == 'rtl';
    }

    get isVertical(): boolean {
        return this.direction == 'ttb' || this.direction == 'btt';
    }

    get subMenuPositions(): ConnectionPositionPair[] {
        const horizontalConnectionPos: HorizontalConnectionPos = 'center';
        const verticalConnectionPos: VerticalConnectionPos = 'center';

        let offsetX: number = 0;
        let offsetY: number = 0;

        if (this.isHorizontal) {
            if (this.vAlign == 'top') {
                offsetY = 50;
            } else {
                offsetY = -50;
            }
        } else if (this.isVertical) {
            if (this.hAlign == 'right') {
                offsetX = -50;
            } else {
                offsetX = 50;
            }
        }

        return [
            new ConnectionPositionPair(
                {
                    originX: horizontalConnectionPos,
                    originY: verticalConnectionPos,
                },
                {
                    overlayX: horizontalConnectionPos,
                    overlayY: verticalConnectionPos,
                },
                offsetX,
                offsetY
            ),
        ];
    }

    get colorList(): string[] {
        return ['#FFFFFF', '#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];
    }

    get backgroundColor(): string {
        return this.getEditor()?.commonToolState?.fill;
    }

    get borderColor(): string {
        return this.getEditor()?.commonToolState?.stroke;
    }

    get lineWidth() {
        if (this.getEditor()?.fdToolbar.isToolActive('EraserTool')) {
            return this.getEditor().eraserToolState.size;
        }

        return this.getEditor()?.commonToolState.lineWidth;
    }

    get isSetBackgroundColor() {
        return this.getEditor().commonToolState.fill != null;
    }

    get isSetBorderColor() {
        return this.getEditor().commonToolState.stroke != null;
    }

    get hasStartArrow() {
        return this.getEditor().lineV2ToolState.startArrow;
    }

    get hasEndArrow() {
        return this.getEditor().lineV2ToolState.endArrow;
    }

    private getEditor(): FreedrawingTools {
        return this.freTools$.getValue();
    }

    isToolActive(toolType: FreedrawingToolType): boolean {
        return this.toolBar?.isToolActive(toolType);
    }

    isToolEnable(toolType: FreedrawingToolType): boolean {
        return !this.toolBar.isToolDisable(toolType);
    }

    switchTool(toolType: FreedrawingToolType) {
        if (['PencilTool', 'TextTool', 'EraserTool'].includes(toolType)) {
            this.shapeSubMenuOpened = false;
        }
        this.notifier.next({
            source: this,
            eventType: 'switch-tool',
            eventData: toolType,
        });
    }

    onShapeToolBtnClicked(btnData: ButtonData<FreedrawingToolType>) {
        this.switchTool(btnData.name);
    }

    closeShapeSubMenu() {
        this.shapeSubMenuOpened = false;
    }

    onClickShapeMenu() {
        this.lineWidthSubMenuOpened = false;
        this.strokeColorSubMenuOpened = false;
        this.fillColorSubMenuOpened = false;
        this.shapeSubMenuOpened = !this.shapeSubMenuOpened;
    }

    onClickFillColorMenu() {
        this.lineWidthSubMenuOpened = false;
        this.strokeColorSubMenuOpened = false;
        this.fillColorSubMenuOpened = !this.fillColorSubMenuOpened;
    }

    onClickStrokeColorMenu() {
        this.lineWidthSubMenuOpened = false;
        this.fillColorSubMenuOpened = false;
        this.strokeColorSubMenuOpened = !this.strokeColorSubMenuOpened;
    }

    onClickLineWidthMenu() {
        this.strokeColorSubMenuOpened = false;
        this.fillColorSubMenuOpened = false;
        this.lineWidthSubMenuOpened = !this.lineWidthSubMenuOpened;
    }

    onSelectBackgroundColor(c: string) {
        this.getEditor().commonToolState.fill = c;
        this.getEditor().fdToolbar.update('CommonPropertiesTool', this.getEditor().commonToolState);
        this.fillColorSubMenuOpened = false;
    }

    onSelectBorderColor(c: string) {
        this.getEditor().commonToolState.stroke = c;
        this.getEditor().fdToolbar.update('CommonPropertiesTool', this.getEditor().commonToolState);
        this.strokeColorSubMenuOpened = false;
    }

    onSelectLineArrow(isStart: boolean) {
        const state = this.getEditor().lineV2ToolState;
        if (isStart) {
            state.startArrow = !state.startArrow;
        } else {
            state.endArrow = !state.endArrow;
        }
        this.getEditor().fdToolbar.update('LineV2Tool', state);
    }

    onLineWidthChange(value: number) {
        if (this.getEditor().fdToolbar.isToolActive('EraserTool')) {
            this.getEditor().eraserToolState.size = value;
            this.getEditor().fdToolbar.update('EraserPropertiesTool', this.getEditor().eraserToolState);
        } else {
            this.getEditor().commonToolState.lineWidth = value;
            this.getEditor().fdToolbar.update('CommonPropertiesTool', this.getEditor().commonToolState);
        }
        this.lineWidthSubMenuOpened = false;
        this.changeDetectorRef.markForCheck();
    }

    onEraserSizeChange(value: number) {
        this.getEditor().eraserToolState.size = value;
        this.getEditor().fdToolbar.update('EraserPropertiesTool', this.getEditor().eraserToolState);
    }

    get isAllowCreateShape(): boolean {
        return (
            !this.toolBar.isDisabled() ||
            !this.toolBar.isToolDisable('LineV2Tool') ||
            !this.toolBar.isToolDisable('RectangleTool') ||
            !this.toolBar.isToolDisable('OvalTool') ||
            !this.toolBar.isToolDisable('TriangleTool') ||
            !this.toolBar.isToolDisable('HexagonTool')
        );
    }

    get isAllowChangeBackgroundColor() {
        switch (this.getEditor()?.fdToolbar.curTool) {
            case 'LineV2Tool':
            case 'PencilTool':
            case 'EraserTool':
                return false;
            default:
                return true;
        }
    }

    get isAllowChangeBorderColor() {
        switch (this.getEditor()?.fdToolbar.curTool as FreedrawingToolType) {
            case 'EraserTool':
                return false;
            default:
                return true;
        }
    }

    get isAllowChangeLineWidth() {
        return !this.toolBar.isDisabled();
    }

    get isAllowChangeLineArrows() {
        return !this.toolBar.isDisabled();
    }

    connectToolbar<T extends DefaultToolBar<any, any>>(toolbar: T) {
        if (!toolbar) return;
        this.freTools$.next(new FreedrawingTools(toolbar as unknown as FreedrawingToolBar));
    }

    disableUI() {}

    hideUI() {
        this.toolBar?.clearAllFocus();
        this.show = false;
        this.changeDetectorRef.markForCheck();
    }

    showUI() {
        this.show = true;
        this.changeDetectorRef.markForCheck();
    }

    isShowing(): boolean {
        return this.show;
    }

    loadedBy(uiLoader: EditorUILoaderComponent) {
        this.uiLoader = uiLoader;

        this.vAlign = uiLoader.vAlign;
        this.hAlign = uiLoader.hAlign;
        this.direction = uiLoader.direction;
    }

    onMouseEnter(btnType: string) {
        this.btnStateSv.enterPath(btnType);
    }

    onMouseLeave(btnType: string, event: PointerEvent) {
        this.btnStateSv.leavePath(btnType, event);
    }

    private handleEvents(event: CommunicationEvent<FreeDrawingEditorControllerEvent>) {
        if (!event) return;

        switch (event.eventType) {
            case 'switch-tool': {
                const toolType = event.eventData as FreedrawingToolType;

                if (!this.freTools$.getValue().fdToolbar.isToolActive(toolType))
                    this.freTools$.getValue().fdToolbar.focus(toolType);
                else this.freTools$.getValue().fdToolbar.blur(toolType);

                break;
            }
            default:
                break;
        }
    }

    private static FreedrawingToolListener(
        _p: FreedrawingtoolsComponent
    ): ToolEventListener<FreedrawingToolBar, FreedrawingToolType> {
        return new (class implements ToolEventListener<FreedrawingToolBar, FreedrawingToolType> {
            onEvent(eventData: FreedrawingToolEventData): FreedrawingToolEventData {
                _p.changeDetectorRef.markForCheck();
                return eventData;
            }
        })();
    }
}

<ng-template [ngIf]="isShowing() && toolBar">
    <div class="v-toolbar fred-toolbar">
        <div class="v-tool-group">
            <tb-toolbar-button
                *ngFor="let btnData of fredToolsBtnData"
                [data]="btnData"
                [isToolActive]="isToolActive"
                [allowCheck]="isToolEnable"
                [subMenuPositions]="subMenuPositions"
                (btnClicked)="onShapeToolBtnClicked($event)">
            </tb-toolbar-button>

            <div class="v-tool-separation"></div>

            <button
                #backgroundColorTooltip
                class="v-tool-btn"
                cdkOverlayOrigin
                #fillColorSubMenuTrigger
                (click)="onClickFillColorMenu()"
                [ngClass]="{ active: focusFillColor$ | async }"
                [disabled]="!isAllowChangeBackgroundColor"
                (pointerenter)="onMouseEnter('FillColor')"
                (pointerleave)="onMouseLeave('FillColor', $event)">
                <span
                    class="vcon vcon-freedrawing"
                    [ngClass]="{
                        'vcon_color-list_no-color': !backgroundColor,
                        'vcon_color-list': backgroundColor && true,
                    }">
                    <span class="path1"></span>
                    <span class="path2" [style]="'--color--:' + backgroundColor"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                    <span class="path5"></span>
                </span>
            </button>

            <button
                #borderColorTooltip
                class="v-tool-btn"
                cdkOverlayOrigin
                #strokeColorSubMenuTrigger
                (click)="onClickStrokeColorMenu()"
                [ngClass]="{ active: focusBorderColor$ | async }"
                [disabled]="!isAllowChangeBorderColor"
                (pointerenter)="onMouseEnter('BorderColor')"
                (pointerleave)="onMouseLeave('BorderColor', $event)">
                <span
                    class="vcon vcon-freedrawing"
                    [ngClass]="{
                        'vcon_brush-outline_no-color': !borderColor,
                        'vcon_brush-outline': borderColor && true,
                    }">
                    <span class="path1" [style]="'--color--:' + (borderColor || 'black')"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                </span>
            </button>
            <button
                #sizeTooltip
                class="v-tool-btn"
                cdkOverlayOrigin
                #lineWidthSubMenuTrigger
                (click)="onClickLineWidthMenu()"
                [ngClass]="{ active: focusLineWidth$ | async }"
                [disabled]="!isAllowChangeLineWidth"
                (pointerenter)="onMouseEnter('LineWidth')"
                (pointerleave)="onMouseLeave('LineWidth', $event)">
                <span class="vcon vcon-freedrawing vcon_line-weight"></span>
            </button>
            <ng-template [ngIf]="isToolActive('LineV2Tool')">
                <button
                    class="v-tool-btn"
                    (click)="onSelectLineArrow(true)"
                    [disabled]="!isAllowChangeLineArrows"
                    [ngClass]="{ active: hasStartArrow }">
                    <span class="vcon vcon-freedrawing vcon_arrow" style="transform: rotate(-135deg)"></span>
                </button>
                <button
                    class="v-tool-btn"
                    (click)="onSelectLineArrow(false)"
                    [disabled]="!isAllowChangeLineArrows"
                    [ngClass]="{ active: hasEndArrow }">
                    <span class="vcon vcon-freedrawing vcon_arrow" style="transform: rotate(45deg)"></span>
                </button>
            </ng-template>
        </div>

        <!--  =========== line width sub menu tool ===========  -->
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="lineWidthSubMenuTrigger"
            [cdkConnectedOverlayOpen]="isAllowChangeLineWidth && (focusLineWidth$ | async)"
            [cdkConnectedOverlayPositions]="subMenuPositions">
            <div
                class="v-toolbar fred-toolbar"
                (pointerenter)="onMouseEnter('LineWidth')"
                (pointerleave)="onMouseLeave('LineWidth', $event)">
                <div class="v-tool-group line-width-adjust">
                    <!-- Use the reusable width-slider component -->
                    <tb-width-slider
                        [value]="lineWidth"
                        [minValue]="1"
                        [maxValue]="50"
                        [vertical]="isVertical"
                        [height]="isVertical ? 150 : undefined"
                        (valueChange)="onLineWidthChange($event)">
                    </tb-width-slider>
                </div>
            </div>
        </ng-template>

        <!--  ========= stroke color sub menu toll ==========  -->
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="strokeColorSubMenuTrigger"
            [cdkConnectedOverlayOpen]="isAllowChangeBorderColor && (focusBorderColor$ | async)"
            [cdkConnectedOverlayPositions]="subMenuPositions">
            <div
                class="v-toolbar fred-toolbar"
                (pointerenter)="onMouseEnter('BorderColor')"
                (pointerleave)="onMouseLeave('BorderColor', $event)">
                <div class="v-tool-group">
                    <button class="v-tool-btn" [ngClass]="{ active: !borderColor }" (click)="onSelectBorderColor(null)">
                        <span class="vcon vcon-freedrawing vcon_color-list_no-color">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </span>
                    </button>
                    <button
                        *ngFor="let color of colorList"
                        class="v-tool-btn"
                        [ngClass]="{ active: color === borderColor }"
                        (click)="onSelectBorderColor(color)">
                        <span class="vcon vcon-freedrawing vcon_color-list">
                            <span class="path1"></span>
                            <span class="path2" [style]="'--color--:' + color"></span>
                        </span>
                    </button>
                </div>
            </div>
        </ng-template>

        <!--  ========= fill color sub menu toll ==========  -->
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="fillColorSubMenuTrigger"
            [cdkConnectedOverlayOpen]="isAllowChangeBackgroundColor && (focusFillColor$ | async)"
            [cdkConnectedOverlayPositions]="subMenuPositions">
            <div
                class="v-toolbar fred-toolbar"
                (pointerenter)="onMouseEnter('FillColor')"
                (pointerleave)="onMouseLeave('FillColor', $event)">
                <div class="v-tool-group">
                    <button
                        class="v-tool-btn"
                        [ngClass]="{ active: !backgroundColor }"
                        (click)="onSelectBackgroundColor(null)">
                        <span class="vcon vcon-freedrawing vcon_color-list_no-color">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </span>
                    </button>
                    <button
                        *ngFor="let color of colorList"
                        class="v-tool-btn"
                        [ngClass]="{ active: color === backgroundColor }"
                        (click)="onSelectBackgroundColor(color)">
                        <span class="vcon vcon-freedrawing vcon_color-list">
                            <span class="path1"></span>
                            <span class="path2" [style]="'--color--:' + color"></span>
                        </span>
                    </button>
                </div>
            </div>
        </ng-template>
    </div>
</ng-template>

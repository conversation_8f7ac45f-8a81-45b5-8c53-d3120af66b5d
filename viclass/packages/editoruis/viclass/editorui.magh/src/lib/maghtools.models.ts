import {
    Domain,
    EquationStyle,
    MathGraphSettingsState,
    MathGraphToolBar,
    MathGraphToolType,
} from '@viclass/editor.magh';
import { LatexSyntaxError } from 'lib-mathlive';
import { BehaviorSubject } from 'rxjs';

// Token for the UI settings
export const MATHGRAPH_UI_SETTINGS = 'MATHGRAPH_UI_SETTINGS';

export type MathGraphUiSettings = {
    mathFieldAssetsRoot: string;
};

export interface CommunicationEvent<T> {
    source: any;
    eventType: T;
    eventData: any;
}

export type MathGraphEditorControllerEvent = 'switch-tool';

export class MathGraphTools {
    toolbar: MathGraphToolBar;

    constructor(toolBar: MathGraphToolBar) {
        this.toolbar = toolBar;
    }

    activeTool(): MathGraphToolType {
        return this.toolbar.activeTool?.toolType;
    }
}

export type ButtonData<T> = {
    name: T;
    iconClasses: string;
    param?: string;
    onClick?: (btnName: T) => void;
    label?: string;
    children?: ButtonData<T>[];
};

export type UpdateExpressionEvent = {
    index: number;
    expression: string;
    finished?: boolean;
};

export type UpdateVisibilityEvent = {
    index: number;
    hidden: boolean;
};

export type UpdateInternalScopeEvent = {
    index: number;
    min: number;
    max: number;
};

export type UpdateGlobalScopeEvent = {
    index: number;
    min: number;
    max: number;
    step: number;
};

export type DeleteEquationEvent = {
    index: number;
};

export type EquationStylesChangeEvent = {
    index: number;
    style: EquationStyle;
};

export type UpdateDomainEvent = {
    index: number;
    domain: Domain;
};

export type RenderPropsCtx = MathGraphSettingsState & {
    scale: number | null;
};

/**
 * Converts a LaTeX syntax error into a human-readable error message in Vietnamese
 *
 * @param error - The LaTeX syntax error object containing error code and optional argument
 * @returns A localized error message string describing the syntax error
 */
export function getSyntaxErrorMessage(error: LatexSyntaxError): string {
    const argPart = error.arg ? ` (tại: "${error.arg}")` : '';
    switch (error.code) {
        case 'unknown-command':
            return `Lệnh không xác định${argPart}`;
        case 'unknown-environment':
            return `Môi trường không xác định${argPart}`;
        case 'invalid-command':
            return `Lệnh không hợp lệ trong ngữ cảnh hiện tại${argPart}`;
        case 'unbalanced-braces':
            return `Thiếu hoặc dư dấu ngoặc nhọn${argPart}`;
        case 'unbalanced-environment':
            return `Môi trường không được đóng đúng${argPart}`;
        case 'unbalanced-mode-shift':
            return `Dấu chuyển chế độ ($, \\[, ...) không cân đối${argPart}`;
        case 'missing-argument':
            return `Thiếu tham số${argPart}`;
        case 'too-many-infix-commands':
            return `Quá nhiều lệnh infix trong một nhóm${argPart}`;
        case 'unexpected-command-in-string':
            return `Lệnh không hợp lệ trong chuỗi${argPart}`;
        case 'missing-unit':
            return `Thiếu đơn vị cho kích thước${argPart}`;
        case 'unexpected-delimiter':
            return `Dấu phân cách không hợp lệ${argPart}`;
        case 'unexpected-token':
            return `Ký tự không mong đợi${argPart}`;
        case 'unexpected-end-of-string':
            return `Kết thúc biểu thức đột ngột${argPart}`;
        case 'improper-alphabetic-constant':
            return `Hằng số ký tự không đúng định dạng${argPart}`;
        default:
            return `Lỗi cú pháp${argPart}`;
    }
}

/**
 * Detects non-standard input methods (e.g., Vietnamese input methods like UniKey)
 * by listening to the 'keydown' event and checking if the input character falls
 * outside the standard ASCII range.
 *
 * @param targetEl - The HTML element to attach the keydown listener to.
 * @returns An object containing:
 *   - isInvalidInput$: BehaviorSubject<boolean> indicating the detection status.
 *   - cleanup: A function to remove the event listener and clear resources.
 */
export function detectInvalidInputMethod(targetEl: HTMLElement) {
    const isInvalidInput$ = new BehaviorSubject<boolean>(false);
    let timeout: ReturnType<typeof setTimeout> | null = null;

    // Resets the detection status after a specified timeout.
    const reset = () => {
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(() => isInvalidInput$.next(false), 3000); // Reset after 3 seconds of inactivity
    };

    const keydownHandler = (e: KeyboardEvent) => {
        // Check if the key is a single character and its Unicode value exceeds 127 (non-ASCII)
        if (e.key.length === 1 && e.key.charCodeAt(0) > 127) {
            if (!isInvalidInput$.value) isInvalidInput$.next(true);
            reset();
        }
    };

    targetEl.addEventListener('keydown', keydownHandler);

    return {
        isInvalidInput$,
        cleanup: () => {
            targetEl.removeEventListener('keydown', keydownHandler);
            if (timeout) clearTimeout(timeout);
            isInvalidInput$.complete();
        },
    };
}

<div
    class="plot-tool w-[350px] align-middle bg-[BW7] rounded-[15px]"
    (keyup.esc)="onClose.emit()"
    (keydown)="$event.stopPropagation()">
    <div class="plot-tool__header flex gap-[10px] mb-2">
        <div class="flex-1 flex gap-4">
            <button class="v-tool-btn" title="Thêm biểu thức" (click)="addPlotEquation()">
                <span class="vcon vcon-common vcon_page-bar_ad"></span>
            </button>
            <button class="v-tool-btn relative" title="Thêm điểm" (click)="addPointEquation()">
                <span class="vcon vcon-common vcon_page-bar_ad"></span>
                <span class="absolute w-[6px] h-[6px] bg-BW1 rounded-full right-1 bottom-1"></span>
            </button>
            <button class="v-tool-btn relative" title="Thêm biến" (click)="addVarEquation()">
                <span class="vcon vcon-common vcon_page-bar_ad"></span>
                <span class="absolute text-[8px] right-0 bottom-1">var</span>
            </button>
        </div>
        <button class="v-tool-btn" title="Đóng" (click)="onClose.emit()">
            <span class="vcon vcon-general vcon_delete"></span>
        </button>
    </div>
    <div *ngIf="equations.length === 0">
        <div class="flex justify-center items-center">
            <span class="italic text-BW3 text-[12px]">Thêm biểu thức để bắt đầu</span>
        </div>
    </div>

    <div class="max-h-[60vh] overflow-x-hidden overflow-y-auto">
        <div *ngFor="let equation of equations; let idx = index">
            <!-- 0: Plot, 1: ScopeVar, 2: Point -->
            <lib-plot-equation
                *ngIf="equation.equationType === 0"
                class="block mb-[15px] plot-equation"
                [showSettings]="(settingIdx | async) === idx"
                [equation]="equation"
                [colorPalette]="equationsState.colors"
                [tool]="tool"
                (onToggleSettings)="handleToggleSettings($event)"
                (onDelete)="handleDeleteEquation($event)"></lib-plot-equation>
            <lib-var-equation
                *ngIf="equation.equationType === 1"
                class="block mb-[15px] plot-equation"
                [showSettings]="(settingIdx | async) === idx"
                [equation]="equation"
                [colorPalette]="equationsState.colors"
                [tool]="tool"
                (onToggleSettings)="handleToggleSettings($event)"
                (onDelete)="handleDeleteEquation($event)"></lib-var-equation>
            <lib-point-equation
                *ngIf="equation.equationType === 2"
                class="block mb-[15px] plot-equation"
                [showSettings]="(settingIdx | async) === idx"
                [equation]="equation"
                [colorPalette]="equationsState.colors"
                [tool]="tool"
                (onToggleSettings)="handleToggleSettings($event)"
                (onDelete)="handleDeleteEquation($event)"></lib-point-equation>
        </div>
    </div>
</div>

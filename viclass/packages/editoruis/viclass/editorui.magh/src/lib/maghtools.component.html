<div class="v-toolbar magh-toolbar with-sub" *ngIf="isShowing()">
    <div class="v-tool-group">
        <button
            class="v-tool-btn"
            placement="bottom"
            *ngIf="hasTool('CreateMathGraphDocumentTool')"
            [ngClass]="{ active: isToolActive('CreateMathGraphDocumentTool') }"
            (click)="switchTool('CreateMathGraphDocumentTool')"
            [disabled]="!isToolEnable('CreateMathGraphDocumentTool')">
            <span class="vcon vcon-magh vcon_page-bar_ad"></span>
        </button>
        <div *ngIf="hasTool('CreateMathGraphDocumentTool')" class="v-tool-separation"></div>

        <button
            #plotBtnEl
            class="v-tool-btn"
            [ngClass]="{ active: isToolActive('UpdateEquationsTool') }"
            placement="bottom"
            [disabled]="!isToolEnable('UpdateEquationsTool')"
            (click)="switchTool('UpdateEquationsTool')">
            <span class="vcon vcon-magh vcon_fx-list"></span>
        </button>
        <tooltip-magh [toolTipFor]="plotBtnEl" [tooltipContent]="'Nhập công thức'"></tooltip-magh>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayDisableClose]="true"
            [cdkConnectedOverlayOrigin]="plotBtnEl"
            [cdkConnectedOverlayOpen]="isToolActive('UpdateEquationsTool')"
            [cdkConnectedOverlayPositions]="equationsSubMenuPositions">
            <div class="v-toolbar">
                <div class="v-tool-group shadow-SH1 !w-auto bg-BW7 overflow-hidden">
                    <div class="setting-tool-popup shadow-SH1 p-3">
                        <app-plot-dialog
                            [equationsState]="equationsContext$ | async"
                            [tool]="updateTool"
                            (onClose)="switchTool('UpdateEquationsTool')"></app-plot-dialog>
                    </div>
                </div>
            </div>
        </ng-template>
        <button
            #settingBtnEl
            class="v-tool-btn"
            placement="bottom"
            [ngClass]="{
                active: isToolActive('MaghSettingsTool'),
            }"
            [disabled]="!isToolEnable('MaghSettingsTool')"
            (click)="switchTool('MaghSettingsTool')">
            <span class="vcon-general vcon_sidebar-setting"></span>
        </button>
        <tooltip-magh [toolTipFor]="settingBtnEl" [tooltipContent]="'Cài đặt'"></tooltip-magh>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayDisableClose]="true"
            [cdkConnectedOverlayOrigin]="settingBtnEl"
            [cdkConnectedOverlayOpen]="isToolActive('MaghSettingsTool')"
            [cdkConnectedOverlayPositions]="settingsSubMenuPositions">
            <div class="v-toolbar">
                <div class="v-tool-group shadow-SH1 !w-auto bg-BW7">
                    <div class="setting-tool-popup shadow-SH1 p-3">
                        <setting-tool
                            [settings]="renderPropsCtx$ | async"
                            (onClose)="switchTool('MaghSettingsTool')"
                            (onChange)="onSettingFieldChange($event)"></setting-tool>
                    </div>
                </div>
            </div>
        </ng-template>
    </div>
    <span class="v-toolbar-gutter ng-star-inserted"></span>
</div>

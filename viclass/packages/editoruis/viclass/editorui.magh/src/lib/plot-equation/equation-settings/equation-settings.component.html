<div class="flex gap-3 flex-col py-3">
    <span class="text-BW1"><PERSON><PERSON><PERSON> thị đườ<PERSON></span>
    <div class="flex gap-6">
        <div *ngFor="let lineStyle of lineStyles" class="flex gap-2 items-center">
            <input
                type="radio"
                name="lineStyle"
                [ngModel]="equationStyle.lineStyle"
                [value]="lineStyle"
                (ngModelChange)="onLineChange($event)" />
            <div class="flex items-center py-2 cursor-pointer" (click)="onLineChange(lineStyle)">
                <div [class]="'w-[24px] h-[3px] !border-t-[2px] !border-black equation-line-style_' + lineStyle"></div>
            </div>
        </div>
    </div>
    <lib-setting-tool-adjust-number
        label="Độ dày"
        [value]="{ value: equationStyle.lineWidth }"
        field="lineWidth"
        [min]="1"
        [max]="10"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-adjust-number>
    <lib-setting-tool-colors
        label="Màu"
        [value]="{ value: equationStyle.color }"
        field="color"
        [colorList]="colorPalette"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-colors>
    <lib-setting-tool-switch
        label="Đánh dấu miền xác định"
        field="showDomainMarking"
        [value]="{ value: equationStyle.showDomainMarking }"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-switch>
    <lib-setting-tool-switch
        label="Hiển thị nhãn"
        field="showLabel"
        [value]="{ value: equationStyle.showLabel }"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-switch>
    <div *ngIf="equationStyle.showLabel" class="flex flex-col gap-2">
        <span class="text-BW1">Nhãn tùy chỉnh</span>
        <input
            type="text"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Để trống để hiển thị công thức LaTeX"
            [ngModel]="equationStyle.customLabel"
            (blur)="onCustomLabelBlur($event)"
            (input)="onCustomLabelInput($event)" />
    </div>
    <!-- <lib-setting-tool-adjust-number
        label="Hiển thị nền"
        [value]="{ value: equationStyle.fillOpacity ?? 100 }"
        [min]="0"
        [max]="100"
        field="fillOpacity"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-adjust-number> -->
</div>

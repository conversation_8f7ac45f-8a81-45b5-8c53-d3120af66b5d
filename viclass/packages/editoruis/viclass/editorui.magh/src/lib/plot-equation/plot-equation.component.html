<div class="flex gap-[5px]">
    <div class="state-btns flex flex-col justify-start">
        <button class="v-tool-btn" (click)="toggleVisibility()">
            <span
                class="rounded-full w-[14px] h-[14px] border-black border-[1px]"
                [ngStyle]="{ 'background-color': iconColor }"></span>
        </button>
        <button class="v-tool-btn" [ngClass]="{ active: showSettings }" (click)="toggleSettings()">
            <span class="vcon vcon-general vcon_sidebar-setting"></span>
        </button>
    </div>
    <div
        class="flex-1 border-[1px] rounded-[12px] overflow-hidden"
        [ngClass]="{
            'bg-orange-100': (invalidInputMethod$ | async),
            '!border-red-500':
                (syntaxError$ | async) || (!equation.isValid && equation.expression !== equation.defaultValue),
        }">
        <div class="flex justify-between items-start p-[5px] min-h-[35px]" (keyup)="$event.stopPropagation()">
            <span class="text-xs pt-2">Hàm:</span>
            <div #mathRoot class="block flex-1 overflow-auto"></div>
            <div class="action-btns">
                <button class="v-tool-btn" (click)="deleteEquation()">
                    <span class="vcon vcon-general vcon_delete"></span>
                </button>
            </div>
        </div>
        <div *ngIf="isParametric" class="flex justify-start py-1 px-3">
            <input
                type="number"
                class="max-w-[40px] text-center"
                [max]="equation.internalScope.max"
                [ngModel]="equation.internalScope.min"
                (ngModelChange)="changeInternalScopeRange('min', $event)" />
            <span><span class="px-2">&le;</span>t<span class="px-2">&le;</span></span>
            <input
                type="number"
                class="max-w-[40px] text-center"
                [min]="equation.internalScope.min"
                [ngModel]="equation.internalScope.max"
                (ngModelChange)="changeInternalScopeRange('max', $event)" />
        </div>
        <div *ngIf="!isParametric" class="flex justify-between p-1 items-center gap-2">
            <span class="text-xs">Miền xác định:</span>
            <input
                type="number"
                class="w-[60px] text-center"
                [max]="domainMaxValue"
                [ngModel]="domainMinValue"
                placeholder="-∞"
                (focus)="onDomainInputFocus('min')"
                (ngModelChange)="tempDomainMin = $event"
                (blur)="onDomainInputBlur('min')" />
            <span class="text-xs px-1">&le; x &le;</span>
            <input
                type="number"
                class="w-[60px] text-center"
                [min]="domainMinValue"
                [ngModel]="domainMaxValue"
                placeholder="+∞"
                (ngModelChange)="tempDomainMax = $event"
                (focus)="onDomainInputFocus('max')"
                (blur)="onDomainInputBlur('max')" />
            <div class="state-btns">
                <button
                    class="v-tool-btn opacity-80 hover:opacity-100 transition-opacity"
                    [disabled]="domainMinValue === null && domainMaxValue === null"
                    (click)="clearDomain()"
                    title="Xóa miền xác định">
                    <i class="vcon vcon-general vcon_general_reset !text-sm"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<div *ngIf="invalidInputMethod$ | async" class="text-xs text-orange-500 text-center py-1">
    <span class="vcon vcon-general vcon_general_warning !text-xs">
        <span class="path1"></span>
        <span class="path2"></span>
        <span class="path3"></span>
    </span>
    Vui lòng sử dụng bộ gõ tiếng Anh khi nhập công thức.
</div>
<div *ngIf="syntaxError$ | async as errorMessage" class="text-xs text-red-500 text-center py-1">
    {{ errorMessage }}
</div>
<div
    *ngIf="!(syntaxError$ | async) && !equation.isValid && equation.expression !== equation.defaultValue"
    class="text-xs text-red-500 text-center py-1">
    Hàm không hợp lệ
</div>

<lib-equation-settings
    *ngIf="showSettings && equation.styles"
    class="mt-[5px] block"
    [equationStyle]="equation.styles"
    [colorPalette]="colorPalette"
    (onFieldChange)="handleStyleChange($event)"></lib-equation-settings>

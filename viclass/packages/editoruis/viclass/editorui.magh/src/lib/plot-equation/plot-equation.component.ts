import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { EqType, EquationState, UpdateEquationsTool } from '@viclass/editor.magh';
import { MathfieldElement, validateLatex } from 'lib-mathlive';
import { BehaviorSubject, Subject, throttleTime } from 'rxjs';
import { MaghEquationService } from '../magh.equation.service';
import { detectInvalidInputMethod, getSyntaxErrorMessage, UpdateExpressionEvent } from '../maghtools.models';
import { SettingFieldChangeEmitterData } from '../setting-tool/setting-tool.models';

@Component({
    selector: 'lib-plot-equation',
    templateUrl: './plot-equation.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PlotEquationComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('mathRoot', { read: ElementRef })
    mathRoot: ElementRef<HTMLDivElement>;

    mathfield: MathfieldElement;

    @Input() equation: Extract<EquationState, { equationType: EqType.Plot }>;
    @Input() showSettings: boolean = false;
    @Input() colorPalette: string[] = [];
    @Input() tool: UpdateEquationsTool;

    @Output() onToggleSettings = new EventEmitter<number>();
    @Output() onDelete = new EventEmitter<number>();

    exprThrottler = new Subject<UpdateExpressionEvent>();
    unsubscribes: (() => void)[] = [];

    syntaxError$ = new BehaviorSubject<string | null>(null);

    invalidInputMethod$ = new BehaviorSubject<boolean>(false);

    get isParametric(): boolean {
        return this.equation.supportInternalScope;
    }

    get iconColor(): string {
        return this.equation.hidden ? '#fff' : this.equation.styles.color;
    }

    // Temporary values for domain inputs to prevent focus loss during typing
    protected tempDomainMin: string | null = null;
    protected tempDomainMax: string | null = null;

    get domainMinValue(): number | null {
        return this.tempDomainMin !== null
            ? Number(this.tempDomainMin)
            : this.equation.domain?.min === -Infinity
              ? null
              : (this.equation.domain?.min ?? null);
    }

    get domainMaxValue(): number | null {
        return this.tempDomainMax !== null
            ? Number(this.tempDomainMax)
            : this.equation.domain?.max === Infinity
              ? null
              : (this.equation.domain?.max ?? null);
    }

    constructor(
        private cdr: ChangeDetectorRef,
        private maghEquationService: MaghEquationService
    ) {}

    ngOnInit(): void {
        const exprSubsc = this.exprThrottler
            .pipe(throttleTime(100, undefined, { leading: true, trailing: true }))
            .subscribe(ev => {
                this.maghEquationService.updateExpression(this.tool, ev);
                this.validateExpression(ev.expression);
            });

        const changedSusc = this.maghEquationService.equationChanged$.subscribe(() => {
            this.cdr.markForCheck();
            // because the value is not mapped directly to the mathfield, we need to do it manually
            requestAnimationFrame(() => {
                if (!this.mathfield) return;
                const latex = this.equation.expression;
                if (latex !== this.mathfield.value) {
                    this.mathfield.setValue(latex);
                    this.validateExpression(latex);
                }
            });
        });

        this.unsubscribes.push(
            () => exprSubsc.unsubscribe(),
            () => changedSusc.unsubscribe()
        );
    }

    ngOnDestroy(): void {
        this.unsubscribes.forEach(unsubscribe => unsubscribe());
        this.unsubscribes.length = 0;

        this.mathfield?.disconnectedCallback();
        this.mathfield?.remove();
        delete this.mathfield;
    }

    ngAfterViewInit(): void {
        const equation = this.equation;
        this.mathfield = new MathfieldElement();
        this.mathfield.setValue(equation.expression);
        Object.assign(this.mathfield.style, {
            display: 'block',
            outline: 'none',
            borderColor: 'transparent',
            userSelect: 'auto',
        });
        this.mathRoot.nativeElement.appendChild(this.mathfield);

        this.mathfield.menuItems = [];
        const inputListener = () => {
            this.exprThrottler.next({
                index: equation.index,
                expression: this.mathfield.value,
                finished: false,
            });
        };
        const changeListener = () => {
            this.exprThrottler.next({
                index: equation.index,
                expression: this.mathfield.value,
                finished: true,
            });
        };
        const keydownListener = (e: KeyboardEvent) => {
            if (e.code.startsWith('Digit') || e.code.startsWith('Key')) {
                e.stopPropagation();
            }
        };
        this.mathfield.addEventListener('input', inputListener);
        this.mathfield.addEventListener('change', changeListener);
        this.mathfield.addEventListener('keydown', keydownListener);
        this.unsubscribes.push(() => {
            this.mathfield.removeEventListener('input', inputListener);
            this.mathfield.removeEventListener('change', changeListener);
            this.mathfield.removeEventListener('keydown', keydownListener);
        });

        const { isInvalidInput$, cleanup } = detectInvalidInputMethod(this.mathfield);
        this.invalidInputMethod$ = isInvalidInput$;
        this.unsubscribes.push(cleanup);

        this.validateExpression(equation.expression);
    }

    toggleVisibility() {
        this.maghEquationService.updateVisibility(this.tool, {
            index: this.equation.index,
            hidden: !this.equation.hidden,
        });
    }

    toggleSettings() {
        this.onToggleSettings.emit(this.equation.index);
    }

    deleteEquation() {
        this.onDelete.emit(this.equation.index);
    }

    handleStyleChange(ev: SettingFieldChangeEmitterData) {
        this.maghEquationService.changeStyle(this.tool, {
            index: this.equation.index,
            style: {
                ...this.equation.styles,
                [ev.field]: ev.value,
            },
        });
    }

    changeGlobalScope($event: Event) {
        const newVal = ($event.target as HTMLInputElement).value;
        const latexParts = this.mathfield.value.split('=');
        if (latexParts.length < 2) {
            return;
        }
        latexParts.pop();
        latexParts.push(newVal);
        const newLatex = latexParts.join('=');
        this.mathfield.setValue(newLatex);

        this.exprThrottler.next({
            index: this.equation.index,
            expression: newLatex,
            finished: false,
        });
    }

    changeInternalScopeRange(field: 'min' | 'max', value: number | null) {
        if (value !== null) {
            this.equation.internalScope[field] = value;

            this.maghEquationService.changeInternalScope(this.tool, {
                index: this.equation.index,
                min: this.equation.internalScope.min,
                max: this.equation.internalScope.max,
            });
        }
    }

    onDomainInputFocus(field: 'min' | 'max') {
        const domain = this.equation.domain;
        if (field === 'min') {
            this.tempDomainMin = domain.min !== null && isFinite(domain.min) ? domain.min.toString() : null;
        } else {
            this.tempDomainMax = domain.max !== null && isFinite(domain.max) ? domain.max.toString() : null;
        }
    }

    // Commit domain changes when input loses focus
    onDomainInputBlur(field: 'min' | 'max') {
        const value = field === 'min' ? this.tempDomainMin : this.tempDomainMax;

        let domain = this.equation.domain;
        if (!domain) {
            domain = { min: -Infinity, max: Infinity };
        }

        if (value !== null && isFinite(Number(value))) {
            domain[field] = Number(value);
        } else {
            // Set to infinity if empty
            domain[field] = field === 'min' ? -Infinity : Infinity;
        }

        this.maghEquationService.changeDomain(this.tool, {
            index: this.equation.index,
            domain: domain,
        });

        // Clear temporary values
        if (field === 'min') {
            this.tempDomainMin = null;
        } else {
            this.tempDomainMax = null;
        }
    }

    clearDomain() {
        this.equation.domain = { min: -Infinity, max: Infinity };
        this.maghEquationService.changeDomain(this.tool, {
            index: this.equation.index,
            domain: undefined,
        });
    }

    private validateExpression(latex: string): void {
        if (!latex) {
            this.syntaxError$.next(null);
            return;
        }

        const errors = validateLatex(latex);
        if (errors.length > 0) {
            const error = errors[0];
            this.syntaxError$.next(getSyntaxErrorMessage(error));
        } else {
            this.syntaxError$.next(null);
        }
    }
}

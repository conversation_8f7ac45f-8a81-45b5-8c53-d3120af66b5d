<div class="flex gap-[5px]">
    <div class="state-btns flex flex-col justify-start">
        <button class="v-tool-btn" (click)="toggleVisibility()">
            <span
                class="rounded-full w-[14px] h-[14px] border-black border-[1px]"
                [ngStyle]="{ 'background-color': iconColor }"></span>
        </button>
        <button class="v-tool-btn" [ngClass]="{ active: showSettings }" (click)="toggleSettings()">
            <span class="vcon vcon-general vcon_sidebar-setting"></span>
        </button>
    </div>
    <div
        class="flex-1 border-[1px] rounded-[12px] overflow-hidden"
        [ngClass]="{
            'bg-orange-100': (invalidInputMethod$ | async),
            '!border-red-500':
                (syntaxError$ | async) || (!equation.isValid && equation.expression !== equation.defaultValue),
        }">
        <div class="flex justify-between items-start p-[5px] min-h-[35px]" (keyup)="$event.stopPropagation()">
            <span class="text-xs pt-2">Đi<PERSON>m:</span>
            <div #mathRoot class="block flex-1 overflow-auto"></div>
            <div class="action-btns">
                <button class="v-tool-btn" (click)="deleteEquation()">
                    <span class="vcon vcon-general vcon_delete"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<div *ngIf="invalidInputMethod$ | async" class="text-xs text-orange-500 text-center py-1">
    <span class="vcon vcon-general vcon_general_warning !text-xs">
        <span class="path1"></span>
        <span class="path2"></span>
        <span class="path3"></span>
    </span>
    Vui lòng sử dụng bộ gõ tiếng Anh khi nhập công thức.
</div>
<div *ngIf="syntaxError$ | async as errorMessage" class="text-xs text-red-500 text-center py-1">
    {{ errorMessage }}
</div>
<div
    *ngIf="!(syntaxError$ | async) && !equation.isValid && equation.expression !== equation.defaultValue"
    class="text-xs text-red-500 text-center py-1">
    Điểm phải có dạng <i class="font-bold">A=(1,2)</i> hoặc <i class="font-bold">(1,2)</i><br />và không được chứa
    <i class="font-bold">x</i>,
    <i class="font-bold">y</i>
</div>

<lib-point-equation-settings
    *ngIf="showSettings && equation.styles"
    class="mt-[5px] block"
    [equationStyle]="equation.styles"
    [colorPalette]="colorPalette"
    (onFieldChange)="handleStyleChange($event)"></lib-point-equation-settings>

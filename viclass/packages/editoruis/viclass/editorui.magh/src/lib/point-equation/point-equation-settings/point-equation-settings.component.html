<div class="flex gap-3 flex-col py-3">
    <lib-setting-tool-switch
        label="<PERSON>ên điể<PERSON>"
        field="showLabel"
        [value]="{ value: equationStyle.showLabel }"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-switch>
    <lib-setting-tool-adjust-number
        label="<PERSON><PERSON>ch thước điểm"
        [value]="{ value: equationStyle.size }"
        field="size"
        [min]="1"
        [max]="50"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-adjust-number>
    <lib-setting-tool-switch
        label="Đường gióng"
        field="showAxisLines"
        [value]="{ value: equationStyle.showAxisLines }"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-switch>
    <ng-template [ngIf]="equationStyle.showAxisLines">
        <span class="text-BW1"><PERSON><PERSON><PERSON> gióng</span>
        <div class="flex gap-6">
            <div *ngFor="let lineStyle of lineStyles" class="flex gap-2 items-center">
                <input
                    type="radio"
                    name="lineStyle"
                    [ngModel]="equationStyle.lineStyle"
                    [value]="lineStyle"
                    (ngModelChange)="onLineChange($event)" />
                <div class="flex items-center py-2 cursor-pointer" (click)="onLineChange(lineStyle)">
                    <div
                        [class]="
                            'w-[24px] h-[3px] !border-t-[2px] !border-black equation-line-style_' + lineStyle
                        "></div>
                </div>
            </div>
        </div>
        <lib-setting-tool-adjust-number
            label="Độ dày đường gióng"
            [value]="{ value: equationStyle.lineWidth }"
            field="lineWidth"
            [min]="1"
            [max]="10"
            (onChange)="onFieldChange.emit($event)"></lib-setting-tool-adjust-number>
    </ng-template>
    <lib-setting-tool-colors
        label="Màu"
        [value]="{ value: equationStyle.color }"
        field="color"
        [colorList]="colorPalette"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-colors>
    <!-- <lib-setting-tool-adjust-number
        label="Hiển thị nền"
        [value]="{ value: equationStyle.fillOpacity ?? 100 }"
        [min]="0"
        [max]="100"
        field="fillOpacity"
        (onChange)="onFieldChange.emit($event)"></lib-setting-tool-adjust-number> -->
</div>

@font-face {
    font-family: 'ViClass-magh';
    src:
        url('fonts/ViClass-magh.ttf?uj8rht') format('truetype'),
        url('fonts/ViClass-magh.woff?uj8rht') format('woff'),
        url('fonts/ViClass-magh.svg?uj8rht#ViClass-magh') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.vcon-magh {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'ViClass-magh' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vcon_properties:before {
    content: '\e566';
}
.vcon_fx-list:before {
    content: '\e621';
}
.vcon_document_magh:before {
    content: '\e568';
}
.vcon_empty:before {
    content: '\e006';
}
.vcon_page-bar_ad:before {
    content: '\e938';
}

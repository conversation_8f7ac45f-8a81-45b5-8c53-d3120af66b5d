syntax = "proto3";

package viclass.proto.magh.msg;

message DocRenderPropProto {
    optional int32 screen_unit = 1;
    optional double scale = 2;
    repeated double translation = 3;
    repeated double rotation = 4;
    optional bool valid = 5;

    optional bool axis = 6;
    optional bool grid = 7;
    optional bool detail_grid = 8;
    optional bool show_axis_arrows = 12;
    optional bool show_axis_labels = 13;
    optional bool use_pi_grid = 14;

    optional string line_style = 9;
    optional int32 line_width = 10;
    optional double opacity = 11;
}
syntax = "proto3";

package ComposerCommands;

// --------------- DATA STRUCTURE -------------

enum ComposerCmdTypeProto {
    DUMMY = 0;

    // command to modify the document content
    WRAPPING_SUB_EDITOR_CMD = 1;
    UPDATE_INTERNAL_DOC_MAPPING = 2;

    UPDATE_DOC_SETTINGS = 3;

    // internal viewport manipulation
    UPDATE_VIEWPORT_VIEW_STATE = 4;
}

message ComposerBoundaryProto {
    ComposerPositionProto start = 1;
    ComposerPositionProto end = 2;
  }

message ComposerPositionProto {
    double x = 1;
    double y = 2;
}

// ----------------End DATA STRUCTURE ----------

// In general, the composer document take the size of the
// container, i.e. the size of the layer. However, for the
// first insert inside classroom, we include the boundary
// so that receivers of the commands will create the document
// at the correct position
message InsertDocCmdProto {
    ComposerBoundaryProto boundary = 1;
    string global_id = 2;
    string viewport_el_class = 3; // the class that will be used for the viewport
    string content = 4;
}

message RemoveDocCmdProto {
    int32 local_id = 1;
    string global_id = 4;
}

message InsertLayerCmdProto {
    ComposerBoundaryProto boundary = 1;
    int32 z_index = 2;
}

message NewDocBoundaryCmdProto {
    ComposerBoundaryProto boundary = 1;
}

message InsertViewportCmdProto {
    repeated int32 start_container = 1;  // a list of integer mark the path from the root element of the content to the start container
    repeated int32 end_container = 2;
    int32 start_offset = 3;
    int32 end_offset = 4;
    string edType = 5;  // type of the editor whose document will be inserted into the viewport
    string viewport_id = 6;
    string viewport_type = 7;
    optional string width = 8;   // width of the viewport
    optional string height = 9;    // height the viewport
}

message WrappingSubEditorCmdProto {
    bytes underlying = 1;
}

message UpdateInternalDocMappingCmdProto {
    string internal_vm_id = 1;
    string internal_doc_global_id = 2;
    int32 internal_doc_local_id = 3;
}

message UpdateViewportViewStateCmdProto {
    string internal_vm_id = 1;
    optional ComposerPositionProto look_at = 2;
    optional float zoom = 3;
}

message DocSettingsProto {
    int32 doc_local_id = 1;
    string doc_global_id = 2;
    string setting_json = 3;
}

message UpdateDocSettingsCmdProto {
    repeated DocSettingsProto settings = 1;
}
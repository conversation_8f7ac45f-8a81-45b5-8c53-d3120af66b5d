{"name": "@viclass/proto", "version": "0.0.1", "scripts": {"build": "yarn conc 'yarn:compile-*' && npx ngc -p tsconfig.lib.json && yarn copy", "copy": "node copy.js", "compile-core": "cd ./src/editor.core && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:.", "compile-freedrawing": "cd ./src/editor.freedrawing && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:.", "compile-geo": "cd ./src/editor.geo && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:.", "compile-word": "cd ./src/editor.word && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:.", "compile-composer": "cd ./src/editor.composer && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:.", "compile-math": "cd ./src/editor.math && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:.", "compile-magh": "cd ./src/editor.magh && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:.", "compile-feature.common": "cd ./src/feature.common && protoc -I=. ./*.proto --js_out=import_style=commonjs,binary:. --grpc-web_out=import_style=commonjs+dts,mode=grpcweb:."}, "peerDependencies": {"google-protobuf": "^3.21.4"}, "dependencies": {"tslib": "^2.3.0"}, "exports": {"./editor.core": "./editor.core/index.js", "./editor.freedrawing": "./editor.freedrawing/index.js", "./editor.geo": "./editor.geo/index.js", "./editor.word": "./editor.word/index.js", "./editor.composer": "./editor.composer/index.js", "./editor.math": "./editor.math/index.js", "./editor.magh": "./editor.magh/index.js", "./feature.common": "./feature.common/index.js"}, "devDependencies": {"concurrently": "^8.2.2", "protoc-gen-grpc-web": "^1.5.0"}}
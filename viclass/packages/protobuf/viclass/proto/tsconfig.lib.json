{"extends": "../../../../tsconfig.json", "compilerOptions": {"module": "commonjs", "allowJs": true, "outDir": "../../../../dist/viclass/proto", "declaration": true, "declarationMap": true, "inlineSources": true, "types": []}, "include": ["src/editor.core/index.ts", "src/editor.freedrawing/index.ts", "src/editor.geo/index.ts", "src/editor.word/index.ts", "src/editor.composer/index.ts", "src/editor.math/index.ts", "src/editor.magh/index.ts", "src/feature.common/index.ts"], "exclude": ["src/test.ts", "**/*.spec.ts"]}
<!-- header bar -->
<div class="flex flex-col h-full home-layout">
    <div
        [ngClass]="[(isScroll$ | async) ? 'scrolled text-BW1 lg:text-inherit' : 'lg:bg-inherit lg:text-BW7']"
        class="bg-BW7/95 lg:text-BW7 top-0 fixed w-full z-[999]">
        <div class="lg:h-[80px] lg:px-largePageMargin md:h-[70px] h-[50px] px-smallPageMargin flex flex-row">
            <div class="self-center w-full flex flex-row">
                <!-- logo -->
                <div class="md:block grow-0 flex align-items-center">
                    <a href="/">
                        <img src="assets/img/vi-logo.svg" class="lg:h-[35px] md:h-[30px] xs:h-[20px]" />
                    </a>
                </div>

                <!-- navigation -->
                <nav class="lg:flex hidden grow flex-row justify-center items-stretch text-BW3 space-x-[30px]">
                    <div
                        class="flex items-center"
                        [ngClass]="{
                            'active-page': currentChildrenRouteName === 'home',
                        }">
                        <a class="topbar-menu-item" href="/">Trang chủ</a>
                    </div>
                    <!-- <div
                        class="flex items-center"
                        [ngClass]="{
                            'active-page': currentChildrenRouteName === 'hiring',
                        }">
                        <a class="topbar-menu-item" href="/hiring">Câu chuyện</a>
                    </div> -->
                    <div
                        class="flex items-center"
                        [ngClass]="{
                            'active-page': currentChildrenRouteName === 'features',
                        }">
                        <a class="topbar-menu-item" href="/features">Tính năng</a>
                    </div>
                </nav>

                <ng-container *ngIf="userService.isUserLoaded$ | async">
                    <!-- Stretcher -->
                    <div class="lg:hidden grow"></div>

                    <div
                        class="lg:flex hidden grow-0 flex-row justify-center items-center text-BW3 space-x-[30px]"
                        *ngIf="(userService.curUser$ | async) === null">
                        <a
                            class="registration-btn-hover"
                            href="/registration"
                            *ngIf="(appState.curPage$ | async)?.name !== 'RegistrationPageComponent'">
                            Đăng ký
                        </a>
                        <a
                            class="vi-btn vi-btn-normal vi-btn-focus"
                            href="/login"
                            *ngIf="(appState.curPage$ | async)?.name !== 'LoginPageComponent'">
                            Đăng nhập
                        </a>
                    </div>

                    <ng-template [ngIf]="userService.curUser$ | async" let-profile="ngIf">
                        <!-- Clicking on this div is switching the menu too-->
                        <div
                            class="flex grow-0 flex-row justify-center items-center cursor-pointer"
                            (click)="menuSwitch.switch()">
                            <div
                                class="w-[24px] h-[24px] avatar-img border-BW7"
                                [ngStyle]="
                                    (userService.curUser$ | async).avatarUrl && {
                                        'background-image': 'url(' + (userService.curUser$ | async).avatarUrl + ')',
                                    }
                                "></div>
                            <div class="max-md:hidden ml-3">
                                Xin chào
                                <strong>{{ (userService.curUser$ | async).username }}</strong>
                            </div>
                        </div>
                    </ng-template>

                    <!--Hamburger menu, hidden from sm onward -->
                    <div class="lg:hidden flex items-center hamburger-menu ml-3">
                        <input
                            class="hamburger-btn"
                            type="checkbox"
                            id="hamburger-btn"
                            #menuSwitch
                            app-menu-switch
                            [show-menu]="homepageMenu" />
                        <label for="hamburger-btn" class="h-[20px] z-[100] cursor-pointer flex items-center">
                            <span class="hamburger-icon"></span>
                        </label>
                    </div>
                </ng-container>
            </div>
            <!-- End navigation -->

            <div class="absolute right-0 bottom-0">
                <app-menu class="absolute" #homepageMenu>
                    <div
                        class="bg-BW7 w-full max-w-[290px] md:max-w-[400px] shadow-[0px_1px_30px_0_rgb(var(--BW1)/0.25)] rounded-bl-[15px]">
                        <div
                            *ngIf="(userService.curUser$ | async) === null"
                            class="py-[20px] flex flex-row justify-center items-center text-BW1 space-x-[30px]">
                            <a
                                class="text-BW3 hover:text-BW1"
                                href="/registration"
                                *ngIf="(appState.curPage$ | async)?.name !== 'RegistrationPageComponent'">
                                Đăng ký
                            </a>
                            <a
                                class="vi-btn vi-btn-normal vi-btn-focus"
                                href="/login"
                                *ngIf="(appState.curPage$ | async)?.name !== 'LoginPageComponent'">
                                Đăng nhập
                            </a>
                        </div>
                        <ng-template [ngIf]="userService.curUser$ | async" let-profile="ngIf">
                            <div>
                                <a
                                    [ngClass]="{
                                        'bg-P3': (appState.curPage$ | async)?.name === 'ProfilePageComponent',
                                    }"
                                    class="py-[18px] px-[30px] hover:bg-P3 hover:text-inherit flex items-center"
                                    routerLink="/profile/info">
                                    <i class="vcon-general vcon_name mr-2"></i>
                                    <span>Thông tin cá nhân</span>
                                </a>
                                <a
                                    [ngClass]="{
                                        'bg-P3': (appState.curPage$ | async)?.name === 'ProfilePageComponent',
                                    }"
                                    class="py-[18px] px-[30px] hover:bg-P3 hover:text-inherit flex items-center"
                                    routerLink="/profile/security">
                                    <i class="vcon-general vcon_general_password mr-2"></i>
                                    <span>Bảo mật</span>
                                </a>
                                <!-- <a
                                    [ngClass]="{
                                        'bg-P3': (appState.curPage$ | async)?.name === 'ProfilePageComponent',
                                    }"
                                    class="py-[18px] px-[30px] hover:bg-P3 hover:text-inherit flex items-center"
                                    routerLink="/profile">
                                    <i class="vcon-general vcon_sidebar-setting mr-2"></i>
                                    <span>Cài đặt</span>
                                </a> -->
                                <div class="bg-BW4 h-[1px]"></div>
                                <a
                                    class="py-[18px] px-[30px] hover:bg-P3 hover:text-inherit flex items-center"
                                    routerLink="/profile/classrooms">
                                    <i class="vcon-general vcon_calendar mr-2"></i>
                                    <span>Buổi học</span>
                                </a>
                                <a
                                    class="py-[18px] px-[30px] hover:bg-P3 hover:text-inherit flex items-center"
                                    routerLink="/profile/documents">
                                    <i class="vcon-general vcon_sidebar-document mr-2"></i>
                                    <span>Thư viện</span>
                                </a>
                            </div>
                        </ng-template>
                        <div class="bg-BW4 h-[1px]"></div>
                        <nav>
                            <!-- <a
                                class="hover:text-inherit flex items-center py-[18px] px-[30px] hover:bg-P3"
                                href="/hiring">
                                <i class="vcon vcon-general vcon_logo-icon mr-2"></i>
                                <span>Câu chuyện</span>
                            </a> -->
                            <a
                                class="hover:text-inherit flex items-center py-[18px] px-[30px] hover:bg-P3"
                                href="/features">
                                <i class="vcon vcon-general vcon-general vcon_feature mr-2"></i>
                                <span>Tính năng</span>
                            </a>
                            <a
                                class="hover:text-inherit flex items-center py-[18px] px-[30px] hover:bg-P3"
                                href="/support"
                                target="_blank">
                                <i class="vcon-general vcon_help mr-2"></i>
                                <span>Trợ giúp</span>
                            </a>
                        </nav>
                        <a
                            *ngIf="userService.curUser$ | async"
                            class="vi-btn vi-btn-normal vi-btn-outline !inline-block ml-[30px] my-3"
                            (click)="logout()">
                            <span>Đăng xuất</span>
                        </a>
                    </div>
                </app-menu>
            </div>
        </div>
        <div
            class="absolute top-0 pointer-events-none w-full h-full shadow-[0px_1px_30px_0_rgb(var(--BW1)/0.25)] z-[1001]"></div>
    </div>
    <div class="grow lg:mt-[80px] md:mt-[70px] mt-[50px]">
        <router-outlet></router-outlet>
    </div>
</div>

import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, NavigationEnd, Router, RouterModule } from '@angular/router';
import { NotificationModule, PlatformService, UserService } from '@viclass/portal.common';
import { BehaviorSubject, filter, firstValueFrom, Subject, Subscription, take, tap } from 'rxjs';
import { AppStateService } from '../app.state.service';
import { MenuSwitchComponent } from '../menu-switch/menu-switch.component';
import { MenuComponent } from '../menu/menu.component';

@Component({
    selector: 'home-layout',
    templateUrl: './home-layout.component.html',
    styleUrls: ['./home-layout.component.scss'],
    standalone: true,
    imports: [RouterModule, CommonModule, MenuSwitchComponent, MenuComponent, OverlayModule, NotificationModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeLayoutComponent implements OnDestroy, OnInit, OnDestroy {
    isScroll$ = new BehaviorSubject(false);
    private scrollEvents$: Subject<number> = new Subject();
    routerSubScription: Subscription;
    currentChildrenRouteName: String = '';
    private routeChangeSub: Subscription;

    constructor(
        public appState: AppStateService,
        private router: Router,
        public userService: UserService,
        public activatedRoute: ActivatedRoute,
        public cdr: ChangeDetectorRef,
        public ps: PlatformService
    ) {
        this.scrollEvents$
            .pipe(filter(num => (num > 0 && !this.isScroll$.getValue()) || (num == 0 && this.isScroll$.getValue())))
            .subscribe(num => {
                const x = document.documentElement.clientHeight;
                const y = document.documentElement.scrollHeight;
                const isScrollable = y > x;
                if (!isScrollable) return;

                if (num > 0) this.isScroll$.next(true);
                else this.isScroll$.next(false);
            });

        // we want to check the current navigation to know which page is currently active
        // to do that, catch the activation start event of the filter, this event is trigger for each of the
        // url segment, the segment is a top page when its component is not empty and all parent
        // leads to root doesn't have any component configured
        this.routerSubScription = router.events
            .pipe(
                tap(e => {
                    if (e instanceof NavigationEnd) {
                        // check if the current page layout is HomeLayoutComponent
                        const root: ActivatedRoute | ActivatedRouteSnapshot = this.activatedRoute.snapshot.root;
                        let homeLayoutRoute = root;
                        while (homeLayoutRoute.component !== HomeLayoutComponent && homeLayoutRoute.firstChild)
                            homeLayoutRoute = homeLayoutRoute.firstChild;

                        // get current page route
                        if (homeLayoutRoute.component === HomeLayoutComponent) {
                            let curPageRoute = homeLayoutRoute;
                            while (curPageRoute.firstChild) curPageRoute = curPageRoute.firstChild;

                            // set current page component
                            this.appState.curPage$.next(curPageRoute.routeConfig!.component as any);
                        }
                    }
                })
            )
            .subscribe();

        // listen to the children route change
        this.routeChangeSub = this.router.events.subscribe(async () => {
            if (!this.activatedRoute.children?.[0]?.data) return;

            // get the name of the current children route
            const data = await firstValueFrom(this.activatedRoute.children[0].data.pipe(take(1)));
            this.currentChildrenRouteName = data['name'];
        });
    }

    ngOnInit() {}

    getRouter() {
        return this.router;
    }

    @HostListener('document:scroll', ['$event'])
    onScroll() {
        this.scrollEvents$.next(window.scrollY);
    }

    ngOnDestroy(): void {
        this.routerSubScription.unsubscribe();
        this.routeChangeSub.unsubscribe();
    }

    async logout() {
        await firstValueFrom(this.userService.doLogout());
        window.location.href = '/'; // go back to home page
    }
}

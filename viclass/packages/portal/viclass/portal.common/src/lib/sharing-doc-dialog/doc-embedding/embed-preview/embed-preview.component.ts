/// <reference types="@viclass/ww/typings" />

import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { FormsModule, UntypedFormBuilder } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { BoardType } from '@viclass/editor.coordinator/docloader';
import { DocumentId, EditorType, Position } from '@viclass/editor.core';
import { BehaviorSubject, map } from 'rxjs';
import { ButtonClickWaitDirective } from '../../../button-click-wait-directive/click-wait.directive';
import { NotificationService } from '../../../notification';
import { ensureRange } from '../../../utils/number.utils';

export type EmbedPreviewType = 'iframe' | 'javascript';

const MIN_DIMENSION = 0;
const MAX_DIMENSION = 10000;

@Component({
    selector: 'app-embed-preview',
    standalone: true,
    imports: [CommonModule, FormsModule, ButtonClickWaitDirective],
    templateUrl: './embed-preview.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmbedPreviewComponent implements OnInit, AfterViewInit, OnDestroy {
    @Input()
    previewType: EmbedPreviewType;

    @Input()
    edType: EditorType;

    @Input()
    docGlobalId: DocumentId;

    @Output()
    closePreview = new EventEmitter<void>();

    @ViewChild('wrapper')
    wrapper: ElementRef<HTMLDivElement>;

    width: number = 1000;
    height: number = 600;

    private zoomLevel: number = 1;
    private lookAt: Position = null;

    get viewportType(): BoardType {
        return ['WordEditor', 'MathEditor'].includes(this.edType) ? 'inline' : 'board';
    }

    embedCode$ = new BehaviorSubject<string>('');

    readonly previewScale = new BehaviorSubject<number>(1);

    /**
     * Will be the ratio of width/height, or null if not fixed
     */
    readonly fixedDimention$ = new BehaviorSubject<number | null>(null);

    get fixedDimention(): number | null {
        return this.fixedDimention$.value;
    }

    private previewSrc$ = new BehaviorSubject<string | null>(null);
    readonly safePreviewSrc$ = this.previewSrc$.pipe(map(src => this.sanitizer.bypassSecurityTrustResourceUrl(src)));

    constructor(
        private sanitizer: DomSanitizer,
        public fb: UntypedFormBuilder,
        private notificationService: NotificationService
    ) {}

    async ngOnInit(): Promise<void> {
        this.fixedDimention$.next(this.width / this.height);
        this.previewSrc$.next(`/user/doc/embed/${this.edType}/${this.docGlobalId}`);

        this.updateEmbedCode();
    }

    ngAfterViewInit(): void {
        this.updateIframeScale();

        // handle viewport events (Ex: pan/zoom) from the iframe
        window.addEventListener('viewport-change', this.handleViewportEvents);
    }

    ngOnDestroy(): void {
        window.removeEventListener('viewport-change', this.handleViewportEvents);
    }

    /**
     * Toggle fixed dimention (width/height) so when width or height changes
     * the other one will be adjusted
     */
    toggleFixedDimention(): void {
        if (this.fixedDimention != null) {
            this.fixedDimention$.next(null);
        } else {
            this.fixedDimention$.next(this.width / this.height);
        }
    }

    onWidthChange(value: number) {
        value = ensureRange(value, MIN_DIMENSION, MAX_DIMENSION);
        this.width = value;
        if (this.fixedDimention !== null) {
            this.height = value === 0 ? 0 : Math.floor(value / this.fixedDimention);
        }
        this.updateEmbedCode();
        this.updateIframeScale();
    }
    onHeightChange(value: number) {
        value = ensureRange(value, MIN_DIMENSION, MAX_DIMENSION);
        this.height = value;
        if (this.fixedDimention !== null) {
            this.width = value === 0 ? 0 : Math.floor(value * this.fixedDimention);
        }
        this.updateEmbedCode();
        this.updateIframeScale();
    }

    /**
     * Update the embed code based on the current settings
     */
    private updateEmbedCode() {
        const embedCode = this.previewType === 'javascript' ? this.getJavascriptEmbedCode() : this.getIframeEmbedCode();
        this.embedCode$.next(embedCode);
    }

    private getJavascriptEmbedCode(): string {
        const viewParams: string[] = [];

        if (this.lookAt) {
            viewParams.push(`data-vi-look-at-x="${this.lookAt.x}"`);
            viewParams.push(`data-vi-look-at-y="${this.lookAt.y}"`);
        }
        if (this.zoomLevel != 1) {
            viewParams.push(`data-vi-zoom="${this.zoomLevel}"`);
        }

        const viewParamsStr = viewParams.length > 0 ? `\n\t${viewParams.join('\n\t')}` : '';

        return `<div 
\tclass="vi-embed-viewport"
\tdata-vi-vp-type="${this.viewportType}" ${viewParamsStr}
\tstyle="width: ${this.width}px; height: ${this.height}px">
\t<div
\t\tclass="vi-embed-doc"
\t\tdata-vi-doc-id="${this.docGlobalId}"
\t\tdata-vi-ed-type="${this.edType}"></div>
</div>`;
    }

    private getIframeEmbedCode(): string {
        const params = new URLSearchParams();
        if (this.lookAt) {
            params.set('lookAtX', this.lookAt.x.toString());
            params.set('lookAtY', this.lookAt.y.toString());
        }
        if (this.zoomLevel != 1) {
            params.set('zoom', this.zoomLevel.toString());
        }
        const embedUrl = new URL(this.previewSrc$.value, window.location.href);
        embedUrl.search = params.toString();

        return `<iframe src="${embedUrl.toString()}" width="${this.width}" height="${this.height}"></iframe>`;
    }

    /**
     *  Scale the iframe to contains it inside the wrapper element
     */
    private updateIframeScale() {
        if (!this.wrapper.nativeElement) return;
        const wrapperRect = this.wrapper.nativeElement.getBoundingClientRect();
        const scale = Math.min(wrapperRect.width / this.width, wrapperRect.height / this.height);
        this.previewScale.next(scale);
    }

    private handleViewportEvents = (e: CustomEvent<viclass.ViDocLoader.ViewportEvent>) => {
        const vpEvent = e.detail;
        console.log(vpEvent);

        switch (vpEvent.eventType) {
            case 'viewport-pan':
                this.lookAt = vpEvent.state;
                break;
            case 'viewport-zoom':
                this.zoomLevel = vpEvent.state;
                break;
        }

        this.updateEmbedCode();
    };

    async copyEmbedCodeToClipboard() {
        try {
            await navigator.clipboard.writeText(this.embedCode$.value);
            this.notificationService.showNotification({
                message: 'Sao chép thành công',
                status: 'success',
            });
        } catch (error) {
            console.error('Async: Could not copy text: ', error);
            this.notificationService.showNotification({
                message: 'Sao chép thất bại. Xin vui lòng thử lại',
                status: 'error',
            });
        }
    }
}

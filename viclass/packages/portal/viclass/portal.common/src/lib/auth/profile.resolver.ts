import { inject } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map, take } from 'rxjs/operators';
import { UserService } from '../services';
import { encodeReturnUrl } from '../utils';
import {
    AUTH_FLOW_CONFIG,
    AuthflowConfig,
    INTERNAL_SERVER_ERROR_PATH,
    LOGIN_PATH,
    PKEY_RURL,
    UserProfile,
} from './model';

/**
 * Resolver function for fetching and caching user profile data during route navigation.
 * Used by Angular Router to resolve data before activating a route.
 * @param route - Current route snapshot containing route parameters
 * @param state - Router state snapshot with navigation info
 * @returns Observable that resolves to the user profile or null on error
 */
export const profileResolveFn: ResolveFn<UserProfile> = (
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
): Observable<UserProfile> => {
    const us = inject(UserService);
    const user$ = us.isUserLoaded$.value ? us.curUser$ : us.profile();
    return user$.pipe(
        take(1),
        catchError(err => of(null)) // Return null if profile fetch fails
    );
};

export const redirectOnLoggedInFn = (): Observable<boolean> => {
    const authflowConf = inject(AUTH_FLOW_CONFIG);
    const userService = inject(UserService);
    const activatedRoute = inject(ActivatedRoute);

    const user$ = userService.isUserLoaded$.value ? userService.curUser$ : userService.profile();

    return user$.pipe(
        take(1),
        map(user => {
            if (!user) {
                // User not logged in, allow navigation
                return true;
            }

            // User is logged in, check for return URL in query params
            let redirectUrl = authflowConf.defaultReturnUrl;
            const queryParams = activatedRoute.snapshot.queryParams;
            if (queryParams?.[PKEY_RURL])
                try {
                    redirectUrl = atob(queryParams[PKEY_RURL]);
                } catch {}

            window.location.href = redirectUrl;

            // Prevent further navigation
            return false;
        }),
        catchError(err => {
            if (err?.status === 401) return of(true);

            const rInfo = {
                actionName: '',
                rURL: window.location.pathname,
            };
            const returnUrl = encodeReturnUrl(rInfo);
            window.location.href = `${INTERNAL_SERVER_ERROR_PATH}?${PKEY_RURL}=${returnUrl}`;

            return of(false);
        })
    );
};

export const loginRequiredFn = (): Observable<boolean> => {
    const authflowConf = inject(AUTH_FLOW_CONFIG);
    const userService = inject(UserService);
    return requireLogin(userService, authflowConf);
};

export const requireLogin = (
    userService: UserService,
    authflowConf: AuthflowConfig,
    actionName?: string
): Observable<boolean> => {
    const user$ = userService.isUserLoaded$.value ? userService.curUser$ : userService.profile();
    return user$.pipe(
        map(user => !!user),
        catchError(err => {
            const rInfo = {
                actionName: actionName || '',
                rURL: window.location.pathname,
            };
            const returnUrl = encodeReturnUrl(rInfo);

            if (err?.status === 401) window.location.href = `${LOGIN_PATH}?${PKEY_RURL}=${returnUrl}`;
            else window.location.href = `${INTERNAL_SERVER_ERROR_PATH}?${PKEY_RURL}=${returnUrl}`;

            return of(false);
        })
    );
};

export const createRequireLoginFn = (actionName?: string) => {
    return (): Observable<boolean> => {
        const authflowConf = inject(AUTH_FLOW_CONFIG);
        const userService = inject(UserService);

        return requireLogin(userService, authflowConf, actionName);
    };
};

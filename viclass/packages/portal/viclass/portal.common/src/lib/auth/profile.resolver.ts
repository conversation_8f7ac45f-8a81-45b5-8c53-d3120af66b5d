import { inject, TransferState } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map, take, tap } from 'rxjs/operators';
import { PlatformService, UserService } from '../services';
import { encodeReturnUrl } from '../utils';
import {
    AUTH_FLOW_CONFIG,
    AuthflowConfig,
    INTERNAL_SERVER_ERROR_PATH,
    LOGIN_PATH,
    PKEY_RURL,
    UserProfile,
} from './model';
import { PROFILE_TRANSFER_TOKEN } from '../transfer.token';

function loadUserByPlatform(ts: TransferState, us: UserService, ps: PlatformService): Observable<UserProfile> {
    try {
        // Server-side rendering path
        if (ps.isServerSide) {
            return us.profile().pipe(
                take(1),
                tap(profile => {
                    // Store profile in TransferState to avoid duplicate requests on a client
                    ts.set(PROFILE_TRANSFER_TOKEN, profile);
                }),
                catchError(_err => of(null)) // Return null if profile fetch fails
            );
        }
        // Client-side rendering path
        else {
            // Try to retrieve the profile from TransferState (if provided by server)
            const profile = ts.get(PROFILE_TRANSFER_TOKEN, null) as UserProfile;
            ts.set(PROFILE_TRANSFER_TOKEN, null); // Reset transfer user data after receiving

            if (profile) {
                // If a profile exists in TransferState, use it without making an API call
                us.curUser$.next(profile);
                return of(profile);
            }

            // If the profile wasn't in TransferState, fetch it from API
            return us.profile().pipe(
                take(1),
                tap(profile => us.curUser$.next(profile)), // Update current user in UserService
                catchError(_err => of(null)) // Return null if profile fetch fails
            );
        }
    } catch (error) {
        console.error('Error in profileResolveFn:', error);
        return of(null); // Fallback to null on any unexpected errors
    }
}

/**
 * Resolver function for fetching and caching user profile data during route navigation.
 * Used by Angular Router to resolve data before activating a route.
 * @param route - Current route snapshot containing route parameters
 * @param state - Router state snapshot with navigation info
 * @returns Observable that resolves to the user profile or null on error
 */
export const profileResolveFn: ResolveFn<UserProfile> = (
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
): Observable<UserProfile> => {
    const us = inject(UserService);
    const ts = inject(TransferState);
    const ps = inject(PlatformService);
    return loadUserByPlatform(ts, us, ps);
};

export const redirectOnLoggedInFn = (): Observable<boolean> => {
    const authflowConf = inject(AUTH_FLOW_CONFIG);
    const activatedRoute = inject(ActivatedRoute);
    const us = inject(UserService);
    const ts = inject(TransferState);
    const ps = inject(PlatformService);
    return loadUserByPlatform(ts, us, ps).pipe(
        take(1),
        map(user => {
            if (!user) {
                // User not logged in, allow navigation
                return true;
            }

            // User is logged in, check for return URL in query params
            let redirectUrl = authflowConf.defaultReturnUrl;
            const queryParams = activatedRoute.snapshot.queryParams;
            if (queryParams?.[PKEY_RURL])
                try {
                    redirectUrl = atob(queryParams[PKEY_RURL]);
                } catch {}

            window.location.href = redirectUrl;

            // Prevent further navigation
            return false;
        }),
        catchError(err => {
            if (err?.status === 401) return of(true);

            const rInfo = {
                actionName: '',
                rURL: window.location.pathname,
            };
            const returnUrl = encodeReturnUrl(rInfo);
            window.location.href = `${INTERNAL_SERVER_ERROR_PATH}?${PKEY_RURL}=${returnUrl}`;

            return of(false);
        })
    );
};

export const requireLogin = (
    us: UserService,
    ts: TransferState,
    ps: PlatformService,
    authflowConf: AuthflowConfig,
    actionName?: string
): Observable<boolean> => {
    return loadUserByPlatform(ts, us, ps).pipe(
        map(user => !!user),
        catchError(err => {
            const rInfo = {
                actionName: actionName || '',
                rURL: window.location.pathname,
            };
            const returnUrl = encodeReturnUrl(rInfo);

            if (err?.status === 401) window.location.href = `${LOGIN_PATH}?${PKEY_RURL}=${returnUrl}`;
            else window.location.href = `${INTERNAL_SERVER_ERROR_PATH}?${PKEY_RURL}=${returnUrl}`;

            return of(false);
        })
    );
};

export const loginRequiredFn = (): Observable<boolean> => {
    const authflowConf = inject(AUTH_FLOW_CONFIG);
    const us = inject(UserService);
    const ts = inject(TransferState);
    const ps = inject(PlatformService);
    return requireLogin(us, ts, ps, authflowConf);
};

export const createRequireLoginFn = (actionName?: string) => {
    return (): Observable<boolean> => {
        const authflowConf = inject(AUTH_FLOW_CONFIG);
        const us = inject(UserService);
        const ts = inject(TransferState);
        const ps = inject(PlatformService);
        return requireLogin(us, ts, ps, authflowConf, actionName);
    };
};

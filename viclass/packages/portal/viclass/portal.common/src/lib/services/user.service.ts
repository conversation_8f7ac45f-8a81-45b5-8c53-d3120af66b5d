import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, finalize, map, take } from 'rxjs/operators';
import {
    ChangePasswordData,
    EmailRegInfoResponse,
    ForgotPasswordData,
    LoginData,
    LoginInfoResponse,
    RegistrationData,
    RegistrationMetadata,
    SendResetPasswordRequest,
    SendVerificationResult,
    SocialUser,
    UpdateProfileData,
    UserProfile,
    VerificationData,
} from '../auth';
import { createRPC } from './api.service.module';
import { UserController } from './route/backend.route.definition';

/**
 * Service to manage user profile and registration
 */
@Injectable({
    providedIn: 'root',
})
export class UserService {
    server = new UserController();

    curUser$: BehaviorSubject<UserProfile> = new BehaviorSubject(null);
    isUserLoaded$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    curUserError$: BehaviorSubject<Error> = new BehaviorSubject(null);

    constructor(private readonly http: HttpClient) {}

    /**
     * API to request forgot password email to be sent to the user
     */
    forgotPassword(data: ForgotPasswordData): Observable<any> {
        return createRPC(this.server.forgotPassword)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(response => response.body)
        );
    }

    /**
     * API to reset with a new password in the reset password page.
     * Need a valid reset password token associated with user email to success
     */
    resetPassword(data: SendResetPasswordRequest): Observable<any> {
        return createRPC(this.server.resetPassword)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(response => response.body)
        );
    }

    /**
     * Check if the reset password token is valid
     */
    verifyResetPasswordToken(jwtToken: string): Observable<any> {
        return createRPC(this.server.verifyResetPasswordToken)(this.http, {}, jwtToken).pipe(
            take(1),
            map(response => response.body)
        );
    }

    /**
     * API to login
     */
    doLogin(data: LoginData): Observable<any> {
        return createRPC(this.server.doLogin)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(response => response.body)
        );
    }

    /**
     * API to change password using the current password
     */
    changePassword(data: ChangePasswordData): Observable<any> {
        return createRPC(this.server.changePassword)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(response => response.body)
        );
    }

    /**
     * API to handle login using social account
     */
    doSocialLogin(data: SocialUser): Observable<any> {
        return createRPC<any>(this.server.doSocialLogin)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(response => response.body)
        );
    }

    /**
     * Fetch the current user profile and store it to be used by other components and services
     */
    profile(): Observable<UserProfile> {
        return createRPC<UserProfile>(this.server.profile)(this.http).pipe(
            take(1),
            map(response => {
                // cache the profile
                this.curUser$.next(response.body);
                return response.body;
            }),
            catchError((err: Error, caught: Observable<UserProfile>) => {
                this.curUserError$.next(err);
                throw err;
            }),
            finalize(() => {
                // Ensure that the user is loaded even if there is an error
                this.isUserLoaded$.next(true);
            })
        );
    }

    /**
     * Update the profile of the current user with the given data.
     * To be used by the profile page
     */
    updateProfile(data: UpdateProfileData): Observable<UserProfile> {
        return createRPC<UserProfile>(this.server.updateProfile)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(response => {
                // cache the profile
                this.curUser$.next(response.body);
                return response.body;
            }),
            catchError((err: Error, caught: Observable<UserProfile>) => {
                this.curUserError$.next(err);
                return of(null);
            })
        );
    }

    /**
     * Get the site key of hcaptcha component
     */
    siteKeyOfCaptcha(): Observable<any> {
        return createRPC<any>(this.server.siteKeyOfCaptcha)(this.http).pipe(
            take(1),
            map(response => {
                return response.body;
            }),
            catchError((err: Error, caught: Observable<UserProfile>) => {
                return of(null);
            })
        );
    }

    /**
     * Brief profile of a list of users
     * To be used by LSession service
     */
    briefProfiles(...ids: string[]): Observable<UserProfile[]> {
        return createRPC<UserProfile[]>(this.server.briefProfiles)(this.http, {
            body: JSON.stringify(ids),
        }).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }

    /**
     * Register a new user in the email-password flow
     */
    register(registerData: RegistrationData): Observable<HttpResponse<any>> {
        return createRPC(this.server.register)(this.http, {
            body: JSON.stringify(registerData),
        }).pipe(take(1));
    }

    /**
     * Check if the username exist in the email-password registration
     */
    checkUserExist(username: string): Observable<Boolean> {
        return createRPC(this.server.checkUserExist)(this.http, {}, username).pipe(
            take(1),
            map(response => true),
            catchError(error => {
                if (error.status && error.status == 404) return of(false);
                else return of(true);
            })
        );
    }

    /**
     * Check if email is already registered by the email-password registration type
     */
    checkEmailExist(registrationType: string, email: string): Observable<Boolean> {
        return createRPC(this.server.checkEmailExist)(this.http, {
            body: JSON.stringify({ registrationType, email }),
        }).pipe(
            take(1),
            map(response => true),
            catchError(error => {
                if (error.status && error.status == 404) return of(false);
                else return of(true);
            })
        );
    }

    /**
     * API to logout
     */
    doLogout(): Observable<HttpResponse<any>> {
        return createRPC(this.server.doLogout)(this.http, {
            body: JSON.stringify({}),
        }).pipe(take(1));
    }

    /**
     * API to verify email associated with user registration.
     * To be used by the email verification page
     */
    verify(data: VerificationData): Observable<HttpResponse<any>> {
        return createRPC(this.server.verifyEmail)(this.http, {
            body: JSON.stringify(data),
        }).pipe(take(1));
    }

    /**
     * API to request send verification email to user.
     * To be used by the email verification page
     */
    sendVerification(registrationId: string): Observable<SendVerificationResult> {
        return createRPC<SendVerificationResult>(this.server.sendVerification)(this.http, {}, registrationId).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }

    /**
     * API to get registration metadata to see if the email is verified.
     * To be used by the enter missing email page on login using social account
     * but we can not infer the email from it
     */
    getRegistrationMetadata(registrationId: string): Observable<RegistrationMetadata> {
        return createRPC<RegistrationMetadata>(this.server.getRegistrationMetadata)(this.http, {}, registrationId).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }

    /**
     * API to add missing email to user social registration
     * when we can not infer the email from social token info.
     */
    addMissingSocialEmail(registrationId: string, email: string) {
        return createRPC(this.server.addMissingSocialEmail)(this.http, {
            body: JSON.stringify({ registrationId, email }),
        }).pipe(take(1));
    }

    /**
     * API to get the list of linked social registrations (i.g. email and social: Google, Facebook)
     */
    linkedRegistrations(): Observable<string[]> {
        return createRPC<string[]>(this.server.linkedRegistrations)(this.http, {}).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }

    /**
     * API to get the email-password registration info, include the login username of this account
     */
    getEmailRegInfo(): Observable<EmailRegInfoResponse> {
        return createRPC<EmailRegInfoResponse>(this.server.emailRegInfo)(this.http, {}).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }

    /**
     * API to unlink a social registration from the current user.
     * When user profile is linked with multiple social account but user want to remove some of them
     */
    unlinkSocial(social: string): Observable<any> {
        return createRPC<boolean>(this.server.unlinkSocial)(this.http, {}, social).pipe(take(1));
    }

    /**
     * API to update the avatar of the current user
     */
    updateAvatar(avatarUrl: string): Observable<HttpResponse<any>> {
        return createRPC(this.server.updateAvatar)(this.http, {
            body: JSON.stringify({ avatarUrl }),
        }).pipe(take(1));
    }

    /**
     * API to get the login information of the current user.
     * It's include the last login time and the created date
     */
    getUserLoginInformation(): Observable<LoginInfoResponse> {
        return createRPC<LoginInfoResponse>(this.server.getUserLoginInformation)(this.http, {}).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }

    /**
     * API to register a new user in the beta registration flow
     */
    userBetaRegister(email: string): Observable<any> {
        return createRPC(this.server.userBetaRegister)(this.http, { body: JSON.stringify({ email }) }).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }
}

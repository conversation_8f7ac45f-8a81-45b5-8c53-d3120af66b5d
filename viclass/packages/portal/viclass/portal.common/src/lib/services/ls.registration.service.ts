import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, ReplaySubject } from 'rxjs';
import { catchError, map, mergeMap, take } from 'rxjs/operators';
import { LSessionRegisteredUserInfo, LSessionRegistrationModel } from '../lsession';
import { createRPC } from './api.service.module';
import { LSessionRegistrationController } from './route/backend.route.definition';

@Injectable({
    providedIn: 'root',
})
export class LsRegistrationService {
    server = new LSessionRegistrationController();

    loggedInMI$: Map<string, ReplaySubject<LSessionRegistrationModel>> = new Map(); // login user member information by session id
    lsRegstrations$: Map<string, ReplaySubject<LSessionRegistrationModel[]>> = new Map(); // list of session members by session id

    constructor(private http: HttpClient) {}

    init(lsId: string) {
        if (this.loggedInMI$.get(lsId)) return;

        this.loggedInMI$.set(lsId, new ReplaySubject(1));
        this.lsRegstrations$.set(lsId, new ReplaySubject(1));
    }

    destroy(lsId: string) {
        this.loggedInMI$.get(lsId).complete();
        this.loggedInMI$.delete(lsId);

        this.lsRegstrations$.get(lsId).complete();
        this.lsRegstrations$.delete(lsId);
    }

    /**
     * Register and reload the member information of the current login user
     * @param lsId
     * @returns
     */
    register(lsId: string): Observable<any> {
        return createRPC<any>(this.server.registerLSession)(this.http, {}, lsId).pipe(
            take(1),
            map(res => res.body),
            mergeMap(result => this.loadLoggedInRegistrationInfo(lsId))
        );
    }

    /**
     * Unregister and reload the member information of the current login user
     * @param lsId id of the registration (note, not the id of the lsId)
     * @returns
     */
    unregister(lsId): Observable<any> {
        return this.loggedInMI$.get(lsId).pipe(
            mergeMap(member => createRPC<any>(this.server.unregisterLSession)(this.http, {}, member.id)),
            take(1),
            map(res => res.body),
            mergeMap(result => this.loadLoggedInRegistrationInfo(lsId))
        );
    }

    getRegistrationById(id: string): Observable<LSessionRegistrationModel> {
        return createRPC<LSessionRegistrationModel>(this.server.getRegistrationById)(this.http, {}, id).pipe(
            map(res => res.body)
        );
    }

    /**
     * Get member information of the login user of the lsession with provided id
     * @param lsId
     * @returns
     */
    loadLoggedInRegistrationInfo(lsId: string): Observable<LSessionRegistrationModel> {
        return createRPC<LSessionRegistrationModel>(this.server.getLoggedInRegistration)(this.http, {}, lsId).pipe(
            take(1),
            map(res => {
                this.loggedInMI$.get(lsId).next(res.body);
                return res.body;
            }),
            catchError(err => {
                this.loggedInMI$.get(lsId).next(null);
                return of(null);
            })
        );
    }

    loadRegisteredUsers(lsId: string): Observable<LSessionRegisteredUserInfo[]> {
        return createRPC<{
            registeredUsers: LSessionRegisteredUserInfo[];
        }>(this.server.getRegisteredUsers)(this.http, {}, lsId).pipe(
            take(1),
            map(res => {
                return res.body.registeredUsers;
            }),
            catchError(err => {
                return of([]);
            })
        );
    }

    loadRegistrationsByLsId(lsId: string): Observable<LSessionRegistrationModel[]> {
        return createRPC<LSessionRegistrationModel[]>(this.server.getRegistrationsByLsId)(this.http, {}, lsId).pipe(
            take(1),
            map(res => {
                this.lsRegstrations$.get(lsId).next(res.body);
                return res.body;
            }),
            catchError(err => {
                this.lsRegstrations$.get(lsId).next(null);
                return of(null);
            })
        );
    }
}

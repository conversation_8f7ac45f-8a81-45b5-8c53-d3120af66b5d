import { CommonModule } from '@angular/common';
import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';
import { TooltipComponent } from '@viclass/portal.common';
import { ClassroomCoordinator } from '@viclass/editor.coordinator/classroom';
import { WrappingBoardViewportManager } from '@viclass/editor.coordinator/common';
import {
    BehaviorSubject,
    debounceTime,
    finalize,
    firstValueFrom,
    map,
    Observable,
    Subject,
    Subscription,
    switchMap,
    tap,
} from 'rxjs';
import { MemberAvatarViewModel } from '../../../model';
import { ClassroomMarkerService } from '../../classroom.marker.service';
import { CoordStatesService } from '../../coord.state.service';
import { OnlineStateService } from '../../online.state.service';
import { ShareScreenService } from '../share-screen.service';
import { ShareScreenComponent } from '../share-screen/share-screen.component';

@Component({
    selector: 'view-full-screen',
    templateUrl: './view-full-screen.component.html',
    standalone: true,
    imports: [CommonModule, TooltipComponent],
    styleUrls: ['./view-full-screen.component.css'],
})
export class ViewFullScreenComponent {
    private _curZoom: number;
    private _commonToolbarEnable = [];
    private _classroomToolbarEnable = [];

    private _resizeSourceSub: Subscription;
    private _sizeObs: ResizeObserver;
    private _resizeSource: Subject<any> = new Subject();
    private _parentViewport: HTMLElement | SVGElement;

    // _curViewportId stores the currently active viewport ID for tracking viewport context
    // _detectChangeViewportSub holds the subscription to viewport change events for cleanup and management
    private _curViewportId: string = null;
    private _detectChangeViewportSub: Subscription;

    private _shareScreenDocUpdateSub: Subscription;

    isResizing$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    shareScreenComp: ShareScreenComponent;

    @ViewChild('viewportContainerElement', { static: true }) viewportContainerElement!: ElementRef<SVGElement>;

    constructor(
        public shareScreenService: ShareScreenService,
        public classroomMarkerService: ClassroomMarkerService,
        public coordStatesService: CoordStatesService,
        public onlineStateService: OnlineStateService,
        public markerS: ClassroomMarkerService
    ) {
        // Listen for viewport changes from coordStatesService
        // If it's the first load or the same viewport is selected again, just update _curViewportId
        // Otherwise, trigger close() to handle viewport change (e.g., detach or clean up resources)
        this._detectChangeViewportSub = this.coordStatesService.selected$.subscribe(vpId => {
            const isFirstLoad = !this._curViewportId && vpId;
            const isSameViewport = this._curViewportId && vpId === this._curViewportId;

            if (isFirstLoad || isSameViewport) {
                this._curViewportId = vpId;
                return;
            }

            this.close();
        });
    }

    @HostListener('document:keydown.escape', ['$event'])
    handleEscape(event: KeyboardEvent) {
        this.close();
    }

    ngAfterViewInit() {
        this._sizeObs = new ResizeObserver(entries => this._resizeSource.next(entries));
        this._resizeSourceSub = this._resizeSource
            .pipe(
                debounceTime(300),
                map(() => this.resize())
            )
            .subscribe();

        this._sizeObs.observe(this.viewportContainerElement.nativeElement);
    }

    ngOnDestroy() {
        if (this._resizeSourceSub) {
            this._resizeSourceSub.unsubscribe();
        }
        if (this._sizeObs) {
            this._sizeObs.disconnect();
        }

        this.detachViewportLayer();
        this._detectChangeViewportSub.unsubscribe();

        if (this._shareScreenDocUpdateSub) {
            this._shareScreenDocUpdateSub.unsubscribe();
        }
    }

    /**
     * Resizes the active viewport to fit the shared screen layer,
     * recalculating its position and zoom level based on current coordinator state.
     */
    private async resize() {
        const coord = await firstValueFrom(this.onlineStateService.coordinator$);
        const viewport = coord.activeViewport;
        const shareScreenEditor = coord.editorByType('ShareScreenEditor');

        const docLocalId = this.shareScreenComp.docLocalId;

        const coordState = this.coordStatesService.get(viewport.id);
        if (!coordState) return;

        const layer = coordState.layers.get(`${shareScreenEditor.id}_${docLocalId}_1`);

        const { width, height, centerX, centerY } = this.calculatePosition(layer.position);
        const zoomLevel = this.calculateZoomLevel(width, height);

        (viewport as WrappingBoardViewportManager).zoom(zoomLevel);
        (viewport as WrappingBoardViewportManager).lookAt({ x: centerX, y: centerY });
    }

    async ngOnInit(): Promise<void> {
        this.shareScreenComp = this.shareScreenService.curFullScreen$.value;
        this._shareScreenDocUpdateSub = this.shareScreenService.curFullScreenUpdateDoc$
            .pipe(
                tap(() => this.isResizing$.next(true)),
                debounceTime(300),
                switchMap(() => {
                    return new Observable<void>(observer => {
                        this.resize();
                        observer.next();
                        observer.complete();
                    }).pipe(
                        finalize(() => {
                            this.isResizing$.next(false);
                        })
                    );
                })
            )
            .subscribe();

        await this.attachViewportLayer();
    }

    /**
     * Calculates the width, height, and center coordinates of a rectangle
     * based on an array of 4 coordinates [x1, y1, x2, y2].
     * Adjusts the vertical center (centerY) to account for the difference
     * between the full window height and the actual container height,
     * then subtracts an additional offset of 7 pixels.
     *
     * @param position - Array of coordinates [x1, y1, x2, y2]
     * @returns An object containing width, height, centerX, and centerY
     */
    private calculatePosition(position: number[]) {
        const width = Math.abs(position[2] - position[0]);
        const height = Math.abs(position[3] - position[1]);
        const centerX = (position[2] + position[0]) / 2;
        const centerY = (position[3] + position[1]) / 2;
        return { width, height, centerX, centerY };
    }

    private calculateZoomLevel(width: number, height: number) {
        const videoWidth = this.viewportContainerElement.nativeElement.clientWidth;
        const videoHeight = this.viewportContainerElement.nativeElement.clientHeight;
        const scaleWidth = width / videoWidth;
        const scaleHeight = height / videoHeight;
        return Math.max(scaleWidth, scaleHeight);
    }

    /**
     * when full view is zoomed in viewport, so will turn off all classroom tools and turn on some necessary tools
     * @param coord
     */
    private disableClassroomToolbar(coord: ClassroomCoordinator) {
        const commonToolbar = coord.getCommonToolbar(coord.activeViewport.id);
        const classToolbar = coord.getClassroomToolbar(coord.activeViewport.id);
        commonToolbar.tools.forEach(t => {
            if (commonToolbar.isToolDisable(t.toolType)) return;

            this._commonToolbarEnable.push(t.toolType);
            commonToolbar.disableTool(t.toolType);
        });
        classToolbar.tools.forEach(t => {
            if (classToolbar.isToolDisable(t.toolType)) return;

            this._classroomToolbarEnable.push(t.toolType);
            classToolbar.disableTool(t.toolType);
        });
    }

    /**
     * After turning off full view, it will re-enable the tools that were disabled when full view was previously enabled.
     * @param coord
     */
    private enableClassroomToolbar(coord: ClassroomCoordinator) {
        const commonToolbar = coord.getCommonToolbar(coord.activeViewport.id);
        const classToolbar = coord.getClassroomToolbar(coord.activeViewport.id);

        this._commonToolbarEnable.forEach(t => commonToolbar.enableTool(t));
        this._classroomToolbarEnable.forEach(t => classToolbar.enableTool(t));

        this._classroomToolbarEnable = [];
        this._commonToolbarEnable = [];
    }

    /**
     * When entering full view, it will attach the current viewport to the viewport container,
     * disable the tools on the common toolbar and classroom toolbar, and enable the markertool.
     */
    private async attachViewportLayer() {
        const coord = await firstValueFrom(this.onlineStateService.coordinator$);
        const viewport = coord.getViewportManager(this._curViewportId);

        if (!viewport) throw new Error('Viewport not found');

        this.disableClassroomToolbar(coord);
        coord.getClassroomToolbar(viewport.id).enableTool('markertool');

        this._curZoom = viewport.getScreenZoom();

        this._parentViewport = viewport.viewportRoot.parentElement;
        this.viewportContainerElement.nativeElement.appendChild(viewport.viewportRoot);

        this.resize();
    }

    /**
     * Detaches the viewport from the viewport container, re-enables the tools on the common toolbar and classroom toolbar,
     * and removes the markertool. Also, it will restore the original viewport attributes and append the viewport to its original
     * position.
     */
    private async detachViewportLayer() {
        this.viewportContainerElement.nativeElement.innerHTML = '';

        const coord = await firstValueFrom(this.onlineStateService.coordinator$);
        const viewport = coord.getViewportManager(this._curViewportId);

        if (!viewport) return;

        this.enableClassroomToolbar(coord);

        this._parentViewport.prepend(viewport.viewportRoot);
        viewport.zoom(this._curZoom);
    }

    get avatarModel$(): Observable<MemberAvatarViewModel> {
        return this.shareScreenComp?.avatarModel$;
    }

    async close() {
        if (this.shareScreenComp) {
            this.shareScreenService.curFullScreen$.next(null);
            this.shareScreenComp = null;
        }
    }
}

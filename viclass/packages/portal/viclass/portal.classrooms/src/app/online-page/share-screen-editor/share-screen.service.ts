import { ComponentRef, createComponent, EnvironmentInjector, Injectable } from '@angular/core';
import {
    CoordinatorEvent,
    DOMElementLayerCtrl,
    EditorAddedCES,
    MuitlDocInfoCES,
    VDocCtrl,
    VEventListener,
    ViewportId,
} from '@viclass/editor.core';
import { LSessionRegistrationModel, NotificationService, UserService } from '@viclass/portal.common';
import JitsiLocalTrack from 'lib-jitsi-meet/esm/modules/RTC/JitsiLocalTrack';
import JitsiTrack from 'lib-jitsi-meet/esm/modules/RTC/JitsiTrack';
import {
    BehaviorSubject,
    combineLatest,
    debounceTime,
    distinctUntilChanged,
    EMPTY,
    firstValueFrom,
    map,
    Observable,
    Subject,
    Subscription,
    switchMap,
} from 'rxjs';
import { MemberActionEvent } from '../../model';
import { ActivityStateService } from '../activity.state.service';
import { BoardActionListener } from '../board.action.listener';
import {
    ClassroomConferenceService,
    ClassroomConferenceServiceEvent,
} from '../conference/classroom.conference.service';
import { CoordStatesService } from '../coord.state.service';
import { EventNotiService } from '../event-notification.component/event.service';
import { MemberActionListener } from '../member.action.listener';
import { MemberStateService } from '../member.state.service';
import { OnlineStateService } from '../online.state.service';
import { UserActionListener } from '../user.action.listener';
import { ShareScreenDocCtrl } from './docs/sharescreen.doc.ctrl';
import { ShareScreenComponent } from './share-screen/share-screen.component';
import { ShareScreenCRUDocEvent, ShareScreenEditor, UserId } from './sharescreen.editor';
import { ClassroomViewportManager } from '@viclass/editor.coordinator/classroom';

/**
 * Represents the information related to a screen-sharing session.
 * This includes the screen track, user details, associated component,
 * video rendering element, viewport reference, subscription for managing
 * the track, and a reactive state to determine if the screen share
 * should also appear in the camera area.
 */
export type ShareScreenInfo = {
    screenTrack: JitsiTrack;
    userId: UserId;
    shareScreenComp: ShareScreenComponent;
    videoElement: HTMLVideoElement;
    viewportId: ViewportId;
    subTrack: Subscription;
    isShowInCameraArea: BehaviorSubject<boolean>;
    localId: number;
    docCtrl: ShareScreenDocCtrl;
};

@Injectable({ providedIn: 'root' })
export class ShareScreenService {
    /**
     * BehaviorSubject managing active shared screens.
     * Holds an array of `ShareScreenInfo` objects, representing users currently sharing their screens.
     */
    private _sharedScreens: BehaviorSubject<ShareScreenInfo[]> = new BehaviorSubject([]);

    /**
     * Tracks the current full-screen shared screen component.
     */
    public curFullScreen$: BehaviorSubject<ShareScreenComponent | null> = new BehaviorSubject(null);

    /**
     * Tracks the current shared screen document controller.
     * Emits `null` when no screen document is being shared.
     */
    public curFullScreenUpdateDoc$: BehaviorSubject<ShareScreenDocCtrl | null> = new BehaviorSubject(null);

    private _shareScreenEditor$ = new BehaviorSubject<ShareScreenEditor>(null);

    // Indicates whether the user is currently in the process of selecting a screen to share
    public selectingScreenToShare$ = new BehaviorSubject<boolean>(false);

    constructor(
        private readonly us: UserService,
        private readonly injector: EnvironmentInjector,
        private readonly eventNotiS: EventNotiService,
        private readonly confS: ClassroomConferenceService,
        private readonly onlineStateS: OnlineStateService,
        private readonly coordStateS: CoordStatesService,
        private readonly memberStateS: MemberStateService,
        private readonly memberActionListener: MemberActionListener,
        private readonly activityStateS: ActivityStateService,
        private readonly userActionListener: UserActionListener,
        private readonly boardTabListener: BoardActionListener,
        private readonly notificationService: NotificationService
    ) {
        this.init();
    }

    async init() {
        this.listenersConferencesService();

        /**
         * Updates the display state of shared screens based on the selected viewport.
         * - Shows the screen in the camera area if its viewport ID does not match the selected one.
         * - Moves the video element if its viewport ID matches the selected one.
         */
        combineLatest([this.coordStateS.selected$, this.sharedScreens$]).subscribe(([vpId, shareScreenInfos]) => {
            shareScreenInfos.forEach(shareScreenInfo => {
                if (shareScreenInfo.viewportId !== vpId && !shareScreenInfo.isShowInCameraArea.value) {
                    shareScreenInfo.isShowInCameraArea.next(true);
                } else if (shareScreenInfo.viewportId === vpId && shareScreenInfo.isShowInCameraArea.value) {
                    shareScreenInfo.shareScreenComp.restoreVideoElement(shareScreenInfo.videoElement);
                    shareScreenInfo.isShowInCameraArea.next(false);
                }
            });
        });

        /**
         * Subscribes to rejection of screen sharing and mutes the local screen share if applicable.
         */
        this.memberStateS.rejectShareScreen$.subscribe(this.rejectShareScreenHandler.bind(this));

        /**
         * Subscribes to screen share acceptance and starts sharing if the user is the presenter.
         */
        this.memberStateS.acceptShareScreen$.subscribe(this.acceptShareScreenHandler.bind(this));

        /**
         * Subscribes to the shareScreenRemoved$ observable to handle the end of a user's screen-sharing session.
         */
        this.memberStateS.shareScreenRemoved$.subscribe(this.shareScreenRemovedHandler.bind(this));

        /**
         * // Registers a custom event handler to listen for conference-level events
         */
        this.confS.registerConferenceEventListener(new this.conferenceEventHandler(this));
    }

    async removeAllShareScreenDocs(vpId: ViewportId) {
        const sharedScreens = this._sharedScreens.value;
        if (sharedScreens.length === 0) return;

        // Group by viewportId
        const groupByVP: Map<ViewportId, ShareScreenDocCtrl[]> = new Map();

        for (const info of sharedScreens) {
            if (!groupByVP.has(info.viewportId)) {
                groupByVP.set(info.viewportId, []);
            }
            groupByVP.get(info.viewportId)!.push(info.docCtrl);
        }

        const docCtrls = groupByVP.get(vpId);
        if (!docCtrls || docCtrls.length === 0) return;

        await this.shareScreenEditor.removeShareScreenDocs(vpId, docCtrls, false);
    }

    private rejectShareScreenHandler(userId) {
        const screenTrack = this.confS.localStream?.value?.screenTrack?.value as JitsiLocalTrack;
        const localStreamUserId = this.confS.localStream?.value?.userId;
        if (localStreamUserId && screenTrack && userId === localStreamUserId && !screenTrack.isMuted()) {
            this.confS.turnOffShareScreen();
        }
    }

    /**
     * Handles the acceptance of a screen-sharing request.
     *
     * @param userId - The ID of the user requesting to share their screen.
     *
     * This function checks if the current user (`curUser`) is the same as the `userId` requesting
     * screen sharing. If they match, the function:
     * - Create new share screen document
     * - Turns on screen sharing using `this.confS.turnOnShareScreen()`.
     * - Selects the currently presenting board (`presentVPId`) and triggers a board selection event.
     * */
    private async acceptShareScreenHandler(userId) {
        if (this.selectingScreenToShare$.value) return;

        try {
            this.selectingScreenToShare$.next(true);
            const curUser = this.us.curUser$.value;
            const isSelf = curUser.id === userId;

            if (isSelf) {
                const presentVPId = await firstValueFrom(this.coordStateS.presenting$);

                this.createShareScreenDoc(userId);
                await this.confS.turnOnShareScreen();
                this.boardTabListener.boardActionEvent$.next({
                    type: 'select-board',
                    id: presentVPId,
                });
            }
        } catch (error) {
            console.log(error);
        } finally {
            this.selectingScreenToShare$.next(false);
        }
    }

    /**
     *
     * @param userId
     * Delete the document in internal when the server notifies that the document on the server should be deleted
     * because the user who was sharing their screen has left the class
     * @returns
     */
    private shareScreenRemovedHandler(userId) {
        if (!this.getSharedScreen(userId)) return;

        const vpId = this.getSharedScreen(userId).viewportId;
        const localId = this.getSharedScreen(userId).localId;
        this.shareScreenEditor.internalRemoveDoc(vpId, localId);
    }

    /**
     * Handles the acceptance of a user's request to start screen sharing.
     * an event is emitted to notify that screen sharing has been accepted.
     */
    async acceptShareScreen(userId: UserId, userRegId) {
        const isOwner = await firstValueFrom(this.onlineStateS.isOwner$);
        if (!isOwner) return;

        const event: MemberActionEvent = {
            action: 'accept-share-screen',
            regId: userRegId,
            activityId: this.activityStateS.requestPresentationActivity$.value?.id,
        };
        this.memberActionListener.memberActionEvent$.next(event);
    }

    /**
     * Handles the rejection of a user's screen sharing request.
     * Only the owner is allowed to perform this action.
     * Emits an event to notify that the screen sharing request has been rejected.
     */
    async rejectShareScreen(userId: UserId, userRegId: string) {
        const isOwner = await firstValueFrom(this.onlineStateS.isOwner$);
        if (!isOwner) return;

        const event: MemberActionEvent = {
            action: 'reject-share-screen',
            regId: userRegId,
        };
        this.memberActionListener.memberActionEvent$.next(event);
    }

    /**
     * Sends a request event indicating that the current user wants to start screen sharing.
     */
    async requestShareScreen() {
        this.userActionListener.userActionEvent$.next('req-share-screen');
    }

    /**
     * Cancels the share screen request. If the user is currently sharing, remove the share screen doc.
     * Otherwise, emit a cancel-share-screen event to stop the pending share screen request.
     * @param userId
     * @param userRegId
     */
    async cancelShareScreen(userId: UserId, userRegId: string) {
        const curUser = this.us.curUser$.value;
        if (curUser.id === userId && this.getSharedScreen(userId)) await this.removeShareScreenDoc(userId);
        else {
            this.userActionListener.userActionEvent$.next('cancel-share-screen');
        }
    }

    /**
     * Provides an observable stream of all active shared screen sessions.
     *
     * @returns An observable that emits a list of `ShareScreenInfo` objects.
     */
    get sharedScreens$(): Observable<ShareScreenInfo[]> {
        return this._sharedScreens;
    }

    /**
     * Determines whether the current user is the presenter.
     *
     * @returns An observable that emits `true` if the user is presenting, otherwise `false`.
     */
    get isPresenter$(): Observable<boolean> {
        return this.us.curUser$.pipe(
            map(user => {
                // Check if the user's activity status is 'PRESENTING'
                return this.memberStateS.memberByUserId$(user.id).value?.userState.raiseHandStatus === 'PRESENTING';
            })
        );
    }

    get curMember$(): Observable<LSessionRegistrationModel> {
        return this.memberStateS.members$.pipe(
            map(members => members.find(m => m.profile.id === this.us.curUser$?.value?.id))
        );
    }

    /**
     * Retrieves screen-sharing information for a specific user.
     *
     * @param userId - The ID of the user whose screen-sharing info is requested.
     * @returns The `ShareScreenInfo` object for the user, or `undefined` if not found.
     */
    getSharedScreen(userId: UserId): ShareScreenInfo | undefined {
        return this._sharedScreens.value.find(s => s.userId === userId);
    }

    /**
     * Adds new screen-sharing information to the shared screen manager.
     *
     * @param info - The `ShareScreenInfo` object to be added.
     */
    private addSharedScreen(info: ShareScreenInfo) {
        // Append the new screen-sharing info and update the observable state
        this._sharedScreens.next([...this._sharedScreens.value, info]);
    }

    /**
     * Removes screen-sharing information for a specific user.
     *
     * @param userId - The ID of the user whose screen-sharing info should be removed.
     */
    private removeSharedScreen(userId: UserId) {
        // Filter out the user's screen-sharing info and update the observable state
        this._sharedScreens.next(this._sharedScreens.value.filter(s => s.userId !== userId));
    }

    /**
     * Listens for changes in conference streams and manages screen-sharing video attachments.
     *
     * This function subscribes to remote and local streams, ensuring shared screens are properly detected
     * and video elements are attached when necessary.
     */
    private listenersConferencesService() {
        /**
         * Subscribes to localStream changes to manage screen-sharing video elements.
         */
        this.confS.localStream.subscribe(stream => {
            if (
                stream &&
                stream.screenTrack.value &&
                this.getSharedScreen(stream.userId) // Ensure the user has screen-sharing info
            ) {
                if (this.getSharedScreen(stream.userId).screenTrack) this._detachVideoElement(stream.userId);
                this._attachVideoElement(stream.screenTrack, stream.userId);
            }
        });
        // Subscribe to remote and local streams and process them
        this.confS.remoteStreams.subscribe(streams => {
            // Iterate through remote streams to check for screen-sharing tracks
            streams.forEach(stream => {
                if (
                    this.getSharedScreen(stream.userId) && // Ensure the user has screen-sharing info
                    !this.getSharedScreen(stream.userId).screenTrack && // Ensure no track is already attached
                    stream.screenTrack
                ) {
                    // Attach the screen-sharing video element
                    this._attachVideoElement(stream.screenTrack, stream.userId);
                }
            });
        });

        // Subscribe to updates in the share screen manager
        this._sharedScreens.subscribe(shareScreenInfos => {
            shareScreenInfos.forEach(shareScreenInfo => {
                if (
                    !shareScreenInfo.screenTrack && // Ensure no existing screen track is assigned
                    this.confS.getStreamByVid(shareScreenInfo.userId) && // Ensure the user has an active stream
                    this.confS.getStreamByVid(shareScreenInfo.userId).screenTrack // Ensure a valid screen-sharing track exists
                ) {
                    // Attach the screen-sharing video element for the user
                    this._attachVideoElement(
                        this.confS.getStreamByVid(shareScreenInfo.userId).screenTrack,
                        shareScreenInfo.userId
                    );
                }
            });
        });
    }

    /**
     * Initializes and manages the resize handling logic for a shared screen video element.
     *
     * This function ensures the shared screen's layout is kept in sync with video size changes
     * by updating the corresponding document boundary when necessary.
     *
     * Cleanup logic is also registered to avoid memory leaks when the component is destroyed.
     *
     * @param componentRef - Reference to the ShareScreenComponent.
     * @param videoElement - The HTML video element being monitored for size changes.
     * @param docCtrl - The controller managing the shared screen document state.
     */
    setupVideoResizeHandler(
        componentRef: ComponentRef<ShareScreenComponent>,
        videoElement: HTMLVideoElement,
        docCtrl: VDocCtrl
    ) {
        const layerCtrl = docCtrl.getLayers()[0] as DOMElementLayerCtrl;
        const vpId = docCtrl.state.globalId?.split('_')[1];
        const resize$ = new Subject<{ width: number; height: number }>();

        // Emit video size whenever resized
        let resizeHandler = () => {
            const { videoWidth, videoHeight } = videoElement;
            resize$.next({ width: videoWidth, height: videoHeight });
        };

        videoElement.onresize = resizeHandler;

        const sub = resize$
            .pipe(
                debounceTime(200),
                distinctUntilChanged((a, b) => a.width / a.height === b.width / b.height),
                map(({ width, height }) => {
                    const { start, height: layerHeight } = layerCtrl.boundary;
                    const newWidth = (layerHeight / height) * width;
                    const newHeight = layerHeight;

                    return {
                        boundary: {
                            start,
                            end: { x: start.x + newWidth, y: start.y - newHeight },
                            width: newWidth,
                            height: newHeight,
                        },
                        aspectRatio: newWidth / newHeight,
                    };
                }),
                switchMap(({ boundary, aspectRatio }) => {
                    const docGlobalId = docCtrl.state.globalId;
                    const userId = docGlobalId?.split('_')[3];
                    const currentUserId = this.us.curUser$.value.id;

                    const oldRatio = layerCtrl.boundary.width / layerCtrl.boundary.height;
                    const isRatioChange = Math.abs(oldRatio - aspectRatio) > 0.01;

                    if (isRatioChange && currentUserId === userId) {
                        return this.shareScreenEditor.saveAndSyncResizeScreenShare(
                            docGlobalId,
                            boundary,
                            docCtrl.state.id,
                            vpId
                        );
                    } else {
                        return EMPTY;
                    }
                })
            )
            .subscribe();

        // Cleanup: unsubscribe and remove the resize handler when the component is destroyed
        componentRef.onDestroy(() => {
            sub.unsubscribe();
            if (videoElement && resizeHandler) {
                videoElement.onresize = null;
                resizeHandler = null;
            }
            resize$.complete();
        });
    }

    /**
     * Creates and initializes a `ShareScreenComponent` for a shared screen document.
     *
     * @param docCtrl - The document controller associated with the shared screen.
     * @param onViewFullScreen - A callback function that handles full-screen viewing of the shared screen.
     */
    private async createComponent(docCtrl: VDocCtrl, onViewFullScreen: (value: string) => void) {
        if (!docCtrl || !docCtrl.state.globalId) return;
        const layerCtrl = docCtrl.getLayers()[0] as DOMElementLayerCtrl;
        const userId = docCtrl.state.globalId?.split('_')[3];
        const vpId = docCtrl.state.globalId?.split('_')[1];
        const docLocalId = docCtrl.state.id;

        // Create ShareScreenComponent dynamically
        const componentRef = createComponent(ShareScreenComponent, {
            // Provide the environment injector
            environmentInjector: this.injector.get(EnvironmentInjector),
            // Attach to the target DOM element
            hostElement: layerCtrl.domEl,
        });

        // Assign component instance to the document controller
        (docCtrl as ShareScreenDocCtrl).setComponent(componentRef.instance);

        // Assign properties to the new shared screen component instance
        componentRef.instance.userId = userId;
        componentRef.instance.docLocalId = docLocalId;
        componentRef.instance.avatarModel$ = this.eventNotiS.avatarModel$(userId);
        componentRef.instance.zoomEventEmitter$ = (docCtrl.viewport as ClassroomViewportManager).zoomEventEmitter();

        // Subscribe to the full-screen view event
        componentRef.instance.onViewFullScreen.subscribe(onViewFullScreen);

        await new Promise(resolve => setTimeout(resolve, 300));

        // Listen for when the video element is ready within the ShareScreenComponent
        componentRef.instance.videoElementReady.subscribe(videoElement => {
            this.setupVideoResizeHandler(componentRef, videoElement, docCtrl);

            /**
             * Assigns a video element to an existing shared screen or adds a new shared screen if not found.
             * - If the shared screen exists but has no video element, it assigns the provided `videoElement`.
             * - If the shared screen does not exist, it creates a new one with the given parameters.
             */
            if (this.getSharedScreen(userId) && !this.getSharedScreen(userId).videoElement && videoElement) {
                this.getSharedScreen(userId).videoElement = videoElement;
            } else if (!this.getSharedScreen(userId) && videoElement) {
                this.addSharedScreen({
                    screenTrack: null,
                    viewportId: vpId,
                    shareScreenComp: componentRef.instance,
                    userId: userId,
                    subTrack: null,
                    videoElement: videoElement,
                    isShowInCameraArea: new BehaviorSubject<boolean>(false),
                    localId: docLocalId,
                    docCtrl: docCtrl as ShareScreenDocCtrl,
                });
            }
        });

        if (!this.getSharedScreen(userId)) {
            this.addSharedScreen({
                screenTrack: null,
                viewportId: vpId,
                shareScreenComp: componentRef.instance,
                userId: userId,
                subTrack: null,
                videoElement: null,
                isShowInCameraArea: new BehaviorSubject<boolean>(false),
                localId: docLocalId,
                docCtrl: docCtrl as ShareScreenDocCtrl,
            });
        }
        componentRef.changeDetectorRef.detectChanges();
    }

    /**
     * Removes a shared screen component and detaches its associated video track.
     * If the user is the local participant, it also turns off screen sharing.
     *
     * @param globalId - The unique global identifier for the shared screen component.
     */
    private async removeComponent(userId: string) {
        if (!userId) return;
        this._detachVideoElement(userId);
        this.removeSharedScreen(userId);

        /**
         * Resets the member's screen sharing status if it is not already set to 'NONE'.
         */
        const member = this.memberStateS.getMemberByUserId(userId);
        if (member.userState.shareScreenStatus !== 'NONE') {
            member.userState.shareScreenStatus = 'NONE';
            this.memberStateS.update(member);
        }
    }

    /**
     * Provides access to the `ShareScreenEditor` instance.
     * This is used for managing shared screen documents.
     *
     * @returns The instance of `ShareScreenEditor` retrieved from `_coord`.
     */
    get shareScreenEditor(): ShareScreenEditor {
        return this._shareScreenEditor$.value;
    }

    get shareScreenEditor$(): Observable<ShareScreenEditor> {
        return this._shareScreenEditor$.asObservable();
    }

    /**
     * Handles full-screen view for a shared screen component.
     * Updates the current full-screen state.
     *
     * @param shareScreenComp - The shared screen component to be displayed in full screen.
     */
    private onViewFullScreen(shareScreenComp: ShareScreenComponent) {
        // Update the current full-screen component
        this.curFullScreen$.next(shareScreenComp);
    }

    /**
     * Attaches a Jitsi video track to a video element for screen sharing.
     *
     * @param trackObs - A BehaviorSubject that emits the Jitsi video track.
     * @param userId - The ID of the user whose screen sharing video should be attached.
     */
    private _attachVideoElement(trackObs: BehaviorSubject<JitsiTrack>, userId: string) {
        // Subscribe to track updates and attach the video track when available
        this.getSharedScreen(userId).subTrack = trackObs.subscribe(track => {
            if (track) {
                // Retrieve the video element associated with the shared screen
                const video = this.getSharedScreen(userId).shareScreenComp.videoElementRef.nativeElement;

                if (!track) throw new Error('track is null'); // Safety check

                track.attach(video); // Attach the video track to the video element
                this.getSharedScreen(userId).screenTrack = track; // Store the track reference
            } else {
                console.warn('screenTrack has no valid value yet.'); // Log a warning if no valid track exists
            }
        });

        // Update the screen visibility state for the user
        const stream = this.confS.getStreamByVid(userId);
        stream.showScreen.next(true);
    }

    /**
     * Detaches a Jitsi video track from the video element and removes tracking information.
     *
     * @param userId - The ID of the user whose screen sharing video should be detached.
     */
    private _detachVideoElement(userId: string) {
        const shareScreen = this.getSharedScreen(userId);

        if (!shareScreen) return;
        // Retrieve the video element associated with the shared screen
        const video = shareScreen.shareScreenComp?.videoElementRef.nativeElement;
        const track = shareScreen.screenTrack;

        // Detach the video track from the video element
        if (video && track) track.detach(video);

        // Unsubscribe from track updates and remove stored screen-sharing info
        if (shareScreen.subTrack) shareScreen.subTrack.unsubscribe();

        // Update the screen visibility state for the user
        const stream = this.confS.getStreamByVid(userId);
        if (stream) stream.showScreen.next(false);
    }

    /**
     * Removes a shared screen document for a specific user.
     *
     * @param userId - The ID of the user whose shared screen document should be removed.
     */
    async removeShareScreenDoc(userId, isEnableDelDocConfirmation = true) {
        const user = this.getSharedScreen(userId);
        const vpId = user.viewportId;
        await this.shareScreenEditor.removeShareScreenDocs(vpId, [user.docCtrl], isEnableDelDocConfirmation);
    }

    /**
     * @param userId - The ID of the user for whom the shared screen document is created.
     *
     * This function retrieves the currently presenting board ID (`presentVPId`) from `this.coordStateS.presenting$`
     * and then calls `this.shareScreenEditor.createShareScreenDoc()` to create a shared screen document
     * associated with the given `userId` and the active presentation board.
     */
    async createShareScreenDoc(userId) {
        const presentVPId = await firstValueFrom(this.coordStateS.presenting$);
        this.shareScreenEditor.createShareScreenDoc(userId, presentVPId);
    }

    /**
     * Handles events related to shared screen documents in the video conference.
     * This class listens for document creation and removal events and manages them accordingly.
     */
    shareScreenDocHandler = class implements VEventListener<ShareScreenCRUDocEvent> {
        constructor(private shareScreenService: ShareScreenService) {}

        /**
         * Processes events related to shared screen documents.
         * Depending on the event type, it either creates or removes a shared screen document component.
         *
         * @param eventData - The event data containing the type and state of the shared screen document.
         * @returns The processed `ShareScreenDocEvent` (or a Promise if async processing is needed).
         */
        onEvent(eventData: ShareScreenCRUDocEvent): ShareScreenCRUDocEvent | Promise<ShareScreenCRUDocEvent> {
            // Extract the document controller from the event data
            const docCtrl = eventData.state as ShareScreenDocCtrl;

            switch (eventData.eventType) {
                case 'doc-created': {
                    // When a document is created, add it as a shared screen component
                    if (docCtrl instanceof ShareScreenDocCtrl) {
                        this.shareScreenService.createComponent(
                            docCtrl,
                            this.shareScreenService.onViewFullScreen.bind(this.shareScreenService) // Bind full-screen view action
                        );
                    }
                    break;
                }
                case 'doc-removed': {
                    // Extract the user ID from the global ID (assuming format contains user ID at index 3)
                    const userId = docCtrl.state.globalId?.split('_')[3];
                    // Update the current full-screen component
                    this.shareScreenService.curFullScreen$.next(null);
                    // When a document is removed, delete the corresponding shared screen component
                    this.shareScreenService.removeComponent(userId);
                    break;
                }
                case 'doc-updated': {
                    const curFullScreen = this.shareScreenService.curFullScreen$.value;
                    if (curFullScreen && docCtrl.state.id == curFullScreen.docLocalId) {
                        this.shareScreenService.curFullScreenUpdateDoc$.next(docCtrl);
                    }
                    break;
                }
                default:
                    break;
            }
            return eventData;
        }
    };

    // Handles coordinator events related to share screen functionality, such as removing share screen docs or detecting new share screen editors.
    coordEventHandler = class implements VEventListener<CoordinatorEvent> {
        constructor(private shareScreenService: ShareScreenService) {}
        onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
            const eventState = eventData.state;

            switch (eventData.eventType) {
                case 'remove-doc-state': {
                    const state = eventState as MuitlDocInfoCES;
                    const docs = state.docInfos.filter(doc => doc.editorType === 'ShareScreenEditor');
                    for (const doc of docs) {
                        const userId = doc.docGlobalId?.split('_')[3];
                        if (userId) this.shareScreenService.rejectShareScreenHandler(userId);
                    }
                    break;
                }
                case 'editor-added': {
                    const ed = (eventData.state as EditorAddedCES).editor;
                    if (ed instanceof ShareScreenEditor) {
                        this.shareScreenService._shareScreenEditor$.next(ed);
                    }
                    break;
                }
                default:
                    break;
            }
            return eventData;
        }
    };

    /**
     * Event listener for handling custom conference-level events in the classroom.
     *
     * - Listens for specific events emitted by the conferencing system.
     * - Currently handles 'stop-share-screen-local-from-browser':
     *   - If the user has an active shared screen document, it removes it without confirmation.
     *
     * This listener helps keep the shared screen state in sync when a user stops sharing directly from the browser.
     */
    conferenceEventHandler = class implements VEventListener<ClassroomConferenceServiceEvent> {
        constructor(private shareScreenService: ShareScreenService) {}
        onEvent(
            eventData: ClassroomConferenceServiceEvent
        ): ClassroomConferenceServiceEvent | Promise<ClassroomConferenceServiceEvent> {
            const state = eventData.state;

            switch (eventData.eventType) {
                case 'stop-share-screen-local-from-browser': {
                    const userId = state.userId;
                    if (this.shareScreenService.getSharedScreen(userId))
                        this.shareScreenService.removeShareScreenDoc(userId, false);
                    break;
                }

                case 'screen-sharing-has-been-stopped': {
                    this.shareScreenService.notificationService.showNotification({
                        message: 'Chia sẻ màn hình đã bị dừng',
                        status: 'error',
                    });

                    break;
                }

                default:
                    break;
            }
            return eventData;
        }
    };
}

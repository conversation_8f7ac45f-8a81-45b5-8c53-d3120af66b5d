import { CommonModule } from '@angular/common';
import {
    AfterViewChecked,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    HostListener,
    Inject,
    OnDestroy,
    OnInit,
    TransferState,
    ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import {
    ClassroomCoordinator,
    ClassRoomCoordinatorState,
    ClassroomCriticalError,
    coordErrorHandlerEmitter,
} from '@viclass/editor.coordinator/classroom';
import {
    AUTH_FLOW_CONFIG,
    AuthflowConfig,
    ClassroomUserState,
    ClickOutsideDirective,
    displayName,
    LSessionRegistrationModel,
    LSessionService,
    PlatformService,
    ProcessingRequestManager,
    requireLogin,
    ResizedElementDirective,
    ShareClassroomDialogComponent,
    SpinnerLabelComponent,
    TooltipComponent,
    UserService,
} from '@viclass/portal.common';
import {
    BehaviorSubject,
    combineLatest,
    defer,
    EMPTY,
    filter,
    firstValueFrom,
    from,
    interval,
    map,
    merge,
    mergeMap,
    Observable,
    of,
    Subject,
    Subscription,
    switchMap,
    take,
    takeLast,
    tap,
    throttleTime,
} from 'rxjs';

import { ClassroomActivity, QuickQuestionAD, RequestPresentationAD } from '../activities';
import { AppStateService } from '../app.state.service';
import { DocumentInfo, MemberActionEvent, MemberItemEventType } from '../model';
import { ClassroomNotification } from '../notifications';
import { ActivityStateService } from './activity.state.service';
import { AwarenessComponent } from './awareness/awareness.component';
import { BadgeCircleComponent } from './badge.circle/badge.circle.component';
import { BoardTabComponent } from './board-tab/board-tab.component';
import { BoardActionListener } from './board.action.listener';
import { ClassroomViewportComponent } from './classroom-viewport/classroom-viewport.component';
import { ClassroomMarkerService } from './classroom.marker.service';
import { ClassroomPresenterService } from './classroom.presenter.service';
import { ClassroomSettingService } from './classroom.setting.service';
import { ClassroomConferenceService } from './conference/classroom.conference.service';
import { MediaDeviceSetupPopupComponent } from './conference/media-device-setup-popup/media-device-setup-popup.component';
import { CoordinatorEventListener } from './coord.event.listener';
import { CoordStatesService } from './coord.state.service';
import { DocinfoStateService } from './docinfo.state.service';
import { DocumentItemComponent } from './document-item/document-item.component';
import { DocumentActionListener } from './document.action.listener';
import { classroomErrorHandlerEmitter, ClassroomErrorHandlerListener } from './error-handler';
import { EventNotificationComponent } from './event-notification.component/event-notification.component';
import { InsertDocumentComponent } from './insert-document/insert-document.component';
import { MarkerToolComponent } from './marker-tool';
import { MemberItemComponent } from './member-item/member.item.component';
import { MemberActionListener } from './member.action.listener';
import { MemberStateService } from './member.state.service';
import { NotificationItemComponent } from './notification-item/notification.item.component';
import { NotificationProcessor } from './notification.processor';
import { NotificationStateService } from './notification.state.service';
import { OnlineStateService } from './online.state.service';
import { ClassEndedPopupComponent } from './popup/class-ended-popup/class.ended.popup.component';
import { ClassNotStartPopupComponent } from './popup/class-not-start-popup/class.notstart.popup.component';
import { ConfirmDeletePagePopupComponent } from './popup/confirm-delete-page-popup/confirm.delete.page.popup.component';
import { ConfirmEndClassPopupComponent } from './popup/confirm-end-class-popup/confirm.end.class.popup.component';
import { ConfirmJoinQuickQuestionPopupComponent } from './popup/confirm-join-quick-question-popup/confirm.join.quick.question.popup.component';
import { ConfirmKickOutPopupComponent } from './popup/confirm-kick-out-popup/confirm.kick.out.popup.component';
import { ConfirmOutClassPopupComponent } from './popup/confirm-out-class-popup/confirm.out.class.popup.component';
import { ConfirmPresentPopupComponent } from './popup/confirm-present-popup/confirm.present.popup.component';
import { ConfirmPresenterSwitchMessagePopup } from './popup/confirm-presenter-switch-message-popup/confirm-presenter-switch-message-popup.component';
import { ConfirmViewportSwitchMessagePopup } from './popup/confirm-viewport-switch-message-popup/confirm-viewport-switch-message-popup.component';
import { ConnectionErrorPopupComponent } from './popup/connection-error-popup/connection.error.popup.component';
import { CreateQuickQuestionPopupComponent } from './popup/create-quick-question-popup/create.quick.question.popup';
import { JoinedInAnotherSessionPopupComponent } from './popup/joined-in-another-session/joined.in.another.session.popup.component';
import { KickedOutPopupComponent } from './popup/kicked-out-popup/kicked.out.popup.component';
import { OfflineMemberPopupComponent } from './popup/offline-popup/offline.member.popup.component';
import { OfflineOwnerPopupComponent } from './popup/offline-popup/offline.owner.popup.component';
import { OfflineUserPopupComponent } from './popup/offline-user-popup/offline.user.popup.component';
import { QuickQuestionContentComponent } from './popup/quick-question-content/quick-question-content.component';
import { SettingToolComponent } from './setting-tool';
import { ConferenceComponent } from './share-screen-editor/conference/conference.component';
import { ShareScreenService } from './share-screen-editor/share-screen.service';
import { SignalClassroomListener } from './signal.classroom.listener';
import { SignalCoordinatorListener } from './signal.coordinator.listener';
import { UserActionListener } from './user.action.listener';
import { UserStatusDetector } from './user.status.detector';
import { ViewportService } from './viewport.service';

const CHECK_LOGGEDIN = 60000;
export const INITIALIZING_REQ = 'initializing';
type SidebarMemberTab = 'confirmed' | 'raising-hand' | 'waiting-confirm';
export type SidebarTab = SidebarMemberTab | 'document' | 'notification' | 'setting' | 'markertool';
const SHOW_CORD_STATE_PARAM = 'show-cstate';

@Component({
    standalone: true,
    imports: [
        CommonModule,
        ClassroomViewportComponent,
        BoardTabComponent,
        SpinnerLabelComponent,
        MemberItemComponent,
        ConfirmPresentPopupComponent,
        CreateQuickQuestionPopupComponent,
        ConfirmJoinQuickQuestionPopupComponent,
        QuickQuestionContentComponent,
        ClickOutsideDirective,
        ResizedElementDirective,
        BadgeCircleComponent,
        NotificationItemComponent,
        OfflineOwnerPopupComponent,
        OfflineMemberPopupComponent,
        ClassEndedPopupComponent,
        ClassNotStartPopupComponent,
        ConfirmEndClassPopupComponent,
        ConfirmOutClassPopupComponent,
        ConnectionErrorPopupComponent,
        KickedOutPopupComponent,
        JoinedInAnotherSessionPopupComponent,
        ConfirmKickOutPopupComponent,
        ConfirmDeletePagePopupComponent,
        ConfirmViewportSwitchMessagePopup,
        ConfirmPresenterSwitchMessagePopup,
        DocumentItemComponent,
        MatDialogModule,
        TooltipComponent,
        SettingToolComponent,
        MarkerToolComponent,
        FormsModule,
        EventNotificationComponent,
        AwarenessComponent,
        ConferenceComponent,
        InsertDocumentComponent,
        OfflineUserPopupComponent,
    ],
    providers: [DocumentActionListener],
    selector: 'app-online-page',
    templateUrl: './online-page.component.html',
    styleUrls: ['./online-page.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OnlinePageComponent implements AfterViewChecked, OnInit, OnDestroy {
    protected displayName = displayName;

    @ViewChild('sidebar') protected sideBarRef: ElementRef<HTMLElement>;
    @ViewChild('clrControllerBar') private clrControllerBarRef: ElementRef<HTMLElement>;
    @ViewChild('vpBar') private vpBarRef: ElementRef<HTMLElement>;

    private changeSubs: Subscription[] = []; // subscription, use for unsubscribe when destroy component
    private viewportCheckSubject$ = new Subject<void>(); // Subject for throttling viewport checks
    protected sidebarTab$: BehaviorSubject<SidebarTab> = new BehaviorSubject<SidebarTab>(undefined);
    protected showViewportMenu$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    protected coordInitialized$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    readonly mediaTogglesInProgress = {
        mic: new BehaviorSubject(false),
        camera: new BehaviorSubject(false),
        screenShare: new BehaviorSubject(false),
        allMics: new BehaviorSubject(false),
    } as const;

    protected filterDocumentQuery: string;

    constructor(
        @Inject(AUTH_FLOW_CONFIG) private readonly authConf: AuthflowConfig,
        protected readonly aS: AppStateService,
        protected readonly notificationStateS: NotificationStateService,
        protected readonly activityStateS: ActivityStateService,
        protected readonly onlStateS: OnlineStateService,
        protected readonly conferenceS: ClassroomConferenceService,
        protected readonly coordStateS: CoordStatesService,
        protected readonly memberStateS: MemberStateService,
        protected readonly settingS: ClassroomSettingService,
        protected readonly presenterS: ClassroomPresenterService,
        protected readonly markerS: ClassroomMarkerService,
        protected readonly classroomErrorHandlerListener: ClassroomErrorHandlerListener,
        private readonly uS: UserService,
        private readonly prm: ProcessingRequestManager,
        private readonly coordListener: CoordinatorEventListener,
        private readonly signalClassroomListener: SignalClassroomListener,
        private readonly signalCoordListener: SignalCoordinatorListener,
        private readonly userStatusDetector: UserStatusDetector,
        private readonly notificationProcessor: NotificationProcessor,
        private readonly userActionListener: UserActionListener,
        private readonly memberActionListener: MemberActionListener,
        private readonly boardActionListener: BoardActionListener,
        private readonly documentActionListener: DocumentActionListener,
        private readonly docInfoStateS: DocinfoStateService,
        private readonly dialog: MatDialog,
        private readonly router: Router,
        private readonly route: ActivatedRoute,
        private readonly lsessionS: LSessionService,
        private readonly viewportS: ViewportService,
        private readonly shareScreenS: ShareScreenService,
        private readonly transferState: TransferState,
        private readonly platformService: PlatformService
    ) {}

    protected get isVideoActive(): Observable<any> {
        return this.conferenceS.localStream.pipe(mergeMap(s => (s ? s.hasVideo : of(false))));
    }

    protected get isMicActive(): Observable<any> {
        return this.conferenceS.localStream.pipe(mergeMap(s => (s ? s.hasAudio : of(false))));
    }

    protected get curMember$(): Observable<LSessionRegistrationModel> {
        return this.memberStateS.member$(this.aS.regUserId);
    }

    protected get creatorMember$(): Observable<LSessionRegistrationModel> {
        return combineLatest({
            details: this.lsessionS.details$.get(this.aS.lsId),
            members: this.memberStateS.members$,
        }).pipe(map(data => data.members.find(m => data.details.creatorId === m.profile.id)));
    }

    protected get canCurMemberStopPresentPresentingMember$(): Observable<boolean> {
        return combineLatest({
            curMember: this.curMember$,
            creatorMember: this.creatorMember$,
            presentingMember: this.memberStateS.presentingMember$,
        }).pipe(
            map(
                data =>
                    data.curMember?.id === data.creatorMember?.id &&
                    data.presentingMember?.id !== data.creatorMember?.id
            )
        );
    }

    async ngOnInit() {
        // Subscribe to classroom-specific error handling
        this.classroomErrorHandlerListener.subscribe([classroomErrorHandlerEmitter, coordErrorHandlerEmitter]);

        this.setupLoginCheck();

        const details = await firstValueFrom(this.onlStateS.lsDetail$);

        if (['ENDED', 'CLOSED'].includes(details.state.status)) {
            throw new ClassroomCriticalError({ type: 'CLASS_ENDED', data: true });
        }

        await firstValueFrom(this.openReviewCamera());

        this.setupHandleActivities();
        this.setupSelectedViewport();
        this.setupViewportBarAutoResize();

        // start initialization process
        await firstValueFrom(this.monitoredInitialization(this.prm));

        const coord = await firstValueFrom(this.onlStateS.coordinator$);

        this.changeSubs.push(
            this.prm
                .monitor(
                    'start-coordinator',
                    defer(() => coord.start()),
                    { parent: INITIALIZING_REQ }
                )
                .subscribe(_ => this.onlStateS.registerStopHook(() => coord.stop())),
            this.prm
                .monitor(
                    'start-userStatusDetector',
                    defer(async () => this.userStatusDetector.start(coord)),
                    { parent: INITIALIZING_REQ }
                )
                .subscribe(_ => this.onlStateS.registerStopHook(() => this.userStatusDetector.stop())),
            this.prm
                .monitor(
                    'start-notificationProcessor',
                    defer(async () => this.notificationProcessor.start()),
                    { parent: INITIALIZING_REQ }
                )
                .subscribe(),
            this.prm
                .monitor(
                    'start-userActionListener',
                    defer(async () => this.userActionListener.start()),
                    { parent: INITIALIZING_REQ }
                )
                .subscribe(),
            this.prm
                .monitor(
                    'start-userActionListener',
                    defer(async () => this.memberActionListener.start()),
                    { parent: INITIALIZING_REQ }
                )
                .subscribe(),
            this.prm
                .monitor(
                    'start-documentActionListener',
                    defer(async () => this.documentActionListener.start()),
                    { parent: INITIALIZING_REQ }
                )
                .subscribe(),
            this.prm
                .monitor(
                    'start-boardActionListener',
                    defer(async () => this.boardActionListener.start()),
                    { parent: INITIALIZING_REQ }
                )
                .subscribe(),
            // toggle classroom setting modal from classroom toolbar hotkey
            this.settingS.settingToolToggle$.subscribe(() => this.switchSideBarTab('setting'))
        );
    }

    /**
     * unsubscribe all the subscription
     */
    ngOnDestroy() {
        this.changeSubs?.map(c => c.unsubscribe());
        this.classroomErrorHandlerListener.unsubscribe();
    }

    ngAfterViewChecked() {
        // Emit to throttled subject instead of executing directly
        this.viewportCheckSubject$.next();
    }

    @HostListener('window:resize', ['$event'])
    onWindowResize() {
        requestAnimationFrame(() => this.adjustsViewportBarWidth());
    }

    /**
     * Recalculates the maximum width of the viewport bar.
     * - Determines available space by subtracting control bar width and a fixed offset.
     * - Updates the max-width style of the viewport bar element.
     */
    private adjustsViewportBarWidth() {
        const controlBarW = this.clrControllerBarRef.nativeElement.offsetWidth;
        this.vpBarRef.nativeElement.style.maxWidth = `${window.innerWidth - controlBarW - 240}px`;
    }

    /**
     * open dialog to share this classroom
     */
    protected async shareClassroom() {
        this.dialog.open(ShareClassroomDialogComponent, { data: { lsId: this.aS.lsId } });
    }

    /**
     * Handles viewport selection changes and updates the UI accordingly.
     * - Updates the URL with the selected state.
     * - Adjusts the viewport bar width dynamically.
     * - Smoothly scrolls to the corresponding tab element.
     */
    protected setupSelectedViewport() {
        this.adjustsViewportBarWidth();

        const s = this.coordStateS.selected$.subscribe((stateId: string) => {
            if (!stateId) return;

            // Update URL with selected state ID
            this.router.navigate([], {
                relativeTo: this.route,
                queryParams: { [SHOW_CORD_STATE_PARAM]: stateId },
                queryParamsHandling: 'merge',
            });

            requestAnimationFrame(() => {
                const element = document.getElementById(`board-tab-${stateId}`);
                element?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
            });
        });

        this.changeSubs.push(s);
    }

    /**
     * Sets up the editor when all viewports are ready.
     * - Waits until all viewport states are available and ready.
     * - Ensures this logic runs only once.
     * - Starts the editors and initializes the viewport.
     */
    protected setupEditorAndInitViewport(coord: ClassroomCoordinator): Observable<any> {
        return of(1).pipe(
            mergeMap(async _ => {
                await coord.startEditors();
                return coord;
            }),
            mergeMap(async coord => {
                await this.switchInitialViewport(coord);
                return coord;
            })
        );
    }

    /**
     * Handles incoming presentation requests and triggers a notification if applicable.
     * - Listens for new presentation request activities.
     * - Checks if the request is targeted to the current user.
     * - Displays a request presentation dialog if the conditions are met.
     */
    private setupHandleActivities() {
        const s = this.activityStateS.requestPresentationActivity$
            .pipe(filter(a => !!a && (a.data as RequestPresentationAD).requestedTo === this.uS.curUser$.value.id))
            .subscribe(a => this.notificationProcessor.showRequestPresentationDialog(a.id));

        this.changeSubs.push(s);
    }

    /**
     * Periodically checks if the user is logged in.
     * - Runs at a fixed interval defined by CHECK_LOGGEDIN.
     * - Calls requireLogin() to ensure session validity.
     */
    private setupLoginCheck() {
        const s = interval(CHECK_LOGGEDIN).subscribe(() =>
            requireLogin(this.uS, this.transferState, this.platformService, this.authConf, 'buổi học')
        );
        this.changeSubs.push(s);
    }

    /**
     * Sets up a check for viewport bar auto resize.
     * This will be triggered from `ngAfterViewChecked` so we need a throttle time to prevent excessive calls.
     */
    private setupViewportBarAutoResize() {
        const s = this.viewportCheckSubject$
            .pipe(throttleTime(1000, undefined, { leading: false, trailing: true }))
            .subscribe(() => {
                if (this.clrControllerBarRef?.nativeElement && this.vpBarRef?.nativeElement) {
                    // update max width of viewport bar element when it overflows
                    // need this logic, if not, the root viewport will be overflow by this element
                    if (this.checkOverflow(this.vpBarRef.nativeElement)) {
                        const controlBarW = this.clrControllerBarRef.nativeElement.offsetWidth;
                        this.vpBarRef.nativeElement.style.maxWidth = window.innerWidth - controlBarW - 240 + 'px';
                    }
                }
            });
        this.changeSubs.push(s);
    }

    /**
     * scroll element with mouse wheel delta (pixels)
     * @param event
     * @param element
     */
    protected scroll(event: any, element: HTMLElement) {
        element.scrollLeft -= event.wheelDelta;
    }

    /**
     * scroll lef element 100 pixels
     * @param element
     * @param delta
     */
    protected scrollLeft(element: HTMLElement, delta: number = 100) {
        element.scrollLeft -= delta ?? 100;
    }

    /**
     * scroll right element 100 pixels
     * @param element
     * @param delta
     */
    protected scrollRight(element: HTMLElement, delta: number = 100) {
        element.scrollLeft += delta ?? 100;
    }

    /**
     * check if element is overflow
     * @param element
     */
    protected checkOverflow(element: HTMLElement) {
        return element.offsetWidth < element.scrollWidth;
    }

    protected quickQuestion(a: ClassroomActivity): string {
        if (a.data.activityType == 'QuickQuestionAD') return (a.data as QuickQuestionAD).question;
        return undefined;
    }

    protected onMemberAction(member: LSessionRegistrationModel, type: MemberItemEventType) {
        const event: MemberActionEvent = {
            action: type,
            regId: member.id,
            activityId: this.activityStateS.requestPresentationActivity$.value?.id,
        };
        this.memberActionListener.memberActionEvent$.next(event);
    }

    protected onChangeSetting($event) {
        this.settingS.onSettingFieldChange($event);
        this.presenterS.onSettingFieldChange($event);
    }

    protected onChangeMarker($event) {
        this.markerS.onMarkerFieldChange($event);
    }

    protected curMemberStopPresentPresentingMember() {
        firstValueFrom(
            combineLatest({
                canCurMemberStopPresentPresentingMember: this.canCurMemberStopPresentPresentingMember$,
                presentingMember: this.memberStateS.presentingMember$,
            }).pipe(take(1))
        ).then(data => {
            if (!data.canCurMemberStopPresentPresentingMember) return;
            this.onMemberAction(data.presentingMember, 'stop-present');
        });
    }

    private monitoredInitialization(prm: ProcessingRequestManager) {
        return prm.monitor(
            INITIALIZING_REQ,
            combineLatest([
                // get the
                prm.monitor('load-editor-conf', this.onlStateS.loadEditorConfiguration(), { parent: INITIALIZING_REQ }),

                // create the coordinator, loading editor implementation
                prm.monitor('create-coordinator', this.onlStateS.createCoordinator(), { parent: INITIALIZING_REQ }),

                // load the room information and coordinator state data inside the coordinator
                // prm.monitor('load-coordinator-states', ),

                // add editors that are gonna be used inside the classroom
                prm.monitor(
                    'add-editors',
                    this.onlStateS.addEditors().pipe(
                        mergeMap(m => from(m.entries())),
                        mergeMap(entry =>
                            prm.monitor(`add-editor-${entry[0]}`, entry[1], {
                                parent: INITIALIZING_REQ,
                                autoClear: true,
                            })
                        ),
                        takeLast(1) // take last to ensure all the editors are added completely
                    ),
                    { parent: INITIALIZING_REQ }
                ),
                // after having the coordinator ready, we initialize room related stuff
                this.onlStateS.coordinator$.pipe(
                    take(1),
                    mergeMap(coord => {
                        // first join room
                        return prm
                            .monitor(
                                'join-room',
                                defer(() => coord.joinRoom()),
                                { parent: INITIALIZING_REQ }
                            )
                            .pipe(
                                // Wait for the lesson join to complete in order to obtain the peerId for initializing the Jitsi token.
                                mergeMap(_ =>
                                    prm.monitor(
                                        'join-conference',
                                        from(
                                            (async () => {
                                                const isNotJoined = await firstValueFrom(
                                                    this.conferenceS.callNotJoined
                                                );
                                                if (isNotJoined) {
                                                    this.conferenceS.joinCall();
                                                }
                                            })()
                                        ),
                                        {
                                            parent: INITIALIZING_REQ,
                                        }
                                    )
                                )
                            )
                            .pipe(
                                // load all members
                                mergeMap(_ =>
                                    prm.monitor('load-member-data', this.memberStateS.initializeData$(), {
                                        parent: INITIALIZING_REQ,
                                    })
                                ),
                                // then load the room information
                                mergeMap(_ =>
                                    prm.monitor('load-room-information', from(coord.loadRoomInfo()), {
                                        parent: INITIALIZING_REQ,
                                    })
                                ),
                                // once room information is ready, load all the coordinator states
                                mergeMap(_ =>
                                    prm.monitor(
                                        'load-all-coord-states',
                                        this.coordStateS.initCoordStates$(coord).pipe(
                                            tap(_ => {
                                                coord.registerCoordEventListener(this.coordListener);
                                                this.onlStateS.registerStopHook(
                                                    coord.unregisterCoordEventListener.bind(coord, this.coordListener)
                                                );
                                            }),
                                            tap(_ => this.coordInitialized$.next(true))
                                        ),
                                        {
                                            parent: INITIALIZING_REQ,
                                        }
                                    )
                                ),
                                // load all notifications
                                mergeMap(_ =>
                                    prm.monitor('load-notification-data', this.notificationStateS.initializeData$(), {
                                        parent: INITIALIZING_REQ,
                                    })
                                ),
                                // get all the online page data like notification, activities
                                mergeMap(_ =>
                                    prm.monitor('load-activity-data', this.activityStateS.initializeData$(), {
                                        parent: INITIALIZING_REQ,
                                    })
                                ),
                                // select a coordinator state to display by default
                                tap(_ => {
                                    coord.signaler.registerListener(this.signalClassroomListener);
                                    coord.signaler.registerListener(this.signalCoordListener);
                                })
                            );
                    })
                ),
                // once coordinator states are all loaded,
                // corresponding viewport component will be created (which will in turn create the viewport inside coordinator)
                // the viewport will be loaded from the onViewportCreated() method, which is triggered from the template
                prm.monitor(
                    'viewports-initialization',
                    combineLatest([
                        this.coordStateS.curStates$,
                        this.viewportS.readyViewportCount$,
                        this.onlStateS.coordinator$,
                    ])
                        .pipe(
                            filter(([state, vpReady, _]) => vpReady > 0 && state.length == vpReady),
                            take(1)
                        )
                        .pipe(mergeMap(([state, vpReady, coord]) => this.setupEditorAndInitViewport(coord)))
                        .pipe(take(1)),
                    { parent: INITIALIZING_REQ }
                ),
                /**
                 * Monitors the online status of the classroom owner and presenter.
                 *
                 * This function observes multiple observables related to the classroom session
                 * and determines if the owner or presenter goes offline. If detected, it triggers
                 * corresponding offline events.
                 *
                 * Observed Streams:
                 * 1. **Coordinator's Room Info (`roomInfoObs$`)** - Retrieves details of the classroom.
                 * 2. **Learning Session Details (`lsDetail$`)** - Gets session metadata.
                 * 3. **Classroom Members (`members$`)** - Fetches all registered users in the session.
                 *
                 * Workflow:
                 * - The code waits until `roomInfo`, `lsDetail`, and `members` are available.
                 * - It then extracts the **owner** and **presenter** from the room info.
                 * - It checks their availability status:
                 *   - If the **owner** is offline and the current user is NOT the owner → trigger `onOwnerOffline()`.
                 *   - If the **presenter** is offline and:
                 *     - The presenter is not the current user.
                 *     - The presenter is not the owner (to avoid redundant calls).
                 *     → Trigger `onPresenterOffline()`.
                 *
                 * This ensures the UI updates properly when the owner or presenter disconnects.
                 */
                prm.monitor(
                    'offline-error',
                    combineLatest([
                        this.onlStateS.coordinator$.pipe(mergeMap(c => c.roomInfoObs$)).pipe(
                            filter(c => c != null),
                            take(1)
                        ),
                        this.onlStateS.lsDetail$.pipe(take(1)),
                        this.memberStateS.members$.pipe(take(1)),
                    ]).pipe(
                        mergeMap(([roomInfo]) => {
                            const ownerMember = this.memberStateS.getMemberByUserId(roomInfo.owner);
                            const presenterMember = this.memberStateS.getMemberByUserId(roomInfo.presentingUser);
                            const currUserId = this.uS.curUser$.value.id;

                            // If the owner is offline and the current user is not the owner, trigger offline event
                            if (
                                ownerMember?.userState?.availableStatus === 'OFFLINE' &&
                                currUserId !== roomInfo.owner
                            ) {
                                this.onlStateS.onOwnerOffline();
                            }

                            // If the presenter is offline and the current user is not the presenter
                            // and the presenter is not also the owner, trigger offline event
                            if (
                                presenterMember?.userState?.availableStatus === 'OFFLINE' &&
                                currUserId !== roomInfo.presentingUser &&
                                roomInfo.presentingUser !== roomInfo.owner // Không gọi nếu presenter là owner
                            ) {
                                this.onlStateS.onPresenterOffline(presenterMember.profile.id);
                            }

                            return of(true);
                        }),
                        take(1)
                    ),
                    { parent: INITIALIZING_REQ }
                ),
            ])
        );
    }

    /**
     * Switch to the initial viewport which is specified in the URL,
     * or the presenting one, or the default one
     *
     * ! This method should be called only when all viewports are loaded
     */
    private async switchInitialViewport(coord: ClassroomCoordinator) {
        // if the URL specify a coord state to be shown,
        // switch to it, else, show the default one, which is the presenting one, or the home one
        const queryCoordStateId = this.route.snapshot.queryParamMap.get(SHOW_CORD_STATE_PARAM);
        if (this.coordStateS.get(queryCoordStateId)) {
            const vpMode = await this.onlStateS.calculateViewportMode(queryCoordStateId);
            await coord.switchViewportMode(queryCoordStateId, vpMode);
            await coord.switchViewport(queryCoordStateId);
        } else {
            const coordStateId = coord.roomInfo.presentingCoordState || coord.roomInfo.defaultCoordState;
            const vpMode = await this.onlStateS.calculateViewportMode(coordStateId);
            await coord.switchViewportMode(coordStateId, vpMode);
            await coord.switchViewport(coordStateId);
        }
    }

    private async initViewport(coord: ClassroomCoordinator, viewportComp: ClassroomViewportComponent) {
        await firstValueFrom(
            this.prm.monitor(
                'documents-loading',
                defer(async () => {
                    await viewportComp.loadAllDocuments(coord);
                    await viewportComp.loadAllDocumentsInfo(coord);
                })
            )
        );
    }

    protected hideSidebar(event: any) {
        // exclude hide sidebar when clicking on top bar
        if (!this.clrControllerBarRef.nativeElement.contains(event.target)) {
            this.sidebarTab$.next(undefined);
        }
    }

    protected onClickOutsideVpMenu(event: any, ...included: HTMLElement[]) {
        if (!included.find(el => el.contains(event.target))) this.showViewportMenu$.next(false);
    }

    protected changeVpMenuHeight(event: any) {
        const s: HTMLElement = event.source;
        const h: number = event.height;
        if (h - 110 < 700) s.style.maxHeight = h - 110 + 'px';
    }

    // update the list of created viewports
    protected async onViewportCreated(component: ClassroomViewportComponent) {
        const vpComps = this.viewportS.viewports;
        vpComps.push(component);

        this.viewportS.updateViewports(vpComps);
        this.addingBoard$.pipe(take(1)).subscribe(async v => {
            const coord = await firstValueFrom(this.onlStateS.coordinator$);
            await this.initViewport(coord, component);
            this.viewportS.updateReadyViewportCount(this.viewportS.readyViewportCount + 1);

            const vmId = component.vm.id;
            const vmState = coord.getCoordState(vmId);

            if (vmState.presenting && !this.coordStateS.isSelected(vmId)) {
                const vpMode = await this.onlStateS.calculateViewportMode(vmId);
                await coord.switchViewportMode(vmId, vpMode);
                await coord.switchViewport(vmId);
            }
        });
    }

    protected addNewBoard() {
        this.boardActionListener.boardActionEvent$.next({
            type: 'new-board',
            id: undefined,
        });
    }

    protected switchViewport(id: string) {
        this.boardActionListener.boardActionEvent$.next({
            type: 'select-board',
            id: id,
        });
    }

    protected switchSideBarTab(tab: SidebarTab) {
        if (this.sidebarTab$.value !== tab) this.sidebarTab$.next(tab);
    }

    /**
     * Subscribe to the afterClosed event to receive a callback
     */
    private openReviewCamera(): Observable<any> {
        const d = this.dialog.open(MediaDeviceSetupPopupComponent, {
            width: '900px',
            height: '500px',
            disableClose: true,
        });

        // Subscribe to the afterClosed event to receive a callback
        return d.afterClosed();
    }

    protected async toggleVideo() {
        this.mediaTogglesInProgress.camera.next(true);
        await this.conferenceS.toggleCamera();
        this.mediaTogglesInProgress.camera.next(false);
    }

    get cameraGranted(): Observable<boolean> {
        return this.conferenceS.cameraGranted$;
    }

    get micGranted(): Observable<boolean> {
        return this.conferenceS.micGranted$;
    }

    /**
     * Observable indicating whether the current member is sharing the screen.
     */
    protected get isShareScreen$(): Observable<boolean> {
        return this.curMember$.pipe(
            map(
                m =>
                    m.regStatus == 'REGISTERED' &&
                    (m.userState.shareScreenStatus == 'SHARING_SCREEN' ||
                        m.userState.shareScreenStatus == 'REQ_SHARE_SCREEN')
            )
        );
    }

    /**
     * Toggles screen sharing based on the current state and user role.
     */
    protected async toggleShareScreen() {
        this.mediaTogglesInProgress.screenShare.next(true);
        const isShareScreen = await firstValueFrom(this.isShareScreen$);
        const isOwner = await firstValueFrom(this.onlStateS.isOwner$);
        const userReg = await firstValueFrom(this.curMember$);
        const userId = userReg.profile.id;

        if (isShareScreen) {
            await this.shareScreenS.cancelShareScreen(userId, userReg.id);
            this.mediaTogglesInProgress.screenShare.next(false);
            return;
        }

        if (isOwner) await this.shareScreenS.acceptShareScreen(userId, userReg.id);
        else await this.shareScreenS.requestShareScreen();

        this.mediaTogglesInProgress.screenShare.next(false);
    }

    protected async toggleMic() {
        this.mediaTogglesInProgress.mic.next(true);
        await this.conferenceS.toggleMic();
        this.mediaTogglesInProgress.mic.next(false);
    }

    /**
     * Mutes all participants' microphones, except for the one who is currently presenting (if any).
     * While the action is in progress, it updates a status flag to indicate activity.
     */
    protected async muteAllMics() {
        try {
            // Indicate that the "mute all mics" process has started
            this.mediaTogglesInProgress.allMics.next(true);
            // Find the member who is currently marked as presenting
            const currentPresenter = this.memberStateS.filterMember(
                member => member.id && member.userState.raiseHandStatus === 'PRESENTING'
            )[0];

            // Retrieve the presenter's user ID from their member stream (if available)
            const presenterId = currentPresenter ? this.memberStateS.getMember(currentPresenter.id)?.profile?.id : null;

            // Mute all participants, excluding the presenter (if there is one)
            await this.conferenceS.muteAllMics(presenterId);
        } catch (error) {
            // Log any errors that occur during the mute operation
            console.error('Failed to mute all microphones:', error);
        } finally {
            // Mark the mute-all process as finished
            this.mediaTogglesInProgress.allMics.next(false);
        }
    }

    protected get selectingScreenToShare(): Observable<boolean> {
        return this.shareScreenS.selectingScreenToShare$;
    }

    protected eraseMyDrawing() {
        this.markerS.onEraseMyDrawing();
    }

    protected eraseOtherDrawing() {
        this.markerS.onEraseOtherDrawing();
    }

    protected newQuickQuestion() {
        this.userActionListener.userActionEvent$.next('new-question');
    }

    protected stopQuickQuestion() {
        this.userActionListener.userActionEvent$.next('stop-question');
    }

    protected emitLeaveEvent() {
        this.userActionListener.userActionEvent$.next('leave');
    }

    protected emitRaiseHandControlEvent() {
        this.curMember$.pipe(take(1)).subscribe(m => {
            switch (m.userState.raiseHandStatus) {
                case 'RAISE_HAND': {
                    this.userActionListener.userActionEvent$.next('cancel-raise-hand');
                    m.userState.raiseHandStatus = 'NONE';
                    break;
                }
                case 'PRESENTING': {
                    this.userActionListener.userActionEvent$.next('stop-present');
                    m.userState.raiseHandStatus = 'NONE';
                    break;
                }
                case 'NONE': {
                    this.userActionListener.userActionEvent$.next('raise-hand');
                    m.userState.raiseHandStatus = 'RAISE_HAND';
                    break;
                }
                default:
                    break;
            }
            this.memberStateS.update(m);
        });
    }

    protected get classroomUserState$(): Observable<ClassroomUserState> {
        return this.curMember$.pipe(map(m => m?.userState));
    }

    protected get members$(): Observable<LSessionRegistrationModel[]> {
        return this.sidebarTab$.pipe(
            switchMap(tab => {
                switch (tab) {
                    case 'confirmed':
                        return this.memberStateS.memberConfirmed$;
                    case 'raising-hand':
                        return this.memberStateS.memberRaisingHand$;
                    case 'waiting-confirm':
                        return this.memberStateS.memberWaitingConfirm$;
                    default:
                        return EMPTY;
                }
            })
        );
    }

    protected get notifications$() {
        return this.notificationStateS.notifications$.pipe(
            map(ls => ls.filter(n => n.emittedBy != this.uS.curUser$.value.id && n.message?.length > 0))
        );
    }

    protected get documents$(): Observable<DocumentInfo[]> {
        return this.coordStateS.selected$.pipe(mergeMap(vpId => this.docInfoStateS.obsByVP$(vpId)));
    }

    protected get validDocuments$(): Observable<DocumentInfo[]> {
        return this.documents$.pipe(
            map(docs =>
                docs.filter(
                    d =>
                        d.details.isValid &&
                        (this.filterDocumentQuery?.length
                            ? d.details.docName.toLowerCase().includes(this.filterDocumentQuery.toLocaleLowerCase())
                            : true)
                )
            )
        );
    }

    protected get newNotifyMember$() {
        return merge(this.memberStateS.newMemberWaitingConfirm$, this.memberStateS.newMemberRaisingHand$);
    }

    protected get newNotifications$(): Observable<number> {
        return this.notificationStateS.newNotification$;
    }

    protected get initializing$(): Observable<boolean> {
        return this.prm.getInprogressObs(INITIALIZING_REQ);
    }

    protected get addingBoard$(): Observable<boolean> {
        return this.prm.getInprogressObs(this.boardActionListener.actionInProgressName('global', 'new-board'));
    }

    protected get removingBoard$(): Observable<boolean> {
        return this.prm.getInprogressObs('removing-board');
    }

    protected trackCoordStateById = (idx: number, item: BehaviorSubject<ClassRoomCoordinatorState>) => item.value.id;

    protected trackMemberById = (idx: number, item: LSessionRegistrationModel) => item.id;

    protected trackDocumentById = (idx: number, item: DocumentInfo) => item.docGlobalId;

    protected trackNotificationById = (idx: number, item: ClassroomNotification) => item.id;
    protected readonly of = of;
}

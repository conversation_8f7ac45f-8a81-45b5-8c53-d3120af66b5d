import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export const MathGraphDocColName = 'magh-documents';

export const MathGraphDocName = 'magh-documents';

export type MathGraphDocMongoDocument = HydratedDocument<MathGraphDocPojo>;

@Schema({
    _id: false, // Disable automatic generation of _id for this schema
    toObject: {
        virtuals: true,
    },
    toJSON: {
        virtuals: true,
    },
})
export class MaghDocRenderPropsPojo {
    @Prop({ default: 10 })
    screenUnit: number;
    @Prop({ default: 1 })
    scale: number;
    @Prop({ default: [0, 0, 0] })
    translation: number[];
    @Prop({ default: [0, 0, 0] })
    rotation: number[];
    @Prop({ default: true })
    valid: boolean;

    // grid
    @Prop({ default: true })
    axis: boolean;
    @Prop({ default: true })
    grid: boolean;
    @Prop({ default: true })
    detailGrid: boolean;
    @Prop({ default: false })
    showAxisArrows: boolean;
    @Prop({ default: true })
    showAxisLabels: boolean;
    @Prop({ default: false })
    usePiGrid: boolean;

    @Prop({ default: 'solid' })
    lineStyle: string;

    @Prop({ default: 1 })
    lineWidth: number;

    @Prop({ default: 1 })
    opacity: number;
}

export const RenderPropsMongoSchema = SchemaFactory.createForClass(MaghDocRenderPropsPojo);

@Schema({
    _id: false, // Disable automatic generation of _id for this schema
    toObject: {
        virtuals: true,
    },
    toJSON: {
        virtuals: true,
    },
})
export class EquationPojo {
    /**
     * main expression in LaTeX
     */
    @Prop({ default: '' })
    latex: string;

    /**
     * the full serialized equation with params
     */
    @Prop({ default: '' })
    serializedValue: string;
}

export const EquationMongoSchema = SchemaFactory.createForClass(EquationPojo);

@Schema({
    toObject: {
        virtuals: true,
        transform(doc, ret, options) {
            // don't want the _id field in the pojo
            delete ret['_id'];
        },
    },
    toJSON: {
        virtuals: true,
    },
    versionKey: 'version',
})
export class MathGraphDocPojo {
    id: string;

    @Prop()
    version: number;

    @Prop({ type: [EquationMongoSchema], default: [] })
    equations: EquationPojo[];

    @Prop({ type: RenderPropsMongoSchema })
    docRenderProps: MaghDocRenderPropsPojo;
}

export const MathGraphDocMongoSchema = SchemaFactory.createForClass(MathGraphDocPojo);

MathGraphDocMongoSchema.virtual('id').get(function () {
    return this._id;
});

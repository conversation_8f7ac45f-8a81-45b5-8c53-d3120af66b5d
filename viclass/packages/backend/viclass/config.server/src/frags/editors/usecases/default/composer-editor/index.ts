import type { EditorLookup, OperationMode } from '@viclass/editor.core';
import { EditorViewportConfig, ComposerEditorConfig, ComposerEditorCoordinatorConfig } from '@viclass/editor.composer';
import {
    Environment,
    frag,
    MFEConfCreator,
    MFEConfRequest,
    MFEDescription,
    MFESpec,
    MFESpecComposerSettings,
} from 'src/app.model';
import * as mapping from '../../../../editor-mapping';

// default configuration for viewport types
// of the editors that are embeddable inside composer editor
const viewportConf: { [edType: string]: EditorViewportConfig } = {
    FreeDrawingEditor: {
        vpType: 'board',
        defaultHeight: '200px',
        defaultWidth: '100%',
    },
    GeometryEditor: {
        vpType: 'board',
        defaultWidth: '100%',
        defaultHeight: '300px',
    },
    ComposerEditor: {
        vpType: 'inline',
        defaultWidth: '100%',
        defaultHeight: '100px',
    },
    MathEditor: {
        vpType: 'inline',
        defaultWidth: '100%',
        defaultHeight: '100px',
    },
    MathGraphEditor: {
        vpType: 'board',
        defaultWidth: '100%',
        defaultHeight: '100px',
    },
};

const LOCAL_CONTENT_SUB_EDITORS = ['MathEditor'];

const create: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const edLookup: EditorLookup = {
        editorType: 'ComposerEditor',
        lookup: {
            type: 'module',
            remoteName: 'editor.composer',
            remoteEntry: `${env.scheme}://${env.domain}/modules/editor.composer/editor.composer.js`,
            exposedModule: './editor.composer',
        },
        // settings:  // settings should be generated dynamically
    };

    const composerCoordConf: ComposerEditorCoordinatorConfig = {
        syncRouting: true,
        edLookups: [],
        viewport: {},
        editorTypeMapping: {},
        viewportElClass: 'vi-composer-editor-viewport',
        id: '',
    };

    // configuration for geo editor and
    if (spec.impl && spec.impl !== true && spec.impl.settings) {
        const settings = spec.impl.settings as MFESpecComposerSettings;
        for (const item of settings.embedded) {
            const creator = (await import(frag(`editors/usecases/embed/${item}`))).default as MFEConfCreator;
            // get the default embed configuration for editors to be embedded inside
            // the composer editor
            const desc = await creator({ item: item, impl: { useCase: 'embed', settings: {} } }, env, null);

            const embedEdLookup = desc.impl as EditorLookup; // we know these are embedded editor
            if (LOCAL_CONTENT_SUB_EDITORS.includes(embedEdLookup.editorType)) {
                embedEdLookup.settings.operationMode = 'LOCAL';
            }

            composerCoordConf.edLookups.push(embedEdLookup);
            const edType = embedEdLookup.editorType;
            composerCoordConf.viewport[edType] = viewportConf[edType];
            composerCoordConf.editorTypeMapping[edType] = mapping.default[edType];
        }
    }

    const composerEditorSettings: Partial<ComposerEditorConfig> = {
        apiUri: `${env.scheme}://${env.domain}/composer`,
        attachmentUri: `${env.scheme}://${env.domain}`,
        composerCoordConf: composerCoordConf,
        operationMode: 'CLOUD' as OperationMode,
    };

    edLookup.settings = composerEditorSettings;

    return {
        item: edLookup.editorType,
        impl: edLookup,
    };
};

export default create;

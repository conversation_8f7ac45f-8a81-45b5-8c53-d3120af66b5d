import type { ComposerEditorConfig } from '@viclass/editor.composer';
import type { EditorLookup, OperationMode } from '@viclass/editor.core';
import { Environment, MFEConfCreator, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';
import create from '../../default/composer-editor';

const embedCreate: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const desc = await create(spec, env, request);
    const settings = (desc.impl as EditorLookup).settings as ComposerEditorConfig;
    settings.docViewMode = 'full-viewport';
    settings.operationMode = 'CLOUD' as OperationMode;

    return desc;
};

export default embedCreate;

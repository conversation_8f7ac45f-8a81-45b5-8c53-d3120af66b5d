import { EditorUILookup } from '@viclass/editorui.loader';
import {
    Environment,
    MFEConfCreator,
    MFEConfRequest,
    MFEDescription,
    MFESpec,
    MFESpecCommonToolUISettings,
    MFESpecComposerSettings,
    frag,
} from 'src/app.model';
import type { ComposerUISettings } from '@viclass/editorui.composer';
import { ModuleLookup } from '@viclass/editor.core';

const embedEditorSettings = {
    iconClasses: {
        FreeDrawingEditor: 'vcon_document_freedrawing',
        GeometryEditor: 'vcon_document_geometry',
        MathEditor: 'vcon_document_mathtype',
        MathGraphEditor: 'vcon_document_magh',
        WordGraphEditor: 'vcon_document_word',
    },
};

const myTheme = 'default';

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    const uiLookup: EditorUILookup = {
        editorType: 'ComposerEditor',
        uiImpl: {
            type: 'module',
            remoteName: 'editorui.composer',
            remoteEntry: `${env.scheme}://${env.domain}/modules/editorui.composer/editorui.composer.js`,
            exposedModule: './editorui.composer',
        },
        style: {
            type: 'module',
            remoteName: 'editorui.composer.style',
            remoteEntry: `${env.scheme}://${env.domain}/modules/themes/editorui.composer.style.js`,
            exposedModule: './editorui.composer.style',
        },
    };

    if (spec.ui && spec.ui !== true && spec.ui.settings) {
        const settings = spec.ui.settings as MFESpecComposerSettings;

        if (settings.embedded) {
            const baseThemeLookupCreator = (await import(frag(`editor-tool-uis/${myTheme}/editor-ui-base-style`)))
                .default as MFEConfCreator;

            const baseThemeLookup = await baseThemeLookupCreator(
                {
                    item: 'editor-ui-base-style',
                    ui: true,
                },
                env,
                null
            );

            const lookupSettings: ComposerUISettings = {
                subEditorUILookups: [],
                iconClasses: {},
                subEditorUIBaseTheme: baseThemeLookup.ui as ModuleLookup,
            };

            for (const item of settings.embedded) {
                const editorUILookupCreator = (await import(frag(`editor-tool-uis/${myTheme}/${item}`)))
                    .default as MFEConfCreator;

                const editorUILookup = (await editorUILookupCreator({ item: item, ui: true }, env, null))
                    .ui as EditorUILookup;

                // generate the look up for the UI of the sub editors
                lookupSettings.subEditorUILookups.push(editorUILookup as EditorUILookup);
                lookupSettings.iconClasses[editorUILookup.editorType] =
                    embedEditorSettings.iconClasses[editorUILookup.editorType];
            }

            uiLookup.settings = lookupSettings;
        }
    }

    return {
        item: 'ComposerEditor',
        ui: uiLookup,
    };
}

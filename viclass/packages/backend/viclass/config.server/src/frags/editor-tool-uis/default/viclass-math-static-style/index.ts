import { Environment, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    return {
        item: 'ViclassMathStaticStyle',
        ui: {
            type: 'module',
            remoteName: 'viclass.math.static',
            remoteEntry: `${env.scheme}://${env.domain}/modules/themes/viclass.math.static.js`,
            exposedModule: './viclass.math.static',
        },
    };
}

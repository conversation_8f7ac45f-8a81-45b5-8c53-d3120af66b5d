import { Editor<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ook<PERSON>, ModuleLookupWithSettings } from '@viclass/editor.core';
import { EditorUILookup } from '@viclass/editorui.loader';
import * as path from 'path';

export function frag(sub: string): string {
    return path.join(__dirname, 'frags', sub);
}

export interface Environment {
    domain: string;
    scheme: 'http' | 'https';
    production: boolean;
    imageProxyPrefix: string;
}

export class MFESpec {
    item: string;
    impl?:
        | {
              settings?: any;
              useCase?: any;
          }
        | boolean;
    ui?:
        | {
              theme?: string;
              settings?: any; // sometimes configuration only available at runtime and needs to be merged with the config from server
          }
        | boolean;
}

export class MFEDescription {
    item: string;
    impl?: EditorLookup | ModuleLookup | ModuleLookupWithSettings;
    ui?: EditorUILookup | ModuleLookup;
}

export class MFEConfRequest {
    specs: MFESpec[];
}

export class MFEConfResponse {
    descs: MFEDescription[];
}

export interface MFEConfCreator {
    // needs original request so that we have more information to do the creation, e.g. in switch tool of the common toolbar, we needs to know all the available editor
    (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription>;
}

// ------------------ WORD EDITOR -----------
export interface MFESpecWordSettings {
    embedded: string[]; // list of editor type to be embedded insde
}

// ------------------ COMPOSER EDITOR -----------
export interface MFESpecComposerSettings {
    embedded: string[]; // list of editor type to be embedded insde
}

// ---------------- Common Tool UI ----------
export interface MFESpecCommonToolUISettings {
    switch: string[]; // list of editor type to be switched among
}

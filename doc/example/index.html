<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="../../lib-jitsi-meet.min.js"></script>
    <script src="example.js" ></script>
</head>
<body>
    <a href="#" onclick="unload()">Unload</a>
    <a href="#" onclick="switchVideo()">switchVideo</a>
    <div id="audioOutputSelectWrapper" style="display: none;">
        Change audio output device
        <select id="audioOutputSelect" onchange="changeAudioOutput(this)"></select>
    </div>
    <!-- <video id="localVideo" autoplay="true"></video> -->
    <!--<audio id="localAudio" autoplay="true" muted="true"></audio>-->
</body>
</html>

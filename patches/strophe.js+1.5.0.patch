diff --git a/node_modules/strophe.js/dist/strophe.common.js b/node_modules/strophe.js/dist/strophe.common.js
index 13dd459..427eb85 100644
--- a/node_modules/strophe.js/dist/strophe.common.js
+++ b/node_modules/strophe.js/dist/strophe.common.js
@@ -5770,7 +5770,7 @@ Strophe.Websocket = class Websocket {
       }
     }
 
-    setTimeout(() => this._conn._doDisconnect, 0);
+    setTimeout(() => this._conn._doDisconnect(), 0);
   }
   /** PrivateFunction: _doDisconnect
    *  _Private_ function to disconnect.
diff --git a/node_modules/strophe.js/dist/strophe.esm.js b/node_modules/strophe.js/dist/strophe.esm.js
index b87a0c7..d27af90 100644
--- a/node_modules/strophe.js/dist/strophe.esm.js
+++ b/node_modules/strophe.js/dist/strophe.esm.js
@@ -5766,7 +5766,7 @@ Strophe.Websocket = class Websocket {
       }
     }
 
-    setTimeout(() => this._conn._doDisconnect, 0);
+    setTimeout(() => this._conn._doDisconnect(), 0);
   }
   /** PrivateFunction: _doDisconnect
    *  _Private_ function to disconnect.
diff --git a/node_modules/strophe.js/dist/strophe.umd.js b/node_modules/strophe.js/dist/strophe.umd.js
index bd8c87c..200ebbf 100644
--- a/node_modules/strophe.js/dist/strophe.umd.js
+++ b/node_modules/strophe.js/dist/strophe.umd.js
@@ -5952,7 +5952,7 @@
                   }
                 }
 
-                setTimeout(() => this._conn._doDisconnect, 0);
+                setTimeout(() => this._conn._doDisconnect(), 0);
               }
               /** PrivateFunction: _doDisconnect
                *  _Private_ function to disconnect.
diff --git a/node_modules/strophe.js/dist/strophe.umd.min.js b/node_modules/strophe.js/dist/strophe.umd.min.js
index 89102aa..c7e6b4d 100644
--- a/node_modules/strophe.js/dist/strophe.umd.min.js
+++ b/node_modules/strophe.js/dist/strophe.umd.min.js
@@ -1 +1 @@
-!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).strophe={})}(this,(function(e){"use strict";var t="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};const s=function(){let e=t.WebSocket;if(void 0===e)try{e=require("ws")}catch(e){throw new Error('You must install the "ws" package to use Strophe in nodejs.')}return e}();const n=function(){let e=t.DOMParser;if(void 0===e)try{e=require("@xmldom/xmldom").DOMParser}catch(e){throw new Error('You must install the "@xmldom/xmldom" package to use Strophe in nodejs.')}return e}();function i(){if("undefined"==typeof document)try{return(new(0,require("@xmldom/xmldom").DOMImplementation)).createDocument("jabber:client","strophe",null)}catch(e){throw new Error('You must install the "@xmldom/xmldom" package to use Strophe in nodejs.')}if(void 0===document.implementation.createDocument||document.implementation.createDocument&&document.documentMode&&document.documentMode<10){const e=function(){const e=["Msxml2.DOMDocument.6.0","Msxml2.DOMDocument.5.0","Msxml2.DOMDocument.4.0","MSXML2.DOMDocument.3.0","MSXML2.DOMDocument","MSXML.DOMDocument","Microsoft.XMLDOM"];for(let t=0;t<e.length;t++)try{return new ActiveXObject(e[t])}catch(e){}}();return e.appendChild(e.createElement("strophe")),e}return document.implementation.createDocument("jabber:client","strophe",null)}const r=function(e,t){const s=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(s>>16)<<16|65535&s},o=function(e){if("string"!=typeof e)throw new Error("str2binl was passed a non-string");const t=[];for(let s=0;s<8*e.length;s+=8)t[s>>5]|=(255&e.charCodeAt(s/8))<<s%32;return t},a=function(e,t,s,n,i,o){return r((a=r(r(t,e),r(n,o)))<<(h=i)|a>>>32-h,s);var a,h},h=function(e,t,s,n,i,r,o){return a(t&s|~t&n,e,t,i,r,o)},l=function(e,t,s,n,i,r,o){return a(t&n|s&~n,e,t,i,r,o)},c=function(e,t,s,n,i,r,o){return a(t^s^n,e,t,i,r,o)},d=function(e,t,s,n,i,r,o){return a(s^(t|~n),e,t,i,r,o)},u=function(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;let s,n,i,o,a=1732584193,u=-271733879,_=-1732584194,m=271733878;for(let t=0;t<e.length;t+=16)s=a,n=u,i=_,o=m,a=h(a,u,_,m,e[t+0],7,-680876936),m=h(m,a,u,_,e[t+1],12,-389564586),_=h(_,m,a,u,e[t+2],17,606105819),u=h(u,_,m,a,e[t+3],22,-1044525330),a=h(a,u,_,m,e[t+4],7,-176418897),m=h(m,a,u,_,e[t+5],12,1200080426),_=h(_,m,a,u,e[t+6],17,-1473231341),u=h(u,_,m,a,e[t+7],22,-45705983),a=h(a,u,_,m,e[t+8],7,1770035416),m=h(m,a,u,_,e[t+9],12,-1958414417),_=h(_,m,a,u,e[t+10],17,-42063),u=h(u,_,m,a,e[t+11],22,-1990404162),a=h(a,u,_,m,e[t+12],7,1804603682),m=h(m,a,u,_,e[t+13],12,-40341101),_=h(_,m,a,u,e[t+14],17,-1502002290),u=h(u,_,m,a,e[t+15],22,1236535329),a=l(a,u,_,m,e[t+1],5,-165796510),m=l(m,a,u,_,e[t+6],9,-1069501632),_=l(_,m,a,u,e[t+11],14,643717713),u=l(u,_,m,a,e[t+0],20,-373897302),a=l(a,u,_,m,e[t+5],5,-701558691),m=l(m,a,u,_,e[t+10],9,38016083),_=l(_,m,a,u,e[t+15],14,-660478335),u=l(u,_,m,a,e[t+4],20,-405537848),a=l(a,u,_,m,e[t+9],5,568446438),m=l(m,a,u,_,e[t+14],9,-1019803690),_=l(_,m,a,u,e[t+3],14,-187363961),u=l(u,_,m,a,e[t+8],20,1163531501),a=l(a,u,_,m,e[t+13],5,-1444681467),m=l(m,a,u,_,e[t+2],9,-51403784),_=l(_,m,a,u,e[t+7],14,1735328473),u=l(u,_,m,a,e[t+12],20,-1926607734),a=c(a,u,_,m,e[t+5],4,-378558),m=c(m,a,u,_,e[t+8],11,-2022574463),_=c(_,m,a,u,e[t+11],16,1839030562),u=c(u,_,m,a,e[t+14],23,-35309556),a=c(a,u,_,m,e[t+1],4,-1530992060),m=c(m,a,u,_,e[t+4],11,1272893353),_=c(_,m,a,u,e[t+7],16,-155497632),u=c(u,_,m,a,e[t+10],23,-1094730640),a=c(a,u,_,m,e[t+13],4,681279174),m=c(m,a,u,_,e[t+0],11,-358537222),_=c(_,m,a,u,e[t+3],16,-722521979),u=c(u,_,m,a,e[t+6],23,76029189),a=c(a,u,_,m,e[t+9],4,-640364487),m=c(m,a,u,_,e[t+12],11,-421815835),_=c(_,m,a,u,e[t+15],16,530742520),u=c(u,_,m,a,e[t+2],23,-995338651),a=d(a,u,_,m,e[t+0],6,-198630844),m=d(m,a,u,_,e[t+7],10,1126891415),_=d(_,m,a,u,e[t+14],15,-1416354905),u=d(u,_,m,a,e[t+5],21,-57434055),a=d(a,u,_,m,e[t+12],6,1700485571),m=d(m,a,u,_,e[t+3],10,-1894986606),_=d(_,m,a,u,e[t+10],15,-1051523),u=d(u,_,m,a,e[t+1],21,-2054922799),a=d(a,u,_,m,e[t+8],6,1873313359),m=d(m,a,u,_,e[t+15],10,-30611744),_=d(_,m,a,u,e[t+6],15,-1560198380),u=d(u,_,m,a,e[t+13],21,1309151649),a=d(a,u,_,m,e[t+4],6,-145523070),m=d(m,a,u,_,e[t+11],10,-1120210379),_=d(_,m,a,u,e[t+2],15,718787259),u=d(u,_,m,a,e[t+9],21,-343485551),a=r(a,s),u=r(u,n),_=r(_,i),m=r(m,o);return[a,u,_,m]},_={hexdigest:function(e){return function(e){const t="0123456789abcdef";let s="";for(let n=0;n<4*e.length;n++)s+=t.charAt(e[n>>2]>>n%4*8+4&15)+t.charAt(e[n>>2]>>n%4*8&15);return s}(u(o(e),8*e.length))},hash:function(e){return function(e){let t="";for(let s=0;s<32*e.length;s+=8)t+=String.fromCharCode(e[s>>5]>>>s%32&255);return t}(u(o(e),8*e.length))}};class m{constructor(e,t,s){this.mechname=e,this.isClientFirst=t,this.priority=s}test(){return!0}onStart(e){this._connection=e}onChallenge(e,t){throw new Error("You should implement challenge handling!")}clientChallenge(e){if(!this.isClientFirst)throw new Error("clientChallenge should not be called if isClientFirst is false!");return this.onChallenge(e)}onFailure(){this._connection=null}onSuccess(){this._connection=null}}const p=function(e){var t,s,n="",i=e.length;for(t=0;t<i;t++)(s=e.charCodeAt(t))>=0&&s<=127?n+=e.charAt(t):s>2047?(n+=String.fromCharCode(224|s>>12&15),n+=String.fromCharCode(128|s>>6&63),n+=String.fromCharCode(128|s>>0&63)):(n+=String.fromCharCode(192|s>>6&31),n+=String.fromCharCode(128|s>>0&63));return n},g=function(e){e=e||{};for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let s="",n="",i="";const r=e[t],o="object"==typeof r,a=escape(unescape(o?r.value:r));o&&(s=r.expires?";expires="+r.expires:"",n=r.domain?";domain="+r.domain:"",i=r.path?";path="+r.path:""),document.cookie=t+"="+a+s+n+i}};function f(e,t){e[t>>5]|=128<<24-t%32,e[15+(t+64>>9<<4)]=t;var s,n,i,r,o,a,h,l,c=new Array(80),d=1732584193,u=-271733879,_=-1732584194,m=271733878,p=-1009589776;for(s=0;s<e.length;s+=16){for(r=d,o=u,a=_,h=m,l=p,n=0;n<80;n++)c[n]=n<16?e[s+n]:N(c[n-3]^c[n-8]^c[n-14]^c[n-16],1),i=x(x(N(d,5),S(n,u,_,m)),x(x(p,c[n]),b(n))),p=m,m=_,_=N(u,30),u=d,d=i;d=x(d,r),u=x(u,o),_=x(_,a),m=x(m,h),p=x(p,l)}return[d,u,_,m,p]}function S(e,t,s,n){return e<20?t&s|~t&n:e<40?t^s^n:e<60?t&s|t&n|s&n:t^s^n}function b(e){return e<20?1518500249:e<40?1859775393:e<60?-1894007588:-899497514}function T(e,t){var s=y(e);s.length>16&&(s=f(s,8*e.length));for(var n=new Array(16),i=new Array(16),r=0;r<16;r++)n[r]=909522486^s[r],i[r]=1549556828^s[r];var o=f(n.concat(y(t)),512+8*t.length);return f(i.concat(o),672)}function x(e,t){var s=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(s>>16)<<16|65535&s}function N(e,t){return e<<t|e>>>32-t}function y(e){for(var t=[],s=0;s<8*e.length;s+=8)t[s>>5]|=(255&e.charCodeAt(s/8))<<24-s%32;return t}function w(e){for(var t,s,n="",i=0;i<4*e.length;i+=3)for(t=(e[i>>2]>>8*(3-i%4)&255)<<16|(e[i+1>>2]>>8*(3-(i+1)%4)&255)<<8|e[i+2>>2]>>8*(3-(i+2)%4)&255,s=0;s<4;s++)8*i+6*s>32*e.length?n+="=":n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t>>6*(3-s)&63);return n}function A(e){for(var t="",s=0;s<32*e.length;s+=8)t+=String.fromCharCode(e[s>>5]>>>24-s%32&255);return t}const C={b64_hmac_sha1:function(e,t){return w(T(e,t))},b64_sha1:function(e){return w(f(y(e),8*e.length))},binb2str:A,core_hmac_sha1:T,str_hmac_sha1:function(e,t){return A(T(e,t))},str_sha1:function(e){return A(f(y(e),8*e.length))}};function E(e){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e);return t<0?void 0:t}var v=function(e){if(0===arguments.length)throw new TypeError("1 argument required, but only 0 present.");if((e=(e=`${e}`).replace(/[ \t\n\f\r]/g,"")).length%4==0&&(e=e.replace(/==?$/,"")),e.length%4==1||/[^+/0-9A-Za-z]/.test(e))return null;let t="",s=0,n=0;for(let i=0;i<e.length;i++)s<<=6,s|=E(e[i]),n+=6,24===n&&(t+=String.fromCharCode((16711680&s)>>16),t+=String.fromCharCode((65280&s)>>8),t+=String.fromCharCode(255&s),s=n=0);return 12===n?(s>>=4,t+=String.fromCharCode(s)):18===n&&(s>>=2,t+=String.fromCharCode((65280&s)>>8),t+=String.fromCharCode(255&s)),t};function O(e){if(e>=0&&e<64)return"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[e]}var H=function(e){if(0===arguments.length)throw new TypeError("1 argument required, but only 0 present.");let t;for(e=`${e}`,t=0;t<e.length;t++)if(e.charCodeAt(t)>255)return null;let s="";for(t=0;t<e.length;t+=3){const n=[void 0,void 0,void 0,void 0];n[0]=e.charCodeAt(t)>>2,n[1]=(3&e.charCodeAt(t))<<4,e.length>t+1&&(n[1]|=e.charCodeAt(t+1)>>4,n[2]=(15&e.charCodeAt(t+1))<<2),e.length>t+2&&(n[2]|=e.charCodeAt(t+2)>>6,n[3]=63&e.charCodeAt(t+2));for(let e=0;e<n.length;e++)void 0===n[e]?s+="=":s+=O(n[e])}return s};var I={atob:v,btoa:H};function R(e,t){return new D.Builder(e,t)}function M(e){return new D.Builder("message",e)}function q(e){return new D.Builder("iq",e)}function L(e){return new D.Builder("presence",e)}const D={VERSION:"1.5.0",NS:{HTTPBIND:"http://jabber.org/protocol/httpbind",BOSH:"urn:xmpp:xbosh",CLIENT:"jabber:client",AUTH:"jabber:iq:auth",ROSTER:"jabber:iq:roster",PROFILE:"jabber:iq:profile",DISCO_INFO:"http://jabber.org/protocol/disco#info",DISCO_ITEMS:"http://jabber.org/protocol/disco#items",MUC:"http://jabber.org/protocol/muc",SASL:"urn:ietf:params:xml:ns:xmpp-sasl",STREAM:"http://etherx.jabber.org/streams",FRAMING:"urn:ietf:params:xml:ns:xmpp-framing",BIND:"urn:ietf:params:xml:ns:xmpp-bind",SESSION:"urn:ietf:params:xml:ns:xmpp-session",VERSION:"jabber:iq:version",STANZAS:"urn:ietf:params:xml:ns:xmpp-stanzas",XHTML_IM:"http://jabber.org/protocol/xhtml-im",XHTML:"http://www.w3.org/1999/xhtml"},XHTML:{tags:["a","blockquote","br","cite","em","img","li","ol","p","span","strong","ul","body"],attributes:{a:["href"],blockquote:["style"],br:[],cite:["style"],em:[],img:["src","alt","style","height","width"],li:["style"],ol:["style"],p:["style"],span:["style"],strong:[],ul:["style"],body:[]},css:["background-color","color","font-family","font-size","font-style","font-weight","margin-left","margin-right","text-align","text-decoration"],validTag(e){for(let t=0;t<D.XHTML.tags.length;t++)if(e===D.XHTML.tags[t])return!0;return!1},validAttribute(e,t){if(void 0!==D.XHTML.attributes[e]&&D.XHTML.attributes[e].length>0)for(let s=0;s<D.XHTML.attributes[e].length;s++)if(t===D.XHTML.attributes[e][s])return!0;return!1},validCSS(e){for(let t=0;t<D.XHTML.css.length;t++)if(e===D.XHTML.css[t])return!0;return!1}},Status:{ERROR:0,CONNECTING:1,CONNFAIL:2,AUTHENTICATING:3,AUTHFAIL:4,CONNECTED:5,DISCONNECTED:6,DISCONNECTING:7,ATTACHED:8,REDIRECT:9,CONNTIMEOUT:10,BINDREQUIRED:11,ATTACHFAIL:12},ErrorCondition:{BAD_FORMAT:"bad-format",CONFLICT:"conflict",MISSING_JID_NODE:"x-strophe-bad-non-anon-jid",NO_AUTH_MECH:"no-auth-mech",UNKNOWN_REASON:"unknown"},LogLevel:{DEBUG:0,INFO:1,WARN:2,ERROR:3,FATAL:4},ElementType:{NORMAL:1,TEXT:3,CDATA:4,FRAGMENT:11},TIMEOUT:1.1,SECONDARY_TIMEOUT:.1,addNamespace(e,t){D.NS[e]=t},forEachChild(e,t,s){for(let n=0;n<e.childNodes.length;n++){const i=e.childNodes[n];i.nodeType!==D.ElementType.NORMAL||t&&!this.isTagEqual(i,t)||s(i)}},isTagEqual:(e,t)=>e.tagName===t,_xmlGenerator:null,xmlGenerator:()=>(D._xmlGenerator||(D._xmlGenerator=i()),D._xmlGenerator),xmlElement(e){if(!e)return null;const t=D.xmlGenerator().createElement(e);for(let e=1;e<arguments.length;e++){const s=arguments[e];if(s)if("string"==typeof s||"number"==typeof s)t.appendChild(D.xmlTextNode(s));else if("object"==typeof s&&"function"==typeof s.sort)for(let e=0;e<s.length;e++){const n=s[e];"object"==typeof n&&"function"==typeof n.sort&&void 0!==n[1]&&null!==n[1]&&t.setAttribute(n[0],n[1])}else if("object"==typeof s)for(const e in s)Object.prototype.hasOwnProperty.call(s,e)&&void 0!==s[e]&&null!==s[e]&&t.setAttribute(e,s[e])}return t},xmlescape:e=>e=(e=(e=(e=(e=e.replace(/\&/g,"&amp;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;")).replace(/'/g,"&apos;")).replace(/"/g,"&quot;"),xmlunescape:e=>e=(e=(e=(e=(e=e.replace(/\&amp;/g,"&")).replace(/&lt;/g,"<")).replace(/&gt;/g,">")).replace(/&apos;/g,"'")).replace(/&quot;/g,'"'),xmlTextNode:e=>D.xmlGenerator().createTextNode(e),xmlHtmlNode(e){let t;if(n){t=(new n).parseFromString(e,"text/xml")}else t=new ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(e);return t},getText(e){if(!e)return null;let t="";0===e.childNodes.length&&e.nodeType===D.ElementType.TEXT&&(t+=e.nodeValue);for(let s=0;s<e.childNodes.length;s++)e.childNodes[s].nodeType===D.ElementType.TEXT&&(t+=e.childNodes[s].nodeValue);return D.xmlescape(t)},copyElement(e){let t;if(e.nodeType===D.ElementType.NORMAL){t=D.xmlElement(e.tagName);for(let s=0;s<e.attributes.length;s++)t.setAttribute(e.attributes[s].nodeName,e.attributes[s].value);for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.copyElement(e.childNodes[s]))}else e.nodeType===D.ElementType.TEXT&&(t=D.xmlGenerator().createTextNode(e.nodeValue));return t},createHtml(e){let t;if(e.nodeType===D.ElementType.NORMAL){const s=e.nodeName.toLowerCase();if(D.XHTML.validTag(s))try{t=D.xmlElement(s);for(let n=0;n<D.XHTML.attributes[s].length;n++){const i=D.XHTML.attributes[s][n];let r=e.getAttribute(i);if(null!=r&&""!==r&&!1!==r&&0!==r)if("style"===i&&"object"==typeof r&&void 0!==r.cssText&&(r=r.cssText),"style"===i){const e=[],s=r.split(";");for(let t=0;t<s.length;t++){const n=s[t].split(":"),i=n[0].replace(/^\s*/,"").replace(/\s*$/,"").toLowerCase();if(D.XHTML.validCSS(i)){const t=n[1].replace(/^\s*/,"").replace(/\s*$/,"");e.push(i+": "+t)}}e.length>0&&(r=e.join("; "),t.setAttribute(i,r))}else t.setAttribute(i,r)}for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.createHtml(e.childNodes[s]))}catch(e){t=D.xmlTextNode("")}else{t=D.xmlGenerator().createDocumentFragment();for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.createHtml(e.childNodes[s]))}}else if(e.nodeType===D.ElementType.FRAGMENT){t=D.xmlGenerator().createDocumentFragment();for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.createHtml(e.childNodes[s]))}else e.nodeType===D.ElementType.TEXT&&(t=D.xmlTextNode(e.nodeValue));return t},escapeNode:e=>"string"!=typeof e?e:e.replace(/^\s+|\s+$/g,"").replace(/\\/g,"\\5c").replace(/ /g,"\\20").replace(/\"/g,"\\22").replace(/\&/g,"\\26").replace(/\'/g,"\\27").replace(/\//g,"\\2f").replace(/:/g,"\\3a").replace(/</g,"\\3c").replace(/>/g,"\\3e").replace(/@/g,"\\40"),unescapeNode:e=>"string"!=typeof e?e:e.replace(/\\20/g," ").replace(/\\22/g,'"').replace(/\\26/g,"&").replace(/\\27/g,"'").replace(/\\2f/g,"/").replace(/\\3a/g,":").replace(/\\3c/g,"<").replace(/\\3e/g,">").replace(/\\40/g,"@").replace(/\\5c/g,"\\"),getNodeFromJid:e=>e.indexOf("@")<0?null:e.split("@")[0],getDomainFromJid(e){const t=D.getBareJidFromJid(e);if(t.indexOf("@")<0)return t;{const e=t.split("@");return e.splice(0,1),e.join("@")}},getResourceFromJid(e){if(!e)return null;const t=e.split("/");return t.length<2?null:(t.splice(0,1),t.join("/"))},getBareJidFromJid:e=>e?e.split("/")[0]:null,_handleError(e){void 0!==e.stack&&D.fatal(e.stack),e.sourceURL?D.fatal("error: "+this.handler+" "+e.sourceURL+":"+e.line+" - "+e.name+": "+e.message):e.fileName?D.fatal("error: "+this.handler+" "+e.fileName+":"+e.lineNumber+" - "+e.name+": "+e.message):D.fatal("error: "+e.message)},log(e,t){var s;e===this.LogLevel.FATAL&&(null===(s=console)||void 0===s||s.error(t))},debug(e){this.log(this.LogLevel.DEBUG,e)},info(e){this.log(this.LogLevel.INFO,e)},warn(e){this.log(this.LogLevel.WARN,e)},error(e){this.log(this.LogLevel.ERROR,e)},fatal(e){this.log(this.LogLevel.FATAL,e)},serialize(e){if(!e)return null;"function"==typeof e.tree&&(e=e.tree());const t=[...Array(e.attributes.length).keys()].map((t=>e.attributes[t].nodeName));t.sort();let s=t.reduce(((t,s)=>`${t} ${s}="${D.xmlescape(e.attributes.getNamedItem(s).value)}"`),`<${e.nodeName}`);if(e.childNodes.length>0){s+=">";for(let t=0;t<e.childNodes.length;t++){const n=e.childNodes[t];switch(n.nodeType){case D.ElementType.NORMAL:s+=D.serialize(n);break;case D.ElementType.TEXT:s+=D.xmlescape(n.nodeValue);break;case D.ElementType.CDATA:s+="<![CDATA["+n.nodeValue+"]]>"}}s+="</"+e.nodeName+">"}else s+="/>";return s},_requestId:0,_connectionPlugins:{},addConnectionPlugin(e,t){D._connectionPlugins[e]=t},Builder:class{constructor(e,t){"presence"!==e&&"message"!==e&&"iq"!==e||(t&&!t.xmlns?t.xmlns=D.NS.CLIENT:t||(t={xmlns:D.NS.CLIENT})),this.nodeTree=D.xmlElement(e,t),this.node=this.nodeTree}tree(){return this.nodeTree}toString(){return D.serialize(this.nodeTree)}up(){return this.node=this.node.parentNode,this}root(){return this.node=this.nodeTree,this}attrs(e){for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&(void 0===e[t]?this.node.removeAttribute(t):this.node.setAttribute(t,e[t]));return this}c(e,t,s){const n=D.xmlElement(e,t,s);return this.node.appendChild(n),"string"!=typeof s&&"number"!=typeof s&&(this.node=n),this}cnode(e){let t;const s=D.xmlGenerator();try{t=void 0!==s.importNode}catch(e){t=!1}const n=t?s.importNode(e,!0):D.copyElement(e);return this.node.appendChild(n),this.node=n,this}t(e){const t=D.xmlTextNode(e);return this.node.appendChild(t),this}h(e){const t=D.xmlGenerator().createElement("body");t.innerHTML=e;const s=D.createHtml(t);for(;s.childNodes.length>0;)this.node.appendChild(s.childNodes[0]);return this}},Handler:function(e,t,s,n,i,r,o){this.handler=e,this.ns=t,this.name=s,this.type=n,this.id=i,this.options=o||{matchBareFromJid:!1,ignoreNamespaceFragment:!1},this.options.matchBare&&(D.warn('The "matchBare" option is deprecated, use "matchBareFromJid" instead.'),this.options.matchBareFromJid=this.options.matchBare,delete this.options.matchBare),this.options.matchBareFromJid?this.from=r?D.getBareJidFromJid(r):null:this.from=r,this.user=!0}};D.Handler.prototype={getNamespace(e){let t=e.getAttribute("xmlns");return t&&this.options.ignoreNamespaceFragment&&(t=t.split("#")[0]),t},namespaceMatch(e){let t=!1;return!this.ns||(D.forEachChild(e,null,(e=>{this.getNamespace(e)===this.ns&&(t=!0)})),t||this.getNamespace(e)===this.ns)},isMatch(e){let t=e.getAttribute("from");this.options.matchBareFromJid&&(t=D.getBareJidFromJid(t));const s=e.getAttribute("type");return!(!this.namespaceMatch(e)||this.name&&!D.isTagEqual(e,this.name)||this.type&&(Array.isArray(this.type)?-1===this.type.indexOf(s):s!==this.type)||this.id&&e.getAttribute("id")!==this.id||this.from&&t!==this.from)},run(e){let t=null;try{t=this.handler(e)}catch(e){throw D._handleError(e),e}return t},toString(){return"{Handler: "+this.handler+"("+this.name+","+this.id+","+this.ns+")}"}},D.TimedHandler=class{constructor(e,t){this.period=e,this.handler=t,this.lastCalled=(new Date).getTime(),this.user=!0}run(){return this.lastCalled=(new Date).getTime(),this.handler()}reset(){this.lastCalled=(new Date).getTime()}toString(){return"{TimedHandler: "+this.handler+"("+this.period+")}"}},D.Connection=class{constructor(e,t){this.service=e,this.options=t||{},this.setProtocol(),this.jid="",this.domain=null,this.features=null,this._sasl_data={},this.do_bind=!1,this.do_session=!1,this.mechanisms={},this.timedHandlers=[],this.handlers=[],this.removeTimeds=[],this.removeHandlers=[],this.addTimeds=[],this.addHandlers=[],this.protocolErrorHandlers={HTTP:{},websocket:{}},this._idleTimeout=null,this._disconnectTimeout=null,this.authenticated=!1,this.connected=!1,this.disconnecting=!1,this.do_authentication=!0,this.paused=!1,this.restored=!1,this._data=[],this._uniqueId=0,this._sasl_success_handler=null,this._sasl_failure_handler=null,this._sasl_challenge_handler=null,this.maxRetries=5,this._idleTimeout=setTimeout((()=>this._onIdle()),100),g(this.options.cookies),this.registerSASLMechanisms(this.options.mechanisms),this.iqFallbackHandler=new D.Handler((e=>this.send(q({type:"error",id:e.getAttribute("id")}).c("error",{type:"cancel"}).c("service-unavailable",{xmlns:D.NS.STANZAS}))),null,"iq",["get","set"]);for(const e in D._connectionPlugins)if(Object.prototype.hasOwnProperty.call(D._connectionPlugins,e)){const t=function(){};t.prototype=D._connectionPlugins[e],this[e]=new t,this[e].init(this)}}setProtocol(){const e=this.options.protocol||"";this.options.worker?this._proto=new D.WorkerWebsocket(this):0===this.service.indexOf("ws:")||0===this.service.indexOf("wss:")||0===e.indexOf("ws")?this._proto=new D.Websocket(this):this._proto=new D.Bosh(this)}reset(){this._proto._reset(),this.do_session=!1,this.do_bind=!1,this.timedHandlers=[],this.handlers=[],this.removeTimeds=[],this.removeHandlers=[],this.addTimeds=[],this.addHandlers=[],this.authenticated=!1,this.connected=!1,this.disconnecting=!1,this.restored=!1,this._data=[],this._requests=[],this._uniqueId=0}pause(){this.paused=!0}resume(){this.paused=!1}getUniqueId(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}));return"string"==typeof e||"number"==typeof e?t+":"+e:t+""}addProtocolErrorHandler(e,t,s){this.protocolErrorHandlers[e][t]=s}connect(e,t,s,n,i,r,o){let a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:3e3;this.jid=e,this.authzid=D.getBareJidFromJid(this.jid),this.authcid=o||D.getNodeFromJid(this.jid),this.pass=t,this.connect_callback=s,this.disconnecting=!1,this.connected=!1,this.authenticated=!1,this.restored=!1,this.disconnection_timeout=a,this.domain=D.getDomainFromJid(this.jid),this._changeConnectStatus(D.Status.CONNECTING,null),this._proto._connect(n,i,r)}attach(e,t,s,n,i,r,o){if(this._proto._attach)return this._proto._attach(e,t,s,n,i,r,o);{const e=new Error('The "attach" method is not available for your connection protocol');throw e.name="StropheSessionError",e}}restore(e,t,s,n,i){if(!this._sessionCachingSupported()){const e=new Error('The "restore" method can only be used with a BOSH connection.');throw e.name="StropheSessionError",e}this._proto._restore(e,t,s,n,i)}_sessionCachingSupported(){if(this._proto instanceof D.Bosh){if(!JSON)return!1;try{sessionStorage.setItem("_strophe_","_strophe_"),sessionStorage.removeItem("_strophe_")}catch(e){return!1}return!0}return!1}xmlInput(e){}xmlOutput(e){}rawInput(e){}rawOutput(e){}nextValidRid(e){}send(e){if(null!==e){if("function"==typeof e.sort)for(let t=0;t<e.length;t++)this._queueData(e[t]);else"function"==typeof e.tree?this._queueData(e.tree()):this._queueData(e);this._proto._send()}}flush(){clearTimeout(this._idleTimeout),this._onIdle()}sendPresence(e,t,s,n){let i=null;"function"==typeof e.tree&&(e=e.tree());let r=e.getAttribute("id");if(r||(r=this.getUniqueId("sendPresence"),e.setAttribute("id",r)),"function"==typeof t||"function"==typeof s){const e=this.addHandler((e=>{i&&this.deleteTimedHandler(i),"error"===e.getAttribute("type")?s&&s(e):t&&t(e)}),null,"presence",null,r);n&&(i=this.addTimedHandler(n,(()=>(this.deleteHandler(e),s&&s(null),!1))))}return this.send(e),r}sendIQ(e,t,s,n){let i=null;"function"==typeof e.tree&&(e=e.tree());let r=e.getAttribute("id");if(r||(r=this.getUniqueId("sendIQ"),e.setAttribute("id",r)),"function"==typeof t||"function"==typeof s){const e=this.addHandler((e=>{i&&this.deleteTimedHandler(i);const n=e.getAttribute("type");if("result"===n)t&&t(e);else{if("error"!==n){const e=new Error(`Got bad IQ type of ${n}`);throw e.name="StropheError",e}s&&s(e)}}),null,"iq",["error","result"],r);n&&(i=this.addTimedHandler(n,(()=>(this.deleteHandler(e),s&&s(null),!1))))}return this.send(e),r}_queueData(e){if(null===e||!e.tagName||!e.childNodes){const e=new Error("Cannot queue non-DOMElement.");throw e.name="StropheError",e}this._data.push(e)}_sendRestart(){this._data.push("restart"),this._proto._sendRestart(),this._idleTimeout=setTimeout((()=>this._onIdle()),100)}addTimedHandler(e,t){const s=new D.TimedHandler(e,t);return this.addTimeds.push(s),s}deleteTimedHandler(e){this.removeTimeds.push(e)}addHandler(e,t,s,n,i,r,o){const a=new D.Handler(e,t,s,n,i,r,o);return this.addHandlers.push(a),a}deleteHandler(e){this.removeHandlers.push(e);const t=this.addHandlers.indexOf(e);t>=0&&this.addHandlers.splice(t,1)}registerSASLMechanisms(e){this.mechanisms={},(e=e||[D.SASLAnonymous,D.SASLExternal,D.SASLOAuthBearer,D.SASLXOAuth2,D.SASLPlain,D.SASLSHA1]).forEach((e=>this.registerSASLMechanism(e)))}registerSASLMechanism(e){const t=new e;this.mechanisms[t.mechname]=t}disconnect(e){if(this._changeConnectStatus(D.Status.DISCONNECTING,e),e?D.warn("Disconnect was called because: "+e):D.info("Disconnect was called"),this.connected){let e=!1;this.disconnecting=!0,this.authenticated&&(e=L({xmlns:D.NS.CLIENT,type:"unavailable"})),this._disconnectTimeout=this._addSysTimedHandler(this.disconnection_timeout,this._onDisconnectTimeout.bind(this)),this._proto._disconnect(e)}else D.warn("Disconnect was called before Strophe connected to the server"),this._proto._abortAllRequests(),this._doDisconnect()}_changeConnectStatus(e,t,s){for(const s in D._connectionPlugins)if(Object.prototype.hasOwnProperty.call(D._connectionPlugins,s)){const n=this[s];if(n.statusChanged)try{n.statusChanged(e,t)}catch(e){D.error(`${s} plugin caused an exception changing status: ${e}`)}}if(this.connect_callback)try{this.connect_callback(e,t,s)}catch(e){D._handleError(e),D.error(`User connection callback caused an exception: ${e}`)}}_doDisconnect(e){"number"==typeof this._idleTimeout&&clearTimeout(this._idleTimeout),null!==this._disconnectTimeout&&(this.deleteTimedHandler(this._disconnectTimeout),this._disconnectTimeout=null),D.debug("_doDisconnect was called"),this._proto._doDisconnect(),this.authenticated=!1,this.disconnecting=!1,this.restored=!1,this.handlers=[],this.timedHandlers=[],this.removeTimeds=[],this.removeHandlers=[],this.addTimeds=[],this.addHandlers=[],this._changeConnectStatus(D.Status.DISCONNECTED,e),this.connected=!1}_dataRecv(e,t){const s=this._proto._reqToData(e);if(null===s)return;for(this.xmlInput!==D.Connection.prototype.xmlInput&&(s.nodeName===this._proto.strip&&s.childNodes.length?this.xmlInput(s.childNodes[0]):this.xmlInput(s)),this.rawInput!==D.Connection.prototype.rawInput&&(t?this.rawInput(t):this.rawInput(D.serialize(s)));this.removeHandlers.length>0;){const e=this.removeHandlers.pop(),t=this.handlers.indexOf(e);t>=0&&this.handlers.splice(t,1)}for(;this.addHandlers.length>0;)this.handlers.push(this.addHandlers.pop());if(this.disconnecting&&this._proto._emptyQueue())return void this._doDisconnect();const n=s.getAttribute("type");if(null!==n&&"terminate"===n){if(this.disconnecting)return;let e=s.getAttribute("condition");const t=s.getElementsByTagName("conflict");return null!==e?("remote-stream-error"===e&&t.length>0&&(e="conflict"),this._changeConnectStatus(D.Status.CONNFAIL,e)):this._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.UNKOWN_REASON),void this._doDisconnect(e)}D.forEachChild(s,null,(e=>{const t=[];this.handlers=this.handlers.reduce(((s,n)=>{try{!n.isMatch(e)||!this.authenticated&&n.user?s.push(n):(n.run(e)&&s.push(n),t.push(n))}catch(e){D.warn("Removing Strophe handlers due to uncaught exception: "+e.message)}return s}),[]),!t.length&&this.iqFallbackHandler.isMatch(e)&&this.iqFallbackHandler.run(e)}))}_connect_cb(e,t,s){let n;D.debug("_connect_cb was called"),this.connected=!0;try{n=this._proto._reqToData(e)}catch(e){if(e.name!==D.ErrorCondition.BAD_FORMAT)throw e;this._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.BAD_FORMAT),this._doDisconnect(D.ErrorCondition.BAD_FORMAT)}if(!n)return;this.xmlInput!==D.Connection.prototype.xmlInput&&(n.nodeName===this._proto.strip&&n.childNodes.length?this.xmlInput(n.childNodes[0]):this.xmlInput(n)),this.rawInput!==D.Connection.prototype.rawInput&&(s?this.rawInput(s):this.rawInput(D.serialize(n)));if(this._proto._connect_cb(n)===D.Status.CONNFAIL)return;let i;if(i=n.getElementsByTagNameNS?n.getElementsByTagNameNS(D.NS.STREAM,"features").length>0:n.getElementsByTagName("stream:features").length>0||n.getElementsByTagName("features").length>0,!i)return void this._proto._no_auth_received(t);const r=Array.from(n.getElementsByTagName("mechanism")).map((e=>this.mechanisms[e.textContent])).filter((e=>e));0!==r.length||0!==n.getElementsByTagName("auth").length?!1!==this.do_authentication&&this.authenticate(r):this._proto._no_auth_received(t)}sortMechanismsByPriority(e){for(let t=0;t<e.length-1;++t){let s=t;for(let n=t+1;n<e.length;++n)e[n].priority>e[s].priority&&(s=n);if(s!==t){const n=e[t];e[t]=e[s],e[s]=n}}return e}authenticate(e){this._attemptSASLAuth(e)||this._attemptLegacyAuth()}_attemptSASLAuth(e){e=this.sortMechanismsByPriority(e||[]);let t=!1;for(let s=0;s<e.length;++s){if(!e[s].test(this))continue;this._sasl_success_handler=this._addSysHandler(this._sasl_success_cb.bind(this),null,"success",null,null),this._sasl_failure_handler=this._addSysHandler(this._sasl_failure_cb.bind(this),null,"failure",null,null),this._sasl_challenge_handler=this._addSysHandler(this._sasl_challenge_cb.bind(this),null,"challenge",null,null),this._sasl_mechanism=e[s],this._sasl_mechanism.onStart(this);const n=R("auth",{xmlns:D.NS.SASL,mechanism:this._sasl_mechanism.mechname});if(this._sasl_mechanism.isClientFirst){const e=this._sasl_mechanism.clientChallenge(this);n.t(I.btoa(e))}this.send(n.tree()),t=!0;break}return t}_sasl_challenge_cb(e){const t=I.atob(D.getText(e)),s=this._sasl_mechanism.onChallenge(this,t),n=R("response",{xmlns:D.NS.SASL});return""!==s&&n.t(I.btoa(s)),this.send(n.tree()),!0}_attemptLegacyAuth(){null===D.getNodeFromJid(this.jid)?(this._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.MISSING_JID_NODE),this.disconnect(D.ErrorCondition.MISSING_JID_NODE)):(this._changeConnectStatus(D.Status.AUTHENTICATING,null),this._addSysHandler(this._onLegacyAuthIQResult.bind(this),null,null,null,"_auth_1"),this.send(q({type:"get",to:this.domain,id:"_auth_1"}).c("query",{xmlns:D.NS.AUTH}).c("username",{}).t(D.getNodeFromJid(this.jid)).tree()))}_onLegacyAuthIQResult(e){const t=q({type:"set",id:"_auth_2"}).c("query",{xmlns:D.NS.AUTH}).c("username",{}).t(D.getNodeFromJid(this.jid)).up().c("password").t(this.pass);return D.getResourceFromJid(this.jid)||(this.jid=D.getBareJidFromJid(this.jid)+"/strophe"),t.up().c("resource",{}).t(D.getResourceFromJid(this.jid)),this._addSysHandler(this._auth2_cb.bind(this),null,null,null,"_auth_2"),this.send(t.tree()),!1}_sasl_success_cb(e){if(this._sasl_data["server-signature"]){let t;const s=/([a-z]+)=([^,]+)(,|$)/,n=I.atob(D.getText(e)).match(s);if("v"===n[1]&&(t=n[2]),t!==this._sasl_data["server-signature"])return this.deleteHandler(this._sasl_failure_handler),this._sasl_failure_handler=null,this._sasl_challenge_handler&&(this.deleteHandler(this._sasl_challenge_handler),this._sasl_challenge_handler=null),this._sasl_data={},this._sasl_failure_cb(null)}D.info("SASL authentication succeeded."),this._sasl_mechanism&&this._sasl_mechanism.onSuccess(),this.deleteHandler(this._sasl_failure_handler),this._sasl_failure_handler=null,this._sasl_challenge_handler&&(this.deleteHandler(this._sasl_challenge_handler),this._sasl_challenge_handler=null);const t=[],s=(e,t)=>{for(;e.length;)this.deleteHandler(e.pop());return this._onStreamFeaturesAfterSASL(t),!1};return t.push(this._addSysHandler((e=>s(t,e)),null,"stream:features",null,null)),t.push(this._addSysHandler((e=>s(t,e)),D.NS.STREAM,"features",null,null)),this._sendRestart(),!1}_onStreamFeaturesAfterSASL(e){this.features=e;for(let t=0;t<e.childNodes.length;t++){const s=e.childNodes[t];"bind"===s.nodeName&&(this.do_bind=!0),"session"===s.nodeName&&(this.do_session=!0)}return this.do_bind?(this.options.explicitResourceBinding?this._changeConnectStatus(D.Status.BINDREQUIRED,null):this.bind(),!1):(this._changeConnectStatus(D.Status.AUTHFAIL,null),!1)}bind(){if(!this.do_bind)return void D.log(D.LogLevel.INFO,'Strophe.Connection.prototype.bind called but "do_bind" is false');this._addSysHandler(this._onResourceBindResultIQ.bind(this),null,null,null,"_bind_auth_2");const e=D.getResourceFromJid(this.jid);e?this.send(q({type:"set",id:"_bind_auth_2"}).c("bind",{xmlns:D.NS.BIND}).c("resource",{}).t(e).tree()):this.send(q({type:"set",id:"_bind_auth_2"}).c("bind",{xmlns:D.NS.BIND}).tree())}_onResourceBindResultIQ(e){if("error"===e.getAttribute("type")){D.warn("Resource binding failed.");let t;return e.getElementsByTagName("conflict").length>0&&(t=D.ErrorCondition.CONFLICT),this._changeConnectStatus(D.Status.AUTHFAIL,t,e),!1}const t=e.getElementsByTagName("bind");if(!(t.length>0))return D.warn("Resource binding failed."),this._changeConnectStatus(D.Status.AUTHFAIL,null,e),!1;{const e=t[0].getElementsByTagName("jid");e.length>0&&(this.authenticated=!0,this.jid=D.getText(e[0]),this.do_session?this._establishSession():this._changeConnectStatus(D.Status.CONNECTED,null))}}_establishSession(){if(!this.do_session)throw new Error(`Strophe.Connection.prototype._establishSession called but apparently ${D.NS.SESSION} wasn't advertised by the server`);this._addSysHandler(this._onSessionResultIQ.bind(this),null,null,null,"_session_auth_2"),this.send(q({type:"set",id:"_session_auth_2"}).c("session",{xmlns:D.NS.SESSION}).tree())}_onSessionResultIQ(e){if("result"===e.getAttribute("type"))this.authenticated=!0,this._changeConnectStatus(D.Status.CONNECTED,null);else if("error"===e.getAttribute("type"))return this.authenticated=!1,D.warn("Session creation failed."),this._changeConnectStatus(D.Status.AUTHFAIL,null,e),!1;return!1}_sasl_failure_cb(e){return this._sasl_success_handler&&(this.deleteHandler(this._sasl_success_handler),this._sasl_success_handler=null),this._sasl_challenge_handler&&(this.deleteHandler(this._sasl_challenge_handler),this._sasl_challenge_handler=null),this._sasl_mechanism&&this._sasl_mechanism.onFailure(),this._changeConnectStatus(D.Status.AUTHFAIL,null,e),!1}_auth2_cb(e){return"result"===e.getAttribute("type")?(this.authenticated=!0,this._changeConnectStatus(D.Status.CONNECTED,null)):"error"===e.getAttribute("type")&&(this._changeConnectStatus(D.Status.AUTHFAIL,null,e),this.disconnect("authentication failed")),!1}_addSysTimedHandler(e,t){const s=new D.TimedHandler(e,t);return s.user=!1,this.addTimeds.push(s),s}_addSysHandler(e,t,s,n,i){const r=new D.Handler(e,t,s,n,i);return r.user=!1,this.addHandlers.push(r),r}_onDisconnectTimeout(){return D.debug("_onDisconnectTimeout was called"),this._changeConnectStatus(D.Status.CONNTIMEOUT,null),this._proto._onDisconnectTimeout(),this._doDisconnect(),!1}_onIdle(){for(;this.addTimeds.length>0;)this.timedHandlers.push(this.addTimeds.pop());for(;this.removeTimeds.length>0;){const e=this.removeTimeds.pop(),t=this.timedHandlers.indexOf(e);t>=0&&this.timedHandlers.splice(t,1)}const e=(new Date).getTime(),t=[];for(let s=0;s<this.timedHandlers.length;s++){const n=this.timedHandlers[s];if(this.authenticated||!n.user){n.lastCalled+n.period-e<=0?n.run()&&t.push(n):t.push(n)}}this.timedHandlers=t,clearTimeout(this._idleTimeout),this._proto._onIdle(),this.connected&&(this._idleTimeout=setTimeout((()=>this._onIdle()),100))}},D.SASLMechanism=m,D.SASLAnonymous=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ANONYMOUS",arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:20)}test(e){return null===e.authcid}},D.SASLPlain=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"PLAIN",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:50)}test(e){return null!==e.authcid}onChallenge(e){const{authcid:t,authzid:s,domain:n,pass:i}=e;if(!n)throw new Error("SASLPlain onChallenge: domain is not defined!");let r=s!==`${t}@${n}`?s:"";return r+="\0",r+=t,r+="\0",r+=i,p(r)}},D.SASLSHA1=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"SCRAM-SHA-1",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:60)}test(e){return null!==e.authcid}onChallenge(e,t){let s,n,i,r,o,a,h,l,c="c=biws,",d=`${e._sasl_data["client-first-message-bare"]},${t},`;const u=e._sasl_data.cnonce,_=/([a-z]+)=([^,]+)(,|$)/;for(;t.match(_);){const e=t.match(_);switch(t=t.replace(e[0],""),e[1]){case"r":s=e[2];break;case"s":n=e[2];break;case"i":i=e[2]}}if(s.slice(0,u.length)!==u)return e._sasl_data={},e._sasl_failure_cb();c+="r="+s,d+=c,n=atob(n),n+="\0\0\0";const m=p(e.pass);for(r=a=C.core_hmac_sha1(m,n),h=1;h<i;h++){for(o=C.core_hmac_sha1(m,C.binb2str(a)),l=0;l<5;l++)r[l]^=o[l];a=o}r=C.binb2str(r);const g=C.core_hmac_sha1(r,"Client Key"),f=C.str_hmac_sha1(r,"Server Key"),S=C.core_hmac_sha1(C.str_sha1(C.binb2str(g)),d);for(e._sasl_data["server-signature"]=C.b64_hmac_sha1(f,d),l=0;l<5;l++)g[l]^=S[l];return c+=",p="+btoa(C.binb2str(g)),c}clientChallenge(e,t){const s=t||_.hexdigest(""+1234567890*Math.random());let n="n="+p(e.authcid);return n+=",r=",n+=s,e._sasl_data.cnonce=s,e._sasl_data["client-first-message-bare"]=n,n="n,,"+n,n}},D.SASLOAuthBearer=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"OAUTHBEARER",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:40)}test(e){return null!==e.pass}onChallenge(e){let t="n,";return null!==e.authcid&&(t=t+"a="+e.authzid),t+=",",t+="",t+="auth=Bearer ",t+=e.pass,t+="",t+="",p(t)}},D.SASLExternal=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"EXTERNAL",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:10)}onChallenge(e){return e.authcid===e.authzid?"":e.authzid}},D.SASLXOAuth2=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"X-OAUTH2",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:30)}test(e){return null!==e.pass}onChallenge(e){let t="\0";return null!==e.authcid&&(t+=e.authzid),t+="\0",t+=e.pass,p(t)}};var F={Strophe:D,$build:R,$iq:q,$msg:M,$pres:L,SHA1:C,MD5:_,b64_hmac_sha1:C.b64_hmac_sha1,b64_sha1:C.b64_sha1,str_hmac_sha1:C.str_hmac_sha1,str_sha1:C.str_sha1};D.Request=class{constructor(e,t,s,n){this.id=++D._requestId,this.xmlData=e,this.data=D.serialize(e),this.origFunc=t,this.func=t,this.rid=s,this.date=NaN,this.sends=n||0,this.abort=!1,this.dead=null,this.age=function(){if(!this.date)return 0;return(new Date-this.date)/1e3},this.timeDead=function(){if(!this.dead)return 0;return(new Date-this.dead)/1e3},this.xhr=this._newXHR()}getResponse(){let e=null;if(this.xhr.responseXML&&this.xhr.responseXML.documentElement){if(e=this.xhr.responseXML.documentElement,"parsererror"===e.tagName)throw D.error("invalid response received"),D.error("responseText: "+this.xhr.responseText),D.error("responseXML: "+D.serialize(this.xhr.responseXML)),new Error("parsererror")}else if(this.xhr.responseText){if(D.debug("Got responseText but no responseXML; attempting to parse it with DOMParser..."),e=(new n).parseFromString(this.xhr.responseText,"application/xml").documentElement,!e)throw new Error("Parsing produced null node");if(e.querySelector("parsererror")){D.error("invalid response received: "+e.querySelector("parsererror").textContent),D.error("responseText: "+this.xhr.responseText);const t=new Error;throw t.name=D.ErrorCondition.BAD_FORMAT,t}}return e}_newXHR(){let e=null;return window.XMLHttpRequest?(e=new XMLHttpRequest,e.overrideMimeType&&e.overrideMimeType("text/xml; charset=utf-8")):window.ActiveXObject&&(e=new ActiveXObject("Microsoft.XMLHTTP")),e.onreadystatechange=this.func.bind(null,this),e}},D.Bosh=class e{constructor(e){this._conn=e,this.rid=Math.floor(4294967295*Math.random()),this.sid=null,this.hold=1,this.wait=60,this.window=5,this.errors=0,this.inactivity=null,this.lastResponseHeaders=null,this._requests=[]}_buildBody(){const e=R("body",{rid:this.rid++,xmlns:D.NS.HTTPBIND});return null!==this.sid&&e.attrs({sid:this.sid}),this._conn.options.keepalive&&this._conn._sessionCachingSupported()&&this._cacheSession(),e}_reset(){this.rid=Math.floor(4294967295*Math.random()),this.sid=null,this.errors=0,this._conn._sessionCachingSupported()&&window.sessionStorage.removeItem("strophe-bosh-session"),this._conn.nextValidRid(this.rid)}_connect(e,t,s){this.wait=e||this.wait,this.hold=t||this.hold,this.errors=0;const n=this._buildBody().attrs({to:this._conn.domain,"xml:lang":"en",wait:this.wait,hold:this.hold,content:"text/xml; charset=utf-8",ver:"1.6","xmpp:version":"1.0","xmlns:xmpp":D.NS.BOSH});s&&n.attrs({route:s});const i=this._conn._connect_cb;this._requests.push(new D.Request(n.tree(),this._onRequestStateChange.bind(this,i.bind(this._conn)),n.tree().getAttribute("rid"))),this._throttledRequestHandler()}_attach(e,t,s,n,i,r,o){this._conn.jid=e,this.sid=t,this.rid=s,this._conn.connect_callback=n,this._conn.domain=D.getDomainFromJid(this._conn.jid),this._conn.authenticated=!0,this._conn.connected=!0,this.wait=i||this.wait,this.hold=r||this.hold,this.window=o||this.window,this._conn._changeConnectStatus(D.Status.ATTACHED,null)}_restore(e,t,s,n,i){const r=JSON.parse(window.sessionStorage.getItem("strophe-bosh-session"));if(!(null!=r&&r.rid&&r.sid&&r.jid&&(null==e||D.getBareJidFromJid(r.jid)===D.getBareJidFromJid(e)||null===D.getNodeFromJid(e)&&D.getDomainFromJid(r.jid)===e))){const e=new Error("_restore: no restoreable session.");throw e.name="StropheSessionError",e}this._conn.restored=!0,this._attach(r.jid,r.sid,r.rid,t,s,n,i)}_cacheSession(){this._conn.authenticated?this._conn.jid&&this.rid&&this.sid&&window.sessionStorage.setItem("strophe-bosh-session",JSON.stringify({jid:this._conn.jid,rid:this.rid,sid:this.sid})):window.sessionStorage.removeItem("strophe-bosh-session")}_connect_cb(e){const t=e.getAttribute("type");if(null!==t&&"terminate"===t){let t=e.getAttribute("condition");D.error("BOSH-Connection failed: "+t);const s=e.getElementsByTagName("conflict");return null!==t?("remote-stream-error"===t&&s.length>0&&(t="conflict"),this._conn._changeConnectStatus(D.Status.CONNFAIL,t)):this._conn._changeConnectStatus(D.Status.CONNFAIL,"unknown"),this._conn._doDisconnect(t),D.Status.CONNFAIL}this.sid||(this.sid=e.getAttribute("sid"));const s=e.getAttribute("requests");s&&(this.window=parseInt(s,10));const n=e.getAttribute("hold");n&&(this.hold=parseInt(n,10));const i=e.getAttribute("wait");i&&(this.wait=parseInt(i,10));const r=e.getAttribute("inactivity");r&&(this.inactivity=parseInt(r,10))}_disconnect(e){this._sendTerminate(e)}_doDisconnect(){this.sid=null,this.rid=Math.floor(4294967295*Math.random()),this._conn._sessionCachingSupported()&&window.sessionStorage.removeItem("strophe-bosh-session"),this._conn.nextValidRid(this.rid)}_emptyQueue(){return 0===this._requests.length}_callProtocolErrorHandlers(t){const s=e._getRequestStatus(t),n=this._conn.protocolErrorHandlers.HTTP[s];n&&n.call(this,s)}_hitError(e){this.errors++,D.warn("request errored, status: "+e+", number of errors: "+this.errors),this.errors>4&&this._conn._onDisconnectTimeout()}_no_auth_received(e){D.warn("Server did not yet offer a supported authentication mechanism. Sending a blank poll request."),e=e?e.bind(this._conn):this._conn._connect_cb.bind(this._conn);const t=this._buildBody();this._requests.push(new D.Request(t.tree(),this._onRequestStateChange.bind(this,e),t.tree().getAttribute("rid"))),this._throttledRequestHandler()}_onDisconnectTimeout(){this._abortAllRequests()}_abortAllRequests(){for(;this._requests.length>0;){const e=this._requests.pop();e.abort=!0,e.xhr.abort(),e.xhr.onreadystatechange=function(){}}}_onIdle(){const e=this._conn._data;if(this._conn.authenticated&&0===this._requests.length&&0===e.length&&!this._conn.disconnecting&&(D.debug("no requests during idle cycle, sending blank request"),e.push(null)),!this._conn.paused){if(this._requests.length<2&&e.length>0){const t=this._buildBody();for(let s=0;s<e.length;s++)null!==e[s]&&("restart"===e[s]?t.attrs({to:this._conn.domain,"xml:lang":"en","xmpp:restart":"true","xmlns:xmpp":D.NS.BOSH}):t.cnode(e[s]).up());delete this._conn._data,this._conn._data=[],this._requests.push(new D.Request(t.tree(),this._onRequestStateChange.bind(this,this._conn._dataRecv.bind(this._conn)),t.tree().getAttribute("rid"))),this._throttledRequestHandler()}if(this._requests.length>0){const e=this._requests[0].age();null!==this._requests[0].dead&&this._requests[0].timeDead()>Math.floor(D.SECONDARY_TIMEOUT*this.wait)&&this._throttledRequestHandler(),e>Math.floor(D.TIMEOUT*this.wait)&&(D.warn("Request "+this._requests[0].id+" timed out, over "+Math.floor(D.TIMEOUT*this.wait)+" seconds since last activity"),this._throttledRequestHandler())}}}static _getRequestStatus(e,t){let s;if(4===e.xhr.readyState)try{s=e.xhr.status}catch(e){D.error("Caught an error while retrieving a request's status, reqStatus: "+s)}return void 0===s&&(s="number"==typeof t?t:0),s}_onRequestStateChange(t,s){if(D.debug("request id "+s.id+"."+s.sends+" state changed to "+s.xhr.readyState),s.abort)return void(s.abort=!1);if(4!==s.xhr.readyState)return;const n=e._getRequestStatus(s);if(this.lastResponseHeaders=s.xhr.getAllResponseHeaders(),this._conn.disconnecting&&n>=400)return this._hitError(n),void this._callProtocolErrorHandlers(s);const i=this._requests[0]===s,r=this._requests[1]===s,o=n>0&&n<500,a=s.sends>this._conn.maxRetries;(o||a)&&(this._removeRequest(s),D.debug("request id "+s.id+" should now be removed")),200===n?((r||i&&this._requests.length>0&&this._requests[0].age()>Math.floor(D.SECONDARY_TIMEOUT*this.wait))&&this._restartRequest(0),this._conn.nextValidRid(Number(s.rid)+1),D.debug("request id "+s.id+"."+s.sends+" got 200"),t(s),this.errors=0):0===n||n>=400&&n<600||n>=12e3?(D.error("request id "+s.id+"."+s.sends+" error "+n+" happened"),this._hitError(n),this._callProtocolErrorHandlers(s),n>=400&&n<500&&(this._conn._changeConnectStatus(D.Status.DISCONNECTING,null),this._conn._doDisconnect())):D.error("request id "+s.id+"."+s.sends+" error "+n+" happened"),o||a?a&&!this._conn.connected&&this._conn._changeConnectStatus(D.Status.CONNFAIL,"giving-up"):this._throttledRequestHandler()}_processRequest(t){let s=this._requests[t];const n=e._getRequestStatus(s,-1);if(s.sends>this._conn.maxRetries)return void this._conn._onDisconnectTimeout();const i=s.age(),r=!isNaN(i)&&i>Math.floor(D.TIMEOUT*this.wait),o=null!==s.dead&&s.timeDead()>Math.floor(D.SECONDARY_TIMEOUT*this.wait),a=4===s.xhr.readyState&&(n<1||n>=500);if((r||o||a)&&(o&&D.error(`Request ${this._requests[t].id} timed out (secondary), restarting`),s.abort=!0,s.xhr.abort(),s.xhr.onreadystatechange=function(){},this._requests[t]=new D.Request(s.xmlData,s.origFunc,s.rid,s.sends),s=this._requests[t]),0===s.xhr.readyState){D.debug("request id "+s.id+"."+s.sends+" posting");try{const e=this._conn.options.contentType||"text/xml; charset=utf-8";s.xhr.open("POST",this._conn.service,!this._conn.options.sync),void 0!==s.xhr.setRequestHeader&&s.xhr.setRequestHeader("Content-Type",e),this._conn.options.withCredentials&&(s.xhr.withCredentials=!0)}catch(e){return D.error("XHR open failed: "+e.toString()),this._conn.connected||this._conn._changeConnectStatus(D.Status.CONNFAIL,"bad-service"),void this._conn.disconnect()}const e=()=>{if(s.date=new Date,this._conn.options.customHeaders){const e=this._conn.options.customHeaders;for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&s.xhr.setRequestHeader(t,e[t])}s.xhr.send(s.data)};if(s.sends>1){const t=1e3*Math.min(Math.floor(D.TIMEOUT*this.wait),Math.pow(s.sends,3));setTimeout((function(){e()}),t)}else e();s.sends++,this._conn.xmlOutput!==D.Connection.prototype.xmlOutput&&(s.xmlData.nodeName===this.strip&&s.xmlData.childNodes.length?this._conn.xmlOutput(s.xmlData.childNodes[0]):this._conn.xmlOutput(s.xmlData)),this._conn.rawOutput!==D.Connection.prototype.rawOutput&&this._conn.rawOutput(s.data)}else D.debug("_processRequest: "+(0===t?"first":"second")+" request has readyState of "+s.xhr.readyState)}_removeRequest(e){D.debug("removing request");for(let t=this._requests.length-1;t>=0;t--)e===this._requests[t]&&this._requests.splice(t,1);e.xhr.onreadystatechange=function(){},this._throttledRequestHandler()}_restartRequest(e){const t=this._requests[e];null===t.dead&&(t.dead=new Date),this._processRequest(e)}_reqToData(e){try{return e.getResponse()}catch(e){if("parsererror"!==e.message)throw e;this._conn.disconnect("strophe-parsererror")}}_sendTerminate(e){D.debug("_sendTerminate was called");const t=this._buildBody().attrs({type:"terminate"});e&&t.cnode(e.tree());const s=new D.Request(t.tree(),this._onRequestStateChange.bind(this,this._conn._dataRecv.bind(this._conn)),t.tree().getAttribute("rid"));this._requests.push(s),this._throttledRequestHandler()}_send(){clearTimeout(this._conn._idleTimeout),this._throttledRequestHandler(),this._conn._idleTimeout=setTimeout((()=>this._conn._onIdle()),100)}_sendRestart(){this._throttledRequestHandler(),clearTimeout(this._conn._idleTimeout)}_throttledRequestHandler(){this._requests?D.debug("_throttledRequestHandler called with "+this._requests.length+" requests"):D.debug("_throttledRequestHandler called with undefined requests"),this._requests&&0!==this._requests.length&&(this._requests.length>0&&this._processRequest(0),this._requests.length>1&&Math.abs(this._requests[0].rid-this._requests[1].rid)<this.window&&this._processRequest(1))}},D.Bosh.prototype.strip=null,D.Websocket=class{constructor(e){this._conn=e,this.strip="wrapper";const t=e.service;if(0!==t.indexOf("ws:")&&0!==t.indexOf("wss:")){let s="";"ws"===e.options.protocol&&"https:"!==window.location.protocol?s+="ws":s+="wss",s+="://"+window.location.host,0!==t.indexOf("/")?s+=window.location.pathname+t:s+=t,e.service=s}}_buildStream(){return R("open",{xmlns:D.NS.FRAMING,to:this._conn.domain,version:"1.0"})}_checkStreamError(e,t){let s;if(s=e.getElementsByTagNameNS?e.getElementsByTagNameNS(D.NS.STREAM,"error"):e.getElementsByTagName("stream:error"),0===s.length)return!1;const n=s[0];let i="",r="";for(let e=0;e<n.childNodes.length;e++){const t=n.childNodes[e];if("urn:ietf:params:xml:ns:xmpp-streams"!==t.getAttribute("xmlns"))break;"text"===t.nodeName?r=t.textContent:i=t.nodeName}let o="WebSocket stream error: ";return o+=i||"unknown",r&&(o+=" - "+r),D.error(o),this._conn._changeConnectStatus(t,i),this._conn._doDisconnect(),!0}_reset(){}_connect(){this._closeSocket(),this.socket=new s(this._conn.service,"xmpp"),this.socket.onopen=()=>this._onOpen(),this.socket.onerror=e=>this._onError(e),this.socket.onclose=e=>this._onClose(e),this.socket.onmessage=e=>this._onInitialMessage(e)}_connect_cb(e){if(this._checkStreamError(e,D.Status.CONNFAIL))return D.Status.CONNFAIL}_handleStreamStart(e){let t=!1;const s=e.getAttribute("xmlns");"string"!=typeof s?t="Missing xmlns in <open />":s!==D.NS.FRAMING&&(t="Wrong xmlns in <open />: "+s);const n=e.getAttribute("version");return"string"!=typeof n?t="Missing version in <open />":"1.0"!==n&&(t="Wrong version in <open />: "+n),!t||(this._conn._changeConnectStatus(D.Status.CONNFAIL,t),this._conn._doDisconnect(),!1)}_onInitialMessage(e){if(0===e.data.indexOf("<open ")||0===e.data.indexOf("<?xml")){const t=e.data.replace(/^(<\?.*?\?>\s*)*/,"");if(""===t)return;const s=(new n).parseFromString(t,"text/xml").documentElement;this._conn.xmlInput(s),this._conn.rawInput(e.data),this._handleStreamStart(s)&&this._connect_cb(s)}else if(0===e.data.indexOf("<close ")){const t=(new n).parseFromString(e.data,"text/xml").documentElement;this._conn.xmlInput(t),this._conn.rawInput(e.data);const s=t.getAttribute("see-other-uri");if(s){const e=this._conn.service;(e.indexOf("wss:")>=0&&s.indexOf("wss:")>=0||e.indexOf("ws:")>=0)&&(this._conn._changeConnectStatus(D.Status.REDIRECT,"Received see-other-uri, resetting connection"),this._conn.reset(),this._conn.service=s,this._connect())}else this._conn._changeConnectStatus(D.Status.CONNFAIL,"Received closing stream"),this._conn._doDisconnect()}else{this._replaceMessageHandler();const t=this._streamWrap(e.data),s=(new n).parseFromString(t,"text/xml").documentElement;this._conn._connect_cb(s,null,e.data)}}_replaceMessageHandler(){this.socket.onmessage=e=>this._onMessage(e)}_disconnect(e){if(this.socket&&this.socket.readyState!==s.CLOSED){e&&this._conn.send(e);const t=R("close",{xmlns:D.NS.FRAMING});this._conn.xmlOutput(t.tree());const s=D.serialize(t);this._conn.rawOutput(s);try{this.socket.send(s)}catch(e){D.warn("Couldn't send <close /> tag.")}}setTimeout((()=>this._conn._doDisconnect),0)}_doDisconnect(){D.debug("WebSockets _doDisconnect was called"),this._closeSocket()}_streamWrap(e){return"<wrapper>"+e+"</wrapper>"}_closeSocket(){if(this.socket)try{this.socket.onclose=null,this.socket.onerror=null,this.socket.onmessage=null,this.socket.close()}catch(e){D.debug(e.message)}this.socket=null}_emptyQueue(){return!0}_onClose(e){this._conn.connected&&!this._conn.disconnecting?(D.error("Websocket closed unexpectedly"),this._conn._doDisconnect()):e&&1006===e.code&&!this._conn.connected&&this.socket?(D.error("Websocket closed unexcectedly"),this._conn._changeConnectStatus(D.Status.CONNFAIL,"The WebSocket connection could not be established or was disconnected."),this._conn._doDisconnect()):D.debug("Websocket closed")}_no_auth_received(e){D.error("Server did not offer a supported authentication mechanism"),this._conn._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.NO_AUTH_MECH),e&&e.call(this._conn),this._conn._doDisconnect()}_onDisconnectTimeout(){}_abortAllRequests(){}_onError(e){D.error("Websocket error "+JSON.stringify(e)),this._conn._changeConnectStatus(D.Status.CONNFAIL,"The WebSocket connection could not be established or was disconnected."),this._disconnect()}_onIdle(){const e=this._conn._data;if(e.length>0&&!this._conn.paused){for(let t=0;t<e.length;t++)if(null!==e[t]){let s;s="restart"===e[t]?this._buildStream().tree():e[t];const n=D.serialize(s);this._conn.xmlOutput(s),this._conn.rawOutput(n),this.socket.send(n)}this._conn._data=[]}}_onMessage(e){let t;const s='<close xmlns="urn:ietf:params:xml:ns:xmpp-framing" />';if(e.data===s)return this._conn.rawInput(s),this._conn.xmlInput(e),void(this._conn.disconnecting||this._conn._doDisconnect());if(0===e.data.search("<open ")){if(t=(new n).parseFromString(e.data,"text/xml").documentElement,!this._handleStreamStart(t))return}else{const s=this._streamWrap(e.data);t=(new n).parseFromString(s,"text/xml").documentElement}return this._checkStreamError(t,D.Status.ERROR)?void 0:this._conn.disconnecting&&"presence"===t.firstChild.nodeName&&"unavailable"===t.firstChild.getAttribute("type")?(this._conn.xmlInput(t),void this._conn.rawInput(D.serialize(t))):void this._conn._dataRecv(t,e.data)}_onOpen(){D.debug("Websocket open");const e=this._buildStream();this._conn.xmlOutput(e.tree());const t=D.serialize(e);this._conn.rawOutput(t),this.socket.send(t)}_reqToData(e){return e}_send(){this._conn.flush()}_sendRestart(){clearTimeout(this._conn._idleTimeout),this._conn._onIdle.bind(this._conn)()}};const k={};k.debug=D.LogLevel.DEBUG,k.info=D.LogLevel.INFO,k.warn=D.LogLevel.WARN,k.error=D.LogLevel.ERROR,k.fatal=D.LogLevel.FATAL,D.WorkerWebsocket=class extends D.Websocket{constructor(e){super(e),this._conn=e,this.worker=new SharedWorker(this._conn.options.worker,"Strophe XMPP Connection"),this.worker.onerror=e=>{var t;null===(t=console)||void 0===t||t.error(e),D.log(D.LogLevel.ERROR,`Shared Worker Error: ${e}`)}}get socket(){return{send:e=>this.worker.port.postMessage(["send",e])}}_connect(){this._messageHandler=e=>this._onInitialMessage(e),this.worker.port.start(),this.worker.port.onmessage=e=>this._onWorkerMessage(e),this.worker.port.postMessage(["_connect",this._conn.service,this._conn.jid])}_attach(e){this._messageHandler=e=>this._onMessage(e),this._conn.connect_callback=e,this.worker.port.start(),this.worker.port.onmessage=e=>this._onWorkerMessage(e),this.worker.port.postMessage(["_attach",this._conn.service])}_attachCallback(e,t){e===D.Status.ATTACHED?(this._conn.jid=t,this._conn.authenticated=!0,this._conn.connected=!0,this._conn.restored=!0,this._conn._changeConnectStatus(D.Status.ATTACHED)):e===D.Status.ATTACHFAIL&&(this._conn.authenticated=!1,this._conn.connected=!1,this._conn.restored=!1,this._conn._changeConnectStatus(D.Status.ATTACHFAIL))}_disconnect(e,t){t&&this._conn.send(t);const s=R("close",{xmlns:D.NS.FRAMING});this._conn.xmlOutput(s.tree());const n=D.serialize(s);this._conn.rawOutput(n),this.worker.port.postMessage(["send",n]),this._conn._doDisconnect()}_onClose(e){this._conn.connected&&!this._conn.disconnecting?(D.error("Websocket closed unexpectedly"),this._conn._doDisconnect()):e&&1006===e.code&&!this._conn.connected?(D.error("Websocket closed unexcectedly"),this._conn._changeConnectStatus(D.Status.CONNFAIL,"The WebSocket connection could not be established or was disconnected."),this._conn._doDisconnect()):D.debug("Websocket closed")}_closeSocket(){this.worker.port.postMessage(["_closeSocket"])}_replaceMessageHandler(){this._messageHandler=e=>this._onMessage(e)}_onWorkerMessage(e){const{data:t}=e,s=t[0];if("_onMessage"===s)this._messageHandler(t[1]);else if(s in this)try{this[s].apply(this,e.data.slice(1))}catch(e){D.log(D.LogLevel.ERROR,e)}else if("log"===s){const e=t[1],s=t[2];D.log(k[e],s)}else D.log(D.LogLevel.ERROR,`Found unhandled service worker message: ${t}`)}},t.$build=F.$build,t.$iq=F.$iq,t.$msg=F.$msg,t.$pres=F.$pres,t.Strophe=F.Strophe;const{b64_sha1:B}=C;e.$build=R,e.$iq=q,e.$msg=M,e.$pres=L,e.Strophe=D,e.b64_sha1=B,Object.defineProperty(e,"__esModule",{value:!0})}));
+!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).strophe={})}(this,(function(e){"use strict";var t="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};const s=function(){let e=t.WebSocket;if(void 0===e)try{e=require("ws")}catch(e){throw new Error('You must install the "ws" package to use Strophe in nodejs.')}return e}();const n=function(){let e=t.DOMParser;if(void 0===e)try{e=require("@xmldom/xmldom").DOMParser}catch(e){throw new Error('You must install the "@xmldom/xmldom" package to use Strophe in nodejs.')}return e}();function i(){if("undefined"==typeof document)try{return(new(0,require("@xmldom/xmldom").DOMImplementation)).createDocument("jabber:client","strophe",null)}catch(e){throw new Error('You must install the "@xmldom/xmldom" package to use Strophe in nodejs.')}if(void 0===document.implementation.createDocument||document.implementation.createDocument&&document.documentMode&&document.documentMode<10){const e=function(){const e=["Msxml2.DOMDocument.6.0","Msxml2.DOMDocument.5.0","Msxml2.DOMDocument.4.0","MSXML2.DOMDocument.3.0","MSXML2.DOMDocument","MSXML.DOMDocument","Microsoft.XMLDOM"];for(let t=0;t<e.length;t++)try{return new ActiveXObject(e[t])}catch(e){}}();return e.appendChild(e.createElement("strophe")),e}return document.implementation.createDocument("jabber:client","strophe",null)}const r=function(e,t){const s=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(s>>16)<<16|65535&s},o=function(e){if("string"!=typeof e)throw new Error("str2binl was passed a non-string");const t=[];for(let s=0;s<8*e.length;s+=8)t[s>>5]|=(255&e.charCodeAt(s/8))<<s%32;return t},a=function(e,t,s,n,i,o){return r((a=r(r(t,e),r(n,o)))<<(h=i)|a>>>32-h,s);var a,h},h=function(e,t,s,n,i,r,o){return a(t&s|~t&n,e,t,i,r,o)},l=function(e,t,s,n,i,r,o){return a(t&n|s&~n,e,t,i,r,o)},c=function(e,t,s,n,i,r,o){return a(t^s^n,e,t,i,r,o)},d=function(e,t,s,n,i,r,o){return a(s^(t|~n),e,t,i,r,o)},u=function(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;let s,n,i,o,a=1732584193,u=-271733879,_=-1732584194,m=271733878;for(let t=0;t<e.length;t+=16)s=a,n=u,i=_,o=m,a=h(a,u,_,m,e[t+0],7,-680876936),m=h(m,a,u,_,e[t+1],12,-389564586),_=h(_,m,a,u,e[t+2],17,606105819),u=h(u,_,m,a,e[t+3],22,-1044525330),a=h(a,u,_,m,e[t+4],7,-176418897),m=h(m,a,u,_,e[t+5],12,1200080426),_=h(_,m,a,u,e[t+6],17,-1473231341),u=h(u,_,m,a,e[t+7],22,-45705983),a=h(a,u,_,m,e[t+8],7,1770035416),m=h(m,a,u,_,e[t+9],12,-1958414417),_=h(_,m,a,u,e[t+10],17,-42063),u=h(u,_,m,a,e[t+11],22,-1990404162),a=h(a,u,_,m,e[t+12],7,1804603682),m=h(m,a,u,_,e[t+13],12,-40341101),_=h(_,m,a,u,e[t+14],17,-1502002290),u=h(u,_,m,a,e[t+15],22,1236535329),a=l(a,u,_,m,e[t+1],5,-165796510),m=l(m,a,u,_,e[t+6],9,-1069501632),_=l(_,m,a,u,e[t+11],14,643717713),u=l(u,_,m,a,e[t+0],20,-373897302),a=l(a,u,_,m,e[t+5],5,-701558691),m=l(m,a,u,_,e[t+10],9,38016083),_=l(_,m,a,u,e[t+15],14,-660478335),u=l(u,_,m,a,e[t+4],20,-405537848),a=l(a,u,_,m,e[t+9],5,568446438),m=l(m,a,u,_,e[t+14],9,-1019803690),_=l(_,m,a,u,e[t+3],14,-187363961),u=l(u,_,m,a,e[t+8],20,1163531501),a=l(a,u,_,m,e[t+13],5,-1444681467),m=l(m,a,u,_,e[t+2],9,-51403784),_=l(_,m,a,u,e[t+7],14,1735328473),u=l(u,_,m,a,e[t+12],20,-1926607734),a=c(a,u,_,m,e[t+5],4,-378558),m=c(m,a,u,_,e[t+8],11,-2022574463),_=c(_,m,a,u,e[t+11],16,1839030562),u=c(u,_,m,a,e[t+14],23,-35309556),a=c(a,u,_,m,e[t+1],4,-1530992060),m=c(m,a,u,_,e[t+4],11,1272893353),_=c(_,m,a,u,e[t+7],16,-155497632),u=c(u,_,m,a,e[t+10],23,-1094730640),a=c(a,u,_,m,e[t+13],4,681279174),m=c(m,a,u,_,e[t+0],11,-358537222),_=c(_,m,a,u,e[t+3],16,-722521979),u=c(u,_,m,a,e[t+6],23,76029189),a=c(a,u,_,m,e[t+9],4,-640364487),m=c(m,a,u,_,e[t+12],11,-421815835),_=c(_,m,a,u,e[t+15],16,530742520),u=c(u,_,m,a,e[t+2],23,-995338651),a=d(a,u,_,m,e[t+0],6,-198630844),m=d(m,a,u,_,e[t+7],10,1126891415),_=d(_,m,a,u,e[t+14],15,-1416354905),u=d(u,_,m,a,e[t+5],21,-57434055),a=d(a,u,_,m,e[t+12],6,1700485571),m=d(m,a,u,_,e[t+3],10,-1894986606),_=d(_,m,a,u,e[t+10],15,-1051523),u=d(u,_,m,a,e[t+1],21,-2054922799),a=d(a,u,_,m,e[t+8],6,1873313359),m=d(m,a,u,_,e[t+15],10,-30611744),_=d(_,m,a,u,e[t+6],15,-1560198380),u=d(u,_,m,a,e[t+13],21,1309151649),a=d(a,u,_,m,e[t+4],6,-145523070),m=d(m,a,u,_,e[t+11],10,-1120210379),_=d(_,m,a,u,e[t+2],15,718787259),u=d(u,_,m,a,e[t+9],21,-343485551),a=r(a,s),u=r(u,n),_=r(_,i),m=r(m,o);return[a,u,_,m]},_={hexdigest:function(e){return function(e){const t="0123456789abcdef";let s="";for(let n=0;n<4*e.length;n++)s+=t.charAt(e[n>>2]>>n%4*8+4&15)+t.charAt(e[n>>2]>>n%4*8&15);return s}(u(o(e),8*e.length))},hash:function(e){return function(e){let t="";for(let s=0;s<32*e.length;s+=8)t+=String.fromCharCode(e[s>>5]>>>s%32&255);return t}(u(o(e),8*e.length))}};class m{constructor(e,t,s){this.mechname=e,this.isClientFirst=t,this.priority=s}test(){return!0}onStart(e){this._connection=e}onChallenge(e,t){throw new Error("You should implement challenge handling!")}clientChallenge(e){if(!this.isClientFirst)throw new Error("clientChallenge should not be called if isClientFirst is false!");return this.onChallenge(e)}onFailure(){this._connection=null}onSuccess(){this._connection=null}}const p=function(e){var t,s,n="",i=e.length;for(t=0;t<i;t++)(s=e.charCodeAt(t))>=0&&s<=127?n+=e.charAt(t):s>2047?(n+=String.fromCharCode(224|s>>12&15),n+=String.fromCharCode(128|s>>6&63),n+=String.fromCharCode(128|s>>0&63)):(n+=String.fromCharCode(192|s>>6&31),n+=String.fromCharCode(128|s>>0&63));return n},g=function(e){e=e||{};for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let s="",n="",i="";const r=e[t],o="object"==typeof r,a=escape(unescape(o?r.value:r));o&&(s=r.expires?";expires="+r.expires:"",n=r.domain?";domain="+r.domain:"",i=r.path?";path="+r.path:""),document.cookie=t+"="+a+s+n+i}};function f(e,t){e[t>>5]|=128<<24-t%32,e[15+(t+64>>9<<4)]=t;var s,n,i,r,o,a,h,l,c=new Array(80),d=1732584193,u=-271733879,_=-1732584194,m=271733878,p=-1009589776;for(s=0;s<e.length;s+=16){for(r=d,o=u,a=_,h=m,l=p,n=0;n<80;n++)c[n]=n<16?e[s+n]:N(c[n-3]^c[n-8]^c[n-14]^c[n-16],1),i=x(x(N(d,5),S(n,u,_,m)),x(x(p,c[n]),b(n))),p=m,m=_,_=N(u,30),u=d,d=i;d=x(d,r),u=x(u,o),_=x(_,a),m=x(m,h),p=x(p,l)}return[d,u,_,m,p]}function S(e,t,s,n){return e<20?t&s|~t&n:e<40?t^s^n:e<60?t&s|t&n|s&n:t^s^n}function b(e){return e<20?1518500249:e<40?1859775393:e<60?-1894007588:-899497514}function T(e,t){var s=y(e);s.length>16&&(s=f(s,8*e.length));for(var n=new Array(16),i=new Array(16),r=0;r<16;r++)n[r]=909522486^s[r],i[r]=1549556828^s[r];var o=f(n.concat(y(t)),512+8*t.length);return f(i.concat(o),672)}function x(e,t){var s=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(s>>16)<<16|65535&s}function N(e,t){return e<<t|e>>>32-t}function y(e){for(var t=[],s=0;s<8*e.length;s+=8)t[s>>5]|=(255&e.charCodeAt(s/8))<<24-s%32;return t}function w(e){for(var t,s,n="",i=0;i<4*e.length;i+=3)for(t=(e[i>>2]>>8*(3-i%4)&255)<<16|(e[i+1>>2]>>8*(3-(i+1)%4)&255)<<8|e[i+2>>2]>>8*(3-(i+2)%4)&255,s=0;s<4;s++)8*i+6*s>32*e.length?n+="=":n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t>>6*(3-s)&63);return n}function A(e){for(var t="",s=0;s<32*e.length;s+=8)t+=String.fromCharCode(e[s>>5]>>>24-s%32&255);return t}const C={b64_hmac_sha1:function(e,t){return w(T(e,t))},b64_sha1:function(e){return w(f(y(e),8*e.length))},binb2str:A,core_hmac_sha1:T,str_hmac_sha1:function(e,t){return A(T(e,t))},str_sha1:function(e){return A(f(y(e),8*e.length))}};function E(e){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e);return t<0?void 0:t}var v=function(e){if(0===arguments.length)throw new TypeError("1 argument required, but only 0 present.");if((e=(e=`${e}`).replace(/[ \t\n\f\r]/g,"")).length%4==0&&(e=e.replace(/==?$/,"")),e.length%4==1||/[^+/0-9A-Za-z]/.test(e))return null;let t="",s=0,n=0;for(let i=0;i<e.length;i++)s<<=6,s|=E(e[i]),n+=6,24===n&&(t+=String.fromCharCode((16711680&s)>>16),t+=String.fromCharCode((65280&s)>>8),t+=String.fromCharCode(255&s),s=n=0);return 12===n?(s>>=4,t+=String.fromCharCode(s)):18===n&&(s>>=2,t+=String.fromCharCode((65280&s)>>8),t+=String.fromCharCode(255&s)),t};function O(e){if(e>=0&&e<64)return"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[e]}var H=function(e){if(0===arguments.length)throw new TypeError("1 argument required, but only 0 present.");let t;for(e=`${e}`,t=0;t<e.length;t++)if(e.charCodeAt(t)>255)return null;let s="";for(t=0;t<e.length;t+=3){const n=[void 0,void 0,void 0,void 0];n[0]=e.charCodeAt(t)>>2,n[1]=(3&e.charCodeAt(t))<<4,e.length>t+1&&(n[1]|=e.charCodeAt(t+1)>>4,n[2]=(15&e.charCodeAt(t+1))<<2),e.length>t+2&&(n[2]|=e.charCodeAt(t+2)>>6,n[3]=63&e.charCodeAt(t+2));for(let e=0;e<n.length;e++)void 0===n[e]?s+="=":s+=O(n[e])}return s};var I={atob:v,btoa:H};function R(e,t){return new D.Builder(e,t)}function M(e){return new D.Builder("message",e)}function q(e){return new D.Builder("iq",e)}function L(e){return new D.Builder("presence",e)}const D={VERSION:"1.5.0",NS:{HTTPBIND:"http://jabber.org/protocol/httpbind",BOSH:"urn:xmpp:xbosh",CLIENT:"jabber:client",AUTH:"jabber:iq:auth",ROSTER:"jabber:iq:roster",PROFILE:"jabber:iq:profile",DISCO_INFO:"http://jabber.org/protocol/disco#info",DISCO_ITEMS:"http://jabber.org/protocol/disco#items",MUC:"http://jabber.org/protocol/muc",SASL:"urn:ietf:params:xml:ns:xmpp-sasl",STREAM:"http://etherx.jabber.org/streams",FRAMING:"urn:ietf:params:xml:ns:xmpp-framing",BIND:"urn:ietf:params:xml:ns:xmpp-bind",SESSION:"urn:ietf:params:xml:ns:xmpp-session",VERSION:"jabber:iq:version",STANZAS:"urn:ietf:params:xml:ns:xmpp-stanzas",XHTML_IM:"http://jabber.org/protocol/xhtml-im",XHTML:"http://www.w3.org/1999/xhtml"},XHTML:{tags:["a","blockquote","br","cite","em","img","li","ol","p","span","strong","ul","body"],attributes:{a:["href"],blockquote:["style"],br:[],cite:["style"],em:[],img:["src","alt","style","height","width"],li:["style"],ol:["style"],p:["style"],span:["style"],strong:[],ul:["style"],body:[]},css:["background-color","color","font-family","font-size","font-style","font-weight","margin-left","margin-right","text-align","text-decoration"],validTag(e){for(let t=0;t<D.XHTML.tags.length;t++)if(e===D.XHTML.tags[t])return!0;return!1},validAttribute(e,t){if(void 0!==D.XHTML.attributes[e]&&D.XHTML.attributes[e].length>0)for(let s=0;s<D.XHTML.attributes[e].length;s++)if(t===D.XHTML.attributes[e][s])return!0;return!1},validCSS(e){for(let t=0;t<D.XHTML.css.length;t++)if(e===D.XHTML.css[t])return!0;return!1}},Status:{ERROR:0,CONNECTING:1,CONNFAIL:2,AUTHENTICATING:3,AUTHFAIL:4,CONNECTED:5,DISCONNECTED:6,DISCONNECTING:7,ATTACHED:8,REDIRECT:9,CONNTIMEOUT:10,BINDREQUIRED:11,ATTACHFAIL:12},ErrorCondition:{BAD_FORMAT:"bad-format",CONFLICT:"conflict",MISSING_JID_NODE:"x-strophe-bad-non-anon-jid",NO_AUTH_MECH:"no-auth-mech",UNKNOWN_REASON:"unknown"},LogLevel:{DEBUG:0,INFO:1,WARN:2,ERROR:3,FATAL:4},ElementType:{NORMAL:1,TEXT:3,CDATA:4,FRAGMENT:11},TIMEOUT:1.1,SECONDARY_TIMEOUT:.1,addNamespace(e,t){D.NS[e]=t},forEachChild(e,t,s){for(let n=0;n<e.childNodes.length;n++){const i=e.childNodes[n];i.nodeType!==D.ElementType.NORMAL||t&&!this.isTagEqual(i,t)||s(i)}},isTagEqual:(e,t)=>e.tagName===t,_xmlGenerator:null,xmlGenerator:()=>(D._xmlGenerator||(D._xmlGenerator=i()),D._xmlGenerator),xmlElement(e){if(!e)return null;const t=D.xmlGenerator().createElement(e);for(let e=1;e<arguments.length;e++){const s=arguments[e];if(s)if("string"==typeof s||"number"==typeof s)t.appendChild(D.xmlTextNode(s));else if("object"==typeof s&&"function"==typeof s.sort)for(let e=0;e<s.length;e++){const n=s[e];"object"==typeof n&&"function"==typeof n.sort&&void 0!==n[1]&&null!==n[1]&&t.setAttribute(n[0],n[1])}else if("object"==typeof s)for(const e in s)Object.prototype.hasOwnProperty.call(s,e)&&void 0!==s[e]&&null!==s[e]&&t.setAttribute(e,s[e])}return t},xmlescape:e=>e=(e=(e=(e=(e=e.replace(/\&/g,"&amp;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;")).replace(/'/g,"&apos;")).replace(/"/g,"&quot;"),xmlunescape:e=>e=(e=(e=(e=(e=e.replace(/\&amp;/g,"&")).replace(/&lt;/g,"<")).replace(/&gt;/g,">")).replace(/&apos;/g,"'")).replace(/&quot;/g,'"'),xmlTextNode:e=>D.xmlGenerator().createTextNode(e),xmlHtmlNode(e){let t;if(n){t=(new n).parseFromString(e,"text/xml")}else t=new ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(e);return t},getText(e){if(!e)return null;let t="";0===e.childNodes.length&&e.nodeType===D.ElementType.TEXT&&(t+=e.nodeValue);for(let s=0;s<e.childNodes.length;s++)e.childNodes[s].nodeType===D.ElementType.TEXT&&(t+=e.childNodes[s].nodeValue);return D.xmlescape(t)},copyElement(e){let t;if(e.nodeType===D.ElementType.NORMAL){t=D.xmlElement(e.tagName);for(let s=0;s<e.attributes.length;s++)t.setAttribute(e.attributes[s].nodeName,e.attributes[s].value);for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.copyElement(e.childNodes[s]))}else e.nodeType===D.ElementType.TEXT&&(t=D.xmlGenerator().createTextNode(e.nodeValue));return t},createHtml(e){let t;if(e.nodeType===D.ElementType.NORMAL){const s=e.nodeName.toLowerCase();if(D.XHTML.validTag(s))try{t=D.xmlElement(s);for(let n=0;n<D.XHTML.attributes[s].length;n++){const i=D.XHTML.attributes[s][n];let r=e.getAttribute(i);if(null!=r&&""!==r&&!1!==r&&0!==r)if("style"===i&&"object"==typeof r&&void 0!==r.cssText&&(r=r.cssText),"style"===i){const e=[],s=r.split(";");for(let t=0;t<s.length;t++){const n=s[t].split(":"),i=n[0].replace(/^\s*/,"").replace(/\s*$/,"").toLowerCase();if(D.XHTML.validCSS(i)){const t=n[1].replace(/^\s*/,"").replace(/\s*$/,"");e.push(i+": "+t)}}e.length>0&&(r=e.join("; "),t.setAttribute(i,r))}else t.setAttribute(i,r)}for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.createHtml(e.childNodes[s]))}catch(e){t=D.xmlTextNode("")}else{t=D.xmlGenerator().createDocumentFragment();for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.createHtml(e.childNodes[s]))}}else if(e.nodeType===D.ElementType.FRAGMENT){t=D.xmlGenerator().createDocumentFragment();for(let s=0;s<e.childNodes.length;s++)t.appendChild(D.createHtml(e.childNodes[s]))}else e.nodeType===D.ElementType.TEXT&&(t=D.xmlTextNode(e.nodeValue));return t},escapeNode:e=>"string"!=typeof e?e:e.replace(/^\s+|\s+$/g,"").replace(/\\/g,"\\5c").replace(/ /g,"\\20").replace(/\"/g,"\\22").replace(/\&/g,"\\26").replace(/\'/g,"\\27").replace(/\//g,"\\2f").replace(/:/g,"\\3a").replace(/</g,"\\3c").replace(/>/g,"\\3e").replace(/@/g,"\\40"),unescapeNode:e=>"string"!=typeof e?e:e.replace(/\\20/g," ").replace(/\\22/g,'"').replace(/\\26/g,"&").replace(/\\27/g,"'").replace(/\\2f/g,"/").replace(/\\3a/g,":").replace(/\\3c/g,"<").replace(/\\3e/g,">").replace(/\\40/g,"@").replace(/\\5c/g,"\\"),getNodeFromJid:e=>e.indexOf("@")<0?null:e.split("@")[0],getDomainFromJid(e){const t=D.getBareJidFromJid(e);if(t.indexOf("@")<0)return t;{const e=t.split("@");return e.splice(0,1),e.join("@")}},getResourceFromJid(e){if(!e)return null;const t=e.split("/");return t.length<2?null:(t.splice(0,1),t.join("/"))},getBareJidFromJid:e=>e?e.split("/")[0]:null,_handleError(e){void 0!==e.stack&&D.fatal(e.stack),e.sourceURL?D.fatal("error: "+this.handler+" "+e.sourceURL+":"+e.line+" - "+e.name+": "+e.message):e.fileName?D.fatal("error: "+this.handler+" "+e.fileName+":"+e.lineNumber+" - "+e.name+": "+e.message):D.fatal("error: "+e.message)},log(e,t){var s;e===this.LogLevel.FATAL&&(null===(s=console)||void 0===s||s.error(t))},debug(e){this.log(this.LogLevel.DEBUG,e)},info(e){this.log(this.LogLevel.INFO,e)},warn(e){this.log(this.LogLevel.WARN,e)},error(e){this.log(this.LogLevel.ERROR,e)},fatal(e){this.log(this.LogLevel.FATAL,e)},serialize(e){if(!e)return null;"function"==typeof e.tree&&(e=e.tree());const t=[...Array(e.attributes.length).keys()].map((t=>e.attributes[t].nodeName));t.sort();let s=t.reduce(((t,s)=>`${t} ${s}="${D.xmlescape(e.attributes.getNamedItem(s).value)}"`),`<${e.nodeName}`);if(e.childNodes.length>0){s+=">";for(let t=0;t<e.childNodes.length;t++){const n=e.childNodes[t];switch(n.nodeType){case D.ElementType.NORMAL:s+=D.serialize(n);break;case D.ElementType.TEXT:s+=D.xmlescape(n.nodeValue);break;case D.ElementType.CDATA:s+="<![CDATA["+n.nodeValue+"]]>"}}s+="</"+e.nodeName+">"}else s+="/>";return s},_requestId:0,_connectionPlugins:{},addConnectionPlugin(e,t){D._connectionPlugins[e]=t},Builder:class{constructor(e,t){"presence"!==e&&"message"!==e&&"iq"!==e||(t&&!t.xmlns?t.xmlns=D.NS.CLIENT:t||(t={xmlns:D.NS.CLIENT})),this.nodeTree=D.xmlElement(e,t),this.node=this.nodeTree}tree(){return this.nodeTree}toString(){return D.serialize(this.nodeTree)}up(){return this.node=this.node.parentNode,this}root(){return this.node=this.nodeTree,this}attrs(e){for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&(void 0===e[t]?this.node.removeAttribute(t):this.node.setAttribute(t,e[t]));return this}c(e,t,s){const n=D.xmlElement(e,t,s);return this.node.appendChild(n),"string"!=typeof s&&"number"!=typeof s&&(this.node=n),this}cnode(e){let t;const s=D.xmlGenerator();try{t=void 0!==s.importNode}catch(e){t=!1}const n=t?s.importNode(e,!0):D.copyElement(e);return this.node.appendChild(n),this.node=n,this}t(e){const t=D.xmlTextNode(e);return this.node.appendChild(t),this}h(e){const t=D.xmlGenerator().createElement("body");t.innerHTML=e;const s=D.createHtml(t);for(;s.childNodes.length>0;)this.node.appendChild(s.childNodes[0]);return this}},Handler:function(e,t,s,n,i,r,o){this.handler=e,this.ns=t,this.name=s,this.type=n,this.id=i,this.options=o||{matchBareFromJid:!1,ignoreNamespaceFragment:!1},this.options.matchBare&&(D.warn('The "matchBare" option is deprecated, use "matchBareFromJid" instead.'),this.options.matchBareFromJid=this.options.matchBare,delete this.options.matchBare),this.options.matchBareFromJid?this.from=r?D.getBareJidFromJid(r):null:this.from=r,this.user=!0}};D.Handler.prototype={getNamespace(e){let t=e.getAttribute("xmlns");return t&&this.options.ignoreNamespaceFragment&&(t=t.split("#")[0]),t},namespaceMatch(e){let t=!1;return!this.ns||(D.forEachChild(e,null,(e=>{this.getNamespace(e)===this.ns&&(t=!0)})),t||this.getNamespace(e)===this.ns)},isMatch(e){let t=e.getAttribute("from");this.options.matchBareFromJid&&(t=D.getBareJidFromJid(t));const s=e.getAttribute("type");return!(!this.namespaceMatch(e)||this.name&&!D.isTagEqual(e,this.name)||this.type&&(Array.isArray(this.type)?-1===this.type.indexOf(s):s!==this.type)||this.id&&e.getAttribute("id")!==this.id||this.from&&t!==this.from)},run(e){let t=null;try{t=this.handler(e)}catch(e){throw D._handleError(e),e}return t},toString(){return"{Handler: "+this.handler+"("+this.name+","+this.id+","+this.ns+")}"}},D.TimedHandler=class{constructor(e,t){this.period=e,this.handler=t,this.lastCalled=(new Date).getTime(),this.user=!0}run(){return this.lastCalled=(new Date).getTime(),this.handler()}reset(){this.lastCalled=(new Date).getTime()}toString(){return"{TimedHandler: "+this.handler+"("+this.period+")}"}},D.Connection=class{constructor(e,t){this.service=e,this.options=t||{},this.setProtocol(),this.jid="",this.domain=null,this.features=null,this._sasl_data={},this.do_bind=!1,this.do_session=!1,this.mechanisms={},this.timedHandlers=[],this.handlers=[],this.removeTimeds=[],this.removeHandlers=[],this.addTimeds=[],this.addHandlers=[],this.protocolErrorHandlers={HTTP:{},websocket:{}},this._idleTimeout=null,this._disconnectTimeout=null,this.authenticated=!1,this.connected=!1,this.disconnecting=!1,this.do_authentication=!0,this.paused=!1,this.restored=!1,this._data=[],this._uniqueId=0,this._sasl_success_handler=null,this._sasl_failure_handler=null,this._sasl_challenge_handler=null,this.maxRetries=5,this._idleTimeout=setTimeout((()=>this._onIdle()),100),g(this.options.cookies),this.registerSASLMechanisms(this.options.mechanisms),this.iqFallbackHandler=new D.Handler((e=>this.send(q({type:"error",id:e.getAttribute("id")}).c("error",{type:"cancel"}).c("service-unavailable",{xmlns:D.NS.STANZAS}))),null,"iq",["get","set"]);for(const e in D._connectionPlugins)if(Object.prototype.hasOwnProperty.call(D._connectionPlugins,e)){const t=function(){};t.prototype=D._connectionPlugins[e],this[e]=new t,this[e].init(this)}}setProtocol(){const e=this.options.protocol||"";this.options.worker?this._proto=new D.WorkerWebsocket(this):0===this.service.indexOf("ws:")||0===this.service.indexOf("wss:")||0===e.indexOf("ws")?this._proto=new D.Websocket(this):this._proto=new D.Bosh(this)}reset(){this._proto._reset(),this.do_session=!1,this.do_bind=!1,this.timedHandlers=[],this.handlers=[],this.removeTimeds=[],this.removeHandlers=[],this.addTimeds=[],this.addHandlers=[],this.authenticated=!1,this.connected=!1,this.disconnecting=!1,this.restored=!1,this._data=[],this._requests=[],this._uniqueId=0}pause(){this.paused=!0}resume(){this.paused=!1}getUniqueId(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}));return"string"==typeof e||"number"==typeof e?t+":"+e:t+""}addProtocolErrorHandler(e,t,s){this.protocolErrorHandlers[e][t]=s}connect(e,t,s,n,i,r,o){let a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:3e3;this.jid=e,this.authzid=D.getBareJidFromJid(this.jid),this.authcid=o||D.getNodeFromJid(this.jid),this.pass=t,this.connect_callback=s,this.disconnecting=!1,this.connected=!1,this.authenticated=!1,this.restored=!1,this.disconnection_timeout=a,this.domain=D.getDomainFromJid(this.jid),this._changeConnectStatus(D.Status.CONNECTING,null),this._proto._connect(n,i,r)}attach(e,t,s,n,i,r,o){if(this._proto._attach)return this._proto._attach(e,t,s,n,i,r,o);{const e=new Error('The "attach" method is not available for your connection protocol');throw e.name="StropheSessionError",e}}restore(e,t,s,n,i){if(!this._sessionCachingSupported()){const e=new Error('The "restore" method can only be used with a BOSH connection.');throw e.name="StropheSessionError",e}this._proto._restore(e,t,s,n,i)}_sessionCachingSupported(){if(this._proto instanceof D.Bosh){if(!JSON)return!1;try{sessionStorage.setItem("_strophe_","_strophe_"),sessionStorage.removeItem("_strophe_")}catch(e){return!1}return!0}return!1}xmlInput(e){}xmlOutput(e){}rawInput(e){}rawOutput(e){}nextValidRid(e){}send(e){if(null!==e){if("function"==typeof e.sort)for(let t=0;t<e.length;t++)this._queueData(e[t]);else"function"==typeof e.tree?this._queueData(e.tree()):this._queueData(e);this._proto._send()}}flush(){clearTimeout(this._idleTimeout),this._onIdle()}sendPresence(e,t,s,n){let i=null;"function"==typeof e.tree&&(e=e.tree());let r=e.getAttribute("id");if(r||(r=this.getUniqueId("sendPresence"),e.setAttribute("id",r)),"function"==typeof t||"function"==typeof s){const e=this.addHandler((e=>{i&&this.deleteTimedHandler(i),"error"===e.getAttribute("type")?s&&s(e):t&&t(e)}),null,"presence",null,r);n&&(i=this.addTimedHandler(n,(()=>(this.deleteHandler(e),s&&s(null),!1))))}return this.send(e),r}sendIQ(e,t,s,n){let i=null;"function"==typeof e.tree&&(e=e.tree());let r=e.getAttribute("id");if(r||(r=this.getUniqueId("sendIQ"),e.setAttribute("id",r)),"function"==typeof t||"function"==typeof s){const e=this.addHandler((e=>{i&&this.deleteTimedHandler(i);const n=e.getAttribute("type");if("result"===n)t&&t(e);else{if("error"!==n){const e=new Error(`Got bad IQ type of ${n}`);throw e.name="StropheError",e}s&&s(e)}}),null,"iq",["error","result"],r);n&&(i=this.addTimedHandler(n,(()=>(this.deleteHandler(e),s&&s(null),!1))))}return this.send(e),r}_queueData(e){if(null===e||!e.tagName||!e.childNodes){const e=new Error("Cannot queue non-DOMElement.");throw e.name="StropheError",e}this._data.push(e)}_sendRestart(){this._data.push("restart"),this._proto._sendRestart(),this._idleTimeout=setTimeout((()=>this._onIdle()),100)}addTimedHandler(e,t){const s=new D.TimedHandler(e,t);return this.addTimeds.push(s),s}deleteTimedHandler(e){this.removeTimeds.push(e)}addHandler(e,t,s,n,i,r,o){const a=new D.Handler(e,t,s,n,i,r,o);return this.addHandlers.push(a),a}deleteHandler(e){this.removeHandlers.push(e);const t=this.addHandlers.indexOf(e);t>=0&&this.addHandlers.splice(t,1)}registerSASLMechanisms(e){this.mechanisms={},(e=e||[D.SASLAnonymous,D.SASLExternal,D.SASLOAuthBearer,D.SASLXOAuth2,D.SASLPlain,D.SASLSHA1]).forEach((e=>this.registerSASLMechanism(e)))}registerSASLMechanism(e){const t=new e;this.mechanisms[t.mechname]=t}disconnect(e){if(this._changeConnectStatus(D.Status.DISCONNECTING,e),e?D.warn("Disconnect was called because: "+e):D.info("Disconnect was called"),this.connected){let e=!1;this.disconnecting=!0,this.authenticated&&(e=L({xmlns:D.NS.CLIENT,type:"unavailable"})),this._disconnectTimeout=this._addSysTimedHandler(this.disconnection_timeout,this._onDisconnectTimeout.bind(this)),this._proto._disconnect(e)}else D.warn("Disconnect was called before Strophe connected to the server"),this._proto._abortAllRequests(),this._doDisconnect()}_changeConnectStatus(e,t,s){for(const s in D._connectionPlugins)if(Object.prototype.hasOwnProperty.call(D._connectionPlugins,s)){const n=this[s];if(n.statusChanged)try{n.statusChanged(e,t)}catch(e){D.error(`${s} plugin caused an exception changing status: ${e}`)}}if(this.connect_callback)try{this.connect_callback(e,t,s)}catch(e){D._handleError(e),D.error(`User connection callback caused an exception: ${e}`)}}_doDisconnect(e){"number"==typeof this._idleTimeout&&clearTimeout(this._idleTimeout),null!==this._disconnectTimeout&&(this.deleteTimedHandler(this._disconnectTimeout),this._disconnectTimeout=null),D.debug("_doDisconnect was called"),this._proto._doDisconnect(),this.authenticated=!1,this.disconnecting=!1,this.restored=!1,this.handlers=[],this.timedHandlers=[],this.removeTimeds=[],this.removeHandlers=[],this.addTimeds=[],this.addHandlers=[],this._changeConnectStatus(D.Status.DISCONNECTED,e),this.connected=!1}_dataRecv(e,t){const s=this._proto._reqToData(e);if(null===s)return;for(this.xmlInput!==D.Connection.prototype.xmlInput&&(s.nodeName===this._proto.strip&&s.childNodes.length?this.xmlInput(s.childNodes[0]):this.xmlInput(s)),this.rawInput!==D.Connection.prototype.rawInput&&(t?this.rawInput(t):this.rawInput(D.serialize(s)));this.removeHandlers.length>0;){const e=this.removeHandlers.pop(),t=this.handlers.indexOf(e);t>=0&&this.handlers.splice(t,1)}for(;this.addHandlers.length>0;)this.handlers.push(this.addHandlers.pop());if(this.disconnecting&&this._proto._emptyQueue())return void this._doDisconnect();const n=s.getAttribute("type");if(null!==n&&"terminate"===n){if(this.disconnecting)return;let e=s.getAttribute("condition");const t=s.getElementsByTagName("conflict");return null!==e?("remote-stream-error"===e&&t.length>0&&(e="conflict"),this._changeConnectStatus(D.Status.CONNFAIL,e)):this._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.UNKOWN_REASON),void this._doDisconnect(e)}D.forEachChild(s,null,(e=>{const t=[];this.handlers=this.handlers.reduce(((s,n)=>{try{!n.isMatch(e)||!this.authenticated&&n.user?s.push(n):(n.run(e)&&s.push(n),t.push(n))}catch(e){D.warn("Removing Strophe handlers due to uncaught exception: "+e.message)}return s}),[]),!t.length&&this.iqFallbackHandler.isMatch(e)&&this.iqFallbackHandler.run(e)}))}_connect_cb(e,t,s){let n;D.debug("_connect_cb was called"),this.connected=!0;try{n=this._proto._reqToData(e)}catch(e){if(e.name!==D.ErrorCondition.BAD_FORMAT)throw e;this._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.BAD_FORMAT),this._doDisconnect(D.ErrorCondition.BAD_FORMAT)}if(!n)return;this.xmlInput!==D.Connection.prototype.xmlInput&&(n.nodeName===this._proto.strip&&n.childNodes.length?this.xmlInput(n.childNodes[0]):this.xmlInput(n)),this.rawInput!==D.Connection.prototype.rawInput&&(s?this.rawInput(s):this.rawInput(D.serialize(n)));if(this._proto._connect_cb(n)===D.Status.CONNFAIL)return;let i;if(i=n.getElementsByTagNameNS?n.getElementsByTagNameNS(D.NS.STREAM,"features").length>0:n.getElementsByTagName("stream:features").length>0||n.getElementsByTagName("features").length>0,!i)return void this._proto._no_auth_received(t);const r=Array.from(n.getElementsByTagName("mechanism")).map((e=>this.mechanisms[e.textContent])).filter((e=>e));0!==r.length||0!==n.getElementsByTagName("auth").length?!1!==this.do_authentication&&this.authenticate(r):this._proto._no_auth_received(t)}sortMechanismsByPriority(e){for(let t=0;t<e.length-1;++t){let s=t;for(let n=t+1;n<e.length;++n)e[n].priority>e[s].priority&&(s=n);if(s!==t){const n=e[t];e[t]=e[s],e[s]=n}}return e}authenticate(e){this._attemptSASLAuth(e)||this._attemptLegacyAuth()}_attemptSASLAuth(e){e=this.sortMechanismsByPriority(e||[]);let t=!1;for(let s=0;s<e.length;++s){if(!e[s].test(this))continue;this._sasl_success_handler=this._addSysHandler(this._sasl_success_cb.bind(this),null,"success",null,null),this._sasl_failure_handler=this._addSysHandler(this._sasl_failure_cb.bind(this),null,"failure",null,null),this._sasl_challenge_handler=this._addSysHandler(this._sasl_challenge_cb.bind(this),null,"challenge",null,null),this._sasl_mechanism=e[s],this._sasl_mechanism.onStart(this);const n=R("auth",{xmlns:D.NS.SASL,mechanism:this._sasl_mechanism.mechname});if(this._sasl_mechanism.isClientFirst){const e=this._sasl_mechanism.clientChallenge(this);n.t(I.btoa(e))}this.send(n.tree()),t=!0;break}return t}_sasl_challenge_cb(e){const t=I.atob(D.getText(e)),s=this._sasl_mechanism.onChallenge(this,t),n=R("response",{xmlns:D.NS.SASL});return""!==s&&n.t(I.btoa(s)),this.send(n.tree()),!0}_attemptLegacyAuth(){null===D.getNodeFromJid(this.jid)?(this._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.MISSING_JID_NODE),this.disconnect(D.ErrorCondition.MISSING_JID_NODE)):(this._changeConnectStatus(D.Status.AUTHENTICATING,null),this._addSysHandler(this._onLegacyAuthIQResult.bind(this),null,null,null,"_auth_1"),this.send(q({type:"get",to:this.domain,id:"_auth_1"}).c("query",{xmlns:D.NS.AUTH}).c("username",{}).t(D.getNodeFromJid(this.jid)).tree()))}_onLegacyAuthIQResult(e){const t=q({type:"set",id:"_auth_2"}).c("query",{xmlns:D.NS.AUTH}).c("username",{}).t(D.getNodeFromJid(this.jid)).up().c("password").t(this.pass);return D.getResourceFromJid(this.jid)||(this.jid=D.getBareJidFromJid(this.jid)+"/strophe"),t.up().c("resource",{}).t(D.getResourceFromJid(this.jid)),this._addSysHandler(this._auth2_cb.bind(this),null,null,null,"_auth_2"),this.send(t.tree()),!1}_sasl_success_cb(e){if(this._sasl_data["server-signature"]){let t;const s=/([a-z]+)=([^,]+)(,|$)/,n=I.atob(D.getText(e)).match(s);if("v"===n[1]&&(t=n[2]),t!==this._sasl_data["server-signature"])return this.deleteHandler(this._sasl_failure_handler),this._sasl_failure_handler=null,this._sasl_challenge_handler&&(this.deleteHandler(this._sasl_challenge_handler),this._sasl_challenge_handler=null),this._sasl_data={},this._sasl_failure_cb(null)}D.info("SASL authentication succeeded."),this._sasl_mechanism&&this._sasl_mechanism.onSuccess(),this.deleteHandler(this._sasl_failure_handler),this._sasl_failure_handler=null,this._sasl_challenge_handler&&(this.deleteHandler(this._sasl_challenge_handler),this._sasl_challenge_handler=null);const t=[],s=(e,t)=>{for(;e.length;)this.deleteHandler(e.pop());return this._onStreamFeaturesAfterSASL(t),!1};return t.push(this._addSysHandler((e=>s(t,e)),null,"stream:features",null,null)),t.push(this._addSysHandler((e=>s(t,e)),D.NS.STREAM,"features",null,null)),this._sendRestart(),!1}_onStreamFeaturesAfterSASL(e){this.features=e;for(let t=0;t<e.childNodes.length;t++){const s=e.childNodes[t];"bind"===s.nodeName&&(this.do_bind=!0),"session"===s.nodeName&&(this.do_session=!0)}return this.do_bind?(this.options.explicitResourceBinding?this._changeConnectStatus(D.Status.BINDREQUIRED,null):this.bind(),!1):(this._changeConnectStatus(D.Status.AUTHFAIL,null),!1)}bind(){if(!this.do_bind)return void D.log(D.LogLevel.INFO,'Strophe.Connection.prototype.bind called but "do_bind" is false');this._addSysHandler(this._onResourceBindResultIQ.bind(this),null,null,null,"_bind_auth_2");const e=D.getResourceFromJid(this.jid);e?this.send(q({type:"set",id:"_bind_auth_2"}).c("bind",{xmlns:D.NS.BIND}).c("resource",{}).t(e).tree()):this.send(q({type:"set",id:"_bind_auth_2"}).c("bind",{xmlns:D.NS.BIND}).tree())}_onResourceBindResultIQ(e){if("error"===e.getAttribute("type")){D.warn("Resource binding failed.");let t;return e.getElementsByTagName("conflict").length>0&&(t=D.ErrorCondition.CONFLICT),this._changeConnectStatus(D.Status.AUTHFAIL,t,e),!1}const t=e.getElementsByTagName("bind");if(!(t.length>0))return D.warn("Resource binding failed."),this._changeConnectStatus(D.Status.AUTHFAIL,null,e),!1;{const e=t[0].getElementsByTagName("jid");e.length>0&&(this.authenticated=!0,this.jid=D.getText(e[0]),this.do_session?this._establishSession():this._changeConnectStatus(D.Status.CONNECTED,null))}}_establishSession(){if(!this.do_session)throw new Error(`Strophe.Connection.prototype._establishSession called but apparently ${D.NS.SESSION} wasn't advertised by the server`);this._addSysHandler(this._onSessionResultIQ.bind(this),null,null,null,"_session_auth_2"),this.send(q({type:"set",id:"_session_auth_2"}).c("session",{xmlns:D.NS.SESSION}).tree())}_onSessionResultIQ(e){if("result"===e.getAttribute("type"))this.authenticated=!0,this._changeConnectStatus(D.Status.CONNECTED,null);else if("error"===e.getAttribute("type"))return this.authenticated=!1,D.warn("Session creation failed."),this._changeConnectStatus(D.Status.AUTHFAIL,null,e),!1;return!1}_sasl_failure_cb(e){return this._sasl_success_handler&&(this.deleteHandler(this._sasl_success_handler),this._sasl_success_handler=null),this._sasl_challenge_handler&&(this.deleteHandler(this._sasl_challenge_handler),this._sasl_challenge_handler=null),this._sasl_mechanism&&this._sasl_mechanism.onFailure(),this._changeConnectStatus(D.Status.AUTHFAIL,null,e),!1}_auth2_cb(e){return"result"===e.getAttribute("type")?(this.authenticated=!0,this._changeConnectStatus(D.Status.CONNECTED,null)):"error"===e.getAttribute("type")&&(this._changeConnectStatus(D.Status.AUTHFAIL,null,e),this.disconnect("authentication failed")),!1}_addSysTimedHandler(e,t){const s=new D.TimedHandler(e,t);return s.user=!1,this.addTimeds.push(s),s}_addSysHandler(e,t,s,n,i){const r=new D.Handler(e,t,s,n,i);return r.user=!1,this.addHandlers.push(r),r}_onDisconnectTimeout(){return D.debug("_onDisconnectTimeout was called"),this._changeConnectStatus(D.Status.CONNTIMEOUT,null),this._proto._onDisconnectTimeout(),this._doDisconnect(),!1}_onIdle(){for(;this.addTimeds.length>0;)this.timedHandlers.push(this.addTimeds.pop());for(;this.removeTimeds.length>0;){const e=this.removeTimeds.pop(),t=this.timedHandlers.indexOf(e);t>=0&&this.timedHandlers.splice(t,1)}const e=(new Date).getTime(),t=[];for(let s=0;s<this.timedHandlers.length;s++){const n=this.timedHandlers[s];if(this.authenticated||!n.user){n.lastCalled+n.period-e<=0?n.run()&&t.push(n):t.push(n)}}this.timedHandlers=t,clearTimeout(this._idleTimeout),this._proto._onIdle(),this.connected&&(this._idleTimeout=setTimeout((()=>this._onIdle()),100))}},D.SASLMechanism=m,D.SASLAnonymous=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ANONYMOUS",arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:20)}test(e){return null===e.authcid}},D.SASLPlain=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"PLAIN",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:50)}test(e){return null!==e.authcid}onChallenge(e){const{authcid:t,authzid:s,domain:n,pass:i}=e;if(!n)throw new Error("SASLPlain onChallenge: domain is not defined!");let r=s!==`${t}@${n}`?s:"";return r+="\0",r+=t,r+="\0",r+=i,p(r)}},D.SASLSHA1=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"SCRAM-SHA-1",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:60)}test(e){return null!==e.authcid}onChallenge(e,t){let s,n,i,r,o,a,h,l,c="c=biws,",d=`${e._sasl_data["client-first-message-bare"]},${t},`;const u=e._sasl_data.cnonce,_=/([a-z]+)=([^,]+)(,|$)/;for(;t.match(_);){const e=t.match(_);switch(t=t.replace(e[0],""),e[1]){case"r":s=e[2];break;case"s":n=e[2];break;case"i":i=e[2]}}if(s.slice(0,u.length)!==u)return e._sasl_data={},e._sasl_failure_cb();c+="r="+s,d+=c,n=atob(n),n+="\0\0\0";const m=p(e.pass);for(r=a=C.core_hmac_sha1(m,n),h=1;h<i;h++){for(o=C.core_hmac_sha1(m,C.binb2str(a)),l=0;l<5;l++)r[l]^=o[l];a=o}r=C.binb2str(r);const g=C.core_hmac_sha1(r,"Client Key"),f=C.str_hmac_sha1(r,"Server Key"),S=C.core_hmac_sha1(C.str_sha1(C.binb2str(g)),d);for(e._sasl_data["server-signature"]=C.b64_hmac_sha1(f,d),l=0;l<5;l++)g[l]^=S[l];return c+=",p="+btoa(C.binb2str(g)),c}clientChallenge(e,t){const s=t||_.hexdigest(""+1234567890*Math.random());let n="n="+p(e.authcid);return n+=",r=",n+=s,e._sasl_data.cnonce=s,e._sasl_data["client-first-message-bare"]=n,n="n,,"+n,n}},D.SASLOAuthBearer=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"OAUTHBEARER",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:40)}test(e){return null!==e.pass}onChallenge(e){let t="n,";return null!==e.authcid&&(t=t+"a="+e.authzid),t+=",",t+="",t+="auth=Bearer ",t+=e.pass,t+="",t+="",p(t)}},D.SASLExternal=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"EXTERNAL",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:10)}onChallenge(e){return e.authcid===e.authzid?"":e.authzid}},D.SASLXOAuth2=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"X-OAUTH2",!(arguments.length>1&&void 0!==arguments[1])||arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:30)}test(e){return null!==e.pass}onChallenge(e){let t="\0";return null!==e.authcid&&(t+=e.authzid),t+="\0",t+=e.pass,p(t)}};var F={Strophe:D,$build:R,$iq:q,$msg:M,$pres:L,SHA1:C,MD5:_,b64_hmac_sha1:C.b64_hmac_sha1,b64_sha1:C.b64_sha1,str_hmac_sha1:C.str_hmac_sha1,str_sha1:C.str_sha1};D.Request=class{constructor(e,t,s,n){this.id=++D._requestId,this.xmlData=e,this.data=D.serialize(e),this.origFunc=t,this.func=t,this.rid=s,this.date=NaN,this.sends=n||0,this.abort=!1,this.dead=null,this.age=function(){if(!this.date)return 0;return(new Date-this.date)/1e3},this.timeDead=function(){if(!this.dead)return 0;return(new Date-this.dead)/1e3},this.xhr=this._newXHR()}getResponse(){let e=null;if(this.xhr.responseXML&&this.xhr.responseXML.documentElement){if(e=this.xhr.responseXML.documentElement,"parsererror"===e.tagName)throw D.error("invalid response received"),D.error("responseText: "+this.xhr.responseText),D.error("responseXML: "+D.serialize(this.xhr.responseXML)),new Error("parsererror")}else if(this.xhr.responseText){if(D.debug("Got responseText but no responseXML; attempting to parse it with DOMParser..."),e=(new n).parseFromString(this.xhr.responseText,"application/xml").documentElement,!e)throw new Error("Parsing produced null node");if(e.querySelector("parsererror")){D.error("invalid response received: "+e.querySelector("parsererror").textContent),D.error("responseText: "+this.xhr.responseText);const t=new Error;throw t.name=D.ErrorCondition.BAD_FORMAT,t}}return e}_newXHR(){let e=null;return window.XMLHttpRequest?(e=new XMLHttpRequest,e.overrideMimeType&&e.overrideMimeType("text/xml; charset=utf-8")):window.ActiveXObject&&(e=new ActiveXObject("Microsoft.XMLHTTP")),e.onreadystatechange=this.func.bind(null,this),e}},D.Bosh=class e{constructor(e){this._conn=e,this.rid=Math.floor(4294967295*Math.random()),this.sid=null,this.hold=1,this.wait=60,this.window=5,this.errors=0,this.inactivity=null,this.lastResponseHeaders=null,this._requests=[]}_buildBody(){const e=R("body",{rid:this.rid++,xmlns:D.NS.HTTPBIND});return null!==this.sid&&e.attrs({sid:this.sid}),this._conn.options.keepalive&&this._conn._sessionCachingSupported()&&this._cacheSession(),e}_reset(){this.rid=Math.floor(4294967295*Math.random()),this.sid=null,this.errors=0,this._conn._sessionCachingSupported()&&window.sessionStorage.removeItem("strophe-bosh-session"),this._conn.nextValidRid(this.rid)}_connect(e,t,s){this.wait=e||this.wait,this.hold=t||this.hold,this.errors=0;const n=this._buildBody().attrs({to:this._conn.domain,"xml:lang":"en",wait:this.wait,hold:this.hold,content:"text/xml; charset=utf-8",ver:"1.6","xmpp:version":"1.0","xmlns:xmpp":D.NS.BOSH});s&&n.attrs({route:s});const i=this._conn._connect_cb;this._requests.push(new D.Request(n.tree(),this._onRequestStateChange.bind(this,i.bind(this._conn)),n.tree().getAttribute("rid"))),this._throttledRequestHandler()}_attach(e,t,s,n,i,r,o){this._conn.jid=e,this.sid=t,this.rid=s,this._conn.connect_callback=n,this._conn.domain=D.getDomainFromJid(this._conn.jid),this._conn.authenticated=!0,this._conn.connected=!0,this.wait=i||this.wait,this.hold=r||this.hold,this.window=o||this.window,this._conn._changeConnectStatus(D.Status.ATTACHED,null)}_restore(e,t,s,n,i){const r=JSON.parse(window.sessionStorage.getItem("strophe-bosh-session"));if(!(null!=r&&r.rid&&r.sid&&r.jid&&(null==e||D.getBareJidFromJid(r.jid)===D.getBareJidFromJid(e)||null===D.getNodeFromJid(e)&&D.getDomainFromJid(r.jid)===e))){const e=new Error("_restore: no restoreable session.");throw e.name="StropheSessionError",e}this._conn.restored=!0,this._attach(r.jid,r.sid,r.rid,t,s,n,i)}_cacheSession(){this._conn.authenticated?this._conn.jid&&this.rid&&this.sid&&window.sessionStorage.setItem("strophe-bosh-session",JSON.stringify({jid:this._conn.jid,rid:this.rid,sid:this.sid})):window.sessionStorage.removeItem("strophe-bosh-session")}_connect_cb(e){const t=e.getAttribute("type");if(null!==t&&"terminate"===t){let t=e.getAttribute("condition");D.error("BOSH-Connection failed: "+t);const s=e.getElementsByTagName("conflict");return null!==t?("remote-stream-error"===t&&s.length>0&&(t="conflict"),this._conn._changeConnectStatus(D.Status.CONNFAIL,t)):this._conn._changeConnectStatus(D.Status.CONNFAIL,"unknown"),this._conn._doDisconnect(t),D.Status.CONNFAIL}this.sid||(this.sid=e.getAttribute("sid"));const s=e.getAttribute("requests");s&&(this.window=parseInt(s,10));const n=e.getAttribute("hold");n&&(this.hold=parseInt(n,10));const i=e.getAttribute("wait");i&&(this.wait=parseInt(i,10));const r=e.getAttribute("inactivity");r&&(this.inactivity=parseInt(r,10))}_disconnect(e){this._sendTerminate(e)}_doDisconnect(){this.sid=null,this.rid=Math.floor(4294967295*Math.random()),this._conn._sessionCachingSupported()&&window.sessionStorage.removeItem("strophe-bosh-session"),this._conn.nextValidRid(this.rid)}_emptyQueue(){return 0===this._requests.length}_callProtocolErrorHandlers(t){const s=e._getRequestStatus(t),n=this._conn.protocolErrorHandlers.HTTP[s];n&&n.call(this,s)}_hitError(e){this.errors++,D.warn("request errored, status: "+e+", number of errors: "+this.errors),this.errors>4&&this._conn._onDisconnectTimeout()}_no_auth_received(e){D.warn("Server did not yet offer a supported authentication mechanism. Sending a blank poll request."),e=e?e.bind(this._conn):this._conn._connect_cb.bind(this._conn);const t=this._buildBody();this._requests.push(new D.Request(t.tree(),this._onRequestStateChange.bind(this,e),t.tree().getAttribute("rid"))),this._throttledRequestHandler()}_onDisconnectTimeout(){this._abortAllRequests()}_abortAllRequests(){for(;this._requests.length>0;){const e=this._requests.pop();e.abort=!0,e.xhr.abort(),e.xhr.onreadystatechange=function(){}}}_onIdle(){const e=this._conn._data;if(this._conn.authenticated&&0===this._requests.length&&0===e.length&&!this._conn.disconnecting&&(D.debug("no requests during idle cycle, sending blank request"),e.push(null)),!this._conn.paused){if(this._requests.length<2&&e.length>0){const t=this._buildBody();for(let s=0;s<e.length;s++)null!==e[s]&&("restart"===e[s]?t.attrs({to:this._conn.domain,"xml:lang":"en","xmpp:restart":"true","xmlns:xmpp":D.NS.BOSH}):t.cnode(e[s]).up());delete this._conn._data,this._conn._data=[],this._requests.push(new D.Request(t.tree(),this._onRequestStateChange.bind(this,this._conn._dataRecv.bind(this._conn)),t.tree().getAttribute("rid"))),this._throttledRequestHandler()}if(this._requests.length>0){const e=this._requests[0].age();null!==this._requests[0].dead&&this._requests[0].timeDead()>Math.floor(D.SECONDARY_TIMEOUT*this.wait)&&this._throttledRequestHandler(),e>Math.floor(D.TIMEOUT*this.wait)&&(D.warn("Request "+this._requests[0].id+" timed out, over "+Math.floor(D.TIMEOUT*this.wait)+" seconds since last activity"),this._throttledRequestHandler())}}}static _getRequestStatus(e,t){let s;if(4===e.xhr.readyState)try{s=e.xhr.status}catch(e){D.error("Caught an error while retrieving a request's status, reqStatus: "+s)}return void 0===s&&(s="number"==typeof t?t:0),s}_onRequestStateChange(t,s){if(D.debug("request id "+s.id+"."+s.sends+" state changed to "+s.xhr.readyState),s.abort)return void(s.abort=!1);if(4!==s.xhr.readyState)return;const n=e._getRequestStatus(s);if(this.lastResponseHeaders=s.xhr.getAllResponseHeaders(),this._conn.disconnecting&&n>=400)return this._hitError(n),void this._callProtocolErrorHandlers(s);const i=this._requests[0]===s,r=this._requests[1]===s,o=n>0&&n<500,a=s.sends>this._conn.maxRetries;(o||a)&&(this._removeRequest(s),D.debug("request id "+s.id+" should now be removed")),200===n?((r||i&&this._requests.length>0&&this._requests[0].age()>Math.floor(D.SECONDARY_TIMEOUT*this.wait))&&this._restartRequest(0),this._conn.nextValidRid(Number(s.rid)+1),D.debug("request id "+s.id+"."+s.sends+" got 200"),t(s),this.errors=0):0===n||n>=400&&n<600||n>=12e3?(D.error("request id "+s.id+"."+s.sends+" error "+n+" happened"),this._hitError(n),this._callProtocolErrorHandlers(s),n>=400&&n<500&&(this._conn._changeConnectStatus(D.Status.DISCONNECTING,null),this._conn._doDisconnect())):D.error("request id "+s.id+"."+s.sends+" error "+n+" happened"),o||a?a&&!this._conn.connected&&this._conn._changeConnectStatus(D.Status.CONNFAIL,"giving-up"):this._throttledRequestHandler()}_processRequest(t){let s=this._requests[t];const n=e._getRequestStatus(s,-1);if(s.sends>this._conn.maxRetries)return void this._conn._onDisconnectTimeout();const i=s.age(),r=!isNaN(i)&&i>Math.floor(D.TIMEOUT*this.wait),o=null!==s.dead&&s.timeDead()>Math.floor(D.SECONDARY_TIMEOUT*this.wait),a=4===s.xhr.readyState&&(n<1||n>=500);if((r||o||a)&&(o&&D.error(`Request ${this._requests[t].id} timed out (secondary), restarting`),s.abort=!0,s.xhr.abort(),s.xhr.onreadystatechange=function(){},this._requests[t]=new D.Request(s.xmlData,s.origFunc,s.rid,s.sends),s=this._requests[t]),0===s.xhr.readyState){D.debug("request id "+s.id+"."+s.sends+" posting");try{const e=this._conn.options.contentType||"text/xml; charset=utf-8";s.xhr.open("POST",this._conn.service,!this._conn.options.sync),void 0!==s.xhr.setRequestHeader&&s.xhr.setRequestHeader("Content-Type",e),this._conn.options.withCredentials&&(s.xhr.withCredentials=!0)}catch(e){return D.error("XHR open failed: "+e.toString()),this._conn.connected||this._conn._changeConnectStatus(D.Status.CONNFAIL,"bad-service"),void this._conn.disconnect()}const e=()=>{if(s.date=new Date,this._conn.options.customHeaders){const e=this._conn.options.customHeaders;for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&s.xhr.setRequestHeader(t,e[t])}s.xhr.send(s.data)};if(s.sends>1){const t=1e3*Math.min(Math.floor(D.TIMEOUT*this.wait),Math.pow(s.sends,3));setTimeout((function(){e()}),t)}else e();s.sends++,this._conn.xmlOutput!==D.Connection.prototype.xmlOutput&&(s.xmlData.nodeName===this.strip&&s.xmlData.childNodes.length?this._conn.xmlOutput(s.xmlData.childNodes[0]):this._conn.xmlOutput(s.xmlData)),this._conn.rawOutput!==D.Connection.prototype.rawOutput&&this._conn.rawOutput(s.data)}else D.debug("_processRequest: "+(0===t?"first":"second")+" request has readyState of "+s.xhr.readyState)}_removeRequest(e){D.debug("removing request");for(let t=this._requests.length-1;t>=0;t--)e===this._requests[t]&&this._requests.splice(t,1);e.xhr.onreadystatechange=function(){},this._throttledRequestHandler()}_restartRequest(e){const t=this._requests[e];null===t.dead&&(t.dead=new Date),this._processRequest(e)}_reqToData(e){try{return e.getResponse()}catch(e){if("parsererror"!==e.message)throw e;this._conn.disconnect("strophe-parsererror")}}_sendTerminate(e){D.debug("_sendTerminate was called");const t=this._buildBody().attrs({type:"terminate"});e&&t.cnode(e.tree());const s=new D.Request(t.tree(),this._onRequestStateChange.bind(this,this._conn._dataRecv.bind(this._conn)),t.tree().getAttribute("rid"));this._requests.push(s),this._throttledRequestHandler()}_send(){clearTimeout(this._conn._idleTimeout),this._throttledRequestHandler(),this._conn._idleTimeout=setTimeout((()=>this._conn._onIdle()),100)}_sendRestart(){this._throttledRequestHandler(),clearTimeout(this._conn._idleTimeout)}_throttledRequestHandler(){this._requests?D.debug("_throttledRequestHandler called with "+this._requests.length+" requests"):D.debug("_throttledRequestHandler called with undefined requests"),this._requests&&0!==this._requests.length&&(this._requests.length>0&&this._processRequest(0),this._requests.length>1&&Math.abs(this._requests[0].rid-this._requests[1].rid)<this.window&&this._processRequest(1))}},D.Bosh.prototype.strip=null,D.Websocket=class{constructor(e){this._conn=e,this.strip="wrapper";const t=e.service;if(0!==t.indexOf("ws:")&&0!==t.indexOf("wss:")){let s="";"ws"===e.options.protocol&&"https:"!==window.location.protocol?s+="ws":s+="wss",s+="://"+window.location.host,0!==t.indexOf("/")?s+=window.location.pathname+t:s+=t,e.service=s}}_buildStream(){return R("open",{xmlns:D.NS.FRAMING,to:this._conn.domain,version:"1.0"})}_checkStreamError(e,t){let s;if(s=e.getElementsByTagNameNS?e.getElementsByTagNameNS(D.NS.STREAM,"error"):e.getElementsByTagName("stream:error"),0===s.length)return!1;const n=s[0];let i="",r="";for(let e=0;e<n.childNodes.length;e++){const t=n.childNodes[e];if("urn:ietf:params:xml:ns:xmpp-streams"!==t.getAttribute("xmlns"))break;"text"===t.nodeName?r=t.textContent:i=t.nodeName}let o="WebSocket stream error: ";return o+=i||"unknown",r&&(o+=" - "+r),D.error(o),this._conn._changeConnectStatus(t,i),this._conn._doDisconnect(),!0}_reset(){}_connect(){this._closeSocket(),this.socket=new s(this._conn.service,"xmpp"),this.socket.onopen=()=>this._onOpen(),this.socket.onerror=e=>this._onError(e),this.socket.onclose=e=>this._onClose(e),this.socket.onmessage=e=>this._onInitialMessage(e)}_connect_cb(e){if(this._checkStreamError(e,D.Status.CONNFAIL))return D.Status.CONNFAIL}_handleStreamStart(e){let t=!1;const s=e.getAttribute("xmlns");"string"!=typeof s?t="Missing xmlns in <open />":s!==D.NS.FRAMING&&(t="Wrong xmlns in <open />: "+s);const n=e.getAttribute("version");return"string"!=typeof n?t="Missing version in <open />":"1.0"!==n&&(t="Wrong version in <open />: "+n),!t||(this._conn._changeConnectStatus(D.Status.CONNFAIL,t),this._conn._doDisconnect(),!1)}_onInitialMessage(e){if(0===e.data.indexOf("<open ")||0===e.data.indexOf("<?xml")){const t=e.data.replace(/^(<\?.*?\?>\s*)*/,"");if(""===t)return;const s=(new n).parseFromString(t,"text/xml").documentElement;this._conn.xmlInput(s),this._conn.rawInput(e.data),this._handleStreamStart(s)&&this._connect_cb(s)}else if(0===e.data.indexOf("<close ")){const t=(new n).parseFromString(e.data,"text/xml").documentElement;this._conn.xmlInput(t),this._conn.rawInput(e.data);const s=t.getAttribute("see-other-uri");if(s){const e=this._conn.service;(e.indexOf("wss:")>=0&&s.indexOf("wss:")>=0||e.indexOf("ws:")>=0)&&(this._conn._changeConnectStatus(D.Status.REDIRECT,"Received see-other-uri, resetting connection"),this._conn.reset(),this._conn.service=s,this._connect())}else this._conn._changeConnectStatus(D.Status.CONNFAIL,"Received closing stream"),this._conn._doDisconnect()}else{this._replaceMessageHandler();const t=this._streamWrap(e.data),s=(new n).parseFromString(t,"text/xml").documentElement;this._conn._connect_cb(s,null,e.data)}}_replaceMessageHandler(){this.socket.onmessage=e=>this._onMessage(e)}_disconnect(e){if(this.socket&&this.socket.readyState!==s.CLOSED){e&&this._conn.send(e);const t=R("close",{xmlns:D.NS.FRAMING});this._conn.xmlOutput(t.tree());const s=D.serialize(t);this._conn.rawOutput(s);try{this.socket.send(s)}catch(e){D.warn("Couldn't send <close /> tag.")}}setTimeout((()=>this._conn._doDisconnect()),0)}_doDisconnect(){D.debug("WebSockets _doDisconnect was called"),this._closeSocket()}_streamWrap(e){return"<wrapper>"+e+"</wrapper>"}_closeSocket(){if(this.socket)try{this.socket.onclose=null,this.socket.onerror=null,this.socket.onmessage=null,this.socket.close()}catch(e){D.debug(e.message)}this.socket=null}_emptyQueue(){return!0}_onClose(e){this._conn.connected&&!this._conn.disconnecting?(D.error("Websocket closed unexpectedly"),this._conn._doDisconnect()):e&&1006===e.code&&!this._conn.connected&&this.socket?(D.error("Websocket closed unexcectedly"),this._conn._changeConnectStatus(D.Status.CONNFAIL,"The WebSocket connection could not be established or was disconnected."),this._conn._doDisconnect()):D.debug("Websocket closed")}_no_auth_received(e){D.error("Server did not offer a supported authentication mechanism"),this._conn._changeConnectStatus(D.Status.CONNFAIL,D.ErrorCondition.NO_AUTH_MECH),e&&e.call(this._conn),this._conn._doDisconnect()}_onDisconnectTimeout(){}_abortAllRequests(){}_onError(e){D.error("Websocket error "+JSON.stringify(e)),this._conn._changeConnectStatus(D.Status.CONNFAIL,"The WebSocket connection could not be established or was disconnected."),this._disconnect()}_onIdle(){const e=this._conn._data;if(e.length>0&&!this._conn.paused){for(let t=0;t<e.length;t++)if(null!==e[t]){let s;s="restart"===e[t]?this._buildStream().tree():e[t];const n=D.serialize(s);this._conn.xmlOutput(s),this._conn.rawOutput(n),this.socket.send(n)}this._conn._data=[]}}_onMessage(e){let t;const s='<close xmlns="urn:ietf:params:xml:ns:xmpp-framing" />';if(e.data===s)return this._conn.rawInput(s),this._conn.xmlInput(e),void(this._conn.disconnecting||this._conn._doDisconnect());if(0===e.data.search("<open ")){if(t=(new n).parseFromString(e.data,"text/xml").documentElement,!this._handleStreamStart(t))return}else{const s=this._streamWrap(e.data);t=(new n).parseFromString(s,"text/xml").documentElement}return this._checkStreamError(t,D.Status.ERROR)?void 0:this._conn.disconnecting&&"presence"===t.firstChild.nodeName&&"unavailable"===t.firstChild.getAttribute("type")?(this._conn.xmlInput(t),void this._conn.rawInput(D.serialize(t))):void this._conn._dataRecv(t,e.data)}_onOpen(){D.debug("Websocket open");const e=this._buildStream();this._conn.xmlOutput(e.tree());const t=D.serialize(e);this._conn.rawOutput(t),this.socket.send(t)}_reqToData(e){return e}_send(){this._conn.flush()}_sendRestart(){clearTimeout(this._conn._idleTimeout),this._conn._onIdle.bind(this._conn)()}};const k={};k.debug=D.LogLevel.DEBUG,k.info=D.LogLevel.INFO,k.warn=D.LogLevel.WARN,k.error=D.LogLevel.ERROR,k.fatal=D.LogLevel.FATAL,D.WorkerWebsocket=class extends D.Websocket{constructor(e){super(e),this._conn=e,this.worker=new SharedWorker(this._conn.options.worker,"Strophe XMPP Connection"),this.worker.onerror=e=>{var t;null===(t=console)||void 0===t||t.error(e),D.log(D.LogLevel.ERROR,`Shared Worker Error: ${e}`)}}get socket(){return{send:e=>this.worker.port.postMessage(["send",e])}}_connect(){this._messageHandler=e=>this._onInitialMessage(e),this.worker.port.start(),this.worker.port.onmessage=e=>this._onWorkerMessage(e),this.worker.port.postMessage(["_connect",this._conn.service,this._conn.jid])}_attach(e){this._messageHandler=e=>this._onMessage(e),this._conn.connect_callback=e,this.worker.port.start(),this.worker.port.onmessage=e=>this._onWorkerMessage(e),this.worker.port.postMessage(["_attach",this._conn.service])}_attachCallback(e,t){e===D.Status.ATTACHED?(this._conn.jid=t,this._conn.authenticated=!0,this._conn.connected=!0,this._conn.restored=!0,this._conn._changeConnectStatus(D.Status.ATTACHED)):e===D.Status.ATTACHFAIL&&(this._conn.authenticated=!1,this._conn.connected=!1,this._conn.restored=!1,this._conn._changeConnectStatus(D.Status.ATTACHFAIL))}_disconnect(e,t){t&&this._conn.send(t);const s=R("close",{xmlns:D.NS.FRAMING});this._conn.xmlOutput(s.tree());const n=D.serialize(s);this._conn.rawOutput(n),this.worker.port.postMessage(["send",n]),this._conn._doDisconnect()}_onClose(e){this._conn.connected&&!this._conn.disconnecting?(D.error("Websocket closed unexpectedly"),this._conn._doDisconnect()):e&&1006===e.code&&!this._conn.connected?(D.error("Websocket closed unexcectedly"),this._conn._changeConnectStatus(D.Status.CONNFAIL,"The WebSocket connection could not be established or was disconnected."),this._conn._doDisconnect()):D.debug("Websocket closed")}_closeSocket(){this.worker.port.postMessage(["_closeSocket"])}_replaceMessageHandler(){this._messageHandler=e=>this._onMessage(e)}_onWorkerMessage(e){const{data:t}=e,s=t[0];if("_onMessage"===s)this._messageHandler(t[1]);else if(s in this)try{this[s].apply(this,e.data.slice(1))}catch(e){D.log(D.LogLevel.ERROR,e)}else if("log"===s){const e=t[1],s=t[2];D.log(k[e],s)}else D.log(D.LogLevel.ERROR,`Found unhandled service worker message: ${t}`)}},t.$build=F.$build,t.$iq=F.$iq,t.$msg=F.$msg,t.$pres=F.$pres,t.Strophe=F.Strophe;const{b64_sha1:B}=C;e.$build=R,e.$iq=q,e.$msg=M,e.$pres=L,e.Strophe=D,e.b64_sha1=B,Object.defineProperty(e,"__esModule",{value:!0})}));

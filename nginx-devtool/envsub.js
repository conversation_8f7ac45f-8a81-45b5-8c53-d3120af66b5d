#!/usr/bin/env node

const templateDir = 'src';
const outputDir = 'dist';
const suffix = '.tpl';
const envCmdFile = JSON.parse(process.env.npm_config_argv).original[1]
const userPath = `${process.env.USERNAME ?? process.env.USER}.dev.env`
const envPath = `./${envCmdFile ?? userPath}`;

const fs = require('fs');
const path = require('path');
const ejs = require('ejs');

require('dotenv').config({ path: envPath });

function entrypointLog(message) {
  if (!process.env.NGINX_ENTRYPOINT_QUIET_LOGS) {
    console.log(message);
  }
}

function getEnvVarCaseSensitive(envVar) {
  return Object.keys(process.env).find(key => key === envVar);
}

// Function to replace instances of $ENV with the actual environment variable values
function replaceEnvVariables(templateContent) {
  return templateContent.replace(/\$(\w+)/g, (match, envVar) => {
    const actualEnvVar = getEnvVarCaseSensitive(envVar);
    return actualEnvVar ? process.env[actualEnvVar] : match;
  });
}

// Function to replace instances of ${ENV} with the actual environment variable values
function replaceEnvVariables2(templateContent) {
  return templateContent.replace(/\${(\w+)}/g, (match, envVar) => {
    const actualEnvVar = getEnvVarCaseSensitive(envVar);
    return actualEnvVar ? process.env[actualEnvVar] : match;
  });
}

// Helper function to recursively find all files with the given suffix
function findFilesRecursive(dir, suffix) {
  let results = [];
  const list = fs.readdirSync(dir);

  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat && stat.isDirectory()) {
      // Recursively find files in subdirectory
      results = results.concat(findFilesRecursive(filePath, suffix));
    } else if (file.endsWith(suffix)) {
      // Only include files that end with the suffix
      results.push(filePath);
    }
  });

  return results;
}

// Ensure the output directory exists
if (fs.existsSync(outputDir)) {
  fs.rmSync(outputDir, { recursive: true });
}
fs.mkdirSync(outputDir, { recursive: true });

if (!fs.existsSync(templateDir)) {
  process.exit(0);
}

// Recursively find and process all template files that end with the suffix
const templateFiles = findFilesRecursive(templateDir, suffix);

templateFiles.forEach(templatePath => {
  const relativePath = path.relative(templateDir, templatePath);
  // Remove the '.tpl' suffix from the filename
  const outputPath = path.join(outputDir, relativePath.replace(suffix, ''));
  const subdir = path.dirname(outputPath);

  // Create subdirectories as needed
  fs.mkdirSync(subdir, { recursive: true });

  entrypointLog(`Running EJS render on ${templatePath} to ${outputPath}`);

  // Read and render the EJS template, passing environment variables
  const templateContent = fs.readFileSync(templatePath, 'utf-8');
  let renderedContent = replaceEnvVariables(templateContent);
  renderedContent = replaceEnvVariables2(renderedContent);
  renderedContent = ejs.render(renderedContent, process.env);

  // Write the rendered content to the output file without the suffix
  fs.writeFileSync(outputPath, renderedContent, 'utf-8');
});

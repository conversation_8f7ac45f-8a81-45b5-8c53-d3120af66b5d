const fs = require('fs');
const path = require('path');
const envCmdFile = JSON.parse(process.env.npm_config_argv).original[1]
const userPath = `${process.env.USERNAME ?? process.env.USER}.dev.env`
const envPath = `./${envCmdFile ?? userPath}`;
const outputDir = 'dist';
require('dotenv').config({ path: envPath });

// Copy the environment file to the output directory
fs.copyFile(envPath, path.join(outputDir, '.env'), (err) => {
    if (err) {
        console.error('Error copying .env file:', err);
    } else {
        console.log('.env copied successfully');
    }
});
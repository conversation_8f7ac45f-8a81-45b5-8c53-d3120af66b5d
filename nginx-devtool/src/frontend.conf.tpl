map $http_upgrade $connection_upgrade {
    default upgrade;
    `` close;
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;

    server_name devlocal.viclass.vn;

    # SSL Configuration

    ssl_certificate     /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;
    ssl_protocols       TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers         HIGH:!aNULL:!MD5;

    # ~~~ STATIC DEPLOYMENT ~~~
    # Pages like home page, classrooms, etc.. have their frontend code all distributed and
    # served at a certain folder (prod) or common dev server (dev) through Nginx

    include conf.d/viclass/frontend/$CONF_STATIC.conf;

    include conf.d/viclass/frontend/$CONF_HOMEPAGE.conf;
    include conf.d/viclass/frontend/$CONF_CLASSROOM.conf;
    include conf.d/viclass/frontend/$CONF_SUPPORT.conf;
    include conf.d/viclass/frontend/$CONF_LSESSION.conf;
    include conf.d/viclass/frontend/$CONF_METADATA.conf;

    # ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    # ~~~ API ~~~

    # Other services also expose some APIs so that from frontend / 3rd party website users
    # can access these API e.g. for create document, load document, modify document, etc...

    include conf.d/viclass/frontend/$CONF_API.conf;
    include conf.d/viclass/frontend/$CONF_BETA.conf;
    include conf.d/viclass/frontend/$CONF_SHORTURL.conf;
    include conf.d/viclass/frontend/$CONF_CCS.conf;
    include conf.d/viclass/frontend/$CONF_FREEDRAWING.conf;
    include conf.d/viclass/frontend/$CONF_GEO.conf;
    include conf.d/viclass/frontend/$CONF_WORD.conf;
    include conf.d/viclass/frontend/$CONF_MATH.conf;
    include conf.d/viclass/frontend/$CONF_MAGH.conf;
    include conf.d/viclass/frontend/$CONF_COMPOSER.conf;
    include conf.d/viclass/frontend/$CONF_FILESTORE.conf;

    # configuration service API
    include conf.d/viclass/frontend/$CONF_CONF.conf;

    # ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    # ~~~ FEDERATED MODULES ~~~~
    # Editors, coordinators, and their UIs etc.. are component implementations loaded by the frontend
    # or 3rd party website users. These component implementations are wrapped inside webpack
    # federated module so that their dependencies can be shared among each other or the
    # host applications. The federated modules are distributed and deployed and served
    # through Nginx.

    include conf.d/viclass/frontend/$CONF_MODULE_FREEDRAWING.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_EDIORUI_FREEDRAWING.conf;

    include conf.d/viclass/frontend/$CONF_MODULE_GEO.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_EDIORUI_GEO.conf;

    include conf.d/viclass/frontend/$CONF_MODULE_WORD.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_EDIORUI_WORD.conf;

    include conf.d/viclass/frontend/$CONF_MODULE_MATH.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_EDIORUI_MATH.conf;

    include conf.d/viclass/frontend/$CONF_MODULE_MAGH.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_EDIORUI_MAGH.conf;

    include conf.d/viclass/frontend/$CONF_MODULE_COMPOSER.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_EDIORUI_COMPOSER.conf;

    include conf.d/viclass/frontend/$CONF_MODULE_COORDINATOR.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_WEB_WRAPPERS.conf;
    include conf.d/viclass/frontend/$CONF_MODULE_THEMES.conf;


    # ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    #--- TESTING PURPOSE ----
    location /local-test {
        #proxy_pass http://${VICLASS_LOCAL}:3000/;
        alias /packages/test/viclass;
    }
}

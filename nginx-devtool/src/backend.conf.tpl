stream {
    include conf.d/viclass/backend/$CONF_MONGO.conf;
    # Portal backend is considered frontend, and configured inside frontend/api.*.tpl

    # backend for other service to connect to portal backend
    # include conf.d/viclass/backend/backend/${CONF_PORTAL_BACKEND}.conf;

    include conf.d/viclass/backend/$CONF_CCS_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_PORTAL_CONFIGURATION.conf;
    include conf.d/viclass/backend/$CONF_PORTAL_USER.conf;
    include conf.d/viclass/backend/$CONF_PORTAL_LSESSION.conf;
    include conf.d/viclass/backend/$CONF_PORTAL_NOTIFICATION.conf;
    # HTTP API of Filestore is considered frontend, and configured inside frontend/filestore.*.tpl
    # Below is for the gRPC part of Portal Filestore
    include conf.d/viclass/backend/$CONF_PORTAL_FILESTORE.conf;
    include conf.d/viclass/backend/$CONF_REDIS.conf;
    include conf.d/viclass/backend/$CONF_KAFKA.conf;
    include conf.d/viclass/backend/$CONF_FREEDRAWING_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_GEOMETRY_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_WORD_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_MATH_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_MAGH_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_COMPOSER_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_METADATA_BACKEND.conf;
    include conf.d/viclass/backend/$CONF_PORTAL_BETA.conf;
    include conf.d/viclass/backend/$CONF_PORTAL_SHORTURL.conf;
}

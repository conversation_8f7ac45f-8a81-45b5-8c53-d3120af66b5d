<% if (process.env.VICLASS_LOCAL_NGINX_ENABLE == "true") { %>
location /static {
    # CORS headers
    add_header Access-Control-Allow-Origin * always;

    # Handle preflight requests
    if ($request_method = OPTIONS) {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';
        add_header Access-Control-Max-Age 1728000;
        return 204;
    }
    
    proxy_pass http://$VICLASS_LOCAL_NGINX_HOST:$VICLASS_LOCAL_NGINX_PORT;
}
<% } else { %>
location /static {
    # CORS headers
    add_header Access-Control-Allow-Origin * always;

    # Handle preflight requests
    if ($request_method = OPTIONS) {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';
        add_header Access-Control-Max-Age 1728000;
        return 204;
    }
    
    proxy_pass http://$VICLASS_LOCAL/;
}
<% } %>

{"scripts": {"build": "node envsub.js && node copyenv.js", "up": "yarn build && node -e \"require('child_process').execSync('docker compose --env-file ./dist/.env up -d', { stdio: 'inherit' })\"", "dev": "yarn build && node -e \"require('child_process').execSync('docker compose --env-file ./dist/.env up', { stdio: 'inherit' })\"", "down": "docker compose --env-file ./dist/.env down"}, "dependencies": {"dotenv": "^16.4.5", "ejs": "^3.1.10"}}
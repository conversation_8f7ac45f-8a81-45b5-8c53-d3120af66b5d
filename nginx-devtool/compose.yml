services:
  viclass-devtool:
    container_name: viclass-devtool
    image: nginx:latest
    volumes:
      - ./dist:/etc/nginx/conf.d/viclass:ro # template files to be replaced
      - ./nginx.conf:/etc/nginx/nginx.conf:ro # use our own nginx.conf file
      - ./ssl:/etc/nginx/ssl # ssl keys for viclass domain
      - ../viclass/dist:/dist
      - ../viclass/packages:/packages
    ports:
      - $NGINX_CONNECT_PORT:$NGINX_LISTEN_PORT
      # expose the connect port so that outside service can connect
      # these exposures are needed because backend services are accessed through
      # GRPC connection. Nginx listen at connect port and proxy to the actual listening port of
      # the corresponding services (locally or remotely). It is best to keeps the port that local service binding
      # on the same as the remote port that remote service binds on, to avoid misconfiguration when switching from remote to local or vice versa.
      - $PORTAL_USER_CONNECT_PORT:$PORTAL_USER_CONNECT_PORT
      - $PORTAL_LSESSION_CONNECT_PORT:$PORTAL_LSESSION_CONNECT_PORT
      - $PORTAL_CONFIGURATION_CONNECT_PORT:$PORTAL_CONFIGURATION_CONNECT_PORT
      - $PORTAL_NOTIFICATION_CONNECT_PORT:$PORTAL_NOTIFICATION_CONNECT_PORT
      - $PORTAL_FILESTORE_CONNECT_PORT:$PORTAL_FILESTORE_CONNECT_PORT
      - $PORTAL_JOBRUNR_CONNECT_PORT:$PORTAL_JOBRUNR_CONNECT_PORT
      - $PORTAL_BETA_CONNECT_PORT:$PORTAL_BETA_CONNECT_PORT
      - $PORTAL_SHORTURL_CONNECT_PORT:$PORTAL_SHORTURL_CONNECT_PORT

      - $FREEDRAWING_BACKEND_CONNECT_PORT:$FREEDRAWING_BACKEND_CONNECT_PORT
      - $GEOMETRY_BACKEND_CONNECT_PORT:$GEOMETRY_BACKEND_CONNECT_PORT
      - $WORD_BACKEND_CONNECT_PORT:$WORD_BACKEND_CONNECT_PORT
      - $MATH_BACKEND_CONNECT_PORT:$MATH_BACKEND_CONNECT_PORT
      - $MAGH_BACKEND_CONNECT_PORT:$MAGH_BACKEND_CONNECT_PORT
      - $COMPOSER_BACKEND_CONNECT_PORT:$COMPOSER_BACKEND_CONNECT_PORT
      - $CONF_BACKEND_CONNECT_PORT:$CONF_BACKEND_CONNECT_PORT

      - $KAFKA_CONNECT_PORT:$KAFKA_CONNECT_PORT
      - $MONGO_CONNECT_PORT:$MONGO_CONNECT_PORT
      - $REDIS_CONNECT_PORT:$REDIS_CONNECT_PORT
      - $VINET_CCS_CONNECT_PORT:$VINET_CCS_CONNECT_PORT
      - $PORTAL_METADATA_CONNECT_PORT:$PORTAL_METADATA_CONNECT_PORT

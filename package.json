{"name": "html-to-image", "version": "1.11.11", "description": "Generates an image from a DOM node using HTML5 canvas and SVG.", "main": "lib/index.js", "module": "es/index.js", "unpkg": "dist/html-to-image.js", "types": "lib/index.d.ts", "files": ["dist", "es", "lib", "src"], "keywords": ["screenshot", "capture", "canvas", "html", "dom", "image", "vector", "svg"], "scripts": {"lint": "eslint 'src/**/*.{js,ts}?(x)' --fix", "clean": "rimraf dist es lib", "build:esm": "tsc --module esnext --target es2017 --outDir ./es", "build:cjs": "tsc --module commonjs --target es5 --outDir ./lib", "build:umd": "rollup -c --bundleConfigAsCjs", "build": "run-s build:esm build:cjs build:umd", "dist": "node _dist-copy.js", "test": "karma start", "test:watch": "karma start --single-run=false --auto-watch", "test:debug": "karma start --browsers=Chrome --single-run=false --auto-watch --debug", "coveralls": "cat ./test/coverage/lcov.info | coveralls", "pretest": "rimraf ./test/coverage", "prepare": "is-ci || husky install .husky"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,less,md,json}": ["pretty-quick —-staged"], "*.ts": ["eslint --fix"]}, "release": {"extends": "@bubkoo/semantic-release-config"}, "license": "MIT", "author": {"name": "bubkooo", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/bubkoo/html-to-image.git"}, "bugs": {"url": "https://github.com/bubkoo/html-to-image/issues"}, "homepage": "https://github.com/bubkoo/html-to-image#readme", "devDependencies": {"@bubkoo/commitlint-config": "^1.0.2", "@bubkoo/eslint-config": "^1.3.1", "@bubkoo/rollup-config": "^1.2.1", "@bubkoo/semantic-release-config": "^1.6.1", "@bubkoo/tsconfig": "^1.0.0", "@fortawesome/fontawesome-free": "^6.1.2", "@types/jasmine": "^4.3.1", "@types/pixelmatch": "^5.2.4", "coveralls": "^3.1.1", "eslint": "^8.22.0", "husky": "^8.0.1", "is-ci": "^3.0.1", "jasmine-core": "^4.3.0", "karma": "^6.4.0", "karma-chrome-launcher": "^3.1.1", "karma-coverage": "^2.2.0", "karma-jasmine": "^5.1.0", "karma-spec-reporter": "^0.0.36", "karma-typescript": "^5.5.3", "npm-run-all": "^4.1.5", "pixelmatch": "^5.3.0", "prettier": "^2.8.1", "pretty-quick": "^3.1.3", "rimraf": "^4.1.2", "rollup": "^3.7.4", "tslib": "^2.4.0", "typescript": "^4.9.5", "vinyl-fs": "^4.0.0"}}
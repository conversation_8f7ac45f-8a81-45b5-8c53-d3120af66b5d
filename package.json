{"name": "mathlive", "version": "0.105.2", "description": "A web component for math input", "license": "MIT", "funding": {"type": "individual", "url": "https://paypal.me/arnogourdol"}, "private": true, "files": ["/dist"], "types": "./types/mathlive.d.ts", "module": "./mathlive.min.mjs", "main": "./mathlive.min.js", "exports": {"./vue": "./vue-mathlive.mjs", "./fonts.css": "./mathlive-fonts.css", "./static.css": "./mathlive-static.css", ".": {"browser": {"production": {"types": "./types/mathlive.d.ts", "import": "./mathlive.min.mjs", "require": "./mathlive.min.js"}, "development": {"types": "./types/mathlive.d.ts", "import": "./mathlive.mjs", "require": "./mathlive.js"}}, "node": {"types": "./types/mathlive-ssr.d.ts", "import": "./mathlive-ssr.min.mjs"}, "default": {"types": "./types/mathlive.d.ts", "import": "./mathlive.min.mjs", "require": "./mathlive.min.js"}}, "./ssr": {"types": "./types/mathlive-ssr.d.ts", "import": "./mathlive-ssr.min.mjs"}}, "repository": {"type": "git", "url": "git+https://github.com/arnog/mathlive.git"}, "bugs": "https://github.com/arnog/mathlive/issues/", "scripts": {"doc": "bash ./scripts/update-docs.sh", "build": "bash ./scripts/build.sh production", "clean": "bash ./scripts/clean.sh", "dist": "node _dist-copy.js", "lint": "eslint --fix src/; prettier -w src/", "prepare": "bash ./scripts/build.sh production", "start": "bash ./scripts/start.sh", "test": "bash ./scripts/test.sh", "version": "bash ./scripts/version.sh"}, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> (lilfaf.github.io)", "<PERSON> (https://github.com/NSoiffer)", "<PERSON> (https://github.com/jboxman)", "Synergy Codes (https://www.synergycodes.com/)", "<PERSON><PERSON><PERSON> (https://github.com/rpdiss)", "<PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<PERSON>.Heim<PERSON><EMAIL>>", "<PERSON> <paul<PERSON><EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "le<PERSON>bert <<EMAIL>>", "spirobel (https://github.com/spirobel) <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <johanneswi<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "pao<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> (https://github.com/<PERSON><PERSON><PERSON>)", "<PERSON>er (https://github.com/beneater)", "<PERSON><PERSON><PERSON> (https://github.com/djan<PERSON>)", "<PERSON><PERSON> (https://github.com/physedo)", "<PERSON><PERSON> (https://github.com/gunnarmein-ts)", "<PERSON><PERSON><PERSON> (https://github.com/zeyad-ahmad-aql)", "<PERSON><PERSON><PERSON> (https://github.com/s<PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/kvas-damian)", "https://github.com/EaswarRaju", "https://github.com/wenyufei2018", "https://github.com/xing38", "https://github.com/Neverland", "https://github.com/AceGentile", "https://github.com/eds007", "https://github.com/harrisnl", "https://github.com/rkeulemans", "https://github.com/classicneupane", "<PERSON><PERSON> (https://github.com/z<PERSON><PERSON>)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/Kydyralin)", "<PERSON> (https://github.com/ChrBrkmn)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/xrmx)", "<PERSON><PERSON> (https://github.com/yakovlev-alexey)", "<PERSON> (https://github.com/<PERSON><PERSON><PERSON>-<PERSON>)", "<PERSON><PERSON> (https://github.com/alexprey)", "<PERSON><PERSON> (https://github.com/pontusgranstrom)", "<PERSON> (https://github.com/bengolds)", "Max (https://github.com/manstie)", "https://github.com/stefnotch", "<PERSON> (https://github.com/caleb-flores)", "<PERSON> (https://github.com/dannygreg)", "<PERSON> (https://github.com/LuisMesa)", "<PERSON> (https://github.com/androettop)", "<PERSON> (https://github.com/wildyellowfin)", "<PERSON> (https://github.com/mgreminger)", "<PERSON> (https://github.com/oscarhermoso)"], "browserslist": ["> 1%", "maintained node versions", "not dead"], "engines": {"npm": ">=10.5.0", "node": ">=21.0.0"}, "prettier": "@cortex-js/prettier-config", "dependencies": {"@cortex-js/compute-engine": "0.28.0", "mathlive": "file:", "vinyl-fs": "^4.0.0"}, "devDependencies": {"@arnog/esbuild-plugin-less": "^1.1.0", "@cortex-js/prettier-config": "^1.2.0", "@playwright/test": "^1.43.0", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.26.1", "autoprefixer": "^10.4.17", "concat-md": "^0.5.1", "cssnano": "^6.0.3", "esbuild": "^0.25.1", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-no-unsanitized": "^4.1.2", "eslint-plugin-prettier": "^5.2.5", "jest": "^29.7.0", "jest-silent-reporter": "^0.5.0", "less": "^4.2.2", "postcss": "^8.4.35", "postcss-cli": "^11.0.0", "prettier": "^3.5.3", "ts-jest": "^29.3.0", "typedoc": "^0.28.2", "typedoc-plugin-markdown": "^4.4.2", "typescript": "^5.8.3"}, "keywords": ["math", "editor", "javascript", "math-editing", "cortex", "compute engine", "virtual keyboard", "math keyboard", "latex", "tex", "mathjax", "katex", "<PERSON><PERSON><PERSON>"]}
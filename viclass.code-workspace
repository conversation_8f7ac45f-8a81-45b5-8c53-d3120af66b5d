{
	"folders": [
		{
			"path": "."
		},
		{
			"path": "vinet\\sync"
		},
		{
			"path": "../viclass-jitsi"
		},
		{
			"path": "../viclass-deployment"
		}
	],
	"settings": {
		"java.compile.nullAnalysis.mode": "automatic",
		"java.configuration.updateBuildConfiguration": "interactive",
		"[typescript]": {
			"editor.codeActionsOnSave": {
				"source.organizeImports": "always",
				"source.addMissingImports": "always"
            },
            "editor.defaultFormatter": "esbenp.prettier-vscode"
		},
		"vsicons.presets.angular": true,

		"editor.codeActionsOnSave": {
            "source.fixAll": "explicit",
        },
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.formatOnSave": false,
        "editor.detectIndentation": false,
        "editor.wrappingIndent": "deepIndent",
        "editor.autoIndent": "full",
        "editor.defaultFormatter": "dbaeumer.vscode-eslint",
        "eslint.workingDirectories": [
            "./viclass"
        ],
        "eslint.enable": true,
        "eslint.run": "onSave",
        "eslint.codeActionsOnSave.mode": "all",
        "eslint.lintTask.options": ".",
        "[scss]": {
            "editor.defaultFormatter": "esbenp.prettier-vscode",
        },
        "css.lint.unknownAtRules": "ignore",
        "files.associations": {
            "*.scss": "scss",
            "*.css": "tailwindcss",
        },
        "[markdown]": {
            "editor.defaultFormatter": "esbenp.prettier-vscode",
        },
        "angular.enable-strict-mode-prompt": false,
        "[astro]": {
            "editor.defaultFormatter": "astro-build.astro-vscode",
        },
        "[tailwindcss]": {
            "editor.defaultFormatter": "esbenp.prettier-vscode",
        },
        "[json]": {
            "editor.defaultFormatter": "vscode.json-language-features",
        },
        "markdownlint.config": {
            "MD030": false,
        },
        "[nginx]": {
            "editor.defaultFormatter": "ahmadalli.vscode-nginx-conf",
        },
        "[proto3]": {
            "editor.defaultFormatter": "zxh404.vscode-proto3",
        },
        "scss.lint.unknownAtRules": "ignore",
        "eslint.format.enable": true,
        "eslint.lintTask.enable": true,
        "prettier.configPath": "./viclass/.prettierrc.json",
	},
	"launch": {
		"version": "0.2.0",
		"configurations": []
	}
}
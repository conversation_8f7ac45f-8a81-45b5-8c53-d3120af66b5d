.gradle
.vscode
.idea
.kotlin
.trae
**/.env
**/GEMINI.md
**/build
**/logs
**/out
portal/ui/projects/common/assets/icons/demo.html
node_modules
**/dist
yarn-error.log
.settings/
.project
/editors/client/editor.core/*.exe
/viclass/packages/protobuf/viclass/proto/src/editor.freedrawing/freedrawing_msg_pb.d.ts
/viclass/packages/protobuf/viclass/proto/src/editor.freedrawing/freedrawing_msg_pb.js
/viclass/packages/protobuf/viclass/proto/src/editor.freedrawing/freedrawing_cmd_pb.d.ts
/viclass/packages/protobuf/viclass/proto/src/editor.freedrawing/freedrawing_cmd_pb.js
/viclass/packages/protobuf/viclass/proto/src/editor.core/meta_pb.d.ts
/viclass/packages/protobuf/viclass/proto/src/editor.core/meta_pb.js
/viclass/packages/protobuf/viclass/proto/src/editor.core/coordinator.state_pb.d.ts
/viclass/packages/protobuf/viclass/proto/src/editor.core/coordinator.state_pb.js
/viclass/packages/protobuf/viclass/proto/src/editor.geo/geo_msg_pb.d.ts
/viclass/packages/protobuf/viclass/proto/src/editor.geo/geo_msg_pb.js
/viclass/packages/protobuf/viclass/proto/src/editor.geo/geo_cmd_pb.d.ts
/viclass/packages/protobuf/viclass/proto/src/editor.geo/geo_cmd_pb.js
/viclass/packages/protobuf/viclass/proto/src/editor.word/word_cmd_pb.js
/viclass/packages/protobuf/viclass/proto/src/editor.word/word_cmd_pb.d.ts
common/libs/bin/
portal/datastructures/bin/
**/bin
editors/backend/geo/conf
portal/notification/conf
vinet/ccs/conf
vinet/sync/conf
vinet/sync/pkg
gradle.properties
.DS_Store
/viclass/packages/editoruis/viclass/editorui.freedrawing/assets/output.css
/viclass/packages/editoruis/viclass/editorui.geo/assets/output.css
/viclass/packages/editoruis/viclass/editorui.magh/assets/output.css
/viclass/packages/editoruis/viclass/editorui.math/assets/output.css
/viclass/packages/editoruis/viclass/editorui.word/assets/output.css
/viclass/packages/editoruis/viclass/commontools/assets/output.css
/viclass/packages/editoruis/viclass/classroomtools/assets/output.css
/viclass/packages/portal/viclass/portal.common/assets/output.css
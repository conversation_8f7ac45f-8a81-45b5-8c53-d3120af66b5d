# Github action for automatically adding label or setting assignee when a new
# Issue or PR is opened. https://github.com/marketplace/actions/issue-labeler

name: 🏷️　Label(Title and Body)
on:
  issues:
    types: [opened]
  pull_request_target:
    types: [opened]
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          variable_name: bot_token
      - uses: Naturalclar/issue-action@v2.0.1
        with:
          github-token: ${{ env.bot_token }}
          title-or-body: title
          parameters: >
            [
              {
                "keywords": ["bug", "error"],
                "labels": ["bug"]
              },
              {
                "keywords": ["help", "guidance"],
                "labels": ["help-wanted"]
              }
            ]

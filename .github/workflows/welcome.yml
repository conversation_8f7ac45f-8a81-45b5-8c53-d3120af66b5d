name: 👋　Welcome
on:
  pull_request_target:
    types: [opened, closed]
  issues:
    types: [opened]
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          variable_name: bot_token
      - uses: bubkoo/welcome-action@v1
        with:
          GITHUB_TOKEN: ${{ env.bot_token }}
          FIRST_ISSUE: |
            👋 @{{ author }}

            Thanks for opening your first issue here! If you're reporting a 🐞 bug, please make sure you include steps to reproduce it.
            To help make it easier for us to investigate your issue, please follow the [contributing guidelines](https://github.com/antvis/X6/blob/master/CONTRIBUTING.md).

            We get a lot of issues on this repo, so please be patient and we will get back to you as soon as we can.

          FIRST_PR: |
            👋 @{{ author }}

            💖 Thanks for opening this pull request! 💖

            Please follow the [contributing guidelines](https://github.com/antvis/X6/blob/master/CONTRIBUTING.md). And we use [semantic commit messages](https://www.conventionalcommits.org/en/v1.0.0/) to streamline the release process.

            Examples of commit messages with semantic prefixes:
            - `fix: don't overwrite prevent_default if default wasn't prevented`
            - `feat: add graph.scale() method`
            - `docs: graph.getShortestPath is now available`

            Things that will help get your PR across the finish line:
            - Follow the TypeScript coding style.
            - Run `npm run lint` locally to catch formatting errors earlier.
            - Document any user-facing changes you've made.
            - Include tests when adding/changing behavior.
            - Include screenshots and animated GIFs whenever possible.

            We get a lot of pull requests on this repo, so please be patient and we will get back to you as soon as we can.


          FIRST_PR_MERGED: |
            👋 @{{ author }} Congrats on merging your first pull request! 🎉🎉🎉

name: 👾　Label Commands
on:
  pull_request_target:
    types: [labeled, unlabeled]
  issues:
    types: [labeled, unlabeled]
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          variable_name: bot_token
      - uses: bubkoo/label-commands@v1
        with:
          GITHUB_TOKEN: ${{ env.bot_token }}
          CONFIG_FILE: .github/workflows/config/label-commands.yml

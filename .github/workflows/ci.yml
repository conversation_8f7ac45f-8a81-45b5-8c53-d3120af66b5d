name: 👷　CI
on:
  pull_request_target:
  push:
    branches:
      - master
      - next
      - next-major
      - alpha
      - beta
jobs:
  ci:
    runs-on: ubuntu-latest
    steps:
      - name: ⤵️　Checkout
        uses: actions/checkout@v3

      - name: 🎉　Setup nodejs
        uses: actions/setup-node@v3
        with:
          node-version: 16.x

      - name: 🎉　Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 7
          run_install: false

      - name: 🌱　Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: 🚸　Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 🚧　Install dependencies
        run: pnpm install --no-frozen-lockfile --ignore-scripts

      - name: 📦　Build
        run: pnpm run build

      - name: ✅　Test
        run: pnpm run test

      - name: 🔑　Generate Token
        uses: wow-actions/use-app-token@v2
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}

      - name: 💡　Coveralls
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ env.BOT_TOKEN }}
          path-to-lcov: ./test/coverage/lcov.info

      - name: 💡　Codecov
        uses: codecov/codecov-action@v1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./test/coverage/lcov.info

name: 🎗　Update Authors
on:
  push:
    branches:
      - master
      - alpha
      - beta
jobs:
  authors:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          env_name: bot_token
      - uses: bubkoo/update-authors@v1
        with:
          GITHUB_TOKEN: ${{ env.bot_token  }}
          bots: false
          path: CONTRIBUTORS
          commit: 'chore: update CONTRIBUTORS [skip ci]'

{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "start",
      "type": "shell",
      "command": "bash ./scripts/start.sh &",
      "isBackground": true,
      "problemMatcher": [
        {
          "owner": "custom",
          "pattern": { "regexp": "__________" },
          "background": {
            "activeOnStart": true,
            "beginsPattern": "Preparing",
            "endsPattern": "Server ready"
          }
        }
      ]
    }
  ]
}

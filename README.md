<div align="center">
    <img alt="math live" src="assets/mathlive-1.png?raw=true">
</div>

<h3><strong>MathLive</strong></h3>
<h1>A Web Component for Math Input</h1>

[![Maintenance](https://img.shields.io/maintenance/yes/2025.svg)]()
[![GitHub license](https://img.shields.io/badge/license-MIT-brightgreen.svg)](https://raw.githubusercontent.com/arnog/mathlive/master/LICENSE.txt)

The MathLive library includes a `<math-field>` web component that provides an
easy-to-use interface for editing math.

With over 800 **built-in LaTeX commands**, MathLive renders beautiful,
**TeX-quality typesetting**.

Mathfields are designed for **mobile devices** with an extensive set of virtual
keyboards for math input, and are compatible with screen readers, including
custom math-to-speech support for improved accessibility.

Mathfields output their content as LaTeX, MathML, ASCIIMath, and MathJSON
formats.

And the best part? They're easy to customize to your needs!

<img src="assets/screenshots/mathlive-demo.png">

<table align="center" >
    <tr>
        <td width='50%' align='center' style="border:none;">
            <img alt="The popover panel" 
            style='margin:15px; box-shadow: 0px 5px 15px #000; border: 1px solid #eee' 
            src="assets/screenshots/popover.png">
        </td>
        <td width='50%' align='center' style="border:none;">
            <img alt="A Virtual Keyboard" 
            style='margin:15px; box-shadow: 0px 5px 15px #000; border: 1px solid #eee' 
            src="assets/screenshots/virtualKeyboard.png">
        </td>
    </tr>
    <tr style="background-color: initial; border: none;">
        <td colspan="2" align="center" style="border:none;">
            <img width="50%" alt="The Loop Equation" 
            style='margin:15px; box-shadow: 0px 5px 15px #000; border: 1px solid #eee' 
            src="assets/screenshots/loop-eqn.png">
        </td>
    </tr>
</table>

## 🚀 Getting Started

Using MathLive is easy! Simply add a `<math-field>` tag to your page. It
initializes automatically and works just like a `<textarea>` or `<button>`
element. You can manipulate the mathfield using methods of the element and
listen for events to be notified when its internal state changes.

```bash
npm install mathlive
```

```javascript
import 'mathlive';
```

```html
<!DOCTYPE html>
<html lang="en-US">
  <body>
    <math-field>f(x)= x+1</math-field>
  </body>
</html>
```

You can also add it using a CDN:

```html
<head>
    <script src="https://unpkg.com/mathlive"></script>
</head>
```

Check documentation for [React](https://cortexjs.io/mathfield/guides/react/),
[Svelte](https://cortexjs.io/mathfield/guides/svelte/) and
[interaction with Mathfield](https://cortexjs.io/mathfield/guides/interacting/).

## 📖 Documentation

MathLive has an extensive set of documentation to help you get started,
including guides on interacting with a mathfield, customizing it, executing
commands, defining custom LaTeX macros, managing inline and keyboard shortcuts,
controlling speech output, and displaying static math formulas. You can find all
of these guides on the [CortexJS.io website](https://cortexjs.io/mathfield/).

In addition to the guides, you can also find reference documentation of the
MathLive API on the
[Mathfield API Reference page](https://cortexjs.io/mathfield/api/).

## FAQ

**Q:** When is the next release?

MathLive follows a semi-annual release cycle, with major releases typically
scheduled for June and January. These may be followed by patch releases to
address any issues that arise shortly after deployment. Additionally, an
out-of-band release can be made if requested by a sponsor or if a community
member submits a pull request and requests a release to include their
contribution.

## Related Projects

<dl>
  <dt><a href="https://cortexjs.io/math-json">MathJSON</a> (on <a href="https://github.com/cortex-js/math-json">GitHub</a>)</dt>
  <dd>A lightweight data interchange format for mathematical notation.</dd>  
  <dt><a href="https://cortexjs.io/compute-engine">Compute Engine</a> (on <a href="https://github.com/cortex-js/math-json/tree/master/src/compute-engine">GitHub</a>)</dt>
  <dd>The CortexJS Compute Engine performs calculations on MathJSON expressions</dd>  
  <dt><a href="https://cortexjs.io/cortex">Cortex</a> (on <a href="https://github.com/cortex-js/math-json/tree/master/src/cortex">GitHub</a>)</dt>
  <dd>Cortex is a programming language for scientific computing (work in progress)</dd>  
</dl>

## 💬 Contact Us

- Chat with the
  [CortexJS Assistant](https://chat.openai.com/g/g-8YgEfR7ig-cortexjs-assistant)
- Join our [Discord server](https://discord.gg/yhmvVeJ4Hd)
- Drop a line to [<EMAIL>](<EMAIL>)

## 📃 License

This project is licensed under the [MIT License](LICENSE.txt).

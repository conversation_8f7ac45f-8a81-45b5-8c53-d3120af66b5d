pluginManagement {
    plugins {
        id 'org.jetbrains.kotlin.jvm' version '2.0.21'
    }
}
plugins {
    id 'org.gradle.toolchains.foojay-resolver-convention' version '0.8.0'
}
rootProject.name = 'viclass'

// libraries
//include 'jayeson.lib.session'
//project(':jayeson.lib.session').projectDir = file('../libs/jayeson.lib.session')
//
//include 'jayeson.lib.access'
//project(':jayeson.lib.access').projectDir = file('../libs/jayeson.lib.access')
//
//include 'jayeson.lib.utility'
//project(':jayeson.lib.utility').projectDir = file('../libs/jayeson.lib.utility')

// portal

include ":portal.metadata"
project(":portal.metadata").projectDir = file("portal/metadata")

/******************* PORTAL SCOPE **********************************/
include ":portal.backend"
project(":portal.backend").projectDir = file("portal/backend")

include ":portal.jobrunr"
project(":portal.jobrunr").projectDir = file("portal/jobrunr")

include ":portal.datastructures"
project(":portal.datastructures").projectDir = file("portal/datastructures")

include ":portal.user"
project(":portal.user").projectDir = file("portal/user")

include ":portal.auth"
project(":portal.auth").projectDir = file("portal/auth")

include ":portal.filestore"
project(":portal.filestore").projectDir = file("portal/filestore")

include ":portal.notification"
project(":portal.notification").projectDir = file("portal/notification")

include ":portal.lsession"
project(":portal.lsession").projectDir = file("portal/lsession")

include ":portal.configurations"
project(":portal.configurations").projectDir = file("portal/configurations")

include ":portal.grpc"
project(":portal.grpc").projectDir = file("portal/grpc")

//include ":portal.event"
//project(":portal.event").projectDir = file("portal/event")

include ":portal.kafka"
project(":portal.kafka").projectDir = file("portal/kafka")

/***************************** COMMON LIBRARIES FOR ALL SCOPE ****************************/
include ":common.libs"
project(":common.libs").projectDir = file("common/libs")


/************************** VINET SCOPE **************************************************/

include ":vinet.ccs"
project(":vinet.ccs").projectDir = file("vinet/ccs")

/************************** EDITOR SCOPE **************************************************/

// freedrawing editor
include ":editor.freedrawing"
project(":editor.freedrawing").projectDir = file("editors/backend/freedrawing")

// geo editor
include ":editor.geo"
project(":editor.geo").projectDir = file("editors/backend/geo")

include ":feature.common"
project(":feature.common").projectDir = file("editors/backend/feature.common")

include ":portal.beta"
project(":portal.beta").projectDir = file("portal/beta")

include ":portal.shorturl"
project(":portal.shorturl").projectDir = file("portal/shorturl")
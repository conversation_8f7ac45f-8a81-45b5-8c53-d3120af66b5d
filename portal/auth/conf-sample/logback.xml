<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <file>logs/console.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/console.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="AUTH" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/auth.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/auth.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="DATABASE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/database.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/database.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="DATABASE-GATEWAY" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/database_gateway.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/database_gateway.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="SERVICE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/services.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <logger name="portal.auth.server" level="DEBUG" additivity="false">
        <appender-ref ref="SERVICE"/>
    </logger>

    <logger name="portal.auth.dbgateway" level="DEBUG" additivity="false">
        <appender-ref ref="DATABASE-GATEWAY"/>
    </logger>

    <logger name="portal.auth" level="DEBUG" additivity="false">
        <appender-ref ref="AUTH"/>
    </logger>

    <logger name="org.mongodb" level="DEBUG" additivity="false">
        <appender-ref ref="DATABASE"/>
    </logger>

    <root level="DEBUG">
        <appender-ref ref="APPLICATION"/>
    </root>
</configuration>

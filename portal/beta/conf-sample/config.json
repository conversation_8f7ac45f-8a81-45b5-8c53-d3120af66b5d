{"serverConf": {"port": 1188}, "dbConf": {"connectionString": "mongodb://localhost:27017", "dbName": "viclass"}, "seConf": {"notificationService": {"serviceId": "NS01", "serviceType": "NS", "host": "devlocal.viclass.vn", "port": 11155}, "userService": {"serviceId": "NS02", "serviceType": "NS", "host": "devlocal.viclass.vn", "port": 11122}, "shorturlService": {"serviceId": "SS01", "serviceType": "SS", "host": "devlocal.viclass.vn", "port": 1299}}, "emailConf": {"sendInviteJoinBeta": {"subject": "<PERSON><PERSON><PERSON> tham gia thử nghiệm - Viclass", "templateId": "inviteJoinBeta"}, "sendInviteJoinBetaWithPassword": {"subject": "<PERSON><PERSON><PERSON> bạn tr<PERSON>i nghiệm thử sản phẩm - <PERSON><PERSON>", "templateId": "inviteJoinBetaWithPassword"}}, "jwtConf": {"secret": "UWJ3nYHn9Gg8hqsFpmQvnyfFX", "expiresInMinutes": 180, "claimName": "email"}, "cacheServiceConf": {"host": "redis://devlocal.viclass.vn", "port": 16379}, "betaWhiteListIPAndHost": ["************", "*************", "localhost", "127.0.0.1"], "slack": {"notiHook": "*******************************************************************************"}}
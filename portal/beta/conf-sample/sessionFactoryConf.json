{"defaultSM": "sm-lettuce", "units": [{"smName": "sm-lettuce-withTimeout", "sessionModelConfig": {"SessionModelConfigType": "jayeson.lib.session.lettuce.LettuceSessionModelConfig", "host": "devlocal.viclass.vn", "port": 16379, "timeout": 2000, "readTimeoutMs": 30000, "sessionTimeoutSpecification": {"timeout": 5000, "autoRenewSetting": false}}}, {"smName": "sm-lettuce-withTimeoutAutoRenew", "sessionModelConfig": {"SessionModelConfigType": "jayeson.lib.session.lettuce.LettuceSessionModelConfig", "host": "devlocal.viclass.vn", "port": 16379, "timeout": 2000, "readTimeoutMs": 30000, "sessionTimeoutSpecification": {"timeout": 10000, "autoRenewSetting": true}}}, {"smName": "sm-lettuce", "sessionModelConfig": {"SessionModelConfigType": "jayeson.lib.session.lettuce.LettuceSessionModelConfig", "host": "devlocal.viclass.vn", "port": 16379, "timeout": 2000, "readTimeoutMs": 30000, "sessionTimeoutSpecification": {"timeout": -1, "autoRenewSetting": false}}}, {"smName": "sm-async-mem", "sessionModelConfig": {"SessionModelConfigType": "jayeson.lib.session.memory.AsyncMemorySessionModelConfig", "sessionTimeoutSpecification": {"timeout": -1, "autoRenewSetting": true}}}]}
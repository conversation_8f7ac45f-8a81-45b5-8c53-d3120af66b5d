include:
  - project: "devops/pipeline-library"
    file: "templates/main.yml"
  - project: "devops/pipeline-library"
    file: "viclass/main.yml"

stages:
  - initial
  - build
  - deploy
  - notify

variables:
  ANSIBLE_GROUP: "viclass"
  ANSIBLE_HOST: "portal_beta"

setup:
  extends:
    - .config.env
    - .rules.generals

merge_request_build_test:
  extends:
    - .gradle6.build.backend.viclass
    - .rules.build.merge_request
  dependencies:
    - setup

build_project:
  extends:
    - .gradle6.distribution.backend.viclass
    - .rules.build.publish.general
  dependencies:
    - setup

deploy_to_dev:
  extends:
    - .deploy.backend.viclass
    - .rules.deploy.dev
  dependencies:
    - setup
    - build_project

deploy_to_myTest:
  extends:
    - .deploy.backend.viclass
    - .rules.deploy.myTest
  dependencies:
    - setup
    - build_project

deploy_to_stag:
  extends:
    - .deploy.backend.viclass
    - .rules.deploy.stag
  dependencies:
    - setup
    - build_project

deploy_to_prod:
  extends:
    - .deploy.backend.viclass
    - .rules.deploy.prod
  dependencies:
    - setup
    - build_project

notify_slack_when_success:
  extends:
    - .notify_when_success

notify_slack_when_fail:
  extends:
    - .notify_when_fail

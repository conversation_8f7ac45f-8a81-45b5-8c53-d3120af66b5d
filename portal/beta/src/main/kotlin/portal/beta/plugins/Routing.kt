package portal.beta.plugins

import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.launch
import org.koin.ktor.ext.inject
import portal.beta.Launcher.logger
import portal.beta.configuration.SlackConfig
import portal.beta.gateway.IUserServiceGateway
import portal.beta.gateway.UserServiceGatewayImpl
import portal.beta.models.*
import portal.beta.services.BetaService
import portal.beta.utility.EmailUtils
import portal.beta.utility.LoginUtils
import portal.beta.utility.defaultMapper
import proto.portal.beta.BetaMessage
import proto.portal.beta.BetaServiceGrpcKt
import proto.portal.user.UserMessage
import java.util.*
import portal.beta.configuration.Configuration as BetaConfiguration

fun Application.configureRouting() {
    val userService: IUserServiceGateway by inject<UserServiceGatewayImpl>()
    val betaService: BetaServiceGrpcKt.BetaServiceCoroutineImplBase by inject<BetaService>()
    val config: BetaConfiguration by inject()
    val slackConfig: SlackConfig by inject()

    val INVITATION_CODE_COOKIE = "beta_invitation_code"
    val ADMIN_REGISTER_SUCCESS = "success"

    // If you edit these properties, please edit the same properties in the BetaService
    val INVITATION_CODE_PATH = "api/beta/apply-invitation-code"
    val INVITATION_CODE_QUERY = "data"
    val INVITATION_CODE_KEY = "code"
    val INVITATION_CODE_REDIRECT_KEY = "redirect_url"

    /**
     * Sends a message back to Slack via the given response_url.
     */
    fun sendMessageToSlack(responseUrl: String, message: Map<String, Any?>) {
        if (responseUrl.isNotBlank()) {
            try {
                val httpClient = java.net.http.HttpClient.newHttpClient()
                val requestBody = defaultMapper.writeValueAsString(message)
                val httpRequest = java.net.http.HttpRequest.newBuilder().uri(java.net.URI.create(responseUrl))
                    .header("Content-Type", "application/json")
                    .POST(java.net.http.HttpRequest.BodyPublishers.ofString(requestBody)).build()

                val httpResponse = httpClient.send(httpRequest, java.net.http.HttpResponse.BodyHandlers.ofString())

                if (httpResponse.statusCode() in 200..299) {
                    logger.info("Successfully sent response to Slack response_url: $responseUrl with message: $message")
                } else {
                    logger.error("Failed to send response to Slack response_url: $responseUrl, status: ${httpResponse.statusCode()}, body: ${httpResponse.body()}")
                }
            } catch (e: Exception) {
                logger.error("Failed to send response to Slack response_url: $responseUrl", e)
            }
        } else {
            logger.warn("Slack response_url is empty in payload. Cannot send delayed response.")
        }
    }

    /**
     * Gửi thông báo email mới đăng ký waitlist lên Slack.
     */
    fun notifySlackForBetaRegister(
        email: String, slackConfig: SlackConfig
    ) {
        try {
            val slackJson = mapOf(
                "blocks" to listOf(
                    mapOf(
                        "type" to "section", "text" to mapOf(
                            "type" to "mrkdwn", "text" to "New email registered to waitlist: *`${email}`*"
                        )
                    ),
                    mapOf(
                        "type" to "actions", "elements" to listOf(
                            mapOf(
                                "type" to "button", "text" to mapOf(
                                    "type" to "plain_text", "text" to "Invite to beta", "emoji" to true
                                ), "value" to email, "action_id" to "invite_beta"
                            ), mapOf(
                                "type" to "button", "text" to mapOf(
                                    "type" to "plain_text",
                                    "text" to "Invite to beta and create account",
                                    "emoji" to true
                                ), "value" to email, "action_id" to "invite_beta_with_create_account"
                            )
                        )
                    ),
                )
            )
            val slackPayload = defaultMapper.writeValueAsString(slackJson)
            val client = java.net.http.HttpClient.newHttpClient()
            val request = java.net.http.HttpRequest.newBuilder().uri(java.net.URI.create(slackConfig.notiHook))
                .header("Content-Type", "application/json")
                .POST(java.net.http.HttpRequest.BodyPublishers.ofString(slackPayload)).build()
            val response = client.send(request, java.net.http.HttpResponse.BodyHandlers.ofString())
            if (response.statusCode() in 200..299) {
                logger.info("Successfully sent Slack notification for beta to $email")
            } else {
                logger.warn("Failed to send Slack notification for $email. Status: ${response.statusCode()}, body: ${response.body()}")
            }
        } catch (e: Exception) {
            logger.warn("Failed to notify Slack for beta registration of email $email", e)
        }
    }

    /**
     * Gửi lại message về Slack response_url cho action invite.
     */
    fun sendBetaInviteSlackMessage(
        responseUrl: String, actionId: String, threadTs: String = ""
    ) {
        val (msgText, responseType) = when (actionId) {
            "invite_beta" -> Pair("*Invited to beta*", "in_channel")
            "invite_beta_with_create_account" -> Pair(
                "*Invited to beta and created account*", "in_channel"
            )

            else -> Pair("Unsupported action", "ephemeral")
        }
        val slackMsg = mutableMapOf<String, Any?>(
            "text" to msgText,
            "mrkdwn" to true,
            "response_type" to responseType,
            "replace_original" to false,
            "thread_ts" to threadTs
        )
        sendMessageToSlack(responseUrl, slackMsg)
    }

    /**
     * Send beta invitations for the given emails.
     */
    suspend fun handleBetaInvitation(
        emails: List<String>
    ): BetaInvitationResponse {
        val results = mutableListOf<Pair<String, String>>()
        for (email in emails) {
            if (!EmailUtils.isValidEmail(email)) {
                results.add(email to "Invalid email")
                continue
            }
            val betaRegisterResponse = betaService.createBetaRegistration(
                BetaMessage.CreateBetaRegistrationRequest.newBuilder().setEmail(email).build()
            )
            if (betaRegisterResponse.status.code != Status.Code.OK.value()) {
                results.add(email to betaRegisterResponse.status.message)
                continue
            }
            val userResponse = userService.getUserByEmail(email)
            val isUserExists = userResponse.status.code == Status.Code.OK.value()
            val isUserNotFound = userResponse.status.code == Status.Code.NOT_FOUND.value()
            if (!isUserExists && !isUserNotFound) {
                results.add(email to userResponse.status.message)
                continue
            }
            val userDoesNotExist = !isUserExists
            var redirectUrl = "/"
            if (!userDoesNotExist) {
                val oneTimeLoginToken = userService.createOneTimeLogin(
                    UserMessage.CreateOneTimeLoginRequest.newBuilder().setRegId(userResponse.user.registrationId)
                        .build()
                )
                if (oneTimeLoginToken.status.code != Status.Code.OK.value()) {
                    results.add(email to betaRegisterResponse.status.message)
                    continue
                }
                val longRedirectUrl = LoginUtils.generateOneTimeLoginLink(oneTimeLoginToken.key, null)
                redirectUrl = longRedirectUrl
            }
            val inviteResponse = betaService.inviteJoinBeta(
                BetaMessage.InviteJoinBetaRequest.newBuilder().addAccounts(
                    BetaMessage.InviteJoinBetaAccountRequest.newBuilder().setEmail(email).setRedirectUrl(redirectUrl)
                        .build()
                ).build()
            )
            if (inviteResponse.resultsList.firstOrNull()?.status?.code != Status.Code.OK.value()) {
                results.add(email to inviteResponse.resultsList.first().status.message)
                continue
            }
            results.add(email to ADMIN_REGISTER_SUCCESS)
        }
        return BetaInvitationResponse(
            results.map { (email, message) ->
                BetaInvitationAccountResponse(
                    email = email, success = (message == ADMIN_REGISTER_SUCCESS), message = message
                )
            })
    }

    /**
     * Send beta invitations and create accounts for the given emails/usernames.
     *
     * @param emails List of emails to invite and create accounts for.
     * @param usernames List of usernames corresponding to the emails.
     * @return AdminBetaRegisterResponse containing the result for each email.
     */
    suspend fun handleBetaInvitationWithCreateAccount(
        emails: List<String>, usernames: List<String>
    ): AdminBetaRegisterResponse {
        val results = mutableListOf<Pair<String, String>>()
        for ((i, email) in emails.withIndex()) {
            // Validate email format
            if (!EmailUtils.isValidEmail(email)) {
                results.add(email to "Invalid email $email")
                continue
            }
            val username = usernames[i]

            // Validate username format
            if (!LoginUtils.isValidUsername(username)) {
                results.add(email to "Invalid username $username")
                continue
            }

            // Generate random password and hash it
            val randomPassword = LoginUtils.generateRandomPassword()
            val hashedPassword = LoginUtils.doHashMD5(randomPassword)

            // Try to register user by email
            val createUserResponse = userService.registerByEmail(
                RegistrationUser(username, email, hashedPassword, hashedPassword, "", true)
            )
            val userAlreadyExists = createUserResponse.status.code == Status.Code.ALREADY_EXISTS.value()
            val createOk = createUserResponse.status.code == Status.Code.OK.value()

            // If registration failed and user doesn't already exist, skip
            if (!createOk && !userAlreadyExists) {
                results.add(email to createUserResponse.status.message)
                continue
            }

            // Get registrationId for existing or new user
            var registrationId = createUserResponse.userId
            if (userAlreadyExists) {
                val userResponse = userService.getUserByEmail(email)
                if (userResponse.status.code != Status.Code.OK.value()) {
                    results.add(email to userResponse.status.message)
                    continue
                }
                registrationId = userResponse.user.registrationId
            }

            // Get registration metadata and verify if needed
            val registrationMeta = userService.getRegistrationMetadata(registrationId)
            if (registrationMeta.status.code != Status.Code.OK.value()) {
                results.add(email to registrationMeta.status.message)
                continue
            }
            if (!registrationMeta.isVerified) {
                val verifyUserResponse = userService.verifyEmail(
                    EmailVerificationDto(registrationId, ""), true
                )
                if (verifyUserResponse.status.code != Status.Code.OK.value()) {
                    results.add(email to verifyUserResponse.status.message)
                    continue
                }
            }

            // Ensure user profile exists
            val userResponse = userService.getUserByEmail(email)
            if (userResponse.status.code != Status.Code.OK.value()) {
                results.add(email to userResponse.status.message)
                continue
            }
            if (userResponse.user.id.isNullOrEmpty()) {
                val profileRes = userService.createOrMergeUserProfile(registrationId)
                if (profileRes.status.code != Status.Code.OK.value()) {
                    results.add(email to profileRes.status.message)
                    continue
                }
            }

            // Register email for beta
            val betaRegisterResponse = betaService.createBetaRegistration(
                BetaMessage.CreateBetaRegistrationRequest.newBuilder().setEmail(email).build()
            )
            if (betaRegisterResponse.status.code != Status.Code.OK.value()) {
                results.add(email to betaRegisterResponse.status.message)
                continue
            }

            // Generate one-time login token and link
            val oneTimeLoginToken = userService.createOneTimeLogin(
                UserMessage.CreateOneTimeLoginRequest.newBuilder().setRegId(registrationId).build()
            )
            if (oneTimeLoginToken.status.code != Status.Code.OK.value()) {
                results.add(email to oneTimeLoginToken.status.message)
                continue
            }
            val longOneTimeLoginLink = LoginUtils.generateOneTimeLoginLink(
                oneTimeLoginToken.key, null
            )

            // Shorten the one-time login link
            val oneTimeLoginLink = longOneTimeLoginLink

            // Send beta invitation, include password if user is new
            val inviteResponse = betaService.inviteJoinBeta(
                BetaMessage.InviteJoinBetaRequest.newBuilder().addAccounts(
                    BetaMessage.InviteJoinBetaAccountRequest.newBuilder().setEmail(email)
                        .setPassword(if (!userAlreadyExists) randomPassword else "").setRedirectUrl(oneTimeLoginLink)
                        .build()
                ).build()
            )
            if (inviteResponse.resultsList.firstOrNull()?.status?.code != Status.Code.OK.value()) {
                results.add(email to inviteResponse.resultsList.first().status.message)
                continue
            }

            // Add success result
            results.add(email to ADMIN_REGISTER_SUCCESS)
        }
        // Return summary response
        return AdminBetaRegisterResponse(
            results.map { (email, message) ->
                AdminBetaRegisterAccountResponse(
                    email = email, success = (message == ADMIN_REGISTER_SUCCESS), message = message
                )
            })
    }

    routing {
        route("api") {
            route("beta") {
                post("register") {
                    val emailRequest = call.receive<BetaRegisterRequest>()
                    if (emailRequest.email.isBlank()) {
                        call.respond(
                            HttpStatusCode.BadRequest,
                            BetaRegisterResponse(Status.Code.INVALID_ARGUMENT.value(), "Invalid email")
                        )
                        return@post
                    }

                    logger.debug("Processing registration for account: ${emailRequest.email}")

                    // Check if email already has at least one record with code in beta invitations
                    val existingBetaRegistration = betaService.getFirstBetaRegistration(
                        BetaMessage.GetFirstBetaRegistrationRequest.newBuilder().setEmail(emailRequest.email).build()
                    )
                    val isAllowedInBeta = existingBetaRegistration.status.code == Status.Code.OK.value() &&
                                         existingBetaRegistration.code.isNotEmpty()

                    logger.debug("Email ${emailRequest.email} beta status: allowed=$isAllowedInBeta")

                    if (isAllowedInBeta) {
                        // User is allowed in beta, send beta invitation
                        logger.debug("User ${emailRequest.email} is allowed in beta, sending invitation")

                        val userResponse = userService.getUserByEmail(emailRequest.email)
                        val codeOk = userResponse.status.code == Status.Code.OK.value()
                        val codeNotFound = userResponse.status.code == Status.Code.NOT_FOUND.value()

                        if (!codeOk && !codeNotFound) {
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                BetaRegisterResponse(userResponse.status.code, userResponse.status.message)
                            )
                            return@post
                        }

                        // If user exists, send beta invitation with one-time login
                        if (codeOk) {
                            val registrationId = userResponse.user.registrationId

                            val oneTimeLoginToken = userService.createOneTimeLogin(
                                UserMessage.CreateOneTimeLoginRequest.newBuilder().setRegId(registrationId).build()
                            )
                            if (oneTimeLoginToken.status.code != Status.Code.OK.value()) {
                                call.respond(
                                    HttpStatusCode.InternalServerError,
                                    BetaRegisterResponse(userResponse.status.code, userResponse.status.message)
                                )
                                return@post
                            }

                            val longOneTimeLoginLink = LoginUtils.generateOneTimeLoginLink(oneTimeLoginToken.key, null)
                            val oneTimeLoginLink = longOneTimeLoginLink

                            val inviteJoinBetaResponse = betaService.inviteJoinBeta(
                                BetaMessage.InviteJoinBetaRequest.newBuilder().addAccounts(
                                    BetaMessage.InviteJoinBetaAccountRequest.newBuilder().setEmail(emailRequest.email)
                                        .setRedirectUrl(oneTimeLoginLink).build()
                                ).build()
                            )

                            if (inviteJoinBetaResponse.resultsList.firstOrNull()?.status?.code != Status.Code.OK.value()) {
                                call.respond(
                                    HttpStatusCode.InternalServerError, BetaRegisterResponse(
                                        inviteJoinBetaResponse.resultsList.first().status.code,
                                        inviteJoinBetaResponse.resultsList.first().status.message
                                    )
                                )
                                return@post
                            }
                        } else {
                            // User doesn't exist, send invitation without one-time login
                            val inviteJoinBetaResponse = betaService.inviteJoinBeta(
                                BetaMessage.InviteJoinBetaRequest.newBuilder().addAccounts(
                                    BetaMessage.InviteJoinBetaAccountRequest.newBuilder().setEmail(emailRequest.email)
                                        .setRedirectUrl("/").build()
                                ).build()
                            )

                            if (inviteJoinBetaResponse.resultsList.firstOrNull()?.status?.code != Status.Code.OK.value()) {
                                call.respond(
                                    HttpStatusCode.InternalServerError, BetaRegisterResponse(
                                        inviteJoinBetaResponse.resultsList.first().status.code,
                                        inviteJoinBetaResponse.resultsList.first().status.message
                                    )
                                )
                                return@post
                            }
                        }

                        call.respond(
                            HttpStatusCode.OK, BetaRegisterResponse(
                                Status.Code.OK.value(), "Beta invitation sent successfully"
                            )
                        )
                    } else {
                        // User is not allowed in beta, create new registration and notify Slack
                        logger.debug("User ${emailRequest.email} is not allowed in beta, creating registration and notifying Slack")

                        val betaRegistration = betaService.createBetaRegistration(
                            BetaMessage.CreateBetaRegistrationRequest.newBuilder().setEmail(emailRequest.email).build()
                        )
                        if (betaRegistration.status.code != Status.Code.OK.value()) {
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                BetaRegisterResponse(betaRegistration.status.code, betaRegistration.status.message)
                            )
                            return@post
                        }

                        // Notify Slack of the new registered email asynchronously
                        launch {
                            notifySlackForBetaRegister(emailRequest.email, slackConfig)
                        }

                        call.respond(
                            HttpStatusCode.OK, BetaRegisterResponse(
                                betaRegistration.status.code, "Registration successful, waiting for approval"
                            )
                        )
                    }
                }

                post("slack") {
                    // Parse application/x-www-form-urlencoded; "payload" field contains JSON
                    val params = call.receiveParameters()
                    val payloadString = params["payload"] ?: ""

                    // Parse JSON string to Map
                    val payloadMap: Map<*, *> = runCatching<Map<*, *>> {
                        defaultMapper.readValue(payloadString, Map::class.java)
                    }.getOrElse { emptyMap<Any?, Any?>() }

                    // Extract action details from the payload
                    val firstAction =
                        ((payloadMap["actions"] as? List<*>)?.firstOrNull() as? Map<*, *>) ?: emptyMap<Any?, Any?>()
                    val actionId = firstAction["action_id"] as? String ?: ""
                    val emailValue = firstAction["value"] as? String

                    val email = emailValue?.trim().orEmpty()
                    val emailsRaw = if (email.isNotEmpty()) listOf(email) else emptyList()
                    val usernamesRaw = emailsRaw.map { it.substringBefore("@") }

                    val responseUrl = payloadMap["response_url"] as? String ?: ""

                    when (actionId) {
                        "invite_beta" -> {
                            val response = handleBetaInvitation(emailsRaw)
                            if (responseUrl.isNotBlank()) {
                                val threadTs = ((payloadMap["message"] as? Map<*, *>)?.get("ts") as? String).orEmpty()
                                sendBetaInviteSlackMessage(responseUrl, actionId, threadTs)
                            }
                            call.respond(HttpStatusCode.OK, "OK")
                        }

                        "invite_beta_with_create_account" -> {
                            if (emailsRaw.size != usernamesRaw.size) {
                                call.respond(
                                    HttpStatusCode.BadRequest,
                                    "Invalid request: emails and usernames count do not match."
                                )
                                return@post
                            }
                            val response = handleBetaInvitationWithCreateAccount(emailsRaw, usernamesRaw)
                            if (responseUrl.isNotBlank()) {
                                val threadTs = ((payloadMap["message"] as? Map<*, *>)?.get("ts") as? String).orEmpty()
                                sendBetaInviteSlackMessage(responseUrl, actionId, threadTs)
                            }
                            call.respond(HttpStatusCode.OK, "OK")
                        }

                        else -> {
                            call.respond(HttpStatusCode.BadRequest, "Unsupported action_id")
                        }
                    }
                }

                get("beta-invitation") {
                    val emails = call.request.queryParameters.getAll("emails") ?: emptyList()
                    val response = handleBetaInvitation(emails)
                    call.respond(response)
                }

                get("beta-invitation-with-create-account") {
                    val emails = call.request.queryParameters.getAll("emails") ?: emptyList()
                    val usernames = call.request.queryParameters.getAll("usernames") ?: emptyList()
                    if (usernames.size != emails.size) {
                        call.respond(
                            HttpStatusCode.BadRequest, "Invalid request: emails and usernames count do not match."
                        )
                        return@get
                    }
                    val response = handleBetaInvitationWithCreateAccount(emails, usernames)
                    call.respond(response)
                }

                get("verify-invitation-code") {
                    // Check if the request IP or Host is in the whitelist or is local
                    try {
                        val requestIp = call.request.headers["X-Real-IP"] ?: ""
                        val requestHost = call.request.headers["Host"] ?: ""
                        val whitelist = config.betaWhiteListIPAndHost
                        if (requestIp in whitelist || requestIp.startsWith("192.168") || requestIp.startsWith("10.0") || requestIp.startsWith(
                                "127.0"
                            ) || requestHost in whitelist || requestHost.startsWith("192.168") || requestHost.startsWith(
                                "10.0"
                            ) || requestHost.startsWith("127.0")
                        ) {
                            call.respond(HttpStatusCode.NoContent)
                            return@get
                        }

                        val invitationCode = call.request.cookies[INVITATION_CODE_COOKIE]
                        if (invitationCode.isNullOrBlank()) {
                            call.respond(HttpStatusCode.Unauthorized)
                            return@get
                        }

                        val response = betaService.applyInvitationCode(
                            BetaMessage.ApplyInvitationCodeRequest.newBuilder().setInvitationCode(invitationCode)
                                .build()
                        )
                        if (response.status.code == Status.Code.OK.value()) {
                            call.respond(HttpStatusCode.NoContent)
                        } else {
                            call.respond(HttpStatusCode.Unauthorized)
                        }
                    } catch (e: Exception) {
                        logger.error("Error verifying invitation code", e)
                        call.respond(HttpStatusCode.InternalServerError, "Internal server error")
                    }
                }
            }
        }
        get(INVITATION_CODE_PATH) {
            val base64JsonString = call.request.queryParameters[INVITATION_CODE_QUERY]
            val jsonString = base64JsonString?.let { String(Base64.getDecoder().decode(it)) }

            val jsonObject: Map<*, *>? = jsonString?.let {
                runCatching<Map<*, *>> { defaultMapper.readValue(it, Map::class.java) }.getOrNull()
            }
            if (jsonObject == null) {
                call.respond(HttpStatusCode.BadRequest, "Invalid invitation code")
                return@get
            }

            val invitationCode = jsonObject[INVITATION_CODE_KEY] as? String?
            if (invitationCode.isNullOrBlank()) {
                call.respond(HttpStatusCode.BadRequest, "Invalid invitation code")
                return@get
            }

            val response = betaService.applyInvitationCode(
                BetaMessage.ApplyInvitationCodeRequest.newBuilder().setInvitationCode(invitationCode).build()
            )

            val redirectUrl = jsonObject[INVITATION_CODE_REDIRECT_KEY] as? String?
            // TODO: Validate the redirect URL

            if (response.status.code == Status.Code.OK.value()) {
                call.response.cookies.append(
                    INVITATION_CODE_COOKIE, invitationCode, path = "/", maxAge = (60 * 60 * 24 * 365).toLong()
                )
                call.respondRedirect(redirectUrl ?: "/")
            } else {
                call.respondRedirect("/beta/coming-soon")
            }
        }
    }
}

package portal.beta.services

import com.google.rpc.Status
import common.libs.logger.Logging
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import org.apache.commons.codec.binary.Base64
import org.koin.core.annotation.Singleton
import portal.beta.configuration.ServerConfig
import portal.beta.dbgateway.BetaInvitationGateway
import portal.beta.gateway.IShorturlServiceGateway
import portal.beta.gateway.NotificationServiceGateway
import portal.beta.pojo.InviteJoinBeta
import portal.beta.pojo.InviteJoinBetaWithPassword
import proto.portal.beta.BetaMessage
import proto.portal.beta.BetaServiceGrpcKt
import java.security.SecureRandom

/**
 * Service responsible for managing beta invitations and registrations.
 * This service handles the creation of beta registrations, sending invitation emails,
 * and validating invitation codes.
 *
 * @property notificationServiceGateway Gateway for sending notification emails
 * @property betaInvitationGateway Gateway for accessing beta invitation data in the database
 * @property serverConfig Server configuration containing public host information
 */
@Singleton
class BetaService(
    private val notificationServiceGateway: NotificationServiceGateway,
    private val betaInvitationGateway: BetaInvitationGateway,
    private val serverConfig: ServerConfig,
    private val shorturlService: IShorturlServiceGateway
) : BetaServiceGrpcKt.BetaServiceCoroutineImplBase(), Logging {
    // Status messages for beta invitation operations
    private val CREATE_INVITATION_SUCCESS_MESSAGE = "create invitation success"
    private val CREATE_INVITATION_FAILED_MESSAGE = "create invitation failed"
    private val SEND_INVITE_EMAIL_SUCCESS_MESSAGE = "success"
    private val SEND_INVITE_EMAIL_FAILED_MESSAGE = "send invite email failed"
    private val ERROR_SEND_INVITE_EMAIL_MESSAGE = "error send invite email"
    private val ERROR_CREATE_INVITATION_CODE_MESSAGE = "error create invitation code"
    private val EMAIL_NOT_FOUND_MESSAGE = "email not found in database"
    private val GENERATE_CODE_FAILED_MESSAGE = "generate code failed"

    // Constants for beta invitation URL generation
    // If you edit these properties, please edit the same properties in the BetaController
    val APLLY_INVITATION_CODE_PATH = "api/beta/apply-invitation-code"
    val APPLY_INVITATION_CODE_QUERY_NAME = "data"
    val APPLY_INVITATION_CODE_CODE_KEY = "code"
    val APPLY_INVITATION_CODE_REDIRECT_URL_KEY = "redirect_url"

    /**
     * Generates a verification link for beta invitations.
     * The link contains the invitation code and an optional redirect URL encoded in base64.
     * The link is shortened using the ShortUrl service.
     *
     * @param token The invitation code to include in the link
     * @param redirectUrl Optional URL to redirect to after verification
     * @return The generated verification link
     */
    private suspend fun generateBetaVerificationLink(token: String, redirectUrl: String?): String {
        logger.debug("Generating beta verification link for token: $token with redirectUrl: $redirectUrl")

        // Create a JSON object with the token and redirect URL
        val json = buildJsonObject {
            put(APPLY_INVITATION_CODE_CODE_KEY, token)
            put(APPLY_INVITATION_CODE_REDIRECT_URL_KEY, redirectUrl)
        }

        // Convert the JSON to a string and encode it in base64
        val jsonString = json.toString()
        val encodedBase64JsonString = Base64.encodeBase64String(jsonString.toByteArray())

        // Construct the full URL
        val fullUrl =
            "/$APLLY_INVITATION_CODE_PATH?$APPLY_INVITATION_CODE_QUERY_NAME=${encodedBase64JsonString}"
        logger.debug("Generated full verification link: $fullUrl")

        try {
            // Shorten the URL using the ShortUrl service
            val shortenedUrlResponse = shorturlService.generateShortUrl(fullUrl)
            val shortenedUrl = shortenedUrlResponse.shortUrl
            logger.debug("Shortened verification link: $shortenedUrl")
            return shortenedUrl
        } catch (e: Exception) {
            logger.error("Failed to shorten URL, using original link", e)
            // Return just the path part if shortening fails
            return "/$APLLY_INVITATION_CODE_PATH?$APPLY_INVITATION_CODE_QUERY_NAME=${encodedBase64JsonString}"
        }
    }

    /**
     * Creates a new beta registration with a new code for the given email.
     * Each call will generate a new invitation code, allowing multiple registrations per email.
     *
     * @param request The request containing the email to register
     * @return A BetaRegistration response with status information
     */
    override suspend fun getOrCreateBetaRegistration(request: BetaMessage.GetOrCreateBetaRegistrationRequest): BetaMessage.BetaRegistration {
        logger.info("Processing getOrCreateBetaRegistration for email: ${request.email}")

        // Initialize the response builder with the email from the request
        val builder = BetaMessage.BetaRegistration.newBuilder().setEmail(request.email)
        // Initialize the status builder with a default UNKNOWN status code
        val status = Status.newBuilder().setCode(io.grpc.Status.Code.UNKNOWN.value())

        try {
            // Always create a new beta invitation with a new code
            logger.debug("Creating new beta registration with new code for ${request.email}")
            val newCode = generateVerificationCode()

            if (betaInvitationGateway.createWithCode(request.email, newCode).wasAcknowledged()) {
                logger.info("Successfully created new beta registration with code for ${request.email}")
                builder.success = true
                builder.code = newCode
                status.code = io.grpc.Status.Code.OK.value()
                status.message = CREATE_INVITATION_SUCCESS_MESSAGE
            } else {
                // Handle failure in creating a new beta invitation
                logger.error("Failed to create beta registration for ${request.email}")
                status.message = CREATE_INVITATION_FAILED_MESSAGE
            }
        } catch (t: Throwable) {
            // Handle any exceptions during beta invitation creation
            logger.error("Error in getOrCreateBetaRegistration for ${request.email}", t)
            status.message = t.message ?: ERROR_CREATE_INVITATION_CODE_MESSAGE
        }

        // Build and return the response with the status
        logger.debug("Returning beta registration response with status code: ${status.code}")
        return builder.setStatus(status.build()).build()
    }

    /**
     * Sends beta invitation emails to a list of accounts.
     * For each account, it creates or retrieves a beta registration, generates a verification code,
     * and sends an invitation email with the code.
     *
     * @param request The request containing the list of accounts to invite
     * @return A response containing the results for each account
     */
    override suspend fun inviteJoinBeta(request: BetaMessage.InviteJoinBetaRequest): BetaMessage.InviteJoinBetaResponse {
        logger.info("Processing inviteJoinBeta for ${request.accountsCount} accounts")

        // Initialize the response builder
        val builder = BetaMessage.InviteJoinBetaResponse.newBuilder()

        // Convert the accounts list to a set to remove duplicates
        val accountList = request.accountsList.toSet()
        logger.debug("Processing ${accountList.size} unique accounts after removing duplicates")

        val resultList = accountList.map { account ->
            // Create or retrieve beta registration for the account
            logger.debug("Getting or creating beta registration for ${account.email}")
            val userBetaRegisterResponse = getOrCreateBetaRegistration(
                BetaMessage.GetOrCreateBetaRegistrationRequest.newBuilder().setEmail(account.email).build()
            )

            if (userBetaRegisterResponse.status.code != io.grpc.Status.Code.OK.value()) {
                logger.error("Failed to get or create beta registration for ${account.email}: ${userBetaRegisterResponse.status.message}")
                return@map BetaMessage.InviteJoinBetaAccountResponse.newBuilder().setEmail(account.email).setStatus(
                    Status.newBuilder().setCode(userBetaRegisterResponse.status.code)
                        .setMessage(userBetaRegisterResponse.status.message).build()
                ).build()
            }

            // Use the code from the newly created registration
            logger.debug("Using code from newly created beta registration for ${account.email}")
            val invitationCode = userBetaRegisterResponse.code

            if (invitationCode.isNullOrBlank()) {
                // If no code is available, return an error
                logger.error("No invitation code available for ${account.email}")
                return@map BetaMessage.InviteJoinBetaAccountResponse.newBuilder().setEmail(account.email).setStatus(
                    Status.newBuilder().setCode(io.grpc.Status.Code.INTERNAL.value())
                        .setMessage(GENERATE_CODE_FAILED_MESSAGE).build()
                ).build()
            }

            // Initialize the status builder
            val status = Status.newBuilder()
            try {
                // Determine whether to send a regular invitation or one with a password
                logger.debug("Sending invitation email to ${account.email}")
                val sendResult = if (account.password.isNullOrBlank()) {
                    logger.debug("Sending regular beta invitation (without password)")
                    notificationServiceGateway.sendInviteJoinBeta(
                        account.email, InviteJoinBeta(
                            email = account.email,
                            code = invitationCode,
                            applyBetaCodeUrl = generateBetaVerificationLink(
                                invitationCode, account.redirectUrl
                            ),
                        )
                    )
                } else {
                    logger.debug("Sending beta invitation with password")
                    notificationServiceGateway.sendInviteJoinBetaWithPassword(
                        account.email, InviteJoinBetaWithPassword(
                            email = account.email,
                            code = invitationCode,
                            password = account.password,
                            applyBetaCodeUrl = generateBetaVerificationLink(
                                invitationCode, account.redirectUrl
                            ),
                        )
                    )
                }

                // Set the status based on the result of sending the email
                status.setCode(sendResult.status.code)
                val statusMessage = if (sendResult.status.code == io.grpc.Status.Code.OK.value()) {
                    logger.info("Successfully sent invitation email to ${account.email}")
                    SEND_INVITE_EMAIL_SUCCESS_MESSAGE
                } else {
                    logger.error("Failed to send invitation email to ${account.email}: ${sendResult.status.message}")
                    SEND_INVITE_EMAIL_FAILED_MESSAGE
                }
                status.setMessage(statusMessage)
            } catch (t: Throwable) {
                // Handle any exceptions during the email sending process
                logger.error("Error sending invitation email to ${account.email}", t)
                status.setCode(io.grpc.Status.Code.UNKNOWN.value())
                status.setMessage(t.message ?: ERROR_SEND_INVITE_EMAIL_MESSAGE)
            }

            // Build the response for the current account
            logger.debug("Building response for account: ${account.email}")
            BetaMessage.InviteJoinBetaAccountResponse.newBuilder().setEmail(account.email).setStatus(status.build())
                .build()
        }

        // Add all results to the response builder and return the response
        logger.debug("Completed processing all accounts, returning response with ${resultList.size} results")
        builder.addAllResults(resultList)
        return builder.build()
    }

    /**
     * Validates an invitation code to determine if it's valid.
     *
     * @param request The request containing the invitation code to validate
     * @return A response indicating whether the code is valid
     */
    override suspend fun applyInvitationCode(request: BetaMessage.ApplyInvitationCodeRequest): BetaMessage.ApplyInvitationCodeResponse {
        logger.info("Validating invitation code: ${request.invitationCode}")

        // Retrieve the user beta registration details using the provided invitation code
        logger.debug("Looking up beta registration by code")
        val userBetaRegistration = betaInvitationGateway.getByCode(request.invitationCode)
        val builder = BetaMessage.ApplyInvitationCodeResponse.newBuilder()
        val status = Status.newBuilder()

        // Set the default status code to UNKNOWN
        status.setCode(io.grpc.Status.Code.UNKNOWN.value())

        try {
            if (userBetaRegistration == null) {
                logger.warn("Invitation code not found: ${request.invitationCode}")
                status.setCode(io.grpc.Status.Code.NOT_FOUND.value())
                status.setMessage("code not found")
            } else {
                logger.info("Valid invitation code found for email: ${userBetaRegistration.email}")
                status.setCode(io.grpc.Status.Code.OK.value())
                status.setMessage("check success")
            }
        } catch (t: Throwable) {
            logger.error("Error validating invitation code: ${request.invitationCode}", t)
            status.setMessage(t.message ?: "error check code")
        }

        logger.debug("Returning invitation code validation response with status code: ${status.code}")
        builder.setStatus(status.build())
        return builder.build()
    }

    /**
     * Generates a random verification code of the specified length.
     * The code consists of uppercase letters and numbers.
     *
     * @param length The length of the verification code to generate (default: 9)
     * @return The generated verification code
     */
    private fun generateVerificationCode(length: Int = 9): String {
        logger.debug("Generating verification code of length: $length")

        val allowedCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        val secureRandom = SecureRandom()
        val otp = StringBuilder()

        for (i in 0 until length) {
            val randomIndex: Int = secureRandom.nextInt(allowedCharacters.length)
            otp.append(allowedCharacters[randomIndex])
        }

        // Don't log the full code for security reasons, just the first few characters
        val codePreview = if (otp.length > 4) "${otp.substring(0, 4)}..." else "..."
        logger.debug("Generated verification code preview: $codePreview")

        return otp.toString()
    }
}


package portal.beta.models

data class BetaReg<PERSON>Request(val email: String)
data class BetaRegisterResponse(val status: Int, val message: String)

data class BetaInvitationRequest(val emails: List<String>)
data class BetaInvitationAccountResponse(val email: String, val success: <PERSON>olean, val message: String)
data class BetaInvitationResponse(val results: List<BetaInvitationAccountResponse>)

// Admin Beta Registration
data class AdminBetaRegisterRequest(
    val emails: List<String>,
    val username: List<String>
)

data class AdminBetaRegisterAccountResponse(
    val email: String, val success: Boolean, val message: String
)

data class AdminBetaRegisterResponse(
    val result: List<AdminBetaRegisterAccountResponse>,
)

// Invite join beta
data class InviteJoinBeta(
    val email: String, val password: String?, val redirectUrl: String?
)

package portal.beta.dbgateway

import com.mongodb.client.model.Filters.eq
import com.mongodb.client.model.Updates.set
import com.mongodb.client.result.InsertOneResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.reactive.awaitSingle
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.beta.koin.DatabaseModule
import portal.beta.pojo.BetaInvitationPojo

/**
 * Gateway for accessing and manipulating beta invitation data in MongoDB.
 * This class provides methods to create, retrieve, and update beta invitations.
 *
 * @property collection MongoDB collection for storing beta invitation data
 */
@Singleton
class BetaInvitationGateway(
    @Named(DatabaseModule.MONGO_COLLECTION_BETA_INVITATIONS_PROFILE_POJO) private val collection: MongoCollection<BetaInvitationPojo>,
) : Logging {

    /**
     * Creates a new beta invitation for the specified email.
     *
     * @param email The email address to create the invitation for
     * @return The result of the insert operation
     */
    suspend fun create(email: String): InsertOneResult {
        logger.info("Creating beta invitation for email: $email")
        val result = collection.insertOne(BetaInvitationPojo(email = email)).awaitSingle()
        logger.debug("Beta invitation created for $email, acknowledged: ${result.wasAcknowledged()}")
        return result
    }

    /**
     * Creates a new beta invitation with a code for the specified email.
     * This allows multiple invitations for the same email with different codes.
     *
     * @param email The email address to create the invitation for
     * @param code The invitation code to assign
     * @return The result of the insert operation
     */
    suspend fun createWithCode(email: String, code: String): InsertOneResult {
        logger.info("Creating beta invitation with code for email: $email")
        val invitation = BetaInvitationPojo(email = email, code = code, createdAt = java.util.Date())
        val result = collection.insertOne(invitation).awaitSingle()
        logger.debug("Beta invitation with code created for $email, acknowledged: ${result.wasAcknowledged()}")
        return result
    }

    /**
     * Retrieves a beta invitation by email address.
     *
     * @param email The email address to look up
     * @return The beta invitation if found, null otherwise
     */
    suspend fun get(email: String): BetaInvitationPojo? {
        logger.debug("Retrieving beta invitation for email: $email")
        val invitation = collection.find(eq("email", email)).awaitFirstOrNull()
        if (invitation == null) {
            logger.debug("No beta invitation found for email: $email")
        } else {
            logger.debug("Found beta invitation for email: $email, has code: ${invitation.code != null}")
        }
        return invitation
    }

    /**
     * Retrieves a beta invitation by invitation code.
     *
     * @param code The invitation code to look up
     * @return The beta invitation if found, null otherwise
     */
    suspend fun getByCode(code: String): BetaInvitationPojo? {
        logger.debug("Retrieving beta invitation by code")
        val invitation = collection.find(eq("code", code)).awaitFirstOrNull()
        if (invitation == null) {
            logger.debug("No beta invitation found for the provided code")
        } else {
            logger.debug("Found beta invitation for email: ${invitation.email} using code")
        }
        return invitation
    }

    /**
     * Updates the invitation code for a beta invitation.
     *
     * @param email The email address of the invitation to update
     * @param code The new invitation code
     * @return true if the update was successful, false otherwise
     */
    suspend fun updateCode(email: String, code: String): Boolean {
        logger.debug("Updating invitation code for email: $email")
        val result = collection.updateOne(eq("email", email), set("code", code)).awaitSingle()
        val success = result.modifiedCount > 0
        if (success) {
            logger.debug("Successfully updated invitation code for email: $email")
        } else {
            logger.warn("Failed to update invitation code for email: $email, matched: ${result.matchedCount}, modified: ${result.modifiedCount}")
        }
        return success
    }
}

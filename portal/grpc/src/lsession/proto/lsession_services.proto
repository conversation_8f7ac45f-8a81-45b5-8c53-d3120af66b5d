syntax = 'proto3';

package proto.portal.lsession;

import "lsession_message.proto";
import "google/protobuf/empty.proto";

/**
 *
 * <AUTHOR>
 */

service LSessionService {
  // learning session api
  rpc CreateSession   (CreateSessionRequest)
      returns (CreateSessionResponse);
  rpc GetSessionOnAllById   (GetSessionOnAllByIdRequest)
      returns (GetSessionOnAllByIdResponse);
  rpc GetSessionDetailsById   (GetSessionDetailByIdRequest)
      returns (GetSessionDetailByIdResponse);
  rpc GetSessionDetailsByIds    (stream GetSessionDetailByIdRequest)
      returns (stream GetSessionDetailByIdResponse);
  rpc GetSessionDetailsByUserId   (GetSessionDetailByUserIdRequest)
      returns (GetSessionDetailByUserIdResponse);
  rpc GetSessionDetailsByOwnerId    (GetSessionDetailByOwnerIdRequest)
      returns (GetSessionDetailByOwnerIdResponse);

  rpc GetAllLSessionSummaryByUserId   (SearchLSessionSummaryByUserIdRequest)
      returns (GetAllLSessionsSummaryResponse);

  rpc UpdateSessionOnAll    (UpdateSessionOnAllRequest)
      returns (UpdateSessionOnAllResponse);
  rpc UpdateSessionDetails    (UpdateSessionDetailsRequest)
      returns (UpdateSessionDetailsResponse);
  rpc UpdateSessionAvatar   (UpdateSessionAvatarRequest)
      returns (UpdateSessionAvatarResponse);
  rpc UpdateSessionRegistrations    (UpdateSessionRegistrationsRequest)
      returns (UpdateSessionRegistrationsResponse);
  rpc UpdateSessionState    (UpdateLSessionStateRequest)
      returns (UpdateLSessionStateResponse);
  rpc UpdateRaiseHandStatus   (UpdateRaiseHandStatusRequest)
      returns (UpdateRaiseHandStatusResponse);
  rpc UpdateShareScreenStatus   (UpdateShareScreenStatusRequest)
      returns (UpdateShareScreenStatusResponse);
  rpc UpdateUserAvailableStatus   (UpdateUserAvailableStatusRequest)
      returns (UpdateUserAvailableStatusResponse);
  rpc UpdateRequestPinTab   (UpdateRequestPinTabRequest)
      returns (UpdateRequestPinTabResponse);
  rpc CancelAllRaisingHand    (CancelAllRaisingHandRequest)
      returns (CancelAllRaisingHandResponse);

  // classroom setting api
  rpc UpdateClassroomSettings   (UpdateClassroomSettingRequest)
      returns (UpdateClassroomSettingResponse);
  rpc GetClassroomSettingsById    (GetClassroomSettingByIdRequest)
      returns (GetClassroomSettingByIdResponse);

  // learning session registration api
  rpc CreateSessionRegistration   (CreateSessionRegistrationRequest)
      returns (CreateSessionRegistrationResponse);
  rpc GetSessionRegistrationById    (GetSessionRegistrationByIdRequest)
      returns (GetSessionRegistrationResponse); // get registration by regId
  rpc GetSessionRegistrationsByIds    (stream GetSessionRegistrationByIdRequest)
      returns (stream GetSessionRegistrationResponse);
  rpc GetSessionRegistrationByLsIdAndUserId   (GetSessionRegistrationByLsIdAndUserIdRequest)
      returns (GetSessionRegistrationResponse); // get registration by lsId and userId
  rpc GetSessionRegistrationsByLsId   (GetSessionRegistrationsByLsIdRequest)
      returns (GetSessionRegistrationsResponse);
  rpc GetSessionRegistrationsByLsIdAndStatus    (GetSessionRegistrationsByLsIdAndStatusRequest)
      returns (GetSessionRegistrationsResponse);
  rpc UpdateRegistrationStatus    (UpdateRegistrationStatusRequest)
      returns (UpdateRegistrationStatusResponse);
}

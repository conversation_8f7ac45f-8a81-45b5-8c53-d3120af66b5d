syntax = 'proto3';

package proto.portal.lsession;

import "google/rpc/status.proto";

/**
 *
 * <AUTHOR>
 */

message LSessionStateProto {
  optional string status = 1;
  optional int32 registered = 2;
  optional int64 started_at = 3;
  optional int64 ended_at = 4;
}

message RequestPinTabStateProto {
  string status = 1;
  string tab_id = 2;
  string tabName = 3;
}

message ClassroomUserStateProto {
  string status = 1;
  string user_status = 2;
  optional string share_screen_status = 3;
  repeated RequestPinTabStateProto request_pin_tab_state = 4;
  optional int64 joined_time = 5;
}

message LSessionDetailsProto {
  string id = 1;
  string creatorId = 2;
  string title = 3;
  string description = 4;
  string img_url = 5;
  string grade = 6;
  string subject = 7;
  int64 start_date = 8;
  int64 start_time = 9;
  int32 expected_duration = 10;
  LSessionStateProto state = 11;
}

message ClassroomSettingsProto {
  bool camera = 1;
  bool audio = 2;
  bool chat_box = 3;
  bool auto_record = 4;
  string board_type = 5;
  int32 max_registration = 6;
  bool chatting_filer = 7;
  int32 reject_late_registration_after_minute = 8;
}

message LSessionRegistrationProto {
  string id = 1;
  string ls_id = 2;
  string ls_owner = 3;
  string user_id = 4;
  string reg_status = 5;
  int64 reg_time = 6;
  ClassroomUserStateProto state = 7;
}

message CancelAllRaisingHandRequest {
  string ls_id = 1;
}

message CancelAllRaisingHandResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
}


message CreateSessionRequest {
  // session detail
  LSessionDetailsProto lsession_details = 1;
  // classroom setting
  ClassroomSettingsProto classroom_settings = 2;
}

message CreateSessionResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
}

message UpdateSessionAvatarRequest {
    string ls_id = 1;
    string imgUrl = 2;
}

message UpdateSessionAvatarResponse {
    google.rpc.Status status = 1;
}

message CreateSessionRegistrationRequest {
  string ls_id = 1;
  string ls_owner_id = 2;
  string user_id = 3;
  string reg_status = 4;
  int64 reg_time = 5;
}

message CreateSessionRegistrationResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
}

message GetSessionOnAllByIdRequest {
  string ls_id = 1;
}

message GetSessionOnAllByIdResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
  // session detail
  LSessionDetailsProto lsession_details = 3;
  // classroom setting
  ClassroomSettingsProto classroom_settings = 4;
  // A map containing the information of the registration. The key is the id of the registration
  // the value is the information
  map<string, LSessionRegistrationProto> registrations = 5;
}

message GetSessionDetailByIdRequest {
  string ls_id = 1;
}

message GetSessionDetailByIdResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
  // session detail
  LSessionDetailsProto lsession_details = 3;
}

message GetSessionDetailByUserIdRequest {
  string user_id = 1;
}

message GetSessionDetailByUserIdResponse {
  google.rpc.Status status = 1;
  string user_id = 2;
  // sessions detail map<ls_id, session_detail>
  map<string, LSessionDetailsProto> lsession_details = 3;
}

message GetSessionDetailByOwnerIdRequest {
  string ls_owner_id = 1;
}

message GetSessionDetailByOwnerIdResponse {
  google.rpc.Status status = 1;
  string ls_owner_id = 2;
  // sessions detail map<ls_id, session_detail>
  map<string, LSessionDetailsProto> lsession_details = 3;
}

message GetClassroomSettingByIdRequest {
  string ls_id = 1;
}

message GetClassroomSettingByIdResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
  // setting
  ClassroomSettingsProto settings = 3;
}

message GetSessionRegistrationByIdRequest {
  string reg_id = 1;
}

message GetSessionRegistrationResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
  LSessionRegistrationProto registration = 3;
}

message GetSessionRegistrationByLsIdAndUserIdRequest {
  string user_id = 1;
  string ls_id = 2;
}

message GetSessionRegistrationsByLsIdRequest {
  string ls_id = 1;
}

message GetSessionRegistrationsResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
  // registrations
  map<string, LSessionRegistrationProto> registrations = 3;
}

message GetSessionRegistrationsByLsIdAndStatusRequest {
  string ls_id = 1;
  repeated string reg_status = 2;
}

message SearchLSessionSummaryByUserIdRequest {
  string user_id = 1;
  repeated string regStatus = 2;
  repeated string lsStatus = 3;
}

message GetAllLSessionsSummaryResponse {
  message LSessionProto {
      string ls_id = 1;
      LSessionDetailsProto details = 2;
      ClassroomSettingsProto settings = 3;
      LSessionRegistrationProto registration = 4;
  }
  google.rpc.Status status = 1;
  repeated LSessionProto lsessions = 2;
}

message UpdateSessionOnAllRequest {
  message RegistrationProto {
    string reg_id = 1;
    string reg_status = 2;
  }

  string ls_id = 1;
  // session detail
  LSessionDetailsProto ls_details = 2;
  // classroom setting
  ClassroomSettingsProto classroom_settings = 4;
  // registrations
  map<string, RegistrationProto> registrations = 5;
}

message UpdateSessionOnAllResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
}

message UpdateSessionDetailsRequest {
  string ls_id = 1;
  LSessionDetailsProto ls_details = 2;
}

message UpdateSessionDetailsResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
}

message UpdateSessionImgUrlRequest {
  string ls_id = 1;
  string img_url = 2;
}

message UpdateSessionImgUrlResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
}

message UpdateSessionRegistrationsRequest {
  message RegistrationProto {
    string reg_id = 1;
    string reg_status = 3;
  }

  string ls_id = 1;
  map<string, RegistrationProto> registrations = 3;
}

message UpdateSessionRegistrationsResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
}

message UpdateRaiseHandStatusRequest {
  string reg_id = 1;
  string status = 2;
}

message UpdateShareScreenStatusRequest {
  string reg_id = 1;
  string status = 2;
}

message UpdateShareScreenStatusResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
}

message UpdateRaiseHandStatusResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
}

message UpdateUserAvailableStatusRequest {
  string reg_id = 1;
  string ls_id = 2;
  string status = 3;
}

message UpdateUserAvailableStatusResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
}

message UpdateRequestPinTabRequest {
  string reg_id = 1;
  string tab_id = 2;
  optional string status = 3;
  optional string tab_name = 4;
}

message UpdateRequestPinTabResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
  string tab_id = 3;
}

message UpdateClassroomSettingRequest {
  string ls_id = 1;
  ClassroomSettingsProto classroom_settings = 2;
}

message UpdateClassroomSettingResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
}

message UpdateRegistrationStatusRequest {
  string reg_id = 1;
  string new_status = 2;
  int64 reg_time = 3;
}

message UpdateRegistrationStatusResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
}

message UpdateLSessionStateRequest {
  string ls_id = 1;
  LSessionStateProto state = 2;
}

message UpdateLSessionStateResponse {
  google.rpc.Status status = 1;
  string ls_id = 2;
  LSessionStateProto state = 3;
}

message GetSessionsByFilterRequest {

}

/**
Criteria to filter sessions of an user by registration statuses
 */
message GetSessionIdsByRegistrationStatusRequest {
  string requesting_user_id = 1;
  repeated string reg_statuses = 2;
}

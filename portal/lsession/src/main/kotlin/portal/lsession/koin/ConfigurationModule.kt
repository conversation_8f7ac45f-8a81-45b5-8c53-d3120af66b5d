package portal.lsession.koin

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.logger.Logging
import portal.lsession.configuration.Configuration
import portal.lsession.configuration.DatabaseConfig
import portal.lsession.configuration.ServerConfig
import java.io.File
import kotlin.system.exitProcess
import org.koin.core.annotation.Factory
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton

/**
 *
 * <AUTHOR>
 */
@Module
class ConfigurationModule : Logging {
    private val configPath = "conf/config.json"

    @Singleton
    fun provideConfiguration(): Configuration = try {
        val mapper = jacksonObjectMapper()
        mapper.readValue(File(configPath), Configuration::class.java)
    } catch (t: Throwable) {
        logger.error("Exception when load configurations... ", t)
        exitProcess(1)
    }

    @Singleton
    fun provideServerConfiguration(config: Configuration): ServerConfig = config.serverConf

    @Singleton
    fun provideDatabaseConfiguration(config: Configuration): DatabaseConfig = config.dbConf

}


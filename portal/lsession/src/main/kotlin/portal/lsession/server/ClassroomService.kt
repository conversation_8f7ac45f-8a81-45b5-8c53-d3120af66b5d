package portal.lsession.server

import common.libs.logger.Logging
import io.grpc.Status
import io.grpc.StatusRuntimeException
import kotlinx.coroutines.rx3.await
import kotlinx.coroutines.rx3.awaitSingleOrNull
import org.bson.BsonDocument
import org.bson.BsonDocumentWriter
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistry
import org.bson.json.JsonReader
import portal.datastructures.lsession.RaiseHandStatus
import portal.lsession.dbgateway.ClassroomDBGateway
import portal.lsession.dbgateway.SessionRegistrationGateway
import portal.lsession.pojo.activity.ActivityStatus
import portal.lsession.pojo.activity.ClassroomActivity
import portal.lsession.pojo.activity.YesNoResponse
import proto.portal.classroom.ClassroomMessages.*
import proto.portal.classroom.ClassroomServiceGrpcKt

class ClassroomService constructor(
    private val clrDBGateway: ClassroomDBGateway,
    private val lsRegGateway: SessionRegistrationGateway,
    private val codecRegistry: CodecRegistry
): ClassroomServiceGrpcKt.ClassroomServiceCoroutineImplBase(), Logging {

    private val classroomActivityCodec = codecRegistry.get(ClassroomActivity::class.java)

    override suspend fun createClassroomActivity(request: CreateClassroomActivityRequest): CreateClassroomActivityResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = CreateClassroomActivityResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            var pojo = decodeToPojo(request.jsonActivity)
            pojo = clrDBGateway.insertClassroomActivity(pojo).await()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.activityId = pojo.id
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to create classroom activity")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    private fun decodeToPojo(jsonActivity: String): ClassroomActivity {
        return classroomActivityCodec.decode(JsonReader(jsonActivity), DecoderContext.builder().build())
    }

    override suspend fun loadClassroomActivity(request: LoadClassroomActivityRequest): LoadClassroomActivityResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = LoadClassroomActivityResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            val activities = clrDBGateway.findClassroomActivities(request.lsId).await()
            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            val jsonList = activities.map { serializeClassroomActivity(it) }
            responseBuilder.addAllJsonActivity(jsonList)
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: ---- {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to load classroom activity")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    private fun serializeClassroomActivity(activity: ClassroomActivity): String {
        val document = BsonDocument()

        classroomActivityCodec.encode(
            BsonDocumentWriter(document),
            activity,
            EncoderContext.builder().build()
        )

        return document.toJson()
    }

    override suspend fun updateQuickQuestionActivity(request: UpdateQuickQuestionActivityRequest): UpdateQuickQuestionActivityResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateQuickQuestionActivityResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            clrDBGateway.updateQuickQuestionActivity(request.activityId, request.userId, YesNoResponse.valueOf(request.response)).await()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to update quick question activity")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun updateRequestPresentationActivity(request: UpdateRequestPresentationActivityRequest): UpdateRequestPresentationActivityResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateRequestPresentationActivityResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            clrDBGateway.updateRequestPresentationActivity(request.activityId, YesNoResponse.valueOf(request.response)).awaitSingleOrNull()
                ?: throw Status.FAILED_PRECONDITION
                    .withDescription("Activity not found or activity state is not ON_GOING")
                    .asRuntimeException()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to update request presentation activity")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun updateClassroomActivityStatus(request: UpdateClassroomActivityStatusRequest): UpdateClassroomActivityStatusResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateClassroomActivityStatusResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            clrDBGateway.updateClassroomActivityStatus(
                request.activityId,
                ActivityStatus.valueOf(request.expectedStatus),
                ActivityStatus.valueOf(request.newStatus)
            ).awaitSingleOrNull()
                ?: throw Status.FAILED_PRECONDITION
                    .withDescription("Activity not found or activity state not matched with expected state")
                    .asRuntimeException()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to update classroom activity state")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun getClassroomActivityById(request: GetClassroomActivityByIdRequest): GetClassroomActivityByIdResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = GetClassroomActivityByIdResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            val activity = clrDBGateway.findClassroomActivityById(request.activityId).await()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            val json = serializeClassroomActivity(activity)
            responseBuilder.jsonActivity = json
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to get classroom activity by id")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun getLatestOngoingPresentationRequestActivity(request: GetLatestOngoingPresentationRequestActivityRequest): GetLatestOngoingPresentationRequestActivityResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = GetLatestOngoingPresentationRequestActivityResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            val activity = clrDBGateway.findLatestOngoingPresentationRequestActivity(request.lsId).awaitSingleOrNull()
                ?: throw Status.NOT_FOUND
                    .withDescription("Not found any ongoing presentation request activity")
                    .asRuntimeException()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            val json = serializeClassroomActivity(activity)
            responseBuilder.jsonActivity = json
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to get latest ongoing presentation request activity")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun endClassroom(request: EndClassroomRequest): EndClassroomResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = EndClassroomResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            clrDBGateway.closeAllActivities(request.lsId).awaitSingleOrNull()
                ?: throw Status.INTERNAL
                    .withDescription("Failed to close all activities")
                    .asRuntimeException()

            lsRegGateway.updateAllClassroomUserState(request.lsId, RaiseHandStatus.NONE).awaitSingleOrNull()
                ?: throw Status.INTERNAL
                    .withDescription("Failed to update all classroom user states")
                    .asRuntimeException()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to end classroom")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }
}

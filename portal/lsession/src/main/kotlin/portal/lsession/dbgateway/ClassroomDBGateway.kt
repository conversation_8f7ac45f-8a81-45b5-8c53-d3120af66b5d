package portal.lsession.dbgateway

import com.mongodb.client.model.*
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Maybe
import io.reactivex.rxjava3.core.Single
import org.bson.types.ObjectId
import portal.lsession.pojo.activity.*

class ClassroomDBGateway constructor(
    private val clrActivityCollection: MongoCollection<ClassroomActivity>,
) : Logging {

    fun insertClassroomActivity(activity: ClassroomActivity): Single<ClassroomActivity> {
        val filter = Filters.eq("_id", ObjectId(activity.id))
        val options = FindOneAndReplaceOptions().upsert(true).returnDocument(ReturnDocument.AFTER)
        return Flowable.defer { clrActivityCollection.findOneAndReplace(filter, activity, options) }
            .firstOrError()
            .doOnError {
                logger.error("failed to insert classroom activity {}: ", activity, it)
            }
    }

    fun findClassroomActivities(lsId: String): Single<List<ClassroomActivity>> {
        val filter = Filters.and(
            Filters.eq(ClassroomActivity::lsId.name, ObjectId(lsId)),
            Filters.eq(ClassroomActivity::status.name, ActivityStatus.ON_GOING)
        )
        return Flowable.defer { clrActivityCollection.find(filter) }
            .toList()
            .doOnError {
                logger.error("failed to find all ongoing classroom activity {}: ", lsId, it)
            }
    }

    fun findClassroomActivityById(activityId: String): Single<ClassroomActivity> {
        val filter = Filters.eq("_id", ObjectId(activityId))
        return Flowable.defer { clrActivityCollection.find(filter) }
            .firstOrError()
            .doOnError {
                logger.error("failed to find classroom activity {}: ", activityId, it)
            }
    }

    fun updateQuickQuestionActivity(activityId: String, regId: String, response: YesNoResponse): Single<ClassroomActivity> {
        val filter = Filters.eq("_id", ObjectId(activityId))
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        val updates = Updates.set("${ClassroomActivity::data.name}.${QuickQuestionAD::responses.name}.$regId", response)
        return Flowable.defer { clrActivityCollection.findOneAndUpdate(filter, updates, ops) }
            .doOnError {
                logger.error("failed to update activity {} for user {}: ", activityId, regId, it)
            }.firstOrError()
    }

    fun updateRequestPresentationActivity(activityId: String, response: YesNoResponse): Maybe<ClassroomActivity> {
        val filter = Filters.and(
            Filters.eq("_id", ObjectId(activityId)),
            Filters.eq(ClassroomActivity::status.name, ActivityStatus.ON_GOING)
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        val updates = listOf(
            Updates.set("${ClassroomActivity::data.name}.${RequestPresentationAD::response.name}", response),
            Updates.set(ClassroomActivity::status.name, ActivityStatus.FINISHED),
        )
        return Flowable.defer { clrActivityCollection.findOneAndUpdate(filter, updates, ops) }
            .firstElement()
            .doOnError {
                logger.error("failed to update activity {}: ", activityId, it)
            }
    }

    fun updateClassroomActivityStatus(activityId: String, expectedStatus: ActivityStatus, newStatus: ActivityStatus): Maybe<ClassroomActivity> {
        val filter = Filters.and(
            Filters.eq("_id", ObjectId(activityId)),
            Filters.eq(ClassroomActivity::status.name, expectedStatus)
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        val updates = Updates.set(ClassroomActivity::status.name, newStatus)
        return Flowable.defer { clrActivityCollection.findOneAndUpdate(filter, updates, ops) }
            .firstElement()
            .doOnError {
                logger.error("failed to update activity {} state: ", activityId, it)
            }
    }

    fun findLatestOngoingPresentationRequestActivity(lsId: String): Maybe<ClassroomActivity> {
        val filter = Filters.and(
            Filters.eq(ClassroomActivity::lsId.name, ObjectId(lsId)),
            Filters.eq("${ClassroomActivity::data.name}.activityType", RequestPresentationAD::class.java.simpleName),
            Filters.eq(ClassroomActivity::status.name, ActivityStatus.ON_GOING)
        )
        return Flowable.defer { clrActivityCollection.find(filter).sort(Sorts.descending("_id")).limit(1) }
            .firstElement()
            .doOnError {
                logger.error("failed to get latest ongoing presentation request activity {}: ", lsId, it)
            }
    }

    fun closeAllActivities(lsId: String): Maybe<UpdateResult> {
        val filter = Filters.and(
            Filters.eq(ClassroomActivity::lsId.name, ObjectId(lsId)),
            Filters.eq(ClassroomActivity::status.name, ActivityStatus.ON_GOING)
        )
        val updates = Updates.set(ClassroomActivity::status.name, ActivityStatus.FINISHED)
        return Flowable.defer { clrActivityCollection.updateMany(filter, updates) }
            .firstElement()
            .doOnError {
                logger.error("failed to close all activities in room {}: ", lsId, it)
            }
    }
}

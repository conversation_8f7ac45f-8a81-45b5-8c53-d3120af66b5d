<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <file>logs/console.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/console.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="LSESSION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/lsession.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/lsession.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="PRODUCER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/producer.log</file>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>logs/producer.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- each file should be at most 100MB, keep 30 days worth of history, but at most 5GB -->
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="CONSUMER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/consumer.log</file>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>logs/consumer.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- each file should be at most 100MB, keep 30 days worth of history, but at most 5GB -->
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="REACTOR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/reactor.log</file>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>logs/reactor.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- each file should be at most 100MB, keep 30 days worth of history, but at most 5GB -->
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="DATABASE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/database.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/database.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="DATABASE-GATEWAY" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/database_gateway.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/database_gateway.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <appender name="SERVICE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/services.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %logger{15} %-5level %msg%ex%n</pattern>
        </encoder>
    </appender>

    <logger name="portal.lsession.server" level="DEBUG" additivity="false">
        <appender-ref ref="SERVICE" />
    </logger>

    <logger name="portal.lsession.dbgateway" level="DEBUG" additivity="false">
        <appender-ref ref="DATABASE-GATEWAY" />
    </logger>

    <logger name="portal.lsession" level="DEBUG" additivity="false">
        <appender-ref ref="LSESSION" />
    </logger>

    <logger name="portal.lsession.event.kafka.producer" level="DEBUG" additivity="false">
        <appender-ref ref="PRODUCER"/>
    </logger>

    <logger name="portal.lsession.event.kafka.consumer" level="DEBUG" additivity="false">
        <appender-ref ref="CONSUMER"/>
    </logger>

    <logger name="portal.lsession.event.kafka.producer.topic.ClassroomNotification" level="TRACE" additivity="false">
        <appender-ref ref="PRODUCER"/>
    </logger>

    <logger name="portal.lsession.event.kafka.consumer.topic.ClassroomNotification" level="TRACE" additivity="false">
        <appender-ref ref="CONSUMER"/>
    </logger>

    <logger name="portal.lsession.event.reactor" level="DEBUG" additivity="false">
        <appender-ref ref="REACTOR"/>
    </logger>

    <logger name="org.mongodb" level="DEBUG" additivity="false">
        <appender-ref ref="DATABASE" />
    </logger>

    <root level="DEBUG">
        <appender-ref ref="APPLICATION" />
    </root>
</configuration>

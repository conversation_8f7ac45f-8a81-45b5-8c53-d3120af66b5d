VICLASS_LOCAL=192.168.1.45

# docker
NGINX_DOCKER_CONNECT_PORT_SSL=2443
NGINX_DOCKER_CONNECT_PORT=280

# local connect port
NGINX_CONNECT_PORT_SSL=443
NGINX_CONNECT_PORT=80

# portal
PORTAL_HOMEPAGE_LISTEN_PORT=4000
PORTAL_BACKEND_LISTEN_PORT=9000
PORTAL_BETA_LISTEN_PORT=1188
PORTAL_SHORTURL_LISTEN_PORT=1199
PORTAL_FILESTORE_HTTP_LISTEN_PORT=10000

# editor backend
FREEDRAWING_BACKEND_LISTEN_PORT=8010
GEOMETRY_BACKEND_LISTEN_PORT=8011
WORD_BACKEND_LISTEN_PORT=8012
MATH_BACKEND_LISTEN_PORT=8013
MAGH_BACKEND_LISTEN_PORT=8014
COMPOSER_BACKEND_LISTEN_PORT=8015
CONF_BACKEND_LISTEN_PORT=7000

VINET_CCS_LISTEN_PORT=8000
DASHBOARD_BACKEND_LISTEN_PORT=44007

# other
ENABLE_BETA=false
USE_PORTAL_HOMEPAGE_SPA=true
DEPLOYMENT_ROOT=/opt/viclass
SSL_PATH=/etc/letsencrypt/live/viclass.vn-0003
NGINX_CONF_PATH=/etc/nginx/sites-available/viclass

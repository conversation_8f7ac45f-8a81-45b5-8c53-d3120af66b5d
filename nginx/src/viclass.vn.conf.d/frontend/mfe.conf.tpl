location /modules/editor.freedrawing {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editor.geo {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editor.word {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editor.composer {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editor.math {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editor.magh {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editorui.freedrawing {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editorui.geo {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editorui.word {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editorui.composer {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editorui.math {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/editorui.magh {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/coordinator.docloader {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

location /modules/mfe {
    add_header Access-Control-Allow-Origin *;
    alias "$DEPLOYMENT_ROOT/ui/viclass/mfe";
}

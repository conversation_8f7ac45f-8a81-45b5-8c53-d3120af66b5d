location /static {
    # CORS headers
    add_header Access-Control-Allow-Origin * always;

    # Handle preflight requests
    if ($request_method = OPTIONS) {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';
        add_header Access-Control-Max-Age 1728000;
        return 204;
    }
    
    try_files $uri $uri/ /static/index.html;
    alias "$DEPLOYMENT_ROOT/static";
}

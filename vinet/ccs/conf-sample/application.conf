ktor {
    development = false
    deployment {
        port = 8000
        watch = [ classes ]
        responseWriteTimeoutSeconds = 20
    }
    application {
        modules = [ vinet.ccs.ApplicationKt.module ]
    }
}

jitsi {
    jwtAppId=viclass.classroom
    # Application secret known only to your token generator
    jwtAppSecret= #PROVIDE A SECRET. THIS SHOULD MATCH WITH SECRET PROVIDED TO JITSI PROSODY SERVICE

    jwtAcceptedIssuer=[vinet.ccs]
    jwtAcceptedAudiences=[vinet.ccs]
}
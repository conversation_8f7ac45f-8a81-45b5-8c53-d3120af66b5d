package vinet.ccs.controllers

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.bson.BsonDocument
import org.bson.BsonDocumentWriter
import org.bson.BsonInt64
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistry
import org.bson.json.JsonWriterSettings
import org.koin.core.annotation.Singleton
import portal.datastructures.lsession.*
import portal.lsession.pojo.activity.*
import portal.lsession.pojo.toPojo
import portal.notification.pojo.ClassroomTarget
import portal.notification.pojo.GroupTarget
import portal.notification.pojo.Notification
import portal.notification.pojo.notidata.*
import proto.portal.lsession.LsessionMessage
import vinet.ccs.actor.*
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.db.DatabaseService
import vinet.ccs.exception.PeerException
import vinet.ccs.gateways.ClassroomServiceGateway
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.gateways.NotificationServiceGateway
import vinet.ccs.gateways.UserServiceGateway
import vinet.ccs.model.*
import vinet.ccs.peer.Peer
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.*
import vinet.ccs.processor.ClassroomProcessorManager
import vinet.ccs.utility.*
import java.time.Instant
import java.util.*

@Singleton
class ClassroomCtrl constructor(
    private val userSG: UserServiceGateway,
    private val classroomSG: ClassroomServiceGateway,
    private val lsessionSG: LSessionServiceGateway,
    private val notificationSG: NotificationServiceGateway,
    private val databaseService: DatabaseService,
    private val codecRegistry: CodecRegistry,
    private val activityScheduler: ActivityScheduler,
    private val peerManager: PeerManager,
    private val classroomInfoCache: ClassroomInfoCache,
    private val actorManager: ActorManager,
    private val userAvailableStatusHandler: UserAvailableStatusHandler,
    private val classroomProcessorManager: ClassroomProcessorManager
) : Logging {
    private val clrActivityCodec = codecRegistry.get(ClassroomActivity::class.java)
    private val notificationCodec = codecRegistry.get(Notification::class.java)

    /**
     * Starts a virtual classroom session based on the provided request.
     *
     * This function handles the logic for starting a classroom session, including setting up the room,
     * updating session status, sending notifications and scheduling actor checks.
     *
     * @param call The ApplicationCall representing the incoming request.
     */
    suspend fun startClass(call: ApplicationCall) {
        val request = call.receive<StartClassRequest>()

        classroomProcessorManager.getOrCreateProcessor(request.lsId).submitTaskAsync {
            // Retrieve the learning session details.
            val lSessionProto = getLsessionIfExist(logger, call, lsessionSG, request.lsId) ?: return@submitTaskAsync

            // Validate if the calling user is the owner.
            if (request.callingUserId != lSessionProto.creatorId) {
                logger.error("startClass, Calling user {} is not the owner", request.callingUserId)
                return@submitTaskAsync call.respond(HttpStatusCode.Forbidden, "Permission denied. User is not the owner of the learning session.")
            }

            // Validate the current learning session status.
            val currentSessionState = lSessionProto.state.toPojo()
            if (currentSessionState.status != LSessionStatus.NOT_STARTED && currentSessionState.status != LSessionStatus.ENDED) {
                logger.error("startClass, Learning session {} is not in a valid state to start", request.lsId)
                return@submitTaskAsync call.respond(HttpStatusCode.Forbidden, "Unable to start this learning session")
            }

            // Setup the room if the session is not started.
            if (currentSessionState.status == LSessionStatus.NOT_STARTED && !setupRoom(call, lSessionProto)) return@submitTaskAsync

            // Update learning session status to STARTED.
            val updatedSessionState = try {
                lsessionSG.updateSessionStatusAsync(lSessionProto.id, LSessionStatus.STARTED).await().state.toPojo()
            } catch (exception: Throwable) {
                logger.error("startClass, Exception updating learning session status: ", exception)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Failed to update learning session status: ${exception.message}")
            }

            databaseService.updateRoomStatus(request.lsId, ClassroomStatus.OPENED)

            actorManager.addClassroomChecker(ClassroomChecker(request.lsId))

            // Respond with the updated learning session state.
            call.respond(HttpStatusCode.OK, updatedSessionState)

            // Create and save start class notification.
            val notification = Notification(
                request.callingUserId,
                ClassroomTarget(lSessionProto.id),
                "startClass, Learning Session ${lSessionProto.id} has started",
                Instant.now().plusSeconds(300),
                StartClassND(lSessionProto.id, request.callingUserId)
            )
            notificationSG.saveNotificationAsync(notification).await()
        }.join()
    }

    suspend fun fetchRoom(call: ApplicationCall) {
        if (!call.requireParams(listOf("id"))) return

        val id: String = call.parameters["id"]!!
        try {
            val room = getRoomInfoIfExist(logger, call, classroomInfoCache, id) ?: return
            val roomInfo = ClassroomInfoResponse(
                id = room.id,
                owner = room.owner,
                status = room.status,
                defaultCoordState = room.defaultCoordState,
                presentingUser = room.presentingUser,
                presentingCoordState = room.presentingCoordState,
                presentingPeer = room.presentingPeer,
                pinnedCoordStates = room.pinnedCoordStates,
            )
            call.respond(HttpStatusCode.OK, roomInfo)
        } catch (t: Throwable) {
            logger.error("fetchRoom, failed to fetch room {} ... ", id, t)
            call.respond(HttpStatusCode.InternalServerError,"Failed to fetch room info: ${t.message}")
        }
    }

    /**
     * Sets up a new virtual classroom room.
     *
     * This function inserts a coordinator state and room information into the database.
     * It handles potential errors during database operations and responds to the client accordingly.
     *
     * @param call The ApplicationCall representing the incoming request.
     * @param lsession The LSessionDetailsProto containing learning session information.
     * @return True if the room setup is successful, false otherwise.
     */
    private suspend fun setupRoom(call: ApplicationCall, lsession: LsessionMessage.LSessionDetailsProto): Boolean {
        val owner = lsession.creatorId
        val roomId = lsession.id
        val title = lsession.title

        try {
            // Insert coordinator state into the database.
            val coordinatorResult = databaseService.insertCoordinatorState(CoordinatorState(owner, roomId, title))
            val coordinatorId = coordinatorResult.insertedId?.asObjectId()?.value?.toHexString() ?: run {
                logger.error("setupRoom, Failed to insert coordinator for room {}", roomId)
                call.respond(HttpStatusCode.InternalServerError, "Không tạo được bảng trắng")
                return false
            }

            // Insert room information into the database.
            val roomResult = databaseService.insertRoom(
                ClassroomInfo(
                    id = roomId,
                    owner = owner,
                    status = ClassroomStatus.CREATED,
                    defaultCoordState = coordinatorId,
                    presentingUser = owner,
                    presentingCoordState = coordinatorId,
                )
            )
            if (roomResult.insertedId == null) {
                logger.error("setupRoom, Failed to insert room info for room {}", roomId)
                call.respond(HttpStatusCode.InternalServerError, "Không tạo được lớp học")
                return false
            }

            return true
        } catch (exception: Throwable) {
            logger.error("setupRoom, Error setting up room {} ... ", roomId, exception)
            call.respond(HttpStatusCode.InternalServerError, "Không tạo được lớp học")
            return false
        }
    }

    suspend fun stopClass(call: ApplicationCall) {
        val req = call.receive<StopClassRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val lsessionProto = lsessionSG.getLSessionAsync(req.lsId).await().lsessionDetails ?: run {
                logger.error("stopClass, not found room")
                return@submitTaskAsync call.respond(HttpStatusCode.NotFound, "Not found room")
            }

            if (req.callingUserId != lsessionProto.creatorId) {
                logger.error("stopClass, calling user {} is not owner", req.callingUserId)
                return@submitTaskAsync call.respond(HttpStatusCode.Forbidden, "Permission denied")
            }

            val state = lsessionProto.state.toPojo()
            if (state.status == LSessionStatus.ENDED) {
                logger.info("stopClass, room {} is ended already", req.lsId)
                return@submitTaskAsync call.respond(HttpStatusCode.OK, "class stopped already")
            }

            // update lsession status
            try {
                lsessionSG.updateSessionStatusAsync(lsessionProto.id, LSessionStatus.ENDED).await()
            } catch (t: Throwable) {
                logger.error("stopClass, exception update lsession status...", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Failed to update session status: ${t.message}")
            }

            actorManager.getClassroomChecker(lsessionProto.id)?.shutdownRoom()

            // create and save notification
            val notiData = StopClassND(lsessionProto.id, req.callingUserId)
            val notification = Notification(
                req.callingUserId,
                ClassroomTarget(lsessionProto.id),
                "LSession ${lsessionProto.id} is stopped",
                Instant.now().plusSeconds(300),
                notiData
            )
            notificationSG.saveNotificationAsync(notification).await()

            call.respond(HttpStatusCode.OK)
        }.join()
    }


    suspend fun leaveClass(call: ApplicationCall) {
        val req = call.receive<LeaveClassRequest>()

        val peer = peerManager.getPeer(req.callingPeerId) ?: return call.respond(HttpStatusCode.OK)

        if (peer.info.status != PeerStatus.ACTIVE) {
            logger.error("leaveClass, peer {} not active", req.callingPeerId)
            return call.respond(HttpStatusCode.OK)
        }

        val actor = actorManager.getActor(req.lsId, peer.info.userId) ?: run {
            logger.error("leaveClass, not found actor user {}, room {}", peer.info.userId, req.lsId)
            return call.respond(HttpStatusCode.OK)
        }

        actor.send(ActorTaskType.LeaveClass) {
            doLeaveClass(actor)
        }.join()

        call.respond(HttpStatusCode.OK)
    }

    private suspend fun doLeaveClass(actor: UserClassroomActor) {
        userAvailableStatusHandler.process(actor, UserAvailableStatus.OFFLINE)
        val notiData = LeaveClassND(actor.roomId, actor.userId)
        val notification = Notification(
            actor.userId,
            ClassroomTarget(actor.roomId),
            "${actor.userProto.username} vừa rời khỏi buổi học",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()
    }

    suspend fun newQuestion(call: ApplicationCall) {
        val req = call.receive<NewQuestionRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return@submitTaskAsync

            try {
                actor.send(ActorTaskType.NewQuestion) { doNewQuestion(actor, req, call) }.join()
            } catch (t: Throwable) {
                logger.error("newQuestion, exception stop question... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Failed to send task to actor: ${t.message}")
            }
        }.join()
    }

    private suspend fun doNewQuestion(actor: UserClassroomActor, req: NewQuestionRequest, call: ApplicationCall) {
        val lsessionProto = try {
            lsessionSG.getLSessionAsync(actor.roomId).await().lsessionDetails
        } catch (t: Throwable){
            logger.error("doNewQuestion, not found room {}", actor.roomId)
            return call.respond(HttpStatusCode.InternalServerError, "not found lsession")
        }

        if (lsessionProto.creatorId != actor.userId) {
            return call.respond(HttpStatusCode.Forbidden, "Permission denied")
        }

        val state = lsessionProto.state.toPojo()
        if (state.status != LSessionStatus.STARTED) {
            logger.error("doNewQuestion, room {} not started", req.lsId)
            return call.respond(HttpStatusCode.Forbidden, ErrorResponse(state.status.toResponseError().name, "This session is not started yet"))
        }

        val classroomActivity = ClassroomActivity(actor.roomId, actor.userId, QuickQuestionAD(req.question))
        val saveRes = try {
            classroomSG.saveClassroomActivityAsync(classroomActivity).await()
        } catch (t: Throwable) {
            logger.error("doNewQuestion, exception save classroom activity... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Failed to create new question activity: ${t.message}")
        }
        val res = lsessionSG.cancelAllRaisingHand(actor.roomId).await()
        logger.info("doNewQuestion, cancelAllRaisingHand response {}", res)

        val activityId = saveRes.activityId

        val notiData = NewQuestionND(actor.roomId, activityId, serializeClassroomActivity(classroomActivity))
        val notification = Notification(
            actor.userId,
            ClassroomTarget(actor.roomId),
            "Giáo viên vừa yêu cầu cả lớp tham gia câu hỏi nhanh",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK, mapOf("activityId" to activityId))
    }

    private fun serializeClassroomActivity(activity: ClassroomActivity): String {
        val document = BsonDocument()
        clrActivityCodec.encode(BsonDocumentWriter(document), activity, EncoderContext.builder().build())

        // replace _id by id
        val id = document.getObjectId("_id")
        document.remove("_id")
        document["id"] = id

        // replace time in ISO 8601 spec (e.g 2021-07-10T16:01:18.227Z) by long
        val createdTime = document.getDateTime(ClassroomActivity::createdTime.name)
        document[ClassroomActivity::createdTime.name] = BsonInt64(createdTime.value)

        return document.toJson(JsonWriterSettings.builder().objectIdConverter(ObjectIdConverter()).build())
    }

    suspend fun stopQuestion(call: ApplicationCall) {
        val req = call.receive<StopQuestionRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.lsId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return@submitTaskAsync

            try {
                actor.send(ActorTaskType.StopQuestion) { doStopQuestion(actor, req, call) }.join()
            } catch (t: Throwable) {
                logger.error("stopQuestion, exception when stop question... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Failed to send task to actor: ${t.message}")
            }
        }.join()
    }

    private suspend fun doStopQuestion(actor: UserClassroomActor, req: StopQuestionRequest, call: ApplicationCall) {
        val lsessionProto = getLsessionIfExist(logger, call, lsessionSG, req.lsId) ?: return

        if (lsessionProto.creatorId != actor.userId) {
            logger.error("doStopQuestion, calling user {} is not owner of room {}", actor.userId, req.lsId)
            return call.respond(HttpStatusCode.Forbidden, "Permission denied")
        }

        try {
            classroomSG.updateClassroomActivityStatusAsync(
                req.activityId,
                ActivityStatus.ON_GOING,
                ActivityStatus.FINISHED
            ).await()
            val res = lsessionSG.cancelAllRaisingHand(req.lsId).await()
            logger.info("doStopQuestion, cancelAllRaisingHand response {}", res)
        } catch (t: Throwable) {
            logger.error("doStopQuestion, exception update classroom activity status... ", t)
            return call.respond(HttpStatusCode.InternalServerError,"Failed to update classroom activity state: ${t.message}")
        }

        val notiData = StopQuestionND(req.lsId, req.activityId)
        val notification = Notification(
            actor.userId,
            ClassroomTarget(req.lsId),
            "Giáo viên vừa kết thúc câu hỏi nhanh",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }

    suspend fun requestPresentation(call: ApplicationCall) {
        val req = call.receive<RequestPresentationRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync

            val (lsessionProto, lsRegProto) = loadUserLSessionAndRegistration(
                req.lsId, req.targetUserId, call
            ) ?: run {
                logger.error("requestPresentation, cannot load lsession and registration")
                return@submitTaskAsync call.respond(HttpStatusCode.NotFound, "Không tìm thấy user")
            }

            if (lsessionProto.creatorId != peer.info.userId) {
                logger.error("requestPresentation, calling request is not owner of the room {}", req.lsId)
                return@submitTaskAsync call.respond(HttpStatusCode.Forbidden, "Bạn không phải giáo viên")
            }

            val state = lsessionProto.state.toPojo()
            if (state.status != LSessionStatus.STARTED) {
                logger.error("requestPresentation, room {} is not started", req.lsId)
                return@submitTaskAsync call.respond(HttpStatusCode.Forbidden, ErrorResponse(state.status.toResponseError().name, "Lớp học chưa bắt đầu"))
            }

            val lsReg = lsRegProto.toPojo()
            if (lsReg.regStatus != LSRegStatus.REGISTERED) {
                logger.error("requestPresentation, user {} is not registered in room {}", req.targetUserId, req.lsId)
                return@submitTaskAsync call.respond(HttpStatusCode.Forbidden, "User chưa có trong lớp học")
            }

            try {
                loadAndCancelLatestOngoingPresentationRequestActivity(req.lsId, peer.info.userId)
            } catch (t: Throwable) {
                logger.error("requestPresentation, exception load and cancel latest ongoing presentation request... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            }

            // create new presentation activity
            val activity = try {
                val activityData = RequestPresentationAD(lsReg.userId)
                val activity = ClassroomActivity(req.lsId, peer.info.userId, activityData)
                classroomSG.saveClassroomActivityAsync(activity).await()
                activity
            } catch (t: Throwable) {
                logger.error("requestPresentation, exception save activity... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hẹ thống")
            }

            // schedule canceling an activity task
            activityScheduler.scheduleCancelActivityTask(activity.id, {
                // init and save notification
                val notiData = CancelPresentationRequestND(lsReg.lsId, req.targetUserId, activity.id)
                val notification = Notification(
                    peer.info.userId,
                    GroupTarget(listOf(lsReg.userId, req.targetUserId, peer.info.userId)),
                    "Yêu cầu phát biểu đã hết hạn",
                    Instant.now().plusSeconds(300),
                    notiData,
                    false
                )
                notificationSG.saveNotificationAsync(notification).await()
            })

            val notiData =
                RequestPresentationND(req.lsId, req.targetUserId, activity.id, serializeClassroomActivity(activity))
            val notification = Notification(
                peer.info.userId,
                GroupTarget(listOf(req.targetUserId, peer.info.userId)),
                "Giáo viên vừa yêu cầu bạn phát biểu",
                Instant.now().plusSeconds(300),
                notiData
            )
            notificationSG.saveNotificationAsync(notification).await()

            call.respond(HttpStatusCode.OK, mapOf("activityId" to activity.id))
        }.join()
    }

    suspend fun cancelRequestPresentation(call: ApplicationCall) {
        val req = call.receive<CancelRequestPresentationRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return@submitTaskAsync
            val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, req.lsId) ?: return@submitTaskAsync

            if (roomInfo.owner != peer.info.userId) {
                return@submitTaskAsync call.respond(HttpStatusCode.Forbidden, "Bạn không phải giáo viên")
            }

            try {
                classroomSG.updateClassroomActivityStatusAsync(
                    req.activityId,
                    ActivityStatus.ON_GOING,
                    ActivityStatus.CANCELLED
                ).await()
            } catch (t: Throwable) {
                logger.error("Failed to cancel activity... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            }

            // cancel scheduled task
            activityScheduler.cancelScheduledTask(req.activityId)

            // init and save notification
            val notiData = CancelPresentationRequestND(roomInfo.id, req.targetUserId, req.activityId)
            val notification = Notification(
                roomInfo.owner,
                GroupTarget(listOf(req.targetUserId, roomInfo.owner)),
                "Giáo viên vừa hủy yêu cầu phát biểu",
                Instant.now().plusSeconds(300),
                notiData,
                true
            )
            notificationSG.saveNotificationAsync(notification).await()

            call.respond(HttpStatusCode.OK)
        }.join()
    }

    private suspend fun loadAndCancelLatestOngoingPresentationRequestActivity(lsId: String, ownerId: String) {
        val latestActivity = classroomSG.loadLatestOngoingPresentationActivityAsync(lsId).await() ?: return

        classroomSG.updateClassroomActivityStatusAsync(
            latestActivity.id,
            ActivityStatus.ON_GOING,
            ActivityStatus.CANCELLED
        ).await()

        // cancel scheduled task
        activityScheduler.cancelScheduledTask(latestActivity.id)

        val activityData = latestActivity.data as RequestPresentationAD

        // init and save notification
        val notiData = CancelPresentationRequestND(lsId, activityData.requestedTo, latestActivity.id)
        val notification = Notification(
            latestActivity.createdBy,
            GroupTarget(listOf(activityData.requestedTo, ownerId)),
            "Presentation request ${latestActivity.id} is cancelled",
            Instant.now().plusSeconds(300),
            notiData,
            false
        )
        notificationSG.saveNotificationAsync(notification).await()
    }

    private suspend fun loadUserLSessionAndRegistration(
        lsId: String, targetUserId: String, call: ApplicationCall
    ): Pair<LsessionMessage.LSessionDetailsProto, LsessionMessage.LSessionRegistrationProto>? {
        // query in async
        val lsessionDeferred = lsessionSG.getLSessionAsync(lsId)
        val lsRegistrationDeferred = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(lsId, targetUserId)

        val lsession = try {
            lsessionDeferred.await().lsessionDetails
        } catch (t: Throwable) {
            call.respond(HttpStatusCode.InternalServerError,"Failed to load lsession: ${t.message}")
            return null
        }

        val lsRegistration = try {
            lsRegistrationDeferred.await().registration
        } catch (t: Throwable) {
            call.respond(HttpStatusCode.InternalServerError,"Failed to load lsession registration: ${t.message}")
            return null
        }

        return Pair(lsession, lsRegistration)
    }

    suspend fun acceptRequestPresentation(call: ApplicationCall) {
        val req = call.receive<AcceptRequestPresentationRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return@submitTaskAsync

            try {
                actor.send(ActorTaskType.AcceptPresentation) { doAcceptPresentation(req, call) }.join()
            } catch (t: Throwable) {
                logger.error("acceptRequestPresentation, exception when accept presentation... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Failed to send task to actor: ${t.message}")
            }
        }.join()
    }

    private suspend fun doAcceptPresentation(req: AcceptRequestPresentationRequest, call: ApplicationCall) {
        val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, req.lsId) ?: return
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, req.lsId, peer.info.userId) ?: return

        if (roomInfo.presentingUser == actor.userId) {
            return call.respond(HttpStatusCode.OK)
        }

        loadAndStopCurrentPresentation(roomInfo, peer) ?: return

        val switchPresentTo = if (roomInfo.presentingCoordState == roomInfo.defaultCoordState ||
            roomInfo.pinnedCoordStates.contains(roomInfo.presentingCoordState)
        ) roomInfo.presentingCoordState else roomInfo.defaultCoordState

        updatePresentingStateToRoomInfoAndSyncer(roomInfo, peer.info, switchPresentTo, call) ?: return

        try {
            lsessionSG.updateRaiseHandStatusAsync(lsReg.id, RaiseHandStatus.PRESENTING).await()
            classroomSG.updateRequestPresentationResponseAsync(req.activityId, actor.userId, YesNoResponse.ACCEPTED).await()

            // cancel a scheduled task
            activityScheduler.cancelScheduledTask(req.activityId)
        } catch (t: Throwable) {
            logger.error("doAcceptPresentation, exception when upadte user activity status... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Failed to update classroom user state: ${t.message}")
        }

        // create notification
        val notiData = AcceptPresentationRequestND(lsReg.lsId, peer.info.userId, req.activityId)
        val notification = Notification(
            peer.info.userId,
            ClassroomTarget(lsReg.lsId),
            "Học viên ${actor.userProto.username} đang phát biểu",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        val notiJson = try {
            serializeNotificationToJsonNode(notification, notificationCodec)
        } catch (t: Throwable) {
            logger.error("doAcceptPresentation, Failed to serialize notification to json node: ", t)
            null
        }

        val msgData = json.objectNode()
            .put("roomId", roomInfo.id)
            .put("presentingUserId", peer.info.userId)
            .put("presentingPeerId", peer.info.id)
            .put("presentingCoordStateId", switchPresentTo)
            .putPOJO("notiData", notiJson)
        val msgBuilder: (Peer) -> SignalMessage = {
            SignalMessage("", it.info.id, SignalType.AcceptPresentationRequestND, msgData, null)
        }

        // send message to all peers
        peerManager.filterAndSendAsync(msgBuilder) {
            it.info.roomId == roomInfo.id
                    && it.info.peerType == PeerType.BROWSER
                    && it.info.status == PeerStatus.ACTIVE
        }

        call.respond(HttpStatusCode.OK)
    }

    private suspend fun updatePresentingStateToRoomInfoAndSyncer(
        classroomInfo: ClassroomInfo,
        peer: PeerInfo,
        switchPresentTo: String?,
        call: ApplicationCall
    ): Boolean? {
        try {
            val aliveSyncer = databaseService.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
            aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

            val syncerPeer = aliveSyncer.find { pInfo -> peerManager.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
            if (syncerPeer == null) {
                logger.error("updatePresentingStateToRoomInfoAndSyncer, not found alive peer for {}", classroomInfo.id)
                call.respond(HttpStatusCode.ServiceUnavailable,"Failed to find an alive syncer")
                return null
            }

            databaseService.updatePresenting(classroomInfo.id, peer.userId, peer.id)
            if (classroomInfo.presentingCoordState != switchPresentTo) {
                databaseService.updatePresentingCoordinatorState(classroomInfo.id, switchPresentTo)
                classroomInfo.presentingCoordState = switchPresentTo
            }
            classroomInfo.presentingUser = peer.userId
            classroomInfo.presentingPeer = peer.id

            // request message to syncer
            val data = json.objectNode()
                .put("roomId", classroomInfo.id)
                .put("presentingPeerId", peer.id)
                .put("presentingCoordStateId", switchPresentTo)
            val signalMessage =
                SignalMessage("", syncerPeer.id, SignalType.roomUpdate, data, System.currentTimeMillis().toString())

            logger.debug("updatePresentingStateToRoomInfoAndSyncer, requesting update room {}", signalMessage)
            val message: SignalMessage = peerManager.request(syncerPeer.id, signalMessage)
            logger.info("updatePresentingStateToRoomInfoAndSyncer, update syncing peer message {}", message)

            if (message.data["status"].isNull || message.data["status"].intValue() > 0) {
                call.respond(HttpStatusCode.BadRequest, message.data["message"])
                return null
            }

            return true
        } catch (e: PeerException) {
            call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "Failed to update presenting state")
            return null
        } catch (t: Throwable) {
            logger.error("updatePresentingStateToRoomInfoAndSyncer, room {} exception updateRoomInfoAndSyncer... ", classroomInfo, t)
            return null
        }
    }

    private suspend fun loadAndStopCurrentPresentation(classroomInfo: ClassroomInfo, callingPeer: Peer): Boolean? {
        try {
            val actor = actorManager.getActor(classroomInfo.id, classroomInfo.presentingUser)
            if (actor != null) {
                try {
                    val message = SignalMessage(
                        callingPeer.info.id,
                        actor.peer.info.id,
                        SignalType.StopPresentationND,
                        json.objectNode(),
                        UUID.randomUUID().toString(),
                    )
                    peerManager.send(actor.peer.info.id, message, 3000)
                } catch (e: PeerException) {
                    logger.error("loadAndStopCurrentPresentation, Cannot send message to presenting peer: {}", e.message);
                }
            }

            val lsReg = actor?.lsRegistration ?:
            lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(classroomInfo.id, classroomInfo.presentingUser)
                .await().registration.toPojo()

            lsessionSG.updateRaiseHandStatusAsync(lsReg.id, RaiseHandStatus.NONE).await()
            logger.info("loadAndStopCurrentPresentation, stopped presentation user {} - registration {}", classroomInfo.presentingUser, lsReg.id)

            return true
        } catch (t: Throwable) {
            logger.error("loadAndStopCurrentPresentation, room {} exception when loadAndStopCurrentPresentation...", classroomInfo.id, t)
            return null
        }
    }

    suspend fun rejectPresentation(call: ApplicationCall) {
        val req = call.receive<RejectRequestPresentationRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return@submitTaskAsync
            val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, req.lsId, peer.info.userId) ?: return@submitTaskAsync

            try {
                classroomSG.updateRequestPresentationResponseAsync(req.activityId, actor.userId, YesNoResponse.REJECTED)
                    .await()

                // cancel a scheduled task
                activityScheduler.cancelScheduledTask(req.activityId)
            } catch (t: Throwable) {
                logger.error("rejectPresentation, exception update request presentation... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            }

            // init and save notification
            val notiData = RejectPresentationRequestND(lsReg.lsId, actor.userId, req.activityId)
            val notification = Notification(
                actor.userId,
                GroupTarget(listOf(actor.userId, lsReg.lsOwnerUserId)),
                "Học viên ${actor.userProto.username} vừa từ chối yêu cầu phát biểu",
                Instant.now().plusSeconds(300),
                notiData
            )
            notificationSG.saveNotificationAsync(notification, true).await()

            call.respond(HttpStatusCode.OK)
        }.join()
    }

    suspend fun stopPresentation(call: ApplicationCall) {
        val req = call.receive<StopPresentationRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return@submitTaskAsync

            try {
                actor.send(ActorTaskType.StopPresentation) { doStopPresentation(peer, req, call) }.join()
            } catch (t: Throwable) {
                logger.error("stopPresentation, exception stop presenting... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            }
        }.join()
    }

    private suspend fun doStopPresentation(callingPeer: Peer, req: StopPresentationRequest, call: ApplicationCall) {
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, req.lsId) ?: return

        val ownerPeer = if (callingPeer.info.userId == roomInfo.owner) callingPeer
        else getActorIfExist(logger, call, actorManager, req.lsId, roomInfo.owner)?.peer ?: return
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, req.lsId, req.targetUserId) ?: return

        if (lsReg.userState.raiseHandStatus != RaiseHandStatus.PRESENTING) {
            logger.error("doStopPresentation, user {} is not presenting room {}", lsReg.userId, lsReg.lsId)
            return call.respond(HttpStatusCode.Forbidden, "User đang không thuyết trình")
        }

        val userProfile = getUserProfileIfExist(logger, call, userSG, req.targetUserId) ?: return

        if (roomInfo.owner != callingPeer.info.userId && callingPeer.info.userId != roomInfo.presentingUser) {
            logger.error("doStopPresentation, this user {} is not allow stopping presenting", callingPeer.info.userId)
            return call.respond(HttpStatusCode.Forbidden, "Bạn không có quyền dừng phát biểu")
        }

        val stopPeer = peerManager.getPeer(roomInfo.presentingPeer!!)
        if (stopPeer != null) {
            val message = SignalMessage(
                callingPeer.info.id, stopPeer.info.id, SignalType.StopPresentationND, json.objectNode(),
                System.currentTimeMillis().toString()
            )
            // send a request to the presenting peer (waiting for reply from the presenting peer) to inform the presenting peer about synchronization is stopped
            try {
                peerManager.request(stopPeer.info.id, message, 3000, 3000)
            } catch (e: PeerException) {
                logger.warn(
                    "doStopPresentation, ignore waiting presenter reply user {}, room {}, message: {}",
                    roomInfo.presentingUser,
                    roomInfo.id,
                    e.message
                )
            } catch (t: Throwable) {
                logger.error("doStopPresentation, unknown error...", t)
            }
        }

        val switchPresentTo = if (roomInfo.presentingCoordState == roomInfo.defaultCoordState ||
            roomInfo.pinnedCoordStates.contains(roomInfo.presentingCoordState)
        ) roomInfo.presentingCoordState else roomInfo.defaultCoordState

        updatePresentingStateToRoomInfoAndSyncer(roomInfo, ownerPeer.info, switchPresentTo, call) ?: return

        try {
            lsessionSG.updateRaiseHandStatusAsync(lsReg.id, RaiseHandStatus.NONE).await()
            val ownerRegistration =
                lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(roomInfo.id, roomInfo.owner).await()
            if (ownerRegistration.hasRegistration()) {
                lsessionSG.updateRaiseHandStatusAsync(
                    ownerRegistration.regId,
                    RaiseHandStatus.PRESENTING
                ).await()
            }
        } catch (t: Throwable) {
            logger.error("doStopPresentation, exception stop presentation for user {}, room {}", lsReg.id, lsReg.lsId)
            return call.respond(HttpStatusCode.InternalServerError, "Failed to stop presentation for user: ${t.message}")
        }

        // create notification
        val notiData = StopPresentationND(req.lsId, req.targetUserId)
        val notification = Notification(
            callingPeer.info.userId,
            ClassroomTarget(req.lsId),
            "Học viên ${userProfile.username} vừa kết thúc phát biểu",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        val notiJson = try {
            serializeNotificationToJsonNode(notification, notificationCodec)
        } catch (e: Throwable) {
            logger.error("doStopPresentation, Failed to serialize notification to json node: ", e)
            null
        }

        val msgData = json.objectNode()
            .put("roomId", roomInfo.id)
            .put("presentingUserId", ownerPeer.info.userId)
            .put("presentingPeerId", ownerPeer.info.id)
            .put("presentingCoordStateId", switchPresentTo)
            .putPOJO("notiData", notiJson)
        val msgBuilder: (Peer) -> SignalMessage = {
            SignalMessage("", it.info.id, SignalType.StopPresentationND, msgData, null)
        }

        // send message to all peers
        peerManager.filterAndSendAsync(msgBuilder) {
            it.info.roomId == roomInfo.id
                    && it.info.peerType == PeerType.BROWSER
                    && it.info.status == PeerStatus.ACTIVE
        }

        call.respond(HttpStatusCode.OK)
    }

    suspend fun raiseHand(call: ApplicationCall) {
        val req = call.receive<RaiseHandRequest>()

        val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return

        try {
            actor.send(ActorTaskType.RaiseHand) { doRaiseHand(actor, call) }.join()
        } catch (t: Throwable) {
            logger.error("raiseHand, exception rasing hand...", t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }
    }

    suspend fun reqShareScreen(call: ApplicationCall) {
        val req = call.receive<ReqShareScreenRequest>()

        val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return

        try {
            actor.send(ActorTaskType.ReqShareScreen) { doReqShareScreen(actor, call) }.join()
        } catch (t: Throwable) {
            logger.error("reqShareScreen, exception request share screen...", t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }
    }

    private suspend fun doRaiseHand(actor: UserClassroomActor, call: ApplicationCall) {
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, actor.roomId, actor.userId) ?: return

        try {
            lsessionSG.updateRaiseHandStatusAsync(lsReg.id, RaiseHandStatus.RAISE_HAND).await()
        } catch (t: Throwable) {
            logger.error("doRaiseHand, exception update user activity status... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Failed to update classroom user state: ${t.message}")
        }

        // init and save notification
        val notiData = RaiseHandND(actor.roomId, actor.userId)
        val notification = Notification(
            actor.userId,
            ClassroomTarget(actor.roomId),
            "Học viên ${actor.userProto.username} vừa giơ tay phát biểu",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }

    private suspend fun doReqShareScreen(actor: UserClassroomActor, call: ApplicationCall) {
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, actor.roomId, actor.userId) ?: return

        if (lsReg.userState.shareScreenStatus != ShareScreenStatus.NONE)
            return call.respond(HttpStatusCode.InternalServerError, "The user's current screen share status is incorrect.")
        try {
            lsessionSG.updateShareScreenStatusAsync(lsReg.id, ShareScreenStatus.REQ_SHARE_SCREEN).await()
        } catch (t: Throwable) {
            logger.error("doReqShareScreen, exception update user share screen status... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }

        // init and save notification
        val notiData = ReqShareScreenND(actor.roomId, actor.userId)
        val notification = Notification(
            actor.userId,
            ClassroomTarget(actor.roomId),
            "Học viên ${actor.userProto.username} vừa đăng ký chia sẻ màn hình",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }

    suspend fun cancelRaiseHand(call: ApplicationCall) {
        val req = call.receive<CancelRaiseHandRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return@submitTaskAsync

            try {
                actor.send(ActorTaskType.CancelRaiseHand) { doCancelRaiseHand(actor, call) }.join()
            } catch (t: Throwable) {
                logger.error("cancelRaiseHand, exception doCancelRaiseHand... ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            }
        }.join()
    }

    suspend fun cancelShareScreen(call: ApplicationCall) {
        val req = call.receive<CancelShareScreenRequest>()

        val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return

        try {
            actor.send(ActorTaskType.CancelShareScreen) { doCancelShareScreen(actor, call) }.join()
        } catch (t: Throwable) {
            logger.error("cancelRaiseHand, exception doCancelRaiseHand... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }
    }

    private suspend fun doCancelShareScreen(
        actor: UserClassroomActor,
        call: ApplicationCall
    ) {
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, actor.roomId, actor.userId) ?: return

        try {
            lsessionSG.updateShareScreenStatusAsync(lsReg.id, ShareScreenStatus.NONE).await()
        } catch (t: Throwable) {
            logger.error("doCancelShareScreen, exception update user share screen status... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }

        // init and save notification
        val notiData = CancelShareScreenND(actor.roomId, actor.userId)
        val notification = Notification(
            actor.userId,
            ClassroomTarget(actor.roomId),
            "Học viên ${actor.userProto.username} vừa bỏ tay xuống",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }

    private suspend fun doCancelRaiseHand(actor: UserClassroomActor, call: ApplicationCall) {
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, actor.roomId) ?: return
        if (roomInfo.presentingUser == actor.userId) {
            logger.error("doCancelRaiseHand, user {} is presenting", actor.userId)
            return call.respond(HttpStatusCode.OK, "Bạn đang thuyết trình")
        }
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, actor.roomId, actor.userId) ?: return
        try {
            lsessionSG.updateRaiseHandStatusAsync(lsReg.id, RaiseHandStatus.NONE).await()
        } catch (t: Throwable) {
            logger.error("doCancelRaiseHand, exception update user {} activity status {}", actor.userId, RaiseHandStatus.NONE)
            return call.respond(HttpStatusCode.InternalServerError,"Lỗi hệ thống")
        }

        // init and save notification
        val notiData = CancelRaiseHandND(actor.roomId, actor.userId)
        val notification = Notification(
            actor.userId,
            ClassroomTarget(actor.roomId),
            "Học viên ${actor.userProto.username} vừa bỏ tay xuống",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }

    suspend fun acceptRaiseHand(call: ApplicationCall) {
        val req = call.receive<AcceptRaiseHandRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, req.targetUserId) ?: return@submitTaskAsync

            try {
                actor.send(ActorTaskType.AcceptRaiseHand) { doAcceptRaiseHand(peer, req, call) }.join()
            } catch (t: Throwable) {
                logger.error("acceptRaiseHand, Failed to do accept raising hand for user {} ... ", req.targetUserId, t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Failed to send task to actor: ${t.message}")
            }
        }.join()
    }

    suspend fun acceptShareScreen(call: ApplicationCall) {
        val req = call.receive<AcceptShareScreenRequest>()

        val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, req.lsId, peer.info.userId) ?: return

        try {
            actor.send(ActorTaskType.AcceptShareScreen) { doAcceptShareScreen(peer, req, call) }.join()
        } catch (t: Throwable) {
            logger.error(
                "acceptShareScreen, Failed to do accept raising hand for user {} ... ",
                req.targetUserId,
                t
            )
            return call.respond(HttpStatusCode.InternalServerError, "Failed to send task to actor: ${t.message}")
        }
    }

    private suspend fun doAcceptRaiseHand(callingPeer: Peer, req: AcceptRaiseHandRequest, call: ApplicationCall) {
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, req.lsId) ?: return
        val targetActor = getActorIfExist(logger, call, actorManager, req.lsId, req.targetUserId) ?: return
        val userProfile = getUserProfileIfExist(logger, call, userSG, req.targetUserId) ?: return
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, req.lsId, req.targetUserId) ?: return

        if (roomInfo.presentingUser == req.targetUserId) {
            logger.error("doAcceptRaiseHand, user {} is presenting", req.targetUserId)
            return call.respond(HttpStatusCode.OK, "User đang thuyết trình")
        }

        loadAndStopCurrentPresentation(roomInfo, callingPeer) ?: return

        val switchPresentTo = if (roomInfo.presentingCoordState == roomInfo.defaultCoordState ||
            roomInfo.pinnedCoordStates.contains(roomInfo.presentingCoordState)
        ) roomInfo.presentingCoordState else roomInfo.defaultCoordState

        updatePresentingStateToRoomInfoAndSyncer(roomInfo, targetActor.peer.info, switchPresentTo, call) ?: return

        try {
            lsessionSG.updateRaiseHandStatusAsync(lsReg.id, RaiseHandStatus.PRESENTING).await()
        } catch (t: Throwable) {
            logger.error("doAcceptRaiseHand, Failed to update user {} activity status {} ... ", req.targetUserId, RaiseHandStatus.PRESENTING, t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }

        // create notification
        val notiData = AcceptRaiseHandND(lsReg.lsId, req.targetUserId)
        val notification = Notification(
            callingPeer.info.userId,
            ClassroomTarget(lsReg.lsId),
            "Học viên ${userProfile.username} đang phát biểu",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        val notiJson = try {
            serializeNotificationToJsonNode(notification, notificationCodec)
        } catch (t: Throwable) {
            logger.error("doAcceptRaiseHand, Failed to serialize notification to json node... ", t)
            null
        }

        val msgData = json.objectNode()
            .put("roomId", roomInfo.id)
            .put("presentingUserId", targetActor.peer.info.userId)
            .put("presentingPeerId", targetActor.peer.info.id)
            .put("presentingCoordStateId", switchPresentTo)
            .putPOJO("notiData", notiJson)
        val msgBuilder: (Peer) -> SignalMessage = {
            SignalMessage("", it.info.id, SignalType.AcceptRaiseHandND, msgData, null)
        }

        // send message to all peers
        peerManager.filterAndSendAsync(msgBuilder) {
            it.info.roomId == roomInfo.id && it.info.peerType == PeerType.BROWSER && it.info.status == PeerStatus.ACTIVE
        }

        call.respond(HttpStatusCode.OK)
    }

    private suspend fun doAcceptShareScreen(callingPeer: Peer, req: AcceptShareScreenRequest, call: ApplicationCall) {
        val userProfile = getUserProfileIfExist(logger, call, userSG, req.targetUserId) ?: return
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, req.lsId, req.targetUserId) ?: return

        if (lsReg.userState.shareScreenStatus != ShareScreenStatus.REQ_SHARE_SCREEN)
            if (lsReg.lsOwnerUserId != req.targetUserId)
                return call.respond(HttpStatusCode.InternalServerError, "The user did not request to share the screen")


        try {
            lsessionSG.updateShareScreenStatusAsync(lsReg.id, ShareScreenStatus.SHARE_SCREEN).await()
        } catch (t: Throwable) {
            logger.error("doAcceptShareScreen, exception update user share screen status... ", t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                "Failed to update classroom user state: ${t.message}"
            )
        }

        val notiData = AcceptShareScreenND(lsReg.lsId, req.targetUserId)
        val notification = Notification(
            callingPeer.info.userId,
            ClassroomTarget(lsReg.lsId),
            "Học viên ${userProfile.username} đang chia sẻ màn hình",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }

    suspend fun rejectRaiseHand(call: ApplicationCall) {
        val req = call.receive<RejectRaiseHandRequest>()

        classroomProcessorManager.getOrCreateProcessor(req.lsId).submitTaskAsync {
            val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return@submitTaskAsync
            val actor = getActorIfExist(logger, call, actorManager, req.lsId, req.targetUserId) ?: return@submitTaskAsync

            try {
                actor.send(ActorTaskType.RejectRaiseHand) { doRejectRaiseHand(req, peer, call) }.join()
            } catch (t: Throwable) {
                logger.error("rejectRaiseHand, Exception when reject raising hand, req {} ... ", req, t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            }
        }.join()
    }

    suspend fun rejectShareScreen(call: ApplicationCall) {
        val req = call.receive<RejectShareScreenRequest>()

        val peer = getPeerIfExist(logger, call, peerManager, req.callingPeerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, req.lsId, req.targetUserId) ?: return

        try {
            actor.send(ActorTaskType.RejectShareScreen) { doRejectShareScreen(req, peer, call) }.join()
        } catch (t: Throwable) {
            logger.error("rejectShareScreen, Exception when reject raising hand, req {} ... ", req, t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }
    }

    private suspend fun doRejectShareScreen(req: RejectShareScreenRequest, callingPeer: Peer, call: ApplicationCall) {
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, req.lsId, req.targetUserId) ?: return

        if (lsReg.userState.shareScreenStatus != ShareScreenStatus.REQ_SHARE_SCREEN)
            if (lsReg.lsOwnerUserId != req.targetUserId)
                return call.respond(HttpStatusCode.InternalServerError, "The user did not request to share the screen")

        try {
            lsessionSG.updateShareScreenStatusAsync(lsReg.id, ShareScreenStatus.NONE).await()
        } catch (t: Throwable) {
            logger.error("rejectShareScreen, exception update user share screen status... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }

        // init and save notification
        val notiData = RejectShareScreenND(req.lsId, req.targetUserId)
        val notification = Notification(
            callingPeer.info.userId,
            ClassroomTarget(req.lsId),
            "Giáo viên vừa từ chối yêu cầu share screen của bạn",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }


    private suspend fun doRejectRaiseHand(req: RejectRaiseHandRequest, callingPeer: Peer, call: ApplicationCall) {
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, req.lsId) ?: return
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, req.lsId, req.targetUserId) ?: return

        if (roomInfo.presentingUser == req.targetUserId) {
            return call.respond(HttpStatusCode.OK, "User đang thuyết trình")
        }

        try {
            lsessionSG.updateRaiseHandStatusAsync(lsReg.id, RaiseHandStatus.NONE).await()
        } catch (t: Throwable) {
            logger.error("doRejectRaiseHand, Failed to update classroom user state {} ... ", req, t)
            return call.respond(HttpStatusCode.InternalServerError,"Lỗi hệ thống")
        }

        // init and save notification
        val notiData = RejectRaiseHandND(req.lsId, req.targetUserId)
        val notification = Notification(
            callingPeer.info.userId,
            ClassroomTarget(req.lsId),
            "Giáo viên vừa từ chối yêu cầu phát biểu của bạn",
            Instant.now().plusSeconds(300),
            notiData
        )
        notificationSG.saveNotificationAsync(notification).await()

        call.respond(HttpStatusCode.OK)
    }
}

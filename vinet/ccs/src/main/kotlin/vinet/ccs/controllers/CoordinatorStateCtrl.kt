package vinet.ccs.controllers

import CmdProto.Meta
import CoordinatorStateCmds.CoordinatorState.*
import common.libs.logger.Logging
import io.grpc.Status
import io.grpc.StatusException
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.bson.BsonType
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.datastructures.lsession.ShareScreenStatus
import portal.lsession.pojo.RequestPinTabStatus
import portal.lsession.pojo.toPojo
import portal.notification.pojo.ClassroomTarget
import portal.notification.pojo.Notification
import portal.notification.pojo.notidata.*
import vinet.ccs.actor.ActorManager
import vinet.ccs.actor.ActorTaskType
import vinet.ccs.actor.UserClassroomActor
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.cache.CoordinatorStateCache
import vinet.ccs.cache.DocGlobalIdCache
import vinet.ccs.cache.DocGlobalIdKey
import vinet.ccs.constant.COORD_CHANNEL_CODE
import vinet.ccs.constant.SHARE_SCREEN_CHANNEL_CODE
import vinet.ccs.db.DatabaseService
import vinet.ccs.exception.PeerException
import vinet.ccs.gateways.*
import vinet.ccs.koin.SUGGESTION_NAMES
import vinet.ccs.metatadata.model.CLASSROOM_METADATA_DOC_TYPE
import vinet.ccs.model.*
import vinet.ccs.peer.Peer
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.CoordinatorState
import vinet.ccs.pojo.PeerStatus
import vinet.ccs.pojo.PeerType
import vinet.ccs.processor.ClassroomProcessorManager
import vinet.ccs.utility.*
import java.time.Instant

/**
 *
 * <AUTHOR>
 */
@Singleton
class CoordinatorStateCtrl constructor(
    private val db: DatabaseService,
    private val peerMan: PeerManager,
    private val coordStateCache: CoordinatorStateCache,
    private val classroomInfoCache: ClassroomInfoCache,
    private val docGlobalIdCache: DocGlobalIdCache,
    @Named(SUGGESTION_NAMES) private val suggestionNames: List<String>,
    private val editorBackendServiceGateway: EditorBackendServiceGateway,
    private val metadataServiceGateway: MetadataServiceGateway,
    private val userSG: UserServiceGateway,
    private val lsessionSG: LSessionServiceGateway,
    private val notificationSG: NotificationServiceGateway,
    private val actorManager: ActorManager,
    private val classroomProcessorManager: ClassroomProcessorManager,
) : Logging {

    suspend fun createCoordinatorState(call: ApplicationCall) {
        val request = try {
            call.receive<CreateCoordinatorStateRequest>()
        } catch (e: Throwable) {
            return call.respond(HttpStatusCode.BadRequest, "request data invalid")
        }

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return

        try {
            classroomProcessorManager.getOrCreateProcessor(peerInfo.roomId).submitTaskAsync {
                val title = request.title ?: suggestionNames.random()
                val insertedId = db.insertCoordinatorState(CoordinatorState(peerInfo.userId, peerInfo.roomId, title))
                    .insertedId?.asObjectId()?.value?.toHexString()
                    ?: run {
                        logger.error("createCoordinatorState, failed to insert coordinator state {}", request)
                        return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
                    }

                // request msg to syncer
                val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
                aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

                val syncerPeer =
                    aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
                if (syncerPeer == null) {
                    logger.error(
                        "createCoordinatorState, Unable to find a syncer to serve the room with id {}",
                        peerInfo.roomId
                    )
                    return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, "Lỗi hệ thống")
                }

                val data = CreateCoordStateMessage(
                    peerInfo.roomId,
                    SyncerCoordStateResponse(
                        id = insertedId,
                        ownerId = peerInfo.userId,
                        roomId = peerInfo.roomId,
                        version = 1
                    )
                )

                val signalMessage = SignalMessage(
                    request.peerId,
                    syncerPeer.id,
                    SignalType.roomUpdate,
                    json.pojoNode(data),
                    System.currentTimeMillis().toString()
                )

                logger.debug("createCoordinatorState, requesting update room [{}]", signalMessage)
                val message: SignalMessage = peerMan.request(signalMessage.toPeer, signalMessage)
                logger.info("createCoordinatorState, syncer response create coordinator state [{}]", message)

                return@submitTaskAsync call.respond(HttpStatusCode.OK, insertedId)
            }.join()
        } catch (e: PeerException) {
            logger.error("createCoordinatorState, failed to create coordinator state {} ... ", request, e)
            return call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "Lỗi tạo bảng")
        } catch (t: Throwable) {
            logger.error("createCoordinatorState, failed to create coordinator state {} ... ", request, t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi tạo bảng")
        }
    }

    suspend fun deleteCoordinatorState(call: ApplicationCall) {
        val request: DeleteCoordinatorStateRequest = call.receive()

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return

        classroomProcessorManager.getOrCreateProcessor(peerInfo.roomId).submitTaskAsync {
            val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return@submitTaskAsync
            val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return@submitTaskAsync

            if (!validateRemoveCoordState(logger, call, peerInfo, roomInfo, coordState)) return@submitTaskAsync

            logger.debug("deleteCoordinatorState, room info before delete: {}", roomInfo)

            val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, peerInfo.roomId, peerInfo.userId) ?: return@submitTaskAsync
            if (lsReg.userState.requestPinTabState.any { it.tabId == request.coordStateId }) {
                val notiData = CancelRequestPinTabND(peerInfo.roomId, peerInfo.userId, request.coordStateId)
                checkAndUpdateRequestPinTab(
                    peerInfo.lsRegId,
                    roomInfo.id,
                    peerInfo.userId,
                    coordState.id,
                    title = null,
                    RequestPinTabStatus.CANCELLED,
                    notiData
                ) ?: return@submitTaskAsync
            }

            coordState.docMapping.map { (k, v) ->
                Pair(DocGlobalIdKey(coordState.id, k.split("_")[0].toInt(), k.split("_")[1].toInt()), v)
            }.groupBy({ it.first.channelCode }, { it.second }).map { (channelCode, globalIds) ->
                try {
                    logger.info("deleteCoordinatorState, deleting documents {}", globalIds)
                    editorBackendServiceGateway.submitDeleteDocumentsRequest(channelCode, globalIds)
                    logger.info("deleted documents {}", globalIds)
                } catch (t: Throwable) {
                    logger.error("deleteCoordinatorState, failed to proxy request to backend editor... ", t)
                }
            }

            val rm = db.deleteCoordinator(request.coordStateId)
            coordStateCache.invalidate(coordState.id)
            return@submitTaskAsync if (rm.deletedCount > 0) {
                try {
                    // request msg to syncer
                    val aliveSyncer = db.findAliveSynchronizers()
                    aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

                    val syncerPeer = aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }
                    if (syncerPeer == null) {
                        logger.error(
                            "deleteCoordinatorState, Unable to find a syncer to serve the room with id {}",
                            peerInfo.roomId
                        )
                        return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, "Unable to find a syncer")
                    }

                    val data = RemoveCoordStateMessage(peerInfo.roomId, coordState.id)

                    val signalMessage = SignalMessage(
                        request.peerId,
                        syncerPeer.id,
                        SignalType.roomUpdate,
                        json.pojoNode(data),
                        System.currentTimeMillis().toString()
                    )

                    logger.debug("deleteCoordinatorState, requesting update room {}", signalMessage)
                    val message: SignalMessage = peerMan.request(syncerPeer.id, signalMessage)
                    logger.info("deleteCoordinatorState, syncer response remove coordinator state {}", message)

                    logger.info("deleteCoordinatorState, deleted coordinator {}", coordState.id)
                    call.respond(HttpStatusCode.OK, request.coordStateId)
                } catch (e: PeerException) {
                    logger.error("deleteCoordinatorState, failed to delete coordinator {} ... ", request, e)
                    return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "failed to delete coordinator")
                }
            } else {
                logger.error("deleteCoordinatorState, failed to delete coordinator {}", coordState.id)
                call.respond(HttpStatusCode.InternalServerError, "Không thể xóa bảng")
            }
        }.join()
    }

    suspend fun renameCoordinatorState(call: ApplicationCall) {
        val request: RenameCoordinatorStateRequest = try {
            call.receive()
        } catch (e: Exception) {
            logger.warn("renameCoordinatorState, could not parse request: $e")
            return call.respond(HttpStatusCode.BadRequest, "Invalid request data")
        }

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, peerInfo.roomId, peerInfo.userId) ?: return
        val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, peerInfo.roomId, peerInfo.userId) ?: return

        if (!validateRenameCoordState(logger, call, peerInfo, roomInfo, coordState)) return

        val updateResult = db.updateCoordinatorStateTitle(request.coordStateId, request.title)
        if (updateResult.modifiedCount <= 0L) {
            logger.error("renameCoordinatorState, could not rename coordinator state in DB: {}", request)
            call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            return
        }

        coordState.title = request.title

        val data = RenameCoordStateMessage(coordState.id, request.title)
        val msgBuilder: (Peer) -> SignalMessage = { targetPeer ->
            SignalMessage(
                fromPeer = peerInfo.id,
                toPeer = targetPeer.info.id,
                signalType = SignalType.renamedCoordState,
                data = json.pojoNode(data)
            )
        }

        peerMan.filterAndSendAsync(msgBuilder) {
            it.info.roomId == coordState.room &&
                    it.info.userId != peerInfo.userId && // Do not send to self
                    it.info.peerType == PeerType.BROWSER &&
                    it.info.status == PeerStatus.ACTIVE
        }
        logger.info(
            "renameCoordinatorState, coordinator state [{}] has been renamed to '{}'. Notification sent.",
            coordState.id,
            request.title
        )

        call.respond(HttpStatusCode.OK, coordState.id)

        withContext(Dispatchers.Unconfined) {
            launch {
                val reqPinTab = lsReg.userState.requestPinTabState.find { it.tabId == request.coordStateId }
                if (reqPinTab != null && reqPinTab.status == RequestPinTabStatus.PENDING) {
                    reqPinTab.tabName = request.title
                    val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, peerInfo.roomId, peerInfo.userId) ?: return@launch
                    val notiData = UpdateRequestPinTabND(peerInfo.roomId, peerInfo.userId, request.coordStateId, title = request.title)
                    val notiMessage = "Học viên ${actor.userProto.username} vừa đổi tên bảng cá nhân"
                    checkAndUpdateRequestPinTab(
                        lsReg.id,
                        actor.roomId,
                        actor.userId,
                        coordState.id,
                        title = request.title,
                        status = null,
                        notiData,
                        notiMessage,
                        call = null
                    )
                }
            }
        }
    }

    /**
     * This method receives command from the data saver, it calls the editor backend before return the result.
     * Currently, no batching is implemented
     */
    suspend fun receiveCmd(call: ApplicationCall) {
        if (!call.requireParams(listOf("peerId", "coordinatorId"))) return

        val request = ReceiveCmdRequest(call.parameters["peerId"]!!, call.parameters["coordinatorId"]!!)

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordinatorId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

        if (!validateModifyCoordStageContent(logger, call, peerInfo, roomInfo, coordState)) return

        // get meta data
        val data: ByteArray = call.receive()

        val metaLength = data[0].toInt()
        val metaArrBuf = data.sliceArray(IntRange(1, metaLength))
        val metaData: Meta.CmdMetaProto = Meta.CmdMetaProto.parseFrom(metaArrBuf)
        val dataArrBuf = data.copyOfRange(metaLength + 1, data.size)

        if (metaData.channelCode == COORD_CHANNEL_CODE || metaData.channelCode == 7) { // check allowed cmd channel codes in classroom.coordinator.ts
            // check if cmd is for coordinator channel

            // coordinator channel
            when (metaData.cmdType) {
                CmdTypeProto.UPDATE_LAYER_POSITION_VALUE -> {
                    handleUpdateLayerCmd(call, request, dataArrBuf, metaData)
                }

                CmdTypeProto.ADD_LAYER_VALUE,
                CmdTypeProto.ADD_NEW_DOC_MAPPING_VALUE,
                CmdTypeProto.REMAP_DOC_MAPPING_AND_LAYERS_VALUE,
                CmdTypeProto.REMOVE_DOC_MAPPING_AND_LAYERS_VALUE,
                -> {
                    /**
                     * TODO: REMOVE THE CTRL method that allow update coordinator state through http request
                     * handle these commands and update the state accordingly
                     **/
                    logger.warn(
                        "Currently ignore these commands (ADD_LAYER, ADD_NEW_DOC_MAPPING, REMOVE_DOC_MAPPING_AND_LAYERS) " +
                                "because the coordinator state is currently updated through " +
                                "http request to specific controller, not through the rtc commands"
                    )
                    return call.respond((HttpStatusCode.OK))
                }

                CmdTypeProto.SYNC_DOC_SETTINGS_VALUE -> {
                    handleSyncDocSettingsCmd(call, request, dataArrBuf, metaData)
                }

                CmdTypeProto.SYNC_DEFAULT_DOC_SETTING_VALUE -> {
                    handleSyncDefaultDocSettingsCmd(call, request, dataArrBuf, metaData)
                }

                CmdTypeProto.SYNC_PRESENTER_VALUE -> {
                    handleSyncPresenterStateCmd(call, request, dataArrBuf, metaData)
                }
            }
        } else {
            // try to submit cmd to editor backend
            try {
                submitCmdToBackend(call, request, metaData, data)
            } catch (t: Throwable) {
                logger.error("receiveCmd, have error when submit cmd {} to editor backend... ", metaData, t)
                return call.respond(HttpStatusCode.InternalServerError)
            }
        }
    }

    private suspend fun handleUpdateLayerCmd(
        call: ApplicationCall,
        request: ReceiveCmdRequest,
        data: ByteArray,
        metaData: Meta.CmdMetaProto,
    ) {
        try {
            val dataParsed = UpdateLayerPositionProto.parseFrom(data)

            if (!dataParsed.endPoint) {
                // if not end point. do nothing, just update layer position when cmd is end point cmd
                return call.respond(HttpStatusCode.OK)
            }

            val posStart: Position? = if (dataParsed.hasPositionStart()) Position(
                dataParsed.positionStart.x,
                dataParsed.positionStart.y
            ) else null
            val posEnd: Position? =
                if (dataParsed.hasPositionEnd()) Position(
                    dataParsed.positionEnd.x,
                    dataParsed.positionEnd.y
                ) else null
            val rq = UpdateLayerPositionRequest(
                "", request.coordinatorId, dataParsed.channelCode, dataParsed.docLocalId,
                dataParsed.layerId, posStart, posEnd, metaData.sequence
            )

            return handleUpdateLayerPosition(rq, call)
        } catch (t: Throwable) {
            logger.error("handleUpdateLayerCmd, handle update layer failed: ", t)
            return call.respond(HttpStatusCode.InternalServerError)
        }
    }

    private suspend fun handleSyncDocSettingsCmd(
        call: ApplicationCall,
        request: ReceiveCmdRequest,
        data: ByteArray,
        metaData: Meta.CmdMetaProto,
    ) {
        try {
            val dataParsed = SyncDocSettingsProto.parseFrom(data)
            val docSettings = mutableListOf<CoordinatorState.DocSetting>()
            dataParsed.docSettingsList.forEach {
                docSettings.add(
                    CoordinatorState.DocSetting(
                        channelCode = it.channelCode,
                        docLocalId = it.docLocalId,
                        background = it.background,
                        backgroundColor = it.backgroundColor,
                        shadow = it.shadow,
                        shadowType = it.shadowType,
                        border = it.border,
                        borderColor = it.borderColor,
                        borderType = it.borderType,
                    )
                )
            }

            val coordState = db.updateDocsSettings(metaData.sequence, dataParsed.coordinatorId, docSettings)
            coordStateCache.add(coordState)

            return call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("handleSyncDocSettingsCmd, handle sync doc settings failed: ", t)
            return call.respond(HttpStatusCode.InternalServerError)
        }
    }

    private suspend fun handleSyncPresenterStateCmd(
        call: ApplicationCall,
        request: ReceiveCmdRequest,
        data: ByteArray,
        metaData: Meta.CmdMetaProto,
    ) {
        try {
            val dataParsed = CoordinatorStateCmds.CoordinatorState.SyncPresenterProto.parseFrom(data)
            val presenter = dataParsed.presenter;
            val presnterState = PresenterState(
                vpZoom = presenter.vpZoom,
                vpSize = listOf(presenter.vpSize.width, presenter.vpSize.height),
                vpPos = listOf(presenter.vpPos.x, presenter.vpPos.y),
            )

            val coordState = db.updatePresenterState(metaData.sequence, dataParsed.coordinatorId, presnterState)
            coordStateCache.add(coordState)
            return call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("handleSyncPresenterStateCmd, handle sync default setting failed: ", t)
            return call.respond(HttpStatusCode.InternalServerError, t.message ?: "unknown")
        }
    }

    private suspend fun handleSyncDefaultDocSettingsCmd(
        call: ApplicationCall,
        request: ReceiveCmdRequest,
        data: ByteArray,
        metaData: Meta.CmdMetaProto,
    ) {
        try {
            val dataParsed = CoordinatorStateCmds.CoordinatorState.SyncDefaultDocSettingProto.parseFrom(data)
            val coordId = dataParsed.coordinatorId

            val settingProto = dataParsed.defaultSetting;
            val defaultSetting = DefaultSetting(
                background = settingProto.background,
                backgroundColor = settingProto.backgroundColor,
                shadow = settingProto.shadow,
                shadowType = settingProto.shadowType,
                border = settingProto.border,
                borderColor = settingProto.borderColor,
                borderType = settingProto.borderType,
            )

            val coordState = db.updateDefaultDocSetting(metaData.sequence, dataParsed.coordinatorId, defaultSetting)
            coordStateCache.add(coordState)

            return call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("handleSyncDefaultDocSettingsCmd, handle sync default setting failed: ", t)
            return call.respond(HttpStatusCode.InternalServerError)
        }
    }

    /**
     * Submit cmd to editor backend
     */
    private suspend fun submitCmdToBackend(
        call: ApplicationCall,
        request: ReceiveCmdRequest,
        metaData: Meta.CmdMetaProto,
        data: ByteArray,
    ) {
        val key = DocGlobalIdKey(request.coordinatorId, metaData.channelCode, metaData.versionable)

        val globalId = docGlobalIdCache.get(key) ?: run {
            logger.error("submitCmdToBackend, not found global id for {}", key)
            return call.respond(HttpStatusCode.BadRequest, "missing global id")
        }

        val response: HttpResponse = try {
            editorBackendServiceGateway.submitCmdRequest(metaData.channelCode, globalId, data)
        } catch (t: Throwable) {
            logger.error("submitCmdToBackend, failed to proxy request to backend editor... ", t)
            return call.respond(HttpStatusCode.InternalServerError,"Lỗi hệ thống")
        }

        return call.respond(response.status)
    }

    private suspend fun handleUpdateLayerPosition(
        request: UpdateLayerPositionRequest,
        call: ApplicationCall,
    ) {
        try {
            var coordState: CoordinatorState? = null

            // update layer info to coordinator state
            if (request.positionStart != null) {
                coordState = db.updateLayerPosition(
                    request.coordStateId, request.channelCode, request.docLocalId,
                    request.layerId, request.positionStart, request.positionEnd, request.coordStateVersion
                )
            }

            if (coordState != null) {
                coordStateCache.add(coordState)
            }

            logger.info(
                "handleUpdateLayerPosition, coord [{}] updated layer position start {}, end {}",
                request.coordStateId,
                request.positionStart,
                request.positionEnd
            )
            return call.respond(HttpStatusCode.OK)

        } catch (t: Throwable) {
            logger.error("handleUpdateLayerPosition, handle update layer info failed... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "unknown")
        }
    }


    suspend fun updateLayer(call: ApplicationCall) {
        val request = call.receive<UpdateLayerPositionRequest>()

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

        if (!validateModifyCoordStageContent(logger, call, peerInfo, roomInfo, coordState)) return

        if (!validateCoordVersion(
                logger,
                call,
                coordState.id,
                coordState.version,
                request.coordStateVersion
            )
        ) return

        handleUpdateLayerPosition(request, call)
    }


    private fun mapLayerInfo(layers: List<LayerInfo>?, docLocalId: Int): List<CoordinatorState.LayerInfo>? {
        return layers?.map {
            val position = mutableListOf<Double>().apply {
                it.positionStart?.let { start -> addAll(listOf(start.x, start.y)) }
                it.positionEnd?.let { end -> addAll(listOf(end.x, end.y)) }
            }
            CoordinatorState.LayerInfo(it.layerId, docLocalId, it.layerIndex, position)
        }
    }

    suspend fun addMultipleDocMappingAndLayers(call: ApplicationCall) {
        val request = try {
            call.receive<AddDocsMappingAndLayersRequest>()
        } catch (e: Throwable) {
            logger.error("addMultipleDocMappingAndLayers, failed to parse request: ", e)
            call.respond(HttpStatusCode.BadRequest,"request body invalid")
            return
        }

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return


        /**
         * If not all documents use channelCode 6, validate:
         *  - modification of coordinate stage content
         *  - coordinate version consistency
         * Return early if either check fails.
         */
        val allDocumentsAreChannel6 = request.documents.all { it.channelCode == 6 }
        if (!allDocumentsAreChannel6 && (!validateModifyCoordStageContent(
                logger,
                call,
                peerInfo,
                roomInfo,
                coordState
            ) || !validateCoordVersion(
                logger,
                call,
                coordState.id,
                coordState.version,
                request.coordStateVersion
            ))
        ) return

        try {
            for (docInfo in request.documents) {

                val key = DocGlobalIdKey(request.coordStateId, docInfo.channelCode, docInfo.docLocalId)
                docInfo.docGlobalId?.let { docGlobalIdCache.add(key, it) }

                val layerUpdates = mapLayerInfo(docInfo.layerUpdates, docInfo.docLocalId)
                val layerAdds = mapLayerInfo(docInfo.layerAdds, docInfo.docLocalId)

                listOf(layerAdds, layerUpdates).forEach { layers ->
                    layers?.let {
                        db.addNewLayer(
                            request.coordStateId,
                            request.coordStateVersion,
                            docInfo.docLocalId,
                            docInfo.channelCode,
                            it,
                            false
                        )
                    }
                }

                logger.info(
                    "addMultipleDocMappingAndLayers, coord [{}] success add doc mapping {}-{} and layer update",
                    request.coordStateId,
                    docInfo.docLocalId,
                    docInfo.docLocalId
                )
                            }

            val coord = db.addNewDocMapping(
                request.coordStateId,
                request.coordStateVersion,
                request.documents,
                request.defaultSetting
            )

            coordStateCache.add(coord)

            return call.respond(HttpStatusCode.OK)

        } catch (t: Throwable) {
            val keys = request.documents.map { DocGlobalIdKey(request.coordStateId, it.channelCode, it.docLocalId) }
            logger.error("addMultipleDocMappingAndLayers, handle add doc mapping failed {}: ", keys, t)
            return call.respond(HttpStatusCode.InternalServerError,t.message ?: "unknown")
        }
    }

    suspend fun removeMultipleDocMappingAndLayers(call: ApplicationCall) {
        val request = try {
            call.receive<RemoveMultipleDocMappingAndLayersRequest>()
        } catch (e: Exception) {
            logger.error("removeMultipleDocMappingAndLayers, failed to parse request: ", e)
            return call.respond(HttpStatusCode.BadRequest, "request body invalid")
        }

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

        /**
         * Ensures all documents have `channelCode == 6`. If not, validates modification permissions.
         * If validation fails, the function exits early.
         */
        val allDocumentsAreChannel6 = request.documents.all { it.channelCode == 6 }
        if (!allDocumentsAreChannel6 && !validateModifyCoordStageContent(
                logger,
                call,
                peerInfo,
                roomInfo,
                coordState
            )
        ) return

        try {
            for ((index, docInfo) in request.documents.withIndex()) {
                if (!docInfo.layerRemoves.isNullOrEmpty()) {
                val key = DocGlobalIdKey(request.coordStateId, docInfo.channelCode, docInfo.docLocalId)
                docGlobalIdCache.invalidate(key)

                    // Increase version only if this is the last document and not all documents have channelCode == 6
                    val shouldIncreaseVersion = !allDocumentsAreChannel6 && index == request.documents.lastIndex

                    val coord = db.removeDocMappingAndLayers(
                        request.coordStateId,
                        request.coordStateVersion,
                        docInfo.docLocalId,
                        docInfo.layerRemoves,
                        docInfo.channelCode,
                        increaseVersion = index == request.documents.size - 1
                    )
                if (coord == null) {
                        call.respond(HttpStatusCode.PreconditionFailed,"remove fail")
                    return
                }
                    coordStateCache.add(coord)

                    logger.info(
                        "removeMultipleDocMappingAndLayers, coord [{}] success remove doc mapping {}-{} and layer",
                        request.coordStateId, docInfo.docLocalId, docInfo.docLocalId
                    )
                }

                if (docInfo.channelCode == 6) {
                    val userId = docInfo.docGlobalId?.split("_")?.get(3)
                    val roomId = db.getCoordinatorState(request.coordStateId)?.room
                    if (userId != null && roomId != null) {
                        db.getPeerByUserIdAndRoomId(userId, roomId)
                            ?.lsRegId
                            ?.let { lsRegId ->
                                lsessionSG.updateShareScreenStatusAsync(lsRegId, ShareScreenStatus.NONE).await()
                            }

                        val notification = Notification(
                            userId,
                            ClassroomTarget(roomId),
                            "Học viên ${userId} đã rời khỏi lớp và tự tắt share screen",
                            Instant.now().plusSeconds(300),
                            ShareScreenRemovedND(roomId, userId)
                        )
                        notificationSG.saveNotificationAsync(notification).await()
                    }
                }
            }
            return call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            val keys = request.documents.map { DocGlobalIdKey(request.coordStateId, it.channelCode, it.docLocalId) }
            logger.error("removeMultipleDocMappingAndLayers, remove doc mapping and layers {} failed... ", keys, t)
            return call.respond(HttpStatusCode.InternalServerError, "unknown")
        }
    }

    suspend fun removeLayerInfo(call: ApplicationCall) {
        val request = call.receive<RemoveLayerRequest>()

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

        if (!validateModifyCoordStageContent(logger, call, peerInfo, roomInfo, coordState)) return

        if (!validateCoordVersion(
                logger,
                call,
                coordState.id,
                coordState.version,
                request.coordStateVersion
            )
        ) return

        handleRemoveLayer(request, call)
    }

    private suspend fun handleRemoveLayer(request: RemoveLayerRequest, call: ApplicationCall) {
        return try {
            val coord = db.removeLayer(request.coordStateId, request.coordStateVersion, request.layerId)
            coordStateCache.add(coord)
            logger.info("handleRemoveLayer, coord [{}] success remove layer {}", request.coordStateId, request.layerId)
            call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("handleRemoveLayer, failed to remove layer {} ... ", request, t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    suspend fun getCoordStateById(call: ApplicationCall) {
        if (!call.requireParams(listOf("id"))) return

        val id: String = call.parameters["id"]!!
        try {
            val state: CoordinatorState = getCoordIfExist(logger, call, coordStateCache, id) ?: return
            return call.respond(HttpStatusCode.OK, state)
        } catch (e: NoSuchElementException) {
            logger.error("getCoordStateById, failed to fetch coordinator {}, not found ... ", id, e)
            return call.respond(HttpStatusCode.NotFound,"Not found coordinator state")
        } catch (t: Throwable) {
            logger.error("getCoordStateById, failed to fetch coordinator {} ... ", id, t)
            return call.respond(HttpStatusCode.InternalServerError)
        }
    }

    suspend fun getCoordStatesByIds(call: ApplicationCall) {
        val request: GetCoordinatorStatesRequest = call.receive()
        try {
            val states: List<CoordinatorState> = db.getCoordinatorStates(request.coordStateIds)
            call.respond(HttpStatusCode.OK, states)
        } catch (t: Throwable) {
            logger.error("getCoordStatesByIds, failed to fetch coordinator states {} ... ", request, t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    suspend fun getCoordStateByUser(call: ApplicationCall) {
        if (!call.requireParams(listOf("roomId", "ownerId"))) return

        val room: String = call.parameters["roomId"]!!
        val owner: String = call.parameters["ownerId"]!!
        return try {
            val state: List<CoordinatorState> = db.getCoordinatorStateByUser(room, owner)
            if (state.isEmpty()) {
                logger.debug("getCoordStateByUser, Not found coordinator for user {}", owner)
                call.respond(HttpStatusCode.OK, emptyList<CoordinatorState>())
            } else {
                call.respond(HttpStatusCode.OK, state)
            }
        } catch (e: NoSuchElementException) {
            logger.error("getCoordStateByUser, failed to fetch coordinator for user {}, not found ... ", owner, e)
            call.respond(HttpStatusCode.OK, emptyList<CoordinatorState>())
        } catch (t: Throwable) {
            logger.error("getCoordStateByUser, failed to fetch coordinator for user {} ... ", owner, t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    suspend fun getDocumentByGlobalId(call: ApplicationCall) {
        if (!call.requireParams(listOf("channelCode", "globalId"))) return

        val channelCode = call.parameters["channelCode"]!!.toInt()
        val globalId: String = call.parameters["globalId"]!!

        val response: HttpResponse =
            editorBackendServiceGateway.submitGetDocByGlobalIdRequest(channelCode, globalId);
        editorBackendServiceGateway.proxyEditorBackendResponse(call, response)
    }

    suspend fun getDocumentByLocalId(call: ApplicationCall) {
        if (!call.requireParams(listOf("coordStateId", "channelCode", "localId"))) return

        val coordStateId: String = call.parameters["coordStateId"]!!
        val channelCode = call.parameters["channelCode"]!!.toInt()
        val localId = call.parameters["localId"]!!.toInt()


        val key = DocGlobalIdKey(coordStateId, channelCode, localId)
        val globalId = docGlobalIdCache.get(key) ?: run {
            logger.error("getDocumentByLocalId, find document, not found global id for key {}", key)
            return call.respond(HttpStatusCode.NotFound, "Not found global id")
        }

        val response: HttpResponse = editorBackendServiceGateway.submitGetDocByGlobalIdRequest(channelCode, globalId)
        editorBackendServiceGateway.proxyEditorBackendResponse(call, response)
    }

    suspend fun pinCoordinatorState(call: ApplicationCall) {
        val request: PinCoordinatorStateRequest = call.receive()
        try {
            val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return

            classroomProcessorManager.getOrCreateProcessor(peerInfo.roomId).submitTaskAsync {
                val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return@submitTaskAsync
                val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return@submitTaskAsync

                if (!validatePinCoordState(logger, call, peerInfo, roomInfo, coordState)) return@submitTaskAsync

                logger.debug("pinCoordinatorState, room info before pin: {}", roomInfo)

                // update database
                val updateResult = db.addPinCoordinatorState(roomInfo.id, coordState.id)
                if (updateResult.matchedCount == 0L || updateResult.modifiedCount == 0L) {
                    return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError,"Lỗi ghim bảng")
                }

                // refresh cache
                roomInfo.pinnedCoordStates.add(coordState.id)

                // request msg to syncer
                val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
                aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

                val syncerPeer = aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
                if (syncerPeer == null) {
                    logger.error("pinCoordinatorState, Unable to find a syncer to serve the room with id {}", roomInfo.id)
                    return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable,"Lỗi ghim bảng")
                }

                val data = PinCoordStateMessage(roomInfo.id, coordState.id)

                val signalMessage = SignalMessage(
                    peerInfo.id,
                    syncerPeer.id,
                    SignalType.roomUpdate,
                    json.pojoNode(data),
                    System.currentTimeMillis().toString()
                )

                try {
                    logger.debug("pinCoordinatorState, requesting update room {}", signalMessage)
                val message: SignalMessage = peerMan.request(signalMessage.toPeer, signalMessage)
                    logger.info("pinCoordinatorState, syncer response pin coordinator state {}", message)
                } catch (e: PeerException) {
                    logger.error("pinCoordinatorState, failed to send message to syncer {}", signalMessage)
                    return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "Lỗi ghim bản")
                }

                // send msg to peers
                val notify = json.objectNode()
                    .put("roomId", roomInfo.id)
                    .put("coordStateId", coordState.id)
                val msgBuilder: (Peer) -> SignalMessage = {
                    SignalMessage(peerInfo.id, it.info.id, SignalType.PinnedCoordStateND, notify)
                }

                peerMan.filterAndSendAsync(msgBuilder) {
                    it.info.roomId == roomInfo.id
                            && it.info.peerType == PeerType.BROWSER
                            && it.info.status == PeerStatus.ACTIVE
                }

                logger.info("pinCoordinatorState, coord [{}] success pined", coordState.id)
                return@submitTaskAsync call.respond(HttpStatusCode.OK)
            }.join()
        } catch (t: Throwable) {
            logger.error("pinCoordinatorState, failed to pin coordinator state {} ... ", request, t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi ghim bảng")
        }
    }

    suspend fun unpinCoordinatorState(call: ApplicationCall) {
        val request: UnpinCoordinatorStateRequest = call.receive()
        try {
            val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return

            classroomProcessorManager.getOrCreateProcessor(peerInfo.roomId).submitTaskAsync {
                val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return@submitTaskAsync
                val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return@submitTaskAsync

                if (!validateUnpinCoordState(logger, call, peerInfo, roomInfo, coordState)) return@submitTaskAsync

                // request msg to syncer
                val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
                aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

                val syncerPeer =
                    aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
                if (syncerPeer == null) {
                    logger.error(
                        "unpinCoordinatorState, Unable to find a syncer to serve the room with id {}",
                        roomInfo.id
                    )
                    return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, "Lỗi hệ thống")
                }

                val isOwnedCoord = roomInfo.presentingUser == coordState.owner
                val isDefaultCoord = coordState.id == roomInfo.defaultCoordState
                if (!isOwnedCoord && !isDefaultCoord) {
                    // update db and request to syncer
                    try {
                        db.updatePresentingCoordinatorState(roomInfo.id, roomInfo.defaultCoordState)
                        roomInfo.presentingCoordState = roomInfo.defaultCoordState

                        val data = PresentCoordStateMessage(roomInfo.id, roomInfo.defaultCoordState)
                        val signalMessage = SignalMessage(
                            request.peerId,
                            syncerPeer.id,
                            SignalType.roomUpdate,
                            json.pojoNode(data),
                            System.currentTimeMillis().toString()
                        )

                        logger.debug("unpinCoordinatorState, requesting update room {}", signalMessage)
                        val message: SignalMessage = peerMan.request(signalMessage.toPeer, signalMessage)
                        logger.info("unpinCoordinatorState, update presenting coordinator message {}", message)

                        if (message.data["status"].isNull || message.data["status"].intValue() > 0) {
                            return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, message.data["message"]?.textValue() ?: "Lỗi hệ thống")
                        }
                    } catch (e: PeerException) {
                        logger.error("unpinCoordinatorState, failed to update presenting coordinator and request syncer: ", e)
                        return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "unknown")
                    } catch (t: Throwable) {
                        logger.error("unpinCoordinatorState, failed to update presenting coordinator and request syncer: ", t)
                        return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
                    }

                    // send message to all peers except current requesting peer
                    val data = json.objectNode().put("coordStateId", roomInfo.defaultCoordState)
                    val msgBuilder: (Peer) -> SignalMessage = {
                        SignalMessage(request.peerId, it.info.id, SignalType.presentCoordState, data)
                    }

                    peerMan.filterAndSendAsync(msgBuilder) {
                        it.info.roomId == roomInfo.id && it.info.peerType == PeerType.BROWSER
                    }
                }

                logger.debug("unpinCoordinatorState, room info before unpin: {}", roomInfo)

                // update database
                val updateResult = db.removePinCoordinatorState(roomInfo.id, coordState.id)
                if (updateResult.matchedCount <= 0L || updateResult.modifiedCount <= 0L) {
                    return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
                }

                // refresh cache
                roomInfo.pinnedCoordStates.remove(coordState.id)

                val data = UnpinCoordStateMessage(roomInfo.id, coordState.id)

                val signalMessage = SignalMessage(
                    peerInfo.id,
                    syncerPeer.id,
                    SignalType.roomUpdate,
                    json.pojoNode(data),
                    System.currentTimeMillis().toString()
                )

                try {
                    logger.debug("unpinCoordinatorState, requesting update room {}", signalMessage)
                    val message: SignalMessage = peerMan.request(signalMessage.toPeer, signalMessage)
                    logger.info("unpinCoordinatorState, syncer response unpin coordinator state {}", message)
                } catch (e: PeerException) {
                    logger.error("unpinCoordinatorState, failed to send message to syncer {}", signalMessage)
                    return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "unknown")
                }
                // send msg to peers
                val notify = CoordStateMessage(roomInfo.id, coordState.id)

                val msgBuilder: (Peer) -> SignalMessage = {
                    SignalMessage(peerInfo.id, it.info.id, SignalType.UnpinnedCoordStateND, json.pojoNode(notify))
                }

                peerMan.filterAndSendAsync(msgBuilder) {
                    it.info.roomId == roomInfo.id
                            && it.info.peerType == PeerType.BROWSER
                            && it.info.status == PeerStatus.ACTIVE
                }

                logger.info("unpinCoordinatorState, coord [{}] success unpin", coordState.id)
                return@submitTaskAsync call.respond(HttpStatusCode.OK)
            }.join()
        } catch (t: Throwable) {
            logger.error("unpinCoordinatorState, failed to unpin coordinator state {} ... ", request, t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
        }
    }

    /**
     * Duplicates a coordinator state and its associated documents.
     *
     * @param call - The HTTP request containing duplication details.
     *
     * Steps:
     * 1. Validate peer, coordinator state, and user.
     * 2. Generate a new coordinator ID.
     * 3. Group documents by channel and duplicate them (excluding channel 6 is shareScreenEditor).
     * 4. Copy metadata for duplicated documents.
     * 5. Handle errors and return a response if duplication fails.
     */
    suspend fun duplicateCoordState(call: ApplicationCall) {
        val request = call.receive<DuplicateCoordinatorStateRequest>()

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return

        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
        val user = userSG.getUserByIdAsync(peerInfo.userId).await()
        if (user.status.code != Status.Code.OK.value()) return

        val newCoordId = ObjectId().toHexString()

        // group document by channel
        val reserved = coordState.docMapping.toMutableMap().entries.associate { it.value to it.key }
        val grouped = coordState.docMapping.toMutableMap().entries.groupBy { it.key.split("_")[0].toInt() }
        val newDocMapping = mutableMapOf<String, String>()

        // duplicate documents
        try {
            coroutineScope {
                grouped.forEach { (channelCode, m) ->
                    if (channelCode == SHARE_SCREEN_CHANNEL_CODE) return@forEach
                    val ids = m.map { it.value }
                    val res = editorBackendServiceGateway.submitDuplicateDocumentRequest(channelCode, ids)
                    res.mapping.forEach { (source, target) ->
                        reserved[source]?.let {
                            // duplicate metadata
                            metadataServiceGateway.duplicateMetadataDoc(
                                listOf(
                                    Triple("classroomId", BsonType.STRING, coordState.room),
                                    Triple("coordStateId", BsonType.STRING, newCoordId),
                                    Triple("docGlobalId", BsonType.STRING, target),
                                    Triple("ownerUserId", BsonType.STRING, user.user.id),
                                    Triple("ownerUserName", BsonType.STRING, user.user.username),
                                    Triple("ownerRegId", BsonType.STRING, peerInfo.lsRegId),
                                ), source, target, CLASSROOM_METADATA_DOC_TYPE
                            ).await()
                            // add doc mapping
                            newDocMapping[it] = target
                        }
                    }
                }
            }
        } catch (t: Throwable) {
            logger.error("duplicateCoordState, Send duplicate documents exception... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Failed to duplicate coord: ${t.message}")
        }

        // create new coordinator state
        val newCoordState = CoordinatorState(
            peerInfo.userId, peerInfo.roomId, coordState.title,
            id = newCoordId,
            docMapping = newDocMapping,
            layers = coordState.layers
        )

        val res = try {
            db.insertCoordinatorState(newCoordState)
        } catch (t: Throwable) {
            logger.error("duplicateCoordState, insert new coord exception... ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi tạo bản sao của bảng")
        }

        // add to caches
        coordStateCache.add(newCoordState)
        newDocMapping.entries.forEach {
            val split = it.key.split("_")
            val key = DocGlobalIdKey(newCoordState.id, split[0].toInt(), split[1].toInt())
            docGlobalIdCache.add(key, it.value)
        }

        try {
            // request msg to syncer
            val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
            aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

            val syncerPeer =
                aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
            if (syncerPeer == null) {
                logger.error("duplicateCoordState, Unable to find a syncer to serve the room with id {}", peerInfo.roomId)
                return call.respond(HttpStatusCode.ServiceUnavailable, "Lỗi hệ thống")
            }

            val data = CreateCoordStateMessage(
                peerInfo.roomId,
                SyncerCoordStateResponse(
                    id = newCoordId,
                    ownerId = peerInfo.userId,
                    roomId = peerInfo.roomId,
                    version = 1
                )
            )

            val signalMessage = SignalMessage(
                request.peerId,
                syncerPeer.id,
                SignalType.roomUpdate,
                json.pojoNode(data),
                System.currentTimeMillis().toString()
            )

            logger.debug("duplicateCoordState, requesting update room {}", signalMessage)
            val message: SignalMessage = peerMan.request(signalMessage.toPeer, signalMessage)
            logger.info("duplicateCoordState, syncer response create coordinator state {}", message)

            logger.info("duplicateCoordState, coord [{}] success duplicate from {}", newCoordState.id, coordState.id)
            return call.respond(HttpStatusCode.OK, res.insertedId!!.asObjectId().value.toHexString())
        } catch (e: PeerException) {
            logger.error("duplicateCoordState, failed to duplicate coordinator {} ... ", request, e)
            return call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "Lỗi tạo bản sao của bảng")
        } catch (t: Throwable) {
            logger.error("duplicateCoordState, failed to duplicate coordinator {} ... ", request, t)
            return call.respond(HttpStatusCode.InternalServerError, "Lỗi tạo bản sao của bảng")
        }
    }

    suspend fun presentCoordState(call: ApplicationCall) {
        val request = call.receive<PresentCoordStateRequest>()
        val callPeer = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return

        classroomProcessorManager.getOrCreateProcessor(callPeer.roomId).submitTaskAsync {
            val classroomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, callPeer.roomId) ?: return@submitTaskAsync
            val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return@submitTaskAsync
            val roomId: String = callPeer.roomId
            if (!validatePresentCoordState(logger, call, callPeer, classroomInfo, coordState)) return@submitTaskAsync

            val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
            aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

            val syncerPeer =
                aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
            if (syncerPeer == null) {
                logger.error("presentCoordState, Unable to find a syncer to serve the room with id {}", roomId)
                return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, "Lỗi hệ thống")
            }

            // update db and request to syncer
            try {
                db.updatePresentingCoordinatorState(classroomInfo.id, request.coordStateId)
                classroomInfo.presentingCoordState = request.coordStateId

                val data = PresentCoordStateMessage(classroomInfo.id, request.coordStateId)
                val signalMessage = SignalMessage(
                    request.peerId,
                    syncerPeer.id,
                    SignalType.roomUpdate,
                    json.pojoNode(data),
                    System.currentTimeMillis().toString()
                )

                logger.debug("presentCoordState, requesting update room {}", signalMessage)
                val message: SignalMessage = peerMan.request(signalMessage.toPeer, signalMessage)
                logger.info("update presenting coordinator message {}", message)

                if (message.data["status"].isNull || message.data["status"].intValue() > 0) {
                    return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, message.data["message"]?.textValue() ?: "Lỗi hệ thống")
                }
            } catch (e: PeerException) {
                logger.error("presentCoordState, failed to update presenting coordinator and request syncer: ", e)
                return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "unknown")
            } catch (t: Throwable) {
                logger.error("presentCoordState, failed to update presenting coordinator and request syncer: ", t)
                return@submitTaskAsync call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            }

            // send message to all peers except current requesting peer
            val data = json.objectNode().put("coordStateId", request.coordStateId)
            val msgBuilder: (Peer) -> SignalMessage = {
                SignalMessage(request.peerId, it.info.id, SignalType.presentCoordState, data)
            }

            peerMan.filterAndSendAsync(msgBuilder) {
                it.info.roomId == classroomInfo.id && it.info.peerType == PeerType.BROWSER
            }

            call.respond(HttpStatusCode.OK, "successful")
        }.join()
    }

    suspend fun requestPinTab(call: ApplicationCall) {
        val req = call.receive<RequestPinTabRequest>()
        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, req.peerId) ?: return
        val coordState = getCoordIfExist(logger, call, coordStateCache, req.coordStateId) ?: return
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

        if (!validateRequestPinCoordState(logger, call, peerInfo, roomInfo, coordState)) return

        submitActionToActor(call, req.peerId, ActorTaskType.RequestPinTab) { actor ->
            val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, peerInfo.roomId, peerInfo.userId) ?: return@submitActionToActor
            val notiData = RequestPinTabND(actor.roomId, actor.userId, req.coordStateId)
            val notiMessage = "Học viên ${actor.userProto.username} vừa yêu cầu ghim bảng cá nhân"
            checkAndUpdateRequestPinTab(lsReg.id, actor.roomId, actor.userId, coordState.id, coordState.title, RequestPinTabStatus.PENDING, notiData, notiMessage, call)
        }
    }

    suspend fun cancelRequestPinTab(call: ApplicationCall) {
        val req = call.receive<CancelRequestPinTabRequest>()
        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, req.peerId) ?: return
        classroomProcessorManager.getOrCreateProcessor(peerInfo.roomId).submitTaskAsync {
            val coordState = getCoordIfExist(logger, call, coordStateCache, req.coordStateId) ?: return@submitTaskAsync
            val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return@submitTaskAsync

            if (!validateCancelRequestPinCoordState(logger, call, peerInfo, roomInfo, coordState)) return@submitTaskAsync

            submitActionToActor(call, req.peerId, ActorTaskType.CancelRequestPinTab) { actor ->
                val lsReg = getLSessionRegistrationIfExist(logger, call, lsessionSG, peerInfo.roomId, peerInfo.userId) ?: return@submitActionToActor
                val notiData = CancelRequestPinTabND(actor.roomId, actor.userId, req.coordStateId)
                val notiMessage = "Học viên ${actor.userProto.username} vừa hủy yêu cầu ghim bảng cá nhân"
                checkAndUpdateRequestPinTab(
                    lsReg.id,
                    actor.roomId,
                    actor.userId,
                    coordState.id,
                    title = null,
                    RequestPinTabStatus.CANCELLED,
                    notiData,
                    notiMessage,
                    call
                )
            }
        }.join()
    }

    suspend fun rejectRequestPinTab(call: ApplicationCall) {
        val req = call.receive<RejectRequestPinTabRequest>()
        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, req.peerId) ?: return

        classroomProcessorManager.getOrCreateProcessor(peerInfo.roomId).submitTaskAsync {
            val coordState = getCoordIfExist(logger, call, coordStateCache, req.coordStateId) ?: return@submitTaskAsync
            val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return@submitTaskAsync

            if (!validateRejectRequestPinCoordState(logger, call, peerInfo, roomInfo, coordState)) return@submitTaskAsync

            val lsReg = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(peerInfo.roomId, req.targetUserId).await().registration.toPojo()
            val targetUser = userSG.getUserByIdAsync(req.targetUserId).await().user?.username
            val notiData = RejectRequestPinTabND(peerInfo.roomId, req.targetUserId, req.coordStateId)
            val notiMessage = "Giáo viên vừa từ chối yêu cầu ghim bảng cá nhân của học viên ${targetUser ?: ""}"
            checkAndUpdateRequestPinTab(
                lsReg.id,
                peerInfo.roomId,
                req.targetUserId,
                coordState.id,
                title = null,
                RequestPinTabStatus.REJECTED,
                notiData,
                notiMessage,
                call
            )
        }.join()
    }

    suspend fun approveRequestPinTab(call: ApplicationCall) {
        val req = call.receive<ApproveRequestPinTabRequest>()
        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, req.peerId) ?: return

        classroomProcessorManager.getOrCreateProcessor(peerInfo.roomId).submitTaskAsync {
            val coordState = getCoordIfExist(logger, call, coordStateCache, req.coordStateId) ?: return@submitTaskAsync
            val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return@submitTaskAsync

            if (!validateApproveRequestPinCoordState(logger, call, peerInfo, roomInfo, coordState)) return@submitTaskAsync
            if (!validatePinCoordState(logger, call, peerInfo, roomInfo, coordState)) return@submitTaskAsync

            val targetUser = userSG.getUserByIdAsync(req.targetUserId).await().user?.username

            logger.debug("approveRequestPinTab, room info before pin: {}", roomInfo)

            // update database
            val updateResult = db.addPinCoordinatorState(roomInfo.id, coordState.id)
            if (updateResult.matchedCount == 0L || updateResult.modifiedCount == 0L) {
                return@submitTaskAsync call.respond(
                    HttpStatusCode.InternalServerError,
                    "Failed to update pin coordinator state to database"
                )
            }

            // refresh cache
            roomInfo.pinnedCoordStates.add(coordState.id)

            // request msg to syncer
            val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
            aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

            val syncerPeer =
                aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
            if (syncerPeer == null) {
                logger.error("approveRequestPinTab, Unable to find a syncer to serve the room with id {}", roomInfo.id)
                return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, "Unable to find a syncer")
            }

            val data = PinCoordStateMessage(roomInfo.id, coordState.id)

            val signalMessage = SignalMessage(
                peerInfo.id,
                syncerPeer.id,
                SignalType.roomUpdate,
                json.pojoNode(data),
                System.currentTimeMillis().toString()
            )

            try {
                logger.debug("pinCoordinatorState, requesting update room {}", signalMessage)
                val message: SignalMessage = peerMan.request(signalMessage.toPeer, signalMessage)
                logger.info("pinCoordinatorState, syncer response pin coordinator state {}", message)
            } catch (e: PeerException) {
                logger.error("pinCoordinatorState, failed to send message to syncer {}", signalMessage)
                return@submitTaskAsync call.respond(HttpStatusCode.ServiceUnavailable, e.message ?: "unknown")
            }

            logger.info("approveRequestPinTab, coord [{}] success pined", coordState.id)

            val lsReg = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(peerInfo.roomId, req.targetUserId).await().registration.toPojo()
            val notiData = ApproveRequestPinTabND(peerInfo.roomId, req.targetUserId, req.coordStateId)
            val notiMessage = "Giáo viên vừa chấp nhận yêu cầu ghim bảng cá nhân của học viên ${targetUser ?: ""}"
            checkAndUpdateRequestPinTab(
                lsReg.id,
                peerInfo.roomId,
                req.targetUserId,
                coordState.id,
                title = null,
                RequestPinTabStatus.APPROVED,
                notiData,
                notiMessage,
                call
            )
        }.join()
    }

    private suspend fun checkAndUpdateRequestPinTab(
        lsRegId: String,
        roomId: String,
        userId: String,
        tabId: String,
        title: String? = null,
        status: RequestPinTabStatus? = null,
        notiData: NotificationData? = null,
        notiMessage: String? = null,
        call: ApplicationCall? = null
    ): Boolean? {
        try {
            lsessionSG.updateRequestPinCoordStateAsync(lsRegId, tabId, status, title).await()
        } catch (ex: StatusException) {
            logger.error("exception update request pin tab status... ", ex)
            call?.respond(HttpStatusCode.InternalServerError, ex.message ?: "Lỗi cập nhật trạng thái yêu cầu ghim bảng cá nhân")
            return null
        } catch (t: Throwable) {
            logger.error("exception update request pin tab status... ", t)
            call?.respond(HttpStatusCode.InternalServerError, t.message ?: "Lỗi cập nhật trạng thái yêu cầu ghim bảng cá nhân")
            return null
        }

        if (notiData != null) {
            val notification = Notification(
                userId,
                ClassroomTarget(roomId),
                notiMessage ?: "",
                Instant.now().plusSeconds(300),
                notiData
            )
            try {
                notificationSG.saveNotificationAsync(notification).await()
            } catch (t: Throwable) {
                logger.error("exception send notification... ", t)
                call?.respond(HttpStatusCode.InternalServerError,"Failed to send notification")
                return null
            }
        }
        call?.respond(HttpStatusCode.OK)
        return true
    }

    private suspend fun submitActionToActor(
        call: ApplicationCall,
        peerId: String,
        type: ActorTaskType,
        function: suspend (actor: UserClassroomActor) -> Unit,
    ): Unit? {
        try {
            val peer = getPeerInfoIfExist(logger, call, peerMan, peerId) ?: return null
            val actor = getActorIfExist(logger, call, actorManager, peer.roomId, peer.userId) ?: return null
            return actor.send(type) { function(actor) }.join()
        } catch (t: Throwable) {
            logger.error("exception send task to actor... ", t)
            call.respond(HttpStatusCode.InternalServerError, "Lỗi hệ thống")
            return null
        }
    }
}

package vinet.ccs.controllers

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.koin.core.annotation.Singleton
import portal.datastructures.lsession.LSRegStatus
import portal.datastructures.lsession.UserAvailableStatus
import vinet.ccs.actor.ActorManager
import vinet.ccs.actor.ActorTaskType
import vinet.ccs.actor.UserAvailableStatusHandler
import vinet.ccs.actor.UserClassroomActor
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.db.DatabaseService
import vinet.ccs.exception.*
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.model.ErrorResponse
import vinet.ccs.model.ReportRequest
import vinet.ccs.model.ResponseErrorCode
import vinet.ccs.model.SignalMessage
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.PeerType
import vinet.ccs.utility.getActorIfExist
import vinet.ccs.utility.getPeerIfExist
import vinet.ccs.utility.getRoomInfoIfExist


@Singleton
class SignalCtrl constructor(
    private val db: DatabaseService,
    private val classroomCache: ClassroomInfoCache,
    private val peerMan: PeerManager,
    private val actorManager: ActorManager,
    private val lsessionSG: LSessionServiceGateway,
    private val userAvailableStatusHandler: UserAvailableStatusHandler
) : Logging {

    /**
     * Allow a WebRTC peer to poll for new messages, including, offers, answers, ice candidates, etc..
     * or when the state of the message is expired without anyone polled it
     */
    suspend fun poll(call: ApplicationCall) {
        val peerId = call.parameters["peerId"] ?: return call.respond(HttpStatusCode.BadRequest, "Error! Request not valid. No Peer Id.")

        val peer = getPeerIfExist(logger, call, peerMan, peerId) ?: return

        if (peer.info.peerType != PeerType.SYNCER) {

            val actor = getActorIfExist(logger, call, actorManager, peer.info.roomId, peer.info.userId) ?: return

            if (actor.lsRegistration.regStatus != LSRegStatus.REGISTERED) {
                logger.error(
                    "poll [{}], user [{}] not registered with status [{}] in room [{}]",
                    peerId, actor.userId, actor.lsRegistration.regStatus, actor.roomId
                )
                return call.respond(HttpStatusCode.Forbidden, "User not registered.")
            }
        }


        try {
            // update aliveness of the peer
            peerMan.updateAlive(peerId)
            val message = peerMan.receive(peerId, 15000)
            return call.respond(message)
        } catch (e: PeerNotFound) {
            logger.error("poll [{}], peer not found: {}", peerId, e.message)
            return call.respond(HttpStatusCode.Forbidden, ErrorResponse(ResponseErrorCode.PEER_INVALID.name, "Peer not found."))
        } catch (e: PeerTimeoutException) {
//            logger.warn("poll [{}], receive timeout: {}", peerId, e.message)
            return call.respond(HttpStatusCode.OK)
        } catch (e: PeerClosedException) {
            logger.warn("poll [{}], peer closed: {}", peerId, e.message)
            return call.respond(HttpStatusCode.Forbidden, ErrorResponse(ResponseErrorCode.PEER_INVALID.name, "Peer closed."))
        } catch (e: PeerKickedOutException) {
            logger.warn("poll [{}], peer kicked out: {}", peerId, e.message)
            return call.respond(HttpStatusCode.Forbidden, ErrorResponse(ResponseErrorCode.PEER_KICKED_OUT.name, "Peer kicked out."))
        } catch (e: PeerInactiveException) {
            logger.warn("poll [{}], peer invalid: {}", peerId, e.message)
            return call.respond(HttpStatusCode.Forbidden, ErrorResponse(ResponseErrorCode.PEER_INVALID.name, "Peer invalid."))
        } catch (t: Throwable) {
            logger.error("poll [{}], receive message failed...", peerId, t)
            return call.respond(HttpStatusCode.InternalServerError, "failed to receive message: ${t.message}")
        }
    }

    /**
     * When a peer wants to join a room, it send an offer together with the room id
     * This offer will be sent to a suitable synchronizer if there is one, else it is stored for sometimes
     * on the server.
     *
     * TODO: has to select peer and retry if cannot join room, try sending the message to other sync node for example
     */
    suspend fun join(call: ApplicationCall) {
        val roomId = call.parameters["roomId"] ?: return call.respond(HttpStatusCode.BadRequest, "Need to include room id when join.")

        logger.info("join, a peer wants to join room {}", roomId)

        // validate peer
        val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
        aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

        // find the first alive peer inside peer man
        val syncerPeer = aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) } ?: kotlin.run {
            // not able to find a syncer that will serve the peer, the peer should retry
            logger.info("join, unable to find a syncer to serve the room [{}]", roomId)
            return call.respond(HttpStatusCode.ServiceUnavailable, "Unable to find a syncer")
        }

        call.respond(HttpStatusCode.OK, syncerPeer.id)
    }

    /**
     * Sending a message to another peer
     */
    suspend fun send(call: ApplicationCall) {

        val msg = try {
            call.receive<SignalMessage>()
        } catch (t: Throwable) {
            logger.error("send, parse signal message failed... ", t)
            return call.respond(HttpStatusCode.BadRequest, "failed to parse signal message")
        }

        logger.info("send, Sending message [{}]", msg)

        /**
         * TODO: because peer poll message one by one so fixed time out is not good enough. This has to be a loop
         * and every time it is timeout, has to check the last active time and only if it has past 3s from the last
         * active time, then we stop waiting for the other peer to poll.
         */

        try {
            peerMan.send(msg.toPeer, msg, 3000)  // waiting for someone to receive the message for 3s
            call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("send, The peer [{}] has not polled the message in time ... ", msg.toPeer, t)
            call.respond(HttpStatusCode.InternalServerError, t.message ?: "The peer has not polled the message in time. Retry later.")
        }
    }

    suspend fun request(call: ApplicationCall) {
        val msg = try {
            call.receive<SignalMessage>()
        } catch (t: Throwable) {
            logger.error("request, parse signal message failed... ", t)
            return call.respond(HttpStatusCode.BadRequest, "failed to parse signal message")
        }

        if (msg.requestId == null) {
            return call.respond(HttpStatusCode.BadRequest, "requestId is required")
        }

        val replyMsg = try {
            peerMan.request(msg.toPeer, msg)
        } catch (t: Throwable) {
            logger.error("request, failed to send message [{}]: ", msg, t)
            return call.respond(HttpStatusCode.InternalServerError, "failed to send request to peer ${msg.toPeer}")
        }

        call.respond(replyMsg)
    }

    suspend fun reply(call: ApplicationCall) {
        val msg = try {
            call.receive<SignalMessage>()
        } catch (t: Throwable) {
            logger.error("reply, parse signal message failed... ", t)
            return call.respond(HttpStatusCode.BadRequest, "failed to parse signal message")
        }

        if (msg.requestId == null) {
            return call.respond(HttpStatusCode.BadRequest, "requestId is required")
        }

        try {
            peerMan.reply(msg.fromPeer, msg)
        } catch (t: Throwable) {
            logger.error("reply, failed to send reply to peer, message [{}]: ", msg, t)
            return call.respond(HttpStatusCode.InternalServerError, "failed to send reply to peer ${msg.toPeer}")
        }

        call.respond(HttpStatusCode.OK)
    }

    suspend fun report(call: ApplicationCall) {
        val request = call.receive<ReportRequest>()
        val room = getRoomInfoIfExist(logger, call, classroomCache, request.roomId) ?: return
        val peer = getPeerIfExist(logger, call, peerMan, request.peerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, peer.info.roomId, peer.info.userId) ?: return

        if (room.id != actor.roomId) return call.respond(HttpStatusCode.Forbidden, "Peer in not in correct classroom")

        try {
            actor.send(ActorTaskType.Report) { doReport(actor, request, call) }.join()
        } catch (t: Throwable) {
            logger.error("report, failed to do report: ", t)
            return call.respond(HttpStatusCode.InternalServerError, "Failed to send task to actor: ${t.message}")
        }
    }

    /**
     * Handles the disconnection of a peer from a session.
     *
     * This function validates the peer ID, retrieves the associated user actor,
     * checks their registration status, and proceeds with disconnection if valid.
     * If any validation fails, it responds with the appropriate error message.
     */
    suspend fun disconnect(call: ApplicationCall) {
        val peerId = call.parameters["peerId"] ?: return call.respond(HttpStatusCode.BadRequest, "Error! Request not valid. No Peer Id.")

        val peer = getPeerIfExist(logger, call, peerMan, peerId) ?: return
        val actor = getActorIfExist(logger, call, actorManager, peer.info.roomId, peer.info.userId) ?: return

        doDisconnect(actor, call)
    }


    /**
     * Performs the disconnection process for a user.
     *
     * This function marks the user as offline, sends a status change notification,
     * and broadcasts the update to active peers in the same room. If any errors occur,
     * it logs the issue and responds with an internal server error.
     */

    private suspend fun doDisconnect(actor: UserClassroomActor, call: ApplicationCall) {
        try {
            userAvailableStatusHandler.process(actor, UserAvailableStatus.OFFLINE)
        } catch (t: Throwable) {
            logger.error("doDisconnect, exception disconnect... ", t)
            call.respond(HttpStatusCode.InternalServerError, "Failed to send task to actor: ${t.message}")
        }
    }


    private suspend fun doReport(actor: UserClassroomActor, req: ReportRequest, call: ApplicationCall) {
        val isChanged = actor.report.availableStatus != req.availableStatus || actor.report.rtcConn != req.rtcConn

        // save last report
        actor.report.availableStatus = req.availableStatus
        actor.report.rtcConn = req.rtcConn
        actor.report.reportTime = System.currentTimeMillis()

        if (!isChanged) {
            return call.respond(HttpStatusCode.OK, "not changed")
        }

        // update user status to db
        actor.lsRegistration.userState.availableStatus = req.availableStatus

        lsessionSG.updateUserAvailableStatusAsync(actor.lsRegistration.id, actor.roomId, req.availableStatus).await()

        userAvailableStatusHandler.process(actor, req.availableStatus)

        call.respond(HttpStatusCode.OK, "changed")
    }
}

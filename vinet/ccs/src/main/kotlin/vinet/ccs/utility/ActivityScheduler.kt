package vinet.ccs.utility

import common.libs.logger.Logging
import kotlinx.coroutines.*
import org.koin.core.annotation.Singleton
import portal.lsession.pojo.activity.ActivityStatus
import vinet.ccs.config.ConfigBean
import vinet.ccs.gateways.ClassroomServiceGateway
import java.util.concurrent.ConcurrentHashMap

@Singleton
class ActivityScheduler constructor(
    private val config: ConfigBean,
    private val classroomSG: ClassroomServiceGateway,
) : Logging {

    private val jobs = ConcurrentHashMap<String, Job>()
    private val scope = CoroutineScope(Dispatchers.Default)

    fun scheduleCancelActivityTask(activityId: String, doOnComplete: suspend () -> Unit, timeout: Long? = null) {
        jobs.computeIfAbsent(activityId) {
            scope.launch {
                delay(timeout ?: 120000)
                doCancelActivity(activityId, doOnComplete)
            }
        }
    }

    fun cancelScheduledTask(activityId: String) {
        jobs.computeIfPresent(activityId) { _, job ->
            if (job.isActive) job.cancel()
            null
        }
    }

    private suspend fun doCancelActivity(activityId: String, doOnComplete: suspend () -> Unit) {
        try {
            val activity = classroomSG.loadClassroomActivityByIdAsync(activityId).await()
            if (activity.status != ActivityStatus.ON_GOING) return

            classroomSG.updateClassroomActivityStatusAsync(activityId, ActivityStatus.ON_GOING, ActivityStatus.EXPIRED)
                .await()
            doOnComplete()
        } catch (it: Throwable) {
            logger.error("Failed to cancel activity {}: ", activityId, it)
        }
    }

}

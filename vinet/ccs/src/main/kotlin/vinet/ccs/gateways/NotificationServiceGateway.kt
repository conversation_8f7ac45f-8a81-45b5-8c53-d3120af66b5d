package vinet.ccs.gateways

import common.libs.logger.Logging
import io.grpc.ManagedChannel
import kotlinx.coroutines.*
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistry
import org.bson.json.JsonWriter
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.notification.pojo.Notification
import proto.portal.notification.NotificationMessages
import proto.portal.notification.NotificationMessages.SaveNotificationResponse
import proto.portal.notification.NotificationServiceGrpcKt
import vinet.ccs.koin.NOTIFICATION_SERVICE_CHANNEL
import java.io.StringWriter

@Singleton
class NotificationServiceGateway constructor(
    @Named(NOTIFICATION_SERVICE_CHANNEL) private val channel: ManagedChannel,
    private val codecRegistry: CodecRegistry
) : Logging {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val notificationCodec = codecRegistry.get(Notification::class.java)

    suspend fun saveNotificationAsync(
        notification: Notification,
        skipPersist: Boolean = false
    ): Deferred<SaveNotificationResponse> {
        val writer = StringWriter()
        notificationCodec.encode(
            JsonWriter(writer),
            notification,
            EncoderContext.builder().build()
        )
        val jsonNotification = writer.toString()

        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(channel)
        val req = NotificationMessages.SaveNotificationRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setJsonNotification(jsonNotification)
            .setSkipPersist(skipPersist)
            .build()

        return scope.async {
            try {
                logger.debug("saveNotificationAsync request: {}", req)
                val res = stub.saveNotification(req)

                logger.debug("saveNotificationAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("saveNotificationAsync failed: ", t)
                throw t
            }
        }
    }

}

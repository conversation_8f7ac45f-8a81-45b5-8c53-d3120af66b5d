package vinet.ccs.gateways

import common.libs.logger.Logging
import io.grpc.ManagedChannel
import kotlinx.coroutines.*
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.datastructures.lsession.LSessionStatus
import portal.datastructures.lsession.RaiseHandStatus
import portal.datastructures.lsession.ShareScreenStatus
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.RequestPinTabStatus
import proto.portal.lsession.LSessionServiceGrpcKt
import proto.portal.lsession.LsessionMessage.*
import vinet.ccs.koin.LSESSION_SERVICE_CHANNEL

@Singleton
class LSessionServiceGateway constructor(
    @Named(LSESSION_SERVICE_CHANNEL) private val channel: ManagedChannel,
) : Logging {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    suspend fun getLSessionAsync(lsId: String): Deferred<GetSessionDetailByIdResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req = GetSessionDetailByIdRequest.newBuilder()
            .setLsId(lsId)
            .build()

        val deferred = scope.async {
            try {
                logger.debug("getLSessionAsync request: {}", req)
                val res = stub.getSessionDetailsById(req)

                logger.debug("getLSessionAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("getLSessionAsync failed: ", t)
                throw t
            }
        }

        return deferred
    }

    suspend fun updateSessionStatusAsync(lsId: String, status: LSessionStatus): Deferred<UpdateLSessionStateResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)

        val stateBuilder = LSessionStateProto.newBuilder()
        stateBuilder.status = status.name
        if (status == LSessionStatus.STARTED) stateBuilder.startedAt = System.currentTimeMillis()
        else if (status == LSessionStatus.ENDED) stateBuilder.endedAt = System.currentTimeMillis()

        val req = UpdateLSessionStateRequest.newBuilder()
            .setLsId(lsId)
            .setState(stateBuilder)
            .build()

        val deferred = scope.async {
            try {
                logger.debug("updateSessionStatusAsync request: {}", req)
                val res = stub.updateSessionState(req)

                logger.debug("updateSessionStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateSessionStatusAsync failed: ", t)
                throw t
            }
        }

        return deferred
    }

    suspend fun updateRaiseHandStatusAsync(
        regId: String,
        status: RaiseHandStatus
    ): Deferred<UpdateRaiseHandStatusResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req = UpdateRaiseHandStatusRequest.newBuilder()
            .setRegId(regId)
            .setStatus(status.name)
            .build()

        return scope.async {
            try {
                logger.debug("updateRaiseHandStatusAsync request: {}", req)
                val res = stub.updateRaiseHandStatus(req)

                logger.debug("updateRaiseHandStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateRaiseHandStatusAsync failed: ", t)
                throw t
            }
        }
    }

    suspend fun updateShareScreenStatusAsync(
        regId: String,
        status: ShareScreenStatus
    ): Deferred<UpdateShareScreenStatusResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req = UpdateShareScreenStatusRequest.newBuilder()
            .setRegId(regId)
            .setStatus(status.name)
            .build()

        return scope.async {
            try {
                logger.debug("updateShareScreenStatusAsync request: {}", req)
                val res = stub.updateShareScreenStatus(req)

                logger.debug("updateShareScreenStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateShareScreenStatusAsync failed: ", t)
                throw t
            }
        }
    }

    suspend fun updateUserAvailableStatusAsync(
        regId: String, lsId: String, status: UserAvailableStatus
    ): Deferred<UpdateUserAvailableStatusResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req = UpdateUserAvailableStatusRequest.newBuilder()
            .setLsId(lsId)
            .setRegId(regId)
            .setStatus(status.name)
            .build()

        return scope.async {
            try {
                logger.debug("updateUserAvailableStatusAsync request: {}", req)
                val res = stub.updateUserAvailableStatus(req)

                logger.debug("updateUserAvailableStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateUserAvailableStatusAsync failed: ", t)
                throw t
            }
        }
    }

    fun updateRequestPinCoordStateAsync(
        regId: String, coordStateId: String, status: RequestPinTabStatus? = null, tabName: String? = null
    ): Deferred<UpdateRequestPinTabResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req = UpdateRequestPinTabRequest.newBuilder()
            .setTabId(coordStateId)
            .setRegId(regId)
        status?.let { req.status = status.name }
        tabName?.let { req.tabName = tabName }

        return scope.async {
            try {
                logger.debug("updateRequestPinCoordStateAsync request: {}", req)
                val res = stub.updateRequestPinTab(req.build())

                logger.debug("updateRequestPinCoordStateAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateRequestPinCoordStateAsync failed: ", t)
                throw t
            }
        }
    }

    fun getLSessionRegistrationByLsIdAndUserIdAsync(
        lsId: String,
        userId: String
    ): Deferred<GetSessionRegistrationResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = GetSessionRegistrationByLsIdAndUserIdRequest.newBuilder()
            .setLsId(lsId)
            .setUserId(userId)
            .build()

        return scope.async {

            try {
                logger.debug("getLSessionRegistrationByLsIdAndUserIdAsync request: {}", request)
                val res = stub.getSessionRegistrationByLsIdAndUserId(request)

                logger.debug("getLSessionRegistrationByLsIdAndUserIdAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("getLSessionRegistrationByLsIdAndUserIdAsync failed: ", t)
                throw t
            }
        }
    }

    fun cancelAllRaisingHand(lsId: String): Deferred<CancelAllRaisingHandResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = CancelAllRaisingHandRequest.newBuilder()
            .setLsId(lsId)
            .build()
        return scope.async {
            try {
                logger.debug("cancelAllRaisingHand request: {}", request)
                val res = stub.cancelAllRaisingHand(request)

                logger.debug("cancelAllRaisingHand response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("cancelAllRaisingHand failed: ", t)
                throw t
            }
        }
    }
}

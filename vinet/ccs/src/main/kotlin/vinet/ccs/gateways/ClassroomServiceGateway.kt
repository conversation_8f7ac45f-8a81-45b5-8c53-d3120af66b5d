package vinet.ccs.gateways

import common.libs.logger.Logging
import io.grpc.ManagedChannel
import io.grpc.Status
import kotlinx.coroutines.*
import org.bson.BsonDocument
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistry
import org.bson.json.JsonWriter
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.lsession.pojo.activity.ActivityStatus
import portal.lsession.pojo.activity.ClassroomActivity
import portal.lsession.pojo.activity.YesNoResponse
import proto.portal.classroom.ClassroomMessages.*
import proto.portal.classroom.ClassroomServiceGrpcKt
import vinet.ccs.koin.LSESSION_SERVICE_CHANNEL
import java.io.StringWriter

@Singleton
class ClassroomServiceGateway constructor(
    @Named(LSESSION_SERVICE_CHANNEL) private val channel: ManagedChannel,
    private val codecRegistry: CodecRegistry
) : Logging {
    private val clrActivityCodec = codecRegistry.get(ClassroomActivity::class.java)
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    suspend fun saveClassroomActivityAsync(activity: ClassroomActivity): Deferred<CreateClassroomActivityResponse> {
        val writer = StringWriter()
        clrActivityCodec.encode(
            JsonWriter(writer),
            activity,
            EncoderContext.builder().build()
        )
        val json = writer.toString()

        val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
        val req = CreateClassroomActivityRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setJsonActivity(json)
            .build()

        return scope.async {
            try {
                logger.debug("saveClassroomActivityAsync request: {}", req)
                val res = stub.createClassroomActivity(req)

                logger.debug("saveClassroomActivityAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("saveClassroomActivityAsync failed: ", t)
                throw t
            }
        }
    }

    suspend fun endClassroomAsync(roomId: String): Deferred<EndClassroomResponse> {
        val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
        val req = EndClassroomRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setLsId(roomId)
            .build()

        return scope.async {
            try {
                logger.debug("endClassroomAsync request: {}", req)
                val res = stub.endClassroom(req)

                logger.debug("endClassroomAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("endClassroomAsync failed: ", t)
                throw t
            }
        }
    }

    suspend fun updateClassroomActivityStatusAsync(
        activityId: String,
        expectedStatus: ActivityStatus,
        newStatus: ActivityStatus
    ): Deferred<UpdateClassroomActivityStatusResponse> {
        val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
        val req = UpdateClassroomActivityStatusRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setActivityId(activityId)
            .setExpectedStatus(expectedStatus.name)
            .setNewStatus(newStatus.name)
            .build()

        return scope.async {
            try {
                logger.debug("updateClassroomActivityStatusAsync request: {}", req)
                val res = stub.updateClassroomActivityStatus(req)

                logger.debug("updateClassroomActivityStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateClassroomActivityStatusAsync failed: ", t)
                throw t
            }
        }
    }

    suspend fun loadLatestOngoingPresentationActivityAsync(lsId: String): Deferred<ClassroomActivity?> {
        val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
        val req = GetLatestOngoingPresentationRequestActivityRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setLsId(lsId)
            .build()

        return scope.async {
            try {
                logger.debug("loadLatestOngoingPresentationRequestActivityAsync request: {}", req)
                val res = stub.getLatestOngoingPresentationRequestActivity(req)

                logger.debug("loadLatestOngoingPresentationRequestActivityAsync response: {}", res)
                deserializeClassroomActivity(res.jsonActivity)
            } catch (t: io.grpc.StatusException) {
                if (t.status.code == Status.Code.NOT_FOUND) {
                    logger.info("Not found OngoingPresentationActivity")
                    null
                } else {
                    logger.error("loadLatestOngoingPresentationRequestActivityAsync failed: ", t)
                    throw t
                }
            } catch (t: Throwable) {
                logger.error("loadLatestOngoingPresentationRequestActivityAsync failed: ", t)
                throw t
            }
        }
    }

    suspend fun loadClassroomActivityByIdAsync(activityId: String): Deferred<ClassroomActivity> {
        val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
        val req = GetClassroomActivityByIdRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setActivityId(activityId)
            .build()

        return scope.async {
            try {
                logger.debug("loadClassroomActivityByIdAsync request: {}", req)
                val res = stub.getClassroomActivityById(req)

                logger.debug("loadClassroomActivityByIdAsync response: {}", res)
                deserializeClassroomActivity(res.jsonActivity)
            } catch (t: Throwable) {
                logger.error("loadClassroomActivityByIdAsync failed: ", t)
                throw t
            }
        }
    }

    fun updateRequestPresentationResponseAsync(
        activityId: String,
        userId: String,
        response: YesNoResponse
    ): Deferred<UpdateRequestPresentationActivityResponse> {
        val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
        val req = UpdateRequestPresentationActivityRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setActivityId(activityId)
            .setUserId(userId)
            .setResponse(response.name)
            .build()

        return scope.async {
            try {
                logger.debug("updateRequestPresentationResponseAsync request: {}", req)
                val res = stub.updateRequestPresentationActivity(req)

                logger.debug("updateRequestPresentationResponseAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateRequestPresentationResponseAsync failed: ", t)
                throw t
            }
        }
    }

    private fun deserializeClassroomActivity(json: String): ClassroomActivity {
        return clrActivityCodec.decode(BsonDocument.parse(json).asBsonReader(), DecoderContext.builder().build())
    }

}

package vinet.ccs.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId

/**
 * Should index userId, roomId
 */
data class PeerInfo @BsonCreator constructor(
        @BsonId @BsonRepresentation(BsonType.OBJECT_ID)
        val id: String = ObjectId().toHexString(),

        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("userId") val userId: String = ObjectId().toHexString(),

        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("lsRegId") val lsRegId: String = ObjectId().toHexString(),

        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("roomId") val roomId: String = ObjectId().toHexString(),

        @BsonProperty("peerType") val peerType: PeerType,

        @Volatile @BsonProperty("lastSeen") var lastSeen : Long = System.currentTimeMillis(),

        @Volatile @BsonProperty("status") var status : PeerStatus = PeerStatus.ACTIVE
)

package vinet.ccs.actor

import common.libs.logger.Logging
import kotlinx.coroutines.*
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import portal.datastructures.lsession.UserAvailableStatus
import vinet.ccs.config.ConfigBean
import kotlin.coroutines.cancellation.CancellationException

/**
 * Manages a periodic check on the activity status of a specific [UserClassroomActor].
 *
 * It achieves this by examining the latest report received from the user's peer connection.
 * The check frequency is determined by `config.userPeerAlivePeriod`. If a report is found
 * to be older than this period, or if the report explicitly indicates an OFFLINE status,
 * this checker triggers the process to mark the user as OFFLINE via [UserAvailableStatusHandler].
 *
 * This checker operates using a dedicated coroutine scope and runs continuously until
 * explicitly cancelled, typically when the associated [UserClassroomActor] is terminated.
 */
class ReportChecker constructor(
    private val actor: UserClassroomActor, // The actor instance this checker monitors
) : Logging, KoinComponent {
    override val loggerName: String = "${javaClass.name}@${hashCode()}[${actor.actorName}]"

    // --- Injected Dependencies ---
    private val config: ConfigBean by inject() // Access to application configuration (e.g., timeouts).
    private val userAvailableStatusHandler: UserAvailableStatusHandler by inject() // Handles updating user status (ONLINE/OFFLINE).

    // --- Coroutine Management ---
    // SupervisorJob ensures that failure of this checker doesn't affect other potential siblings (if any).
    private val supervisorJob = SupervisorJob()
    // The dedicated scope for running the periodic check coroutine. Uses Default dispatcher.
    private val scope = CoroutineScope(Dispatchers.Default + supervisorJob + CoroutineName("${actor.actorName}-ReportChecker"))
    // Holds the Job instance of the running periodic check, allowing it to be cancelled.
    private var checkJob: Job? = null

    init {
        // Automatically start the periodic check when an instance of ReportChecker is created.
        startPeriodicCheckTimer()
    }

    /**
     * Initiates the periodic coroutine that performs the report checks.
     * This is called automatically during object initialization.
     */
    private fun startPeriodicCheckTimer() {
        // Prevent multiple concurrent check loops if accidentally called again.
        if (checkJob?.isActive == true) {
            logger.warn("Periodic check timer is already active. Ignoring request to start again.")
            return
        }

        logger.debug("Starting periodic report check timer (interval: ${config.userPeerAlivePeriod}ms)")

        // Launch the main checking coroutine within the dedicated scope.
        checkJob = scope.launch {
            try {
                // Loop indefinitely as long as the coroutine is active (not cancelled).
                while (isActive) {
                    try {
                        // --- Perform a single check cycle ---
                        logger.debug("Dispatching CheckReport task to actor")
                        // Execute the check logic within the actor's sequential context
                        // to ensure safe access to actor state (like latestPeer).
                        val checkReportResult = actor.send(ActorTaskType.CheckReport) { doCheckReport() }
                        // Wait for the actor to complete processing the CheckReport task.
                        checkReportResult.join()
                        logger.debug("Actor ${actor.actorName} completed CheckReport task.")

                        // --- Calculate delay until the next check ---
                        // Base the next check time on the timestamp of the *last* report received.
                        val report = actor.report
                        val now = System.currentTimeMillis()
                        // If no report exists, deadline is 0, ensuring an immediate next check.
                        val reportDeadline = report.reportTime.plus(config.userPeerAlivePeriod)
                        val remainingTime = reportDeadline - now

                        // Delay execution until the calculated next check time.
                        // If the report was already stale (remainingTime <= 0), delay(0) effectively yields
                        // and the next iteration starts almost immediately.
                        if (remainingTime > 0) {
                            logger.trace("Next check scheduled in ${remainingTime}ms")
                            delay(remainingTime)
                        } else {
                            logger.trace("Report already stale or missing, scheduling immediate next check.")
                            // No delay needed, loop will continue promptly.
                        }
                    } catch (e: CancellationException) {
                        // Coroutine cancellation during the delay or actor communication.
                        // Re-throw to be caught by the outer cancellation handler.
                        logger.debug("Check cycle iteration cancelled.")
                        throw e
                    } catch (t: Throwable) {
                        // Handle errors occurring within a single check cycle (e.g., during actor.send, join, doCheckReport).
                        logger.error("Error during report check iteration: ", t)
                        // Avoid tight loops in case of persistent errors within an iteration.
                        delay(1000L) // Wait 1 second before the next attempt.
                    }
                }
            } catch (e: CancellationException) {
                // Outer catch: The entire 'launch' coroutine (checkJob) was cancelled.
                logger.debug("Periodic report check timer job cancelled.")
                // Normal exit path when cancel() is called.
            } catch (t: Throwable) {
                // Catch unexpected errors occurring outside the main loop (less likely).
                logger.error("Fatal error in periodic report checker scope: ", t)
                // The coroutine will terminate.
            } finally {
                // This block executes when the coroutine launched by 'scope.launch' completes,
                // either normally (isActive becomes false), due to cancellation, or an error outside the loop.
                // Ensure we clear the checkJob reference only if this *specific* job instance is finishing.
                // This prevents race conditions if start/cancel were somehow called rapidly.
                if (coroutineContext[Job] == checkJob) {
                    checkJob = null
                    logger.debug("checkJob reference cleared as periodic checker stopped.")
                }
            }
        }
    }


    /**
     * Stops the periodic report checking coroutine and cancels the associated scope.
     * This should be called when the monitored [UserClassroomActor] is being shut down.
     */
    fun cancel() {
        logger.debug("Attempting to cancel periodic report checker...")
        // Cancel the SupervisorJob, which propagates cancellation to the scope and its children (checkJob).
        // Provide a specific CancellationException message for clarity in logs.
        supervisorJob.cancel(CancellationException("ReportChecker explicitly cancelled"))
        // The cancellation is asynchronous; the coroutine's finally block handles cleanup.
        logger.info("Periodic report checker cancellation requested.")
    }

    /**
     * Contains the core logic for checking the peer report's validity and status.
     * IMPORTANT: This function is designed to be executed within the sequential
     * execution context of the associated [UserClassroomActor] via `actor.send`.
     */
    private suspend fun doCheckReport() {
        logger.debug("Performing report check logic inside actor context...")
        try {
            // 1. Get the latest report data from the actor's state.
            val report = actor.report
            val now = System.currentTimeMillis()

            // 2. Determine the deadline for the report to be considered "fresh".
            val reportDeadline = report.reportTime.plus(config.userPeerAlivePeriod)

            // 3. Evaluate report conditions.
            val isReportFresh = reportDeadline > now
            val isReportedAsAvailable = report.availableStatus != UserAvailableStatus.OFFLINE // Consider null or ONLINE/AWAY as available

            // 4. Decision: If the report is fresh AND indicates availability, the user is considered active.
            if (isReportFresh && isReportedAsAvailable) {
                logger.trace("Report check passed: Actor is considered active.")
                // No action needed, the periodic timer will schedule the next check.
                return
            }

            // 5. Action: Report is stale OR indicates user is OFFLINE.
            // Log the specific reason for failure.
            if (!isReportFresh) {
                logger.warn("Report check failed: Report is STALE. Last report time: ${report.reportTime}, Deadline: $reportDeadline, Now: $now")
            } else { // Implies !isReportedAsAvailable (i.e., reported OFFLINE)
                logger.warn("Report check failed: Peer reported status OFFLINE.")
            }

            // Trigger the process to update the user's status to OFFLINE.
            userAvailableStatusHandler.process(actor, UserAvailableStatus.OFFLINE)

        } catch (t: Throwable) {
            // Catch any unexpected errors during the check logic itself.
            logger.error("Unexpected error during doCheckReport execution: ", t)
            // Re-throw so the error is visible to the calling context (the launch block's inner catch).
            throw t
        }
    }
}
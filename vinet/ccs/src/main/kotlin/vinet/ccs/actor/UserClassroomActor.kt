package vinet.ccs.actor

import common.libs.logger.Logging
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import kotlinx.coroutines.channels.ClosedSendChannelException
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.LSessionRegistration
import proto.portal.user.UserMessage
import vinet.ccs.model.ReportRequest
import vinet.ccs.peer.Peer
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.system.measureTimeMillis

/**
 * Represents an Actor for a specific user within a specific classroom.
 *
 * This class encapsulates the state and behavior associated with a single user (`userId`)
 * participating in a classroom (`roomId`). It follows the Actor Model principles using
 * Kotlin Coroutines and a [Channel] to ensure that all tasks related to this user
 * within this room are processed sequentially, providing thread safety for the actor's state
 * (like [peer], [lsRegistration], [lastExecutedTaskTime]).
 *
 * Key responsibilities include:
 * - Receiving tasks ([ActorTask]) via the [tasks] channel.
 * - Executing tasks one by one in a dedicated coroutine loop ([runTaskLoop]).
 * - Managing its own lifecycle via the [destroy] method.
 * - Coordinating with a [ReportChecker] to monitor user activity based on reports.
 * - Updating its internal state based on processed tasks.
 *
 * Instances are typically created and managed by the [ActorManager].
 *
 * @property roomId The unique identifier of the classroom this actor belongs to.
 * @property userId The unique identifier of the user this actor represents.
 * @property lsRegistration The user's registration details for the classroom session (mutable).
 * @property userProto Protocol buffer message containing user details (immutable).
 * @property peer The most recent [Peer] connection associated with this user actor (mutable). Consider making this `var` if it needs to be updated.
 */
class UserClassroomActor constructor(
    val roomId: String,
    val userId: String,
    var lsRegistration: LSessionRegistration,
    val userProto: UserMessage.UserProto,
    var peer: Peer, // Made 'var' as peer connections can change
) : KoinComponent, Logging {
    private val actorManager: ActorManager by inject()

    /** The unique identifier name for this actor, typically "roomId.userId". */
    val actorName = "${roomId}.${userId}"
    override val loggerName: String = "${javaClass.name}@${hashCode()}[$actorName]"

    /** Initial report request based on the initial peer. */
    val report: ReportRequest = ReportRequest(roomId, peer.info.id, UserAvailableStatus.ONLINE)

    /** Parent job for all coroutines launched within this actor's scope. Allows for structured cancellation. */
    private val actorJob = SupervisorJob()
    /** The main CoroutineScope for this actor, using Dispatchers.Default and the [actorJob]. */
    private val userScope = CoroutineScope(Dispatchers.Default + actorJob + CoroutineName(actorName))
    /** Channel for receiving tasks ([ActorTask]). Ensures sequential processing. */
    private val tasks = Channel<ActorTask>(Channel.BUFFERED) // Use BUFFERED or UNLIMITED
    /** Component responsible for periodically checking reports from the peer. */
    private val reportChecker = ReportChecker(this)

    /**
     * Controls the active running state of the actor.
     * Set to `false` when shutdown is initiated by [destroy].
     * Checked before sending new tasks and potentially at the start of the [runTaskLoop].
     */
    private val actorRunning = AtomicBoolean(true)

    /**
     * Ensures the [runTaskLoop] coroutine is launched only once.
     */
    private val executionLoopStarted = AtomicBoolean(false)

    /**
     * Stores the [Job] for the main task processing loop ([runTaskLoop]).
     * Used in [destroy] to wait for the loop to finish gracefully.
     */
    @Volatile // Ensure visibility across threads
    private var loopJob: Job? = null

    /** Timestamp of the last executed non-internal task. */
    var lastExecutedTaskTime: Long = -1L

    /**
     * Ensures that the task processing loop ([runTaskLoop]) is running.
     * This method is idempotent and thread-safe. It only launches the loop
     * if the actor is running ([actorRunning] is true) and the loop hasn't been
     * started before ([executionLoopStarted] is false).
     * It also sets up an `invokeOnCompletion` handler to log and clean up
     * if the loop terminates unexpectedly.
     */
    private fun ensureTaskExecutionLoopRunning() {
        // Only launch if running AND not already started
        if (actorRunning.get() && executionLoopStarted.compareAndSet(false, true)) {
            logger.info("Starting task execution loop...")
            // Launch the loop and store its Job
            loopJob = userScope.launch { runTaskLoop() }
            // Handle potential immediate or later completion/failure of the loop job
            loopJob?.invokeOnCompletion { cause ->
                if (cause != null && cause !is CancellationException) {
                    logger.error("Task execution loop completed exceptionally", cause)
                } else if (cause is CancellationException) {
                    logger.info("Task execution loop was cancelled.", cause)
                } else {
                    logger.info("Task execution loop completed normally.")
                }
                // Ensure actor state reflects loop termination
                actorRunning.set(false)
                // Close channel if not already closed (e.g., if loop failed unexpectedly)
                // Calling close() multiple times is safe.
                tasks.close(cause?.let { CancellationException("Loop failed", it) })
            }
        }
    }

    /**
     * The main coroutine loop that processes tasks from the [tasks] channel sequentially.
     * This loop runs until the [tasks] channel is closed and all buffered tasks
     * have been processed, or until an unexpected error occurs.
     * It executes the `action` of each task, handles errors, completes the task's
     * [CompletableJob], and calls [handlePostTaskLogic].
     */
    private suspend fun runTaskLoop() {
        logger.info("Task execution loop started.")
        try {
            // Loop continues as long as the channel is open and receives tasks
            for (task in tasks) { // Iterates until channel is closed and empty
                logger.debug("Received task {}: {}", task.taskType, task)
                var taskSuccess = false
                val executionTime = measureTimeMillis {
                    try {
                        logger.debug("Executing task action {}: {}", task.taskType, task)
                        task.action() // Execute the task's action
                        logger.debug("Successfully finished task action {}: {}", task.taskType, task)
                        taskSuccess = true
                    } catch (e: CancellationException) {
                        logger.warn("Task action {} cancelled: {}", task.taskType, task, e)
                        // Complete the job as cancelled
                        task.completable.cancel("Task action cancelled", e)
                        // Don't re-throw; SupervisorJob handles cancellation unless propagation is desired
                    } catch (t: Throwable) {
                        logger.error("Exception during task action {}: {}", task.taskType, task, t)
                        // Do not rethrow, allow loop to continue
                    }
                }

                // Complete the job associated with the task
                logger.debug("Completing job for task {}: {}", task.taskType, task)
                task.completable.complete(Unit) // Complete even if action failed
                logger.debug("Job completed for task {}: {}", task.taskType, task)

                // Perform post-task logic (potentially triggering shutdown)
                // Important: Runs *after* task.completable has been completed/cancelled/failed
                handlePostTaskLogic(task)
            }
            // Loop terminates normally when channel is closed and buffer is empty
            logger.info("Task channel closed and all buffered tasks processed.")

        } catch (e: ClosedReceiveChannelException) {
            // This is the expected way the loop terminates gracefully after destroy() closes the channel.
            logger.info("Task channel closed, stopping execution loop gracefully.")
        } catch (t: Throwable) {
            // Catch unexpected errors within the loop itself (outside task.action).
            logger.error("Unexpected error in task execution loop!", t)
            // Ensure state reflects failure
            actorRunning.set(false)
            tasks.close(t) // Close channel with error
            actorJob.cancel("Unexpected loop error", t) // Cancel scope
        } finally {
            logger.info("Task execution loop finished.")
            // State cleanup (like setting actorRunning=false) is now primarily handled
            // by destroy() or the loopJob's invokeOnCompletion handler.
        }
    }

    /**
     * Performs logic after a [task] has been executed and its Job completed.
     * Primarily used to check if the task requires actor shutdown
     * (e.g., `UserOffline`, `LeaveClass`). If so, it launches a separate coroutine
     * to call [destroy] and then safely remove the actor from the [ActorManager]
     * without blocking the [runTaskLoop].
     * It also updates [lastExecutedTaskTime] for non-internal, non-shutdown tasks.
     *
     * @param task The task that was just processed.
     */
    private fun handlePostTaskLogic(task: ActorTask) {
        // Check if the task type indicates the user is leaving/being removed
        val shouldShutdown = when (task.taskType) {
            ActorTaskType.UserOffline,
            ActorTaskType.KickOutPeer,
            ActorTaskType.LeaveClass -> true
            else -> false
        }

        if (shouldShutdown) {
            // Only initiate shutdown if the actor is still considered running
            // (prevents multiple destroy/remove calls if multiple shutdown tasks arrive close together)
            if (actorRunning.get()) {
                logger.info("Task {} indicates actor should shut down. Initiating graceful shutdown.", task.taskType)
                // Launch shutdown in a separate coroutine to avoid blocking the loop
                // Use the actor's scope, but handle potential cancellation if destroy is called externally too
                userScope.launch {
                    try {
                        // Initiate graceful shutdown and wait for completion
                        destroy() // This suspend fun will wait for the loop to finish
                        // Only remove from manager *after* graceful shutdown is complete
                        logger.info("Graceful shutdown complete. Removing actor from manager.")
                        // Call ActorManager's suspend function
                        actorManager.removeActor(this@UserClassroomActor)
                    } catch (e: CancellationException) {
                        logger.warn("Actor shutdown process was cancelled.", e)
                        // Ensure removal if cancellation happened during destroy/removal
                        actorManager.removeActor(this@UserClassroomActor)
                    } catch (e: Exception) {
                        logger.error("Error during actor self-destruction and removal triggered by task {}", task.taskType, e)
                        // Attempt removal even if destroy failed
                        actorManager.removeActor(this@UserClassroomActor)
                    }
                }
            } else {
                logger.warn("Task {} indicates shutdown, but actor is already shutting down/stopped. Ignoring.", task.taskType)
            }
        } else {
            // Update last execution time only for non-internal, non-shutdown tasks
            if (!task.taskType.internal) {
                this.lastExecutedTaskTime = System.currentTimeMillis()
            }
        }
    }

    /**
     * Sends a task ([ActorTask]) to the actor's [tasks] channel for sequential processing.
     * Ensures the task processing loop is running before sending.
     * Checks if the actor is shutting down before sending.
     * Handles exceptions like the channel being closed.
     * This is a `suspend fun`.
     *
     * @param taskType The type of the task being sent.
     * @param task The suspend lambda containing the task's execution logic.
     * @return A [Job] representing the completion of this task. This job will be
     *         completed (successfully, exceptionally, or cancelled) by the [runTaskLoop]
     *         after the task's `action` has been executed.
     */
    suspend fun send(taskType: ActorTaskType, task: suspend () -> Unit): Job { // Changed Any to Unit for clarity
        val actorTask = ActorTask(taskType, task) // Default Job is created within ActorTask
        logger.debug("Schedule sending task {} to channel: {}", taskType, actorTask)

        ensureTaskExecutionLoopRunning() // Ensures loopJob is initialized if needed

        // Check if shutdown has *already* been initiated.
        // actorRunning reflects the intention to shut down.
        if (!actorRunning.get()) {
            logger.warn(
                "Attempting to send task {} to an actor that is shutting down or destroyed. Returning cancelled job.",
                taskType
            )
            // Return an immediately cancelled Job
            return Job().apply { cancel(CancellationException("Actor is shutting down or destroyed")) }
        }

        logger.debug("Sending task {} to channel: {}", taskType, actorTask)
        try {
            tasks.send(actorTask) // Send the task into the channel
            logger.debug("Successfully sent task {} to channel: {}", taskType, actorTask)
        } catch (e: ClosedSendChannelException) {
            // This occurs if destroy() closes the channel concurrently while sending.
            logger.error("Failed to send task {} because channel is closed: {}", taskType, actorTask, e)
            // Cancel the task's job as it will never be processed
            actorTask.completable.cancel("Channel closed before sending task", e)
        } catch (t: Throwable) {
            logger.error("Unexpected error sending task {}: {}", taskType, actorTask, t)
            // Cancel the task's job due to the sending error
            actorTask.completable.cancel("Error sending task", t)
        }
        // Return the task's Job, which will be completed by runTaskLoop
        return actorTask.completable
    }

    /**
     * Initiates a graceful shutdown of the actor.
     * 1. Prevents new tasks from being accepted ([send] will fail).
     * 2. Closes the task channel ([tasks]).
     * 3. **Waits** for the task processing loop ([runTaskLoop]) to finish all buffered tasks.
     * 4. Cancels remaining coroutines in the [userScope] and cleans up resources (e.g., [reportChecker]).
     * This method is idempotent (safe to call multiple times) and is a `suspend fun`
     * because it needs to wait for the [runTaskLoop] to finish.
     */
    suspend fun destroy() {
        // Use compareAndSet for atomic, idempotent shutdown initiation.
        if (actorRunning.compareAndSet(true, false)) {
            logger.info("Initiating graceful shutdown for actor...")

            // 1. Close the tasks Channel.
            //    - Prevents new tasks via `send`.
            //    - Signals the `runTaskLoop` to terminate after processing remaining items.
            logger.debug("Closing task channel...")
            tasks.close() // Safe to call multiple times.
            logger.debug("Task channel closed.")

            // 2. Wait for the task loop coroutine to complete.
            //    This ensures all tasks sent *before* close() are processed.
            val currentLoopJob = loopJob // Capture current job
            if (currentLoopJob != null) {
                logger.info("Waiting for task execution loop to finish...")
                try {
                    currentLoopJob.join() // Suspend until the loop finishes
                    logger.info("Task execution loop finished processing.")
                } catch (e: CancellationException) {
                    logger.warn("Waiting for task loop join was cancelled.", e)
                    // Loop might have been cancelled externally, proceed with cleanup.
                } catch (t: Throwable) {
                    logger.error("Error while waiting for task loop to join", t)
                    // Proceed with cleanup despite error.
                }
            } else {
                // This might happen if destroy is called before the loop could start,
                // or if the loop already finished/failed earlier.
                logger.warn("Task execution loop job was null during destroy, loop might not have started or already finished unexpectedly.")
            }

            // 3. Cancel the actor's main SupervisorJob and scope.
            //    - Cancels any other potentially running coroutines in userScope (e.g., reportChecker's internal jobs if any).
            //    - Redundant for the loop itself if it finished normally, but good practice.
            logger.debug("Cancelling actor coroutine scope...")
            try {
                // Provide a cancellation cause
                actorJob.cancel(CancellationException("Actor destroyed gracefully"))
                logger.debug("Actor coroutine scope cancelled.")
            } catch (e: Throwable) {
                logger.error("Error cancelling actor coroutine scope during destroy", e)
            }

            // 4. Cancel dependent components.
            logger.debug("Cancelling report checker...")
            try {
                reportChecker.cancel() // Ensure checker is stopped
                logger.debug("Report checker cancelled.")
            } catch (e: Throwable) {
                logger.error("Error cancelling report checker during destroy", e)
            }

            logger.info("Actor graceful shutdown complete.")
        } else {
            logger.warn("Destroy called on an actor that is already shutting down or destroyed.")
            // Optional: If called again, maybe wait for the *initial* destroy to finish?
            // loopJob?.join() // Could potentially wait here too if needed
        }
    }

    /**
     * Checks if the actor's shutdown process has been initiated.
     * Based on the state of the [actorRunning] flag.
     *
     * @return true if shutdown has been initiated, false otherwise.
     */
    fun isShuttingDownOrDestroyed(): Boolean {
        return !actorRunning.get()
    }
}
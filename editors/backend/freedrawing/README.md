# Freedrawing Backend

## Overview
Freedrawing Backend is a component of the Viclass system, responsible for handling free drawing operations on digital documents. This backend provides APIs to manage documents, layers, and drawing objects such as shapes, text, pencil strokes, and eraser marks, while storing data in MongoDB.

## Main Features
- Manage free drawing documents (FreedrawingDocument): create, load, update, delete.
- Manage layers within documents: add, remove, update layers.
- Manage drawing objects (FreedrawingObject):
  - Shapes (ShapeObject)
  - Text (TextObject)
  - Pencil strokes (PencilObject)
  - Eraser marks (EraserObject)
  - Lines with enhanced features like arrow head (LineObjectV2)
- Handle editing operations: add, remove, update objects, layers, documents.
- Integration with MongoDB using reactive streams for data storage.
- Use Koin for dependency injection and module configuration.

## Architecture & Main Components
- `Application.kt`: Backend entry point.
- `FreedrawingController.kt`: Handles drawing commands, manages business logic.
- `FreedrawingDBS.kt`: Data access layer, interacts with MongoDB.
- `config/ConfigBean.kt`, `config/DatabaseConfig.kt`: Define system and database configurations.
- `koin/ConfigurationsModule.kt`, `koin/DatabaseModule.kt`, `koin/KoinApplication.kt`: Define DI modules with Koin.
- `pojo/`: Define data models (Document, Layer, Object, ToolState, ...).

## Main Data Structures
- **FreedrawingDocument**: A free drawing document containing multiple layers.
- **FreedrawingLayer**: A layer containing multiple drawing objects.
- **FreedrawingObject**: Common interface for drawing objects (Shape, Text, Pencil, Eraser, LineV2).
- **ToolState**: Drawing tool state (pen, eraser, text, ...).

## Configuration
- Configuration file: `conf/config.json` (contains MongoDB connection info, system parameters).

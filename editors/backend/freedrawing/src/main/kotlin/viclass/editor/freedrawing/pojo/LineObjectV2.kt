package viclass.editor.freedrawing.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

/**
 * Line object V2 with enhanced features separate from ShapeObject.
 * This allows for future extensions like arrows, curves, and other line-specific features.
 */
@BsonDiscriminator(value = "LineObjectV2", key = "objectType")
data class LineObjectV2 @BsonCreator constructor(
    @BsonProperty("localId")
    override val localId: Int,
    
    @BsonProperty("state")
    val state: LineV2ToolState,
    
    @BsonProperty("boundary")
    val boundary: List<Double>,
): FreedrawingObject
package viclass.editor.freedrawing.db

import com.mongodb.client.model.*
import com.mongodb.client.result.InsertManyResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import org.bson.types.ObjectId
import org.koin.core.component.KoinComponent
import viclass.editor.freedrawing.pojo.*

class FreedrawingDBS(
    private val col: MongoCollection<FreedrawingDocument>
): Logging, KoinComponent {

    fun insertDocument(document: FreedrawingDocument): Completable {
        return Flowable.defer { col.insertOne(document) }
            .doOnNext { logger.debug("[{}] insert document result: {}", document.id, it) }
            .doOnError {
                logger.error("[{}] failed to insert document: ", document.id, it)
            }.ignoreElements()
    }

    fun deleteDocuments(globalIds: List<String>): Completable {
        val filter = Filters.`in`("_id", globalIds.map { ObjectId(it) })
        return Flowable.defer { col.deleteMany(filter)}
            .doOnNext { logger.debug("delete document result: {}", it) }
            .doOnError {
                logger.error("[{}] failed to delete document... ", globalIds, it)
            }.ignoreElements()
    }

    fun insertDocuments(documents: List<FreedrawingDocument>): Single<InsertManyResult> {
        return Flowable.defer { col.insertMany(documents) }
            .doOnNext { logger.debug("insert documents result: {}", it) }
            .doOnError {
                logger.error("failed to insert document: ", it)
            }.singleOrError()
    }

    fun insertLayer(layer: FreedrawingLayer, docId: String, sequence: Int): Completable {
        val filter = Filters.eq("_id", ObjectId(docId))
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        val updates = Updates.push(FreedrawingDocument::layers.name, layer)
        return Flowable.defer { col.findOneAndUpdate(filter, updates, ops) }
            .doOnNext { logger.debug("[{}] insert layer {} result: {}", docId, layer.localId, it) }
            .doOnError {
                logger.error("[{}] failed to insert layer {}: ", docId, layer.localId, it)
            }.ignoreElements()
    }

    fun removeLayer(layerId: Int, docId: String): Completable {
        val filter = Filters.eq("_id", ObjectId(docId))
        val condition = Filters.eq(FreedrawingLayer::localId.name, layerId)
        val updates = Updates.pull(FreedrawingDocument::layers.name, condition)
        return Flowable.defer { col.updateOne(filter, updates) }
            .doOnNext { logger.debug("[{}] remove layer {} result: {}", docId, layerId, it) }
            .doOnError {
                logger.error("[{}] failed to remove layer {}: ", docId, layerId, it)
            }.ignoreElements()
    }

    private fun insertObject(obj: FreedrawingObject, layerId: Int, docId: String): Single<UpdateResult> {
        val filter = Filters.and(
            Filters.eq("_id", ObjectId(docId)),
            Filters.elemMatch(FreedrawingDocument::layers.name, Filters.eq("localId", layerId)),
            Filters.not(
                Filters.elemMatch(
                    "${FreedrawingDocument::layers.name}.${FreedrawingLayer::objects.name}",
                    Filters.eq("localId", obj.localId)
                )
            )
        )

        val updates = Updates.addToSet(
            "${FreedrawingDocument::layers.name}.$.${FreedrawingLayer::objects.name}", obj
        )

        return Flowable.defer { col.updateOne(filter, updates) }
            .singleOrError()
    }

    private fun updatePartialObject(obj: FreedrawingObject, layerId: Int, docId: String): Single<UpdateResult> {
        val points: List<Double>
        val state: Any

        when (obj) {
            is PencilObject -> {
                points = obj.points
                state = obj.state
            }

            is EraserObject -> {
                points = obj.points
                state = obj.state
            }

            else -> return Single.error(Exception("failed to update partial object: invalid object type"))
        }

        val filter = Filters.and(
            Filters.eq("_id", ObjectId(docId)),
            Filters.elemMatch(FreedrawingDocument::layers.name, Filters.eq("localId", layerId)),
        )

        val ops = UpdateOptions().arrayFilters(
            listOf(Filters.eq("object.${FreedrawingObject::localId.name}", obj.localId))
        )

        val updates = Updates.combine(
            Updates.pushEach(
                "${FreedrawingDocument::layers.name}.$.${FreedrawingLayer::objects.name}.$[object].points", points
            ),
            Updates.set(
                "${FreedrawingDocument::layers.name}.$.${FreedrawingLayer::objects.name}.$[object].state",
                state
            ),
        )

        return Flowable.defer { col.updateOne(filter, updates, ops) }
            .firstOrError()
    }

    fun insertShapeObject(obj: ShapeObject, layerId: Int, docId: String, sequence: Int): Completable {
        return insertObject(obj, layerId, docId)
            .doOnSuccess {
                logger.debug("[{}] insert ShapeObject {} result: {}", docId, obj.localId, it)
            }
            .doOnError {
                logger.error("[{}] failed to insert ShapeObject {} into layer {}: ", docId, obj.localId, layerId, it)
            }.ignoreElement()
    }

    fun insertLineObjectV2(obj: LineObjectV2, layerId: Int, docId: String, sequence: Int): Completable {
        return insertObject(obj, layerId, docId)
            .doOnSuccess {
                logger.debug("[{}] insert LineObjectV2 {} result: {}", docId, obj.localId, it)
            }
            .doOnError {
                logger.error("[{}] failed to insert LineObjectV2 {} into layer {}: ", docId, obj.localId, layerId, it)
            }.ignoreElement()
    }

    fun removeObject(objId: Int, layerId: Int, docId: String): Completable {
        val filter = Filters.and(
            Filters.eq("_id", ObjectId(docId)),
            Filters.eq("${FreedrawingDocument::layers.name}.${FreedrawingLayer::localId.name}", layerId)
        )
        val condition = Filters.eq(FreedrawingObject::localId.name, objId)
        val updates = Updates.pull("${FreedrawingDocument::layers.name}.$.${FreedrawingLayer::objects.name}", condition)
        return Flowable.defer { col.updateOne(filter, updates) }
            .doOnNext { logger.debug("[{}] remove object {} from layer {} result: {}", docId, objId, layerId, it) }
            .doOnError {
                logger.error("[{}] failed to remove object {} from layer {}: ", docId, objId, layerId, it)
            }.ignoreElements()
    }

    fun insertTextObject(obj: TextObject, layerId: Int, docId: String, sequence: Int): Completable {
        return insertObject(obj, layerId, docId)
            .doOnSuccess {
                logger.debug("[{}] insert TextObject {} result: {}", docId, obj.localId, it)
            }
            .doOnError {
                logger.error("[{}] failed to insert TextObject {} into layer {}: ", docId, obj.localId, layerId, it)
            }.ignoreElement()
    }

    fun insertPencilObject(obj: PencilObject, layerId: Int, docId: String, sequence: Int): Completable {
        return insertObject(obj, layerId, docId)
            .flatMap {
                if (it.matchedCount == 1L && it.modifiedCount == 1L) Single.just(it)
                else updatePartialObject(obj, layerId, docId)
            }.doOnSuccess {
                logger.debug("[{}] insert partial PencilObject {} result: {}", docId, obj.localId, it)
            }
            .doOnError {
                logger.error(
                    "[{}] failed to insert partial PencilObject {} into layer {}: ",
                    docId,
                    obj.localId,
                    layerId,
                    it
                )
            }.ignoreElement()
    }

    fun insertEraserObject(obj: EraserObject, layerId: Int, docId: String, sequence: Int): Completable {
        return insertObject(obj, layerId, docId)
            .flatMap {
                if (it.matchedCount == 1L && it.modifiedCount == 1L) Single.just(it)
                else updatePartialObject(obj, layerId, docId)
            }.doOnSuccess {
                logger.debug("[{}] insert partial EraserObject {} result: {}", docId, obj.localId, it)
            }
            .doOnError {
                logger.error(
                    "[{}] failed to insert partial EraserObject {} into layer {}: ",
                    docId,
                    obj.localId,
                    layerId,
                    it
                )
            }.ignoreElement()
    }

    fun loadDocument(docId: String): Single<FreedrawingDocument> {
        val filter = Filters.eq("_id", ObjectId(docId))
        return Flowable.defer { col.find(filter) }
            .firstOrError()
            .doOnError {
                logger.error("[{}] failed to find document: ", docId, it)
            }
    }

    fun loadDocuments(docIds: List<String>): Single<List<FreedrawingDocument>> {
        val ids = docIds.map { ObjectId(it) }
        val filter = Filters.`in`("_id", ids)
        return Flowable.defer { col.find(filter) }
            .toList()
            .doOnError {
                logger.error("failed to find documents {}: ", docIds, it)
            }
    }

}

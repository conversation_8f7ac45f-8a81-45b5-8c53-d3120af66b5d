package viclass.editor.geo.constructor

import viclass.editor.geo.elements.Element

/**
 * ExtractionResult represents the actual result of the extracting process
 * The getExtractedResult should only return a
 * - ConstructionResult<T>
 * - List<ConstructionResult></ConstructionResult><T>>
 * - Number
 * - List<Number>
 *
 * We need this interface because for different type of ParamStore, the extraction result might contain
 * an array of construction or just a construction.
 *
 * Different ParameterExtractor can return its own extraction result implementation.
 */
abstract class ExtractionResult<Result>(val result: Result)

class ElementExtraction<T : Element>(
    result: ConstructionResult<out T>
) : ExtractionResult<ConstructionResult<out T>>(result)

/**
 * When extract a ParamStoreArray which return list of element of the same type
 */
class ElementListExtraction<T : Element>(
    result: List<ConstructionResult<out T>>
) : ExtractionResult<List<ConstructionResult<out T>>>(result)

class NumberExtraction<T: Number>(
    result: T
) : ExtractionResult<T>(result)

class NumberListExtraction<T: Number>(
    result: List<T>
) : ExtractionResult<List<T>>(result)

class StringExtraction(
    result: String
) : ExtractionResult<String>(result)

class StringListExtraction(
    result: List<String>
) : ExtractionResult<List<String>>(result)

class ExtractableWithConstructions<T : Extractable>(
    result: T
) : ExtractionResult<T>(result)

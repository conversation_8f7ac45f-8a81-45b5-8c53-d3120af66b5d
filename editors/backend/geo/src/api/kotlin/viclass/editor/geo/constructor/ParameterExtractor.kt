package viclass.editor.geo.constructor

import org.reflections.Reflections
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ExtractionFailedException
import kotlin.reflect.KClass

/**
 * Parameter extractors are used to retrieve the
 * actual value to be input into constructor class or
 * constructor method to create the elements.
 *
 * Some example of parameter extractors are:
 * - extractor that extract elements from a document using element names
 * - extractor that represents a constant
 * - extractor that calculate length of a line from a document
 * - etc....
 *
 * Extraction uses existing information or existing elements within the documents to infer data to be extracted.
 * Hence the extraction returns a construction result so that we can have complete information about
 * dependencies for the extracted data. This dependencies
 * will be included into the construction result
 * of the constructor. To give an example, consider extracting a line with
 * param name 'AB' from the document. This means we takes the two points A and B from the geodoc and
 * construct the line from these two points. This construction then gives the main element is the line AB
 * and its inferred elements are the two points A and B. The line AB depends on the two points A and B.
 *
 * The parameter extractors can use suitable constructors to do necessary constructions.
 *
 * Parameter extractor is created at runtime depending on the type of the extraction to be done. For example
 * if a param specs use Param.aPoint and the parameter name is 'name', the PointExtractor will be created.
 */
interface ParameterExtractor {

    var id : String

    /**
     * An extractor extract a particular parameter of certain name. It uses the param specs to know the details
     * of the parameters to be extracted.
     *
     * For example in the above example, the name of the extraction will be 'name' as defined in the ParamKind
     * The actual value to be extracted will be stored the ParamStoreValue of the ParamSpecs corresponding to
     * the extraction 'name', and the ParamStoreValue will have the value of 'AB' which is the actual object
     * to be extracted from the document.
     *
     *
     * @param doc The document that contains the context of the extraction
     * @param extractionName The name of the extraction (defined inside the ParamKind)
     * @param specs Contain the actual value to be extracted.
     * @param ctIdx Construction index, only extract elements whose construction index < ctIdx,
     * cause when construct element. This is mainly used for reconstruction purpose, because we don't want to extract
     * from the same context as when we construct the first time, so all element created from the ctIdx should not be 
     * considered for extraction.
     * @return
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(ExtractionFailedException::class)
    fun <T : ExtractionResult<out Any>> extract(doc: GeoDoc, extractionName: String, paramDefId: String, specs: ParamSpecs, ctIdx: Int): T {
        val store = specs.getParam(extractionName)
            ?: throw ExtractionFailedException("Parameter with name $extractionName doesn't have any value")

        val clazz = specs.getParamDataType(extractionName)
            ?: ConstraintParamDefManager.instance()[paramDefId]?.getKClassType(extractionName)?.let {
                if (it::class.isInstance(Element::class)) it as KClass<out Element> else null
            }
        return extract(doc, store, ctIdx, clazz)
    }

    @Throws(ExtractionFailedException::class)
    @Suppress("UNCHECKED_CAST")
    fun <T : ExtractionResult<out Any>> extract(doc: GeoDoc, store: ParamStore, ctIdx: Int, clazz: KClass<out Element>? = null): T {
        return when (store) {
            is ParamStoreValue -> ElementExtraction(extractConstructionResult(doc, store.value, ctIdx, clazz)) as T
            is ParamStoreArray -> {
                val crList = store.values.map {
                    extractConstructionResult(doc, it, ctIdx, clazz)
                }
                ElementListExtraction(crList) as T
            }
            else -> throw ExtractionFailedException("Param store type not supported")
        }
    }

    fun <T : Element> extractConstructionResult(doc: GeoDoc, pk: String, ctIdx: Int, clazz: KClass<out T>? = null): ConstructionResult<T> {
        throw ExtractionFailedException("method not support yet")
    }

    /**
     * Each extractor is designed to support certain types of parameters, some can extract an expression, some can extract
     * a constant, some can extract unknown length, some can extract
     * @return
     */
    fun supportedTypes(): List<KClass<*>>
}
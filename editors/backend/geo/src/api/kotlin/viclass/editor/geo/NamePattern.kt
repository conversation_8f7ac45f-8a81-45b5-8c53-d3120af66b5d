package viclass.editor.geo

import viclass.editor.geo.elements.*
import kotlin.reflect.KClass

// contain the pattern allowed for a name
// and methods to check if a name is actually acceptable
object NamePattern {
    private val patterns: Map<KClass<out Element>, List<Regex>> =
        mapOf(
            Point::class to listOf(Regex("^[A-Z]\\d*'?$")),
            LineVi::class to
                    listOf(
                        Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)$"), // line segment
                        Regex("^[a-z]\\d*'?$"), // line, ray with lower case name
                        Regex("^([A-Z]\\d*'?)([a-z]\\d*'?)$") // ray
                    ),
            LineSegment::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)$"), Regex("^[a-z]\\d*'?$")),
            VectorVi::class to listOf(
                Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)$"),
                Regex("^[a-z]\\d*'?$")
            ),
            Ray::class to
                    listOf(
                        Regex("^([A-Z]\\d*'?)([A-Za-z]\\d*'?)$"), // Ax, AB
                        Regex("^[a-z]\\d*'?$")
                    ),
            Triangle::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            RightTriangle::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            EquilateralTriangle::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            IsoscelesRightTriangle::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            IsoscelesTriangle::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Polygon::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)*$")),
            Quadrilateral::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Parallelogram::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Rectangle::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Rhombus::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Square::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Trapezoid::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            RegularPolygon::class to listOf(Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)+$")),
            Circle::class to
                    listOf(
                        Regex("^([A-Za-z]\\d*'?)$"),
                        Regex("^([A-Za-z]\\d*'?);([A-Za-z]\\d*'?)$"),
                        Regex("^([A-Za-z]\\d*'?);([A-Za-z]\\d*'?[A-Za-z]\\d*'?)$")
                    ),
            Ellipse::class to listOf(Regex("^([A-Za-z]\\d*'?)$"), Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Semicircle::class to listOf(Regex("^([A-Za-z]\\d*'?)$"), Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            CircularSector::class to listOf(Regex("^([A-Za-z]\\d*'?)$"), Regex("^([A-Z]\\d*'?)([A-Z]\\d*'?)([A-Z]\\d*'?)$")),
            Angle::class to
                    listOf(
                        Regex("^([A-Z]\\d*'?)$"),
                        Regex("^([A-Za-z]\\d*'?)([A-Z]\\d*'?)([A-Za-z]\\d*'?)$"), // ABC, xOy, xOA
                        Regex("^([a-z]\\d*'?)$"),
                    )
        )

    operator fun get(type: KClass<out Element>): List<Regex>? {
        return patterns[type]
    }

    /** Check if a name is valid according to defined patterns */
    fun isNameValid(type: KClass<out Element>, name: String): Boolean {
        /**
         * Temporary fix because the current symmetry drawing returns the constructor type as ELement::class To fix it
         * correctly later, in the symmetry drawing section, each shape should create a separate constructor, receive
         * the correct shape and draw the correct symmetry shape with the properties and characteristics of the drawn
         * shape
         */
        if (type == Element::class) return true

        val p = patterns[type] ?: return false

        for (pattern in p) {
            val matchResult = pattern.find(name)

            matchResult?.let { m -> // if pattern match
                if (m.groupValues.size == 1) return true

                val duplicate =
                    m.groupValues.subList(1, m.groupValues.size).groupingBy { it }.eachCount().filter { it.value > 1 }

                // if no repeating match result group, e.g. AA is not a valid line name
                if (duplicate.isEmpty()) return true
            }
        }

        return false
    }

    fun isSame(type: KClass<out Element>, name1: String, name2: String): Boolean {
        val p = patterns[type] ?: return false

        if (!isNameValid(type, name1) || !isNameValid(type, name2)) return false

        for (pattern in p) {
            val matchResult1 = pattern.find(name1) ?: return false
            val matchResult2 = pattern.find(name2) ?: return false

            val size1 = matchResult1.groupValues.size
            val size2 = matchResult2.groupValues.size

            if (matchResult1.groupValues.size != matchResult2.groupValues.size) return false

            val l1 = matchResult1.groupValues.takeLast(size1 - 1).toSet()
            val l2 = matchResult2.groupValues.takeLast(size2 - 1).toSet()

            if (l1.containsAll(l2) && l2.containsAll(l1)) return true
        }

        return false
    }

    fun extractPointName(type: KClass<out Element>, name: String): List<String> {
        val p = patterns[type] ?: return emptyList()
        if (!isNameValid(type, name)) return emptyList()

        if (Polygon::class.java.isAssignableFrom(type.java)) {
            val rs = ArrayList<String>()
            var n = name
            while (true) {
                val l = Regex("^([A-Z]\\d*'?)(.*)\$").findAll(n).map { it.groupValues }.toList().flatten()
                rs.add(l[1])
                n = l[2]
                if (n.isBlank()) break
            }
            return rs
        }

        for (pattern in p) {
            val matchResult = pattern.find(name)

            matchResult?.let { // if pattern match
                val size = it.groupValues.size

                if (size == 1) return listOf()

                return it.groupValues.takeLast(size - 1)
            }
        }

        return emptyList()
    }
}

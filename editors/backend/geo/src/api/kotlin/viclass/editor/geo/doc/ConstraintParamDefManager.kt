package viclass.editor.geo.doc

import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstraintParamDef
import viclass.editor.geo.entity.ParamKind.PK_Expr
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.extractable.NameForLength
import viclass.editor.geo.extractable.ValueExpression

/**
 * The constraint param definition manager manage all the constraint parameter definitions
 * that used in the system. It allows add / retrieve the parameter definitions by the id of the definition.
 *
 * In this class, we also predefine a few definition commonly used.
 */
class ConstraintParamDefManager : HashMap<String, ConstraintParamDef>() {
    init {
        put(
            aValue, ConstraintParamDefBuilder.create().defId(aValue)
                .defaultTpl("Value {$PK_Value}")
                .pType(PK_Value, Double::class)
                .description("Specifying a constant as a constraint").build()
        )
        put(
            anExpression, ConstraintParamDefBuilder.create().defId(anExpression)
                .defaultTpl("Expression {$PK_Expr}")
                .pType(PK_Expr, ValueExpression::class)
                .description("Specifying a constant as an Expression").build()
        )
        put(
            aName, ConstraintParamDefBuilder.create().defId(aName)
                .defaultTpl("Name {$PK_Name}")
                .pType(PK_Name, String::class)
                .description("Specifying a name as a constraint").build()
        )
        put(
            aPoint, ConstraintParamDefBuilder.create().defId(aPoint)
                .defaultTpl("Point {$PK_Name}")
                .pType(PK_Name, Point::class)
                .description("Specifying a point as a constraint").build()
        )
        put(
            aLine, ConstraintParamDefBuilder.create().defId(aLine)
                .defaultTpl("Line {$PK_Name}")
                .pType(PK_Name, LineVi::class)
                .description("Specifying a line as a constraint").build()
        )
        put(
            aLineSegment, ConstraintParamDefBuilder.create().defId(aLineSegment)
                .defaultTpl("Line segment {$PK_Name}")
                .pType(PK_Name, LineSegment::class)
                .description("Specifying a line segment as a constraint").build()
        )
        put(
            aRay, ConstraintParamDefBuilder.create().defId(aRay)
                .defaultTpl("Ray {$PK_Name}")
                .pType(PK_Name, Ray::class)
                .description("Specifying a ray as a constraint").build()
        )
        put(
            anElement, ConstraintParamDefBuilder.create().defId(anElement)
                .defaultTpl("Shape {$PK_Name}")
                .pType(PK_Name, Element::class)
                .description("Specifying a shape as a constraint").build()
        )
        put(
            lengthAssignment, ConstraintParamDefBuilder.create().defId(lengthAssignment)
                .defaultTpl("Length of {$PK_Name} = {$PK_Expr}")
                .pType(PK_Name, NameForLength::class)
                .pType(PK_Expr, ValueExpression::class)
                .description("Specifying a length as a constraint").build()
        )
        put(
            nameWithValue, ConstraintParamDefBuilder.create().defId(nameWithValue)
                .defaultTpl("Name {$PK_Name} with value {$PK_Expr}")
                .pType(PK_Name, String::class)
                .pType(PK_Value, Double::class)
                .description("Specifying a name and value as a constraint").build()
        )
        put(
            aCircle, ConstraintParamDefBuilder.create().defId(aCircle)
                .defaultTpl("Circle {$PK_Name}")
                .pType(PK_Name, Circle::class)
                .description("Specifying a circle as a constraint").build()
        )
        put(
            anEllipse, ConstraintParamDefBuilder.create().defId(anEllipse)
                .defaultTpl("Ellipse {$PK_Name}")
                .pType(PK_Name, Ellipse::class)
                .description("Specifying a ellipse as a constraint").build()
        )
        put(
            aCircularSector, ConstraintParamDefBuilder.create().defId(aCircularSector)
                .defaultTpl("Circular Sector {$PK_Name}")
                .pType(PK_Name, CircularSector::class)
                .description("Specifying a circular sector as a constraint").build()
        )
        put(
            aTriangle, ConstraintParamDefBuilder.create().defId(aTriangle)
                .defaultTpl("Triangle {$PK_Name}")
                .pType(PK_Name, Triangle::class)
                .description("Specifying a triangle as a constraint").build()
        )
        put(
            aRectangle, ConstraintParamDefBuilder.create().defId(aRectangle)
                .defaultTpl("Rectangle {$PK_Name}")
                .pType(PK_Name, Rectangle::class)
                .description("Specifying a rectangle as a constraint").build()
        )
        put(
            aSquare, ConstraintParamDefBuilder.create().defId(aSquare)
                .defaultTpl("Square {$PK_Name}")
                .pType(PK_Name, Square::class)
                .description("Specifying a square as a constraint").build()
        )
        put(
            anAngle, ConstraintParamDefBuilder.create().defId(anAngle)
                .defaultTpl("Angle {$PK_Name}")
                .pType(PK_Name, Angle::class)
                .description("Specifying an angle as a constraint").build()
        )
        put(
            aPolygon, ConstraintParamDefBuilder.create().defId(aPolygon)
                .defaultTpl("Polygon {$PK_Name}")
                .pType(PK_Name, Polygon::class)
                .description("Specifying a polygon as a constraint").build()
        )
        put(
            aVector, ConstraintParamDefBuilder.create().defId(aVector)
                .defaultTpl("Angle {$PK_Name}")
                .pType(PK_Name, VectorVi::class)
                .description("Specifying an angle as a constraint").build()
        )
    }

    companion object {
        const val aValue = "aValue"
        const val anExpression = "anExpression"
        const val aName = "aName"
        const val aPoint = "aPoint"
        const val aLine = "aLine"
        const val aLineSegment = "aLineSegment"
        const val aTriangle = "aTriangle"
        const val aRectangle = "aRectangle"
        const val aSquare = "aSquare"
        const val aCircle = "aCircle"
        const val anEllipse = "anEllipse"
        const val aCircularSector = "aCircularSector"
        const val anElement = "anElement"
        const val lengthAssignment = "lengthAssignment"
        const val nameWithValue = "nameWithValue"
        const val aRay = "aRay"
        const val anAngle = "anAngle"
        const val aVector = "aVector"
        const val aPolygon = "aPolygon"

        private val _inst = ConstraintParamDefManager()

        fun instance(): ConstraintParamDefManager {
            return _inst
        }
    }
}

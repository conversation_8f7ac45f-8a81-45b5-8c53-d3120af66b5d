package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.movement.path.MovementLineSegmentPath
import viclass.editor.geo.dbentity.transformdata.PointOnLineSegmentWithRatioTransformData
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLine
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.anExpression
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.lengthAssignment
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Expr
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.extractable.NameForLength
import viclass.editor.geo.extractable.ValueExpression
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnLineSegmentWithRatioTransformer
import viclass.editor.geo.impl.transformer.PointOnLineWithCoefficientTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

/**
 * Constructing a point on a line with distance to another point is some length
 */
@Singleton
class PointOnLineEC : ElementConstructor<Point> {

    private enum class CGS {
        OnLineSegmentWithRatio, OnLineSegmentWithRatioExpr, OnLineSegmentWithLength, OnLineSegmentWithDistance,
        OnLineWithLength, OnLineWithDistance, OnLineWithCoefficient, OnLineSegmentWithCoefficient
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .elTypes(Point::class)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineSegmentWithRatio.name)
                    .hints("PointOnLineSegmentWithRatio")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-OnLineSegment"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[anExpression]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-RatioValue",
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineSegmentWithRatioExpr.name)
                    .hints("PointOnLineSegmentWithRatioExpr")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-OnLineSegment"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[anExpression]!!,
                        listOf(0),
                        listOf("Expression"),
                        "tpl-Ratio",
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineSegmentWithLength.name)
                    .hints("PointOnLineSegmentWithLength")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-OnLineSegment"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf(0),
                        listOf("NameOfLineSegment", "Expression"),
                        "tpl-Length",
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineSegmentWithDistance.name)
                    .hints("PointOnLineSegmentWithDistance")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-OnLineSegment"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf(0),
                        listOf("NameOfPoint", "Expression"),
                        "tpl-Distance"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineWithLength.name)
                    .hints("PointOnLineWithLength")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLine]!!,
                        listOf("NameOfLine"),
                        "tpl-OnLine"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[anExpression]!!,
                        listOf(0),
                        listOf("Expression"),
                        "tpl-LengthIs",
                    ).constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-DirectionOfLine"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineWithCoefficient.name)
                    .hints("PointOnLineWithCoefficient")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLine]!!,
                        listOf("NameOfLine"),
                        "tpl-OnLine"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-CoefficientIs",
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineSegmentWithCoefficient.name)
                    .hints("PointOnLineSegmentWithCoefficient")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-OnLineSegment"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-CoefficientIs",
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnLineWithDistance.name)
                    .hints("PointOnLineWithDistance")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLine]!!,
                        listOf("NameOfLine"),
                        "tpl-OnLine"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf(0),
                        listOf("NameOfPoint", "Expression"),
                        "tpl-Distance"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thPoint"
                    )
                    .build(),
            )
            .elTypes(Point::class)
            .build()
    }

    @Throws(RuntimeException::class)
    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {

        return when (CGS.valueOf(c.cgName)) {
            CGS.OnLineSegmentWithRatio -> {
                Validations.validateNumConstraints(c, 2)
                constructPointOnLineSegmentWithRatioValue(doc, inputName, c)
            }

            CGS.OnLineSegmentWithRatioExpr -> {
                Validations.validateNumConstraints(c, 2)
                constructPointOnLineSegmentWithRatioExpression(doc, inputName, c)
            }

            CGS.OnLineWithLength -> {
                Validations.validateNumConstraints(c, 3)
                constructPointOnlineWithLengthExpression(doc, inputName, c)
            }

            CGS.OnLineSegmentWithLength,
            CGS.OnLineSegmentWithDistance -> {
                Validations.validateNumConstraints(c, 2)
                constructPointOnlineWithLength(doc, inputName, c, aLineSegment)
            }

            CGS.OnLineWithDistance -> {
                Validations.validateNumConstraints(c, 3)
                constructPointOnlineWithLength(doc, inputName, c, aLine)
            }

            CGS.OnLineSegmentWithCoefficient,
            CGS.OnLineWithCoefficient -> {
                Validations.validateNumConstraints(c, 2)
                constructPointOnlineWithCoefficient(doc, inputName, c)
            }
        }
    }

    private fun constructPointOnLineSegmentWithRatioValue(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResultImpl<Point> {
        val params = c.params

        var lineSegmentResult: ConstructionResult<out LineSegment>? = null
        var ratioValue: Double? = null

        // Iterate through the parameters to extract the line segment and the ratio value
        params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    // Extract the ConstructionResult for the line segment
                    lineSegmentResult =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, PK_Name, p, c.ctIdx).result
                }
                // If the parameter is a value (ratio)
                aValue -> {
                    // Extract the double value for the ratio
                    ratioValue = extractFirstPossible<NumberExtraction<Double>>(doc, PK_Value, p, c.ctIdx).result
                }
            }
        }

        // Check if all required parameters were successfully extracted and their values are available
        val lineSegment = lineSegmentResult?.result()
        val ratio = ratioValue

        if (lineSegmentResult == null || lineSegment == null || ratio == null) {
            val missingParams = buildList {
                if (lineSegmentResult == null) add("tham số đoạn thẳng")
                if (lineSegment == null) add("giá trị đoạn thẳng")
                if (ratio == null) add("giá trị tỷ lệ")
            }
            throw ConstructionException(
                "Thiếu đầu vào bắt buộc cho điểm trên đoạn thẳng với tỷ lệ: ${
                    missingParams.joinToString(
                        ", "
                    )
                }"
            )
        }

        // Validate the ratio value
        if (ratio < 0 || ratio > 1) {
            throw ConstructionException("Giá trị tỷ lệ phải nằm giữa 0 và 1 (bao gồm cả hai đầu).")
        }

        // Calculate the point on the line segment using the ratio
        // The distance from the start point is lineSegment.length() * ratio
        val distance = lineSegment.length() * ratio
        val points = Lines.findPointOnlineWithLength(aLineSegment, lineSegment, lineSegment.p1, distance)
            ?: throw ConstructionException("Không thể tìm thấy điểm trên đoạn thẳng tại khoảng cách đã tính.")

        // Get the calculated point (assuming only one point is returned)
        if (points.isEmpty()) {
            throw ConstructionException("Không thể tìm thấy điểm trên đoạn thẳng tại khoảng cách đã tính.")
        }
        val v = points[0]

        // Determine the name for the new point, using inputName if provided, otherwise generating one
        val name = inputName ?: generatePointName(doc)

        // Create the new PointImpl object
        val p = PointImpl(doc, name, v.x, v.y, v.z)

        // Set transformation data for the point
        p.transformData = PointOnLineSegmentWithRatioTransformData(
            targetParamIdx = 1, // Index of the ratio parameter
            paramKind = PK_Value, // Kind of the parameter (value)
            sPoint = lineSegment.p1.coordinates().toArray(), // Start point coordinates of the line segment
            ePoint = lineSegment.p2.coordinates().toArray(), // End point coordinates of the line segment
        )
        // Set the transformer class for the point
        p.transformer = TransformMapping.fromClazz(PointOnLineSegmentWithRatioTransformer::class)
        // Set the movement path for the point (along the line segment)
        p.movementPath =
            MovementLineSegmentPath(lineSegment.p1.coordinates().toArray(), lineSegment.p2.coordinates().toArray())

        // Create a result object for the constructed point
        val result = ConstructionResultImpl<Point>()
        result.setResult(p)
        // Merge the line segment result as a dependency
        result.mergeAsDependency(lineSegmentResult)

        return result
    }

    private fun constructPointOnLineSegmentWithRatioExpression(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResultImpl<Point> {
        val params = c.params

        var lineResult: ConstructionResult<out LineSegment>? = null
        var ratio: ValueExpression? = null

        val result = ConstructionResultImpl<Point>()

        params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    lineResult = extractFirstPossible<ElementExtraction<LineSegment>>(doc, PK_Name, p, c.ctIdx).result
                }

                anExpression -> {
                    ratio =
                        extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(doc, PK_Expr, p, c.ctIdx)
                            .result
                }
            }
        }

        if (lineResult == null || ratio == null) {
            throw ConstructionException("Not enough parameters to construct the point on line with ratio expression")
        }

        val lineSegment = lineResult.result()!!

        val pK = lineSegment.p1

        val points =
            Lines.findPointOnlineWithLength(aLineSegment, lineSegment, pK, lineSegment.length() * ratio.value)
        points ?: throw ConstructionException("There is no point")

        val v = points[0]

        val name = inputName ?: generatePointName(doc)

        val p = PointImpl(doc, name, v.x, v.y, v.z)

        result.setResult(p)
        result.mergeAsDependency(lineResult)
        ratio.constructions().forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun constructPointOnlineWithLengthExpression(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResultImpl<Point> {
        val params = c.params

        var lineResult: ConstructionResult<out LineVi>? = null
        var length: ValueExpression? = null
        var lineNameSubmit: String? = null
        var direction: Int? = null

        val result = ConstructionResultImpl<Point>()

        params.forEach { p ->
            when (p.paramDef.id) {
                aLine -> {
                    lineResult = extractFirstPossible<ElementExtraction<LineVi>>(doc, PK_Name, p, c.ctIdx).result
                    lineNameSubmit = lineResult.result()?.name
                }

                anExpression -> {
                    length =
                        extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(doc, PK_Expr, p, c.ctIdx)
                            .result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    direction = extractedResult.result
                }
            }
        }

        if (lineNameSubmit == null || length == null) {
            logger.error(
                "Not enough parameters to construct the point on line with length expression: lineNameSubmit={}, length={}",
                lineNameSubmit,
                length
            )
            throw ConstructionException("Not enough parameters to construct the point on line with length expression")
        }

        val line = lineResult!!.result()!!

        val pK = line.p1

        val points = Lines.findPointOnlineWithLength(aLine, line, pK, length.value)
        points ?: throw ConstructionException("There is no point")

        val v = if (points.size == 1) points[0]
        else if (direction == null) points[0]
        else {
            val nthPoint = if (direction < 0) 0 else 1
            Orders.pointsOnParallelVector(line.parallelVector, points[0], points[1])[nthPoint]
        }

        val name = inputName ?: generatePointName(doc)

        val p = PointImpl(doc, name, v)

        result.setResult(p)
        result.mergeAsDependency(lineResult)
        length.constructions().forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun constructPointOnlineWithLength(
        doc: GeoDoc, inputName: String?, c: Construction, lineType: String
    ): ConstructionResultImpl<Point> {
        val params = c.params

        var l: ConstructionResult<out LineVi>? = null
        var nl: NameForLength? = null
        var le: ValueExpression? = null
        var nth: Int? = null

        val result = ConstructionResultImpl<Point>()

        for (p in params) {
            when (p.paramDef.id) {
                aLine -> {
                    l = extractFirstPossible<ElementExtraction<LineVi>>(doc, PK_Name, p, c.ctIdx).result
                    result.mergeAsDependency(l)
                }

                aLineSegment -> {
                    l = extractFirstPossible<ElementExtraction<LineSegment>>(doc, PK_Name, p, c.ctIdx).result
                }

                lengthAssignment -> {
                    nl = extractFirstPossible<ExtractableWithConstructions<NameForLength>>(
                        doc,
                        PK_Name,
                        p,
                        c.ctIdx
                    ).result

                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc,
                        PK_Expr,
                        p,
                        c.ctIdx
                    ).result

                    // why we can do this? because we know that all points inside
                    result.addDependency(nl.reference!!, listOf(), true)
                    le.constructions().forEach { result.mergeAsDependency(it) }
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    nth = extractedResult.result - 1
                }
            }
        }

        if (l == null || nl == null || le == null) {
            throw ConstructionException("Not enough parameters to construct the point online with distance")
        }

        val line = l.result()!!

        val points = Lines.findPointOnlineWithLength(lineType, line, nl.reference!!, le.value)
        points ?: throw ConstructionException("There is no point")

        val v = if (points.size == 1) points[0]
        else if (nth == null) points[0]
        else {
            Orders.pointsOnParallelVector(line.parallelVector, points[0], points[1])[nth]
        }

        val name = inputName ?: generatePointName(doc)

        val p = PointImpl(doc, name, v.x, v.y, v.z)

        result.setResult(p)

        return result
    }

    private fun constructPointOnlineWithCoefficient(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResultImpl<Point> {
        val params = c.params

        var lineResult: ConstructionResult<out LineVi>? = null
        var coefficient: Double? = null

        val result = ConstructionResultImpl<Point>()

        params.forEach { p ->
            when (p.paramDef.id) {
                aLine -> {
                    lineResult = extractFirstPossible<ElementExtraction<LineVi>>(doc, PK_Name, p, c.ctIdx).result
                }

                aLineSegment -> {
                    lineResult = extractFirstPossible<ElementExtraction<LineSegment>>(doc, PK_Name, p, c.ctIdx).result
                }

                aValue -> {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    coefficient = extractedResult.result
                }
            }
        }

        if (lineResult == null) throw ConstructionException("Missing line")
        if (coefficient == null) throw ConstructionException("Missing coefficient")

        val line = lineResult.result()!!

        val p1 = line.p1.coordinates()
        val unitVector = line.parallelVector.normalize()
        val newVector = unitVector.multiply(coefficient)
        val newPoint = newVector.add(p1)

        if (line is LineSegment) {
            val lineVector = p1.vectorTo(line.p2.coordinates())
            val k = lineVector.x / unitVector.x
            if (k < 0 || k < coefficient)
                throw ConstructionException("Point is out of line segment")
        }

        val name = inputName ?: generatePointName(doc)

        val p = PointImpl(doc, name, newPoint)
        p.transformData = PointOnLineWithCoefficientTransformData(
            targetParamIdx = 1,
            paramKind = ParamKind.PK_Value,
            rootPoint = p1.toArray(),
            unitVector = unitVector.toArray(),
        )
        p.transformer = TransformMapping.fromClazz(PointOnLineWithCoefficientTransformer::class)
        if (line is LineSegment) {
            p.movementPath = MovementLineSegmentPath(p1.toArray(), line.p2.coordinates().toArray())
        } else {
            p.movementPath = MovementLinePath(p1.toArray(), line.parallelVector.toArray())
        }

        result.setResult(p)
        result.mergeAsDependency(lineResult)
        return result
    }

    override fun outputType(): KClass<Point> {
        return Point::class
    }
}

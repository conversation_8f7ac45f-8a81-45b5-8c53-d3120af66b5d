package viclass.editor.geo.impl.constructor.symmetry

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.elements.*

fun projectPointOntoLine(point: DoubleArray, startPoint: DoubleArray, vector: DoubleArray): DoubleArray {
    // Calculate the dot product of the vector formed by the start point and the point to be
    // projected
    val dotProduct = (point[0] - startPoint[0]) * vector[0] + (point[1] - startPoint[1]) * vector[1]

    // Normalize the vector
    val magnitudeSquared = vector[0] * vector[0] + vector[1] * vector[1]
    val normalizedVector = doubleArrayOf(vector[0] / magnitudeSquared, vector[1] / magnitudeSquared)

    // Project the point onto the line
    val projection =
        doubleArrayOf(
            startPoint[0] + dotProduct * normalizedVector[0],
            startPoint[1] + dotProduct * normalizedVector[1]
        )

    return projection
}

fun symmetricPoint(originalPoint: DoubleArray, middlePoint: DoubleArray): DoubleArray {
    return doubleArrayOf(2 * middlePoint[0] - originalPoint[0], 2 * middlePoint[1] - originalPoint[1])
}

fun symmetricPointByLine(originalPoint: DoubleArray, startPoint: DoubleArray, vector: DoubleArray): DoubleArray {
    val middlePoint = projectPointOntoLine(originalPoint, startPoint, vector)
    return symmetricPoint(originalPoint, middlePoint)
}

/**
 * Wrapper function for symmetricPointByLine to work with LineVi element
 */
fun symmetricPointByLine(originalPoint: DoubleArray, line: LineVi): DoubleArray {
    return symmetricPointByLine(
        originalPoint,
        doubleArrayOf(line.p1.coordinates().x, line.p1.coordinates().y),
        doubleArrayOf(line.parallelVector.x, line.parallelVector.y)
    )
}

/**
 * Wrapper function for symmetricPoint to work with Point element
 */
fun symmetricPoint(originalPoint: DoubleArray, centerPoint: Point): DoubleArray {
    return symmetricPoint(
        originalPoint,
        doubleArrayOf(centerPoint.coordinates().x, centerPoint.coordinates().y)
    )
}

fun createSymmetricPoint(
    doc: GeoDoc,
    coord: DoubleArray,
    name: String? // Input parameter for the point name
): ConstructionResultImpl<Point> {
    val cr = ConstructionResultImpl<Point>()
    val actualName = name ?: generatePointName(doc) // Generate name if input is null
    val newpoint = PointImpl(doc, actualName, coord[0], coord[1])
    cr.setResult(newpoint)
    return cr
}

// ===== GENERIC CONSTRUCTION FUNCTIONS =====

/**
 * Generic function to construct symmetric point
 */
fun <T : Element> constructSymmetricPoint(
    doc: GeoDoc,
    inputName: String?,
    c: Construction,
    symmetryFunction: (DoubleArray, T) -> DoubleArray
): ConstructionResult<Point> {
    val symExr: ElementExtraction<T> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
    val targetExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
    val symElement = symExr.result.result()!!
    val targetPoint = targetExr.result.result()!!

    val symmetricPoint = symmetryFunction(
        doubleArrayOf(targetPoint.coordinates().x, targetPoint.coordinates().y),
        symElement
    )

    val name = inputName ?: generatePointName(doc)
    val cr: ConstructionResultImpl<Point> = createSymmetricPoint(doc, symmetricPoint, name)

    // Add dependencies based on symmetry element type
    addSymmetryElementDependencies(cr, symElement)
    cr.addDependency(targetPoint, emptyList(), true)

    return cr
}

/**
 * Generic function to construct symmetric line
 */
fun <T : Element> constructSymmetricLine(
    doc: GeoDoc,
    inputName: String?,
    c: Construction,
    symmetryFunction: (DoubleArray, T) -> DoubleArray
): ConstructionResult<LineVi> {
    val symExr: ElementExtraction<T> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
    val targetExr: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
    val symElement = symExr.result.result()!!
    val targetLine = targetExr.result.result()!!

    val pointNames = if (inputName != null) {
        NamePattern.extractPointName(LineSegment::class, inputName)
    } else {
        val result = arrayListOf<String>()
        result.add(generatePointName(doc))
        result.add(generatePointName(doc, result))
        result
    }

    // Extract common points from target line
    val p1 = targetLine.p1
    val p2 = targetLine.p2

    // Calculate symmetric points
    val symmetricPoint1 = symmetryFunction(
        doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
        symElement
    )
    val symmetricPoint2 = if (p2 != null) {
        symmetryFunction(
            doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
            symElement
        )
    } else null

    val cr: ConstructionResultImpl<LineVi> = when (targetLine.clazz) {
        LineSegment::class -> {
            createSymmetricLineSegment(doc, symmetricPoint1, symmetricPoint2!!, pointNames[0], pointNames[1])
        }

        VectorVi::class -> {
            createSymmetricVector(doc, symmetricPoint1, symmetricPoint2!!, pointNames[0], pointNames[1])
        }

        Ray::class -> {
            createSymmetricRay(
                doc,
                targetLine as Ray,
                symmetricPoint1,
                symmetricPoint2,
                pointNames[0],
                if (p2 != null) pointNames[1] else null
            )
        }

        else -> {
            createSymmetricLineVi(
                doc,
                targetLine,
                symmetricPoint1,
                symmetricPoint2,
                pointNames[0],
                pointNames[1]
            )
        }
    }

    addSymmetryElementDependencies(cr, symElement)
    cr.addDependency(p1, emptyList(), true)
    if (p2 != null) {
        cr.addDependency(p2, emptyList(), true)
    }

    return cr
}

/**
 * Generic function to construct symmetric circle
 */
fun <T : Element> constructSymmetricCircle(
    doc: GeoDoc,
    inputName: String?,
    c: Construction,
    symmetryFunction: (DoubleArray, T) -> DoubleArray
): ConstructionResult<Circle> {
    val symExr: ElementExtraction<T> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
    val targetExr: ElementExtraction<Circle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
    val symElement = symExr.result.result()!!
    val targetCircle = targetExr.result.result()!!

    val symmetricCenter = symmetryFunction(
        doubleArrayOf(targetCircle.centerPoint.coordinates().x, targetCircle.centerPoint.coordinates().y),
        symElement
    )

    val name = inputName ?: generatePointName(doc)
    val cr: ConstructionResultImpl<Circle> = createSymmetricCircle(doc, targetCircle.radius, symmetricCenter, name)

    // Add dependencies
    addSymmetryElementDependencies(cr, symElement)
    cr.addDependency(targetCircle.centerPoint, emptyList(), true)

    return cr
}

/**
 * Generic function to construct symmetric ellipse
 */
fun <T : Element> constructSymmetricEllipse(
    doc: GeoDoc,
    inputName: String?,
    c: Construction,
    symmetryFunction: (DoubleArray, T) -> DoubleArray
): ConstructionResult<Ellipse> {
    val symExr: ElementExtraction<T> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
    val targetExr: ElementExtraction<Ellipse> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
    val symElement = symExr.result.result()!!
    val targetEllipse = targetExr.result.result()!!

    val cr: ConstructionResultImpl<Ellipse> = if (targetEllipse.hasCenterVectorData()) {
        // CENTER_VECTORS mode
        val center = targetEllipse.centerPoint!!
        val vectorA = targetEllipse.vectorA!!
        val vectorB = targetEllipse.vectorB!!

        val symmetricCenter = symmetryFunction(
            doubleArrayOf(center.coordinates().x, center.coordinates().y),
            symElement
        )
        val symmetricVectorA = symmetryFunction(
            doubleArrayOf(vectorA.coordinates().x, vectorA.coordinates().y),
            symElement
        )
        val symmetricVectorB = symmetryFunction(
            doubleArrayOf(vectorB.coordinates().x, vectorB.coordinates().y),
            symElement
        )

        val pointNames: List<String> = if (inputName != null) {
            NamePattern.extractPointName(Ellipse::class, inputName)
        } else {
            val centerName = generatePointName(doc)
            val vectorAName = generatePointName(doc, centerName)
            val vectorBName = generatePointName(doc, centerName, vectorAName)
            listOf(centerName, vectorAName, vectorBName)
        }

        createSymmetricEllipseFromCenterVectors(
            doc,
            symmetricCenter,
            symmetricVectorA,
            symmetricVectorB,
            pointNames.getOrNull(0),
            pointNames.getOrNull(1),
            pointNames.getOrNull(2),
            inputName
        )
    } else {
        // FOCUS_POINTS mode
        val p1 = targetEllipse.f1
        val p2 = targetEllipse.f2

        val symmetricF1 = symmetryFunction(
            doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
            symElement
        )
        val symmetricF2 = symmetryFunction(
            doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
            symElement
        )

        val pointNames: List<String> = if (inputName != null) {
            NamePattern.extractPointName(Ellipse::class, inputName)
        } else {
            val p1Name = generatePointName(doc)
            val p2Name = generatePointName(doc, p1Name)
            listOf(p1Name, p2Name)
        }

        createSymmetricEllipse(
            doc,
            targetEllipse.a,
            targetEllipse.b,
            symmetricF1,
            symmetricF2,
            pointNames[0],
            pointNames[1],
            inputName
        )
    }

    cr.addDependency(targetEllipse, emptyList(), true)
    addSymmetryElementDependencies(cr, symElement)

    // Add dependencies on ellipse points
    if (targetEllipse.hasCenterVectorData()) {
        cr.addDependency(targetEllipse.centerPoint!!, emptyList(), true)
        cr.addDependency(targetEllipse.vectorA!!, emptyList(), true)
        cr.addDependency(targetEllipse.vectorB!!, emptyList(), true)
    } else {
        cr.addDependency(targetEllipse.f1, emptyList(), true)
        cr.addDependency(targetEllipse.f2, emptyList(), true)
    }

    return cr
}

/**
 * Generic function to construct symmetric circular sector
 */
fun <T : Element> constructSymmetricCircularSector(
    doc: GeoDoc,
    inputName: String?,
    c: Construction,
    symmetryFunction: (DoubleArray, T) -> DoubleArray
): ConstructionResult<CircularSector> {
    val symExr: ElementExtraction<T> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
    val targetExr: ElementExtraction<CircularSector> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
    val symElement = symExr.result.result()!!
    val targetSector = targetExr.result.result()!!

    val p1 = targetSector.centerPoint
    val p2 = targetSector.endPoint
    val p3 = targetSector.startPoint

    val symmetricPoint1 = symmetryFunction(
        doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
        symElement
    )
    val symmetricPoint2 = symmetryFunction(
        doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
        symElement
    )
    val symmetricPoint3 = symmetryFunction(
        doubleArrayOf(p3.coordinates().x, p3.coordinates().y),
        symElement
    )

    val pointNames = if (inputName != null) {
        NamePattern.extractPointName(CircularSector::class, inputName)
    } else {
        listOf(p1.name, p2.name, p3.name)
    }

    val cr: ConstructionResultImpl<CircularSector> = createSymmetricCircularSector(
        doc,
        symmetricPoint1,
        symmetricPoint2,
        symmetricPoint3,
        pointNames[0],
        pointNames[1],
        pointNames[2]
    )

    addSymmetryElementDependencies(cr, symElement)
    cr.addDependency(p1, emptyList(), true)
    cr.addDependency(p2, emptyList(), true)
    cr.addDependency(p3, emptyList(), true)

    return cr
}

/**
 * Generic function to construct symmetric polygon
 */
fun <T : Element> constructSymmetricPolygon(
    doc: GeoDoc,
    inputName: String?,
    c: Construction,
    symmetryFunction: (DoubleArray, T) -> DoubleArray
): ConstructionResult<Polygon> {
    val cr = ConstructionResultImpl<Polygon>()
    val symExr: ElementExtraction<T> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
    val targetExr: ElementExtraction<Polygon> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
    val symElement = symExr.result.result()!!
    val targetPolygon = targetExr.result.result()!!

    val pointNames = if (inputName != null) {
        NamePattern.extractPointName(Polygon::class, inputName)
    } else {
        val result = arrayListOf<String>()
        targetPolygon.vertices().forEach { result.add(generatePointName(doc, result)) }
        result
    }

    val newPoints = targetPolygon.vertices().mapIndexed { index, vertex ->
        val symmetricPoint = symmetryFunction(
            doubleArrayOf(vertex.coordinates().x, vertex.coordinates().y),
            symElement
        )
        PointImpl(doc, pointNames[index], symmetricPoint[0], symmetricPoint[1])
    }

    val newPolygon = PolygonImpl(doc, c.elName!!, newPoints)
    cr.setResult(newPolygon)

    newPoints.forEach { cr.addDependency(it, emptyList(), true) }
    addSymmetryElementDependencies(cr, symElement)
    targetPolygon.vertices().forEach { vertex ->
        cr.addDependency(vertex, emptyList(), true)
    }

    return cr
}

/**
 * Helper function to add dependencies based on symmetry element type
 */
private fun <T : Element> addSymmetryElementDependencies(cr: ConstructionResultImpl<*>, symElement: T) {
    when (symElement) {
        is Point -> {
            cr.addDependency(symElement, emptyList(), true)
        }

        is LineVi -> {
            cr.addDependency(symElement.p1, emptyList(), true)
            if (symElement.p2 != null) {
                cr.addDependency(symElement.p2!!, emptyList(), true)
            }
        }
    }
}

fun createSymmetricLineSegment(
    doc: GeoDoc,
    coord1: DoubleArray,
    coord2: DoubleArray,
    name1: String?, // Input parameter for the first point's name
    name2: String? // Input parameter for the second point's name
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val actualName2 =
        name2 ?: generatePointName(doc, actualName1) // Generate name2 if input is null, potentially using actualName1

    val newpoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])
    val newpoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])
    val newLine = LineSegmentImpl(doc, "${newpoint1.name}${newpoint2.name}", newpoint1, newpoint2)

    cr.setResult(newLine)
    cr.addDependency(newpoint1, emptyList(), true)
    cr.addDependency(newpoint2, emptyList(), true)
    return cr
}

fun createSymmetricLineVi(
    doc: GeoDoc,
    line: LineVi,
    coord1: DoubleArray,
    coord2: DoubleArray?,
    name1: String?, // Input parameter for the first point's name
    name2: String? // Input parameter for the second point's name (if applicable)
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val newPoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])
    cr.addDependency(newPoint1, emptyList(), true)

    val newLine: LineVi =
        if (coord2 != null) {
            val actualName2 = name2 ?: generatePointName(doc, actualName1)
            val newPoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])
            cr.addDependency(newPoint2, emptyList(), true)

            val vector = createVectorByEl(doc, newPoint1, newPoint2).let { Vector3D.of(it.x, it.y, 0.0) }
            LineImpl(doc, "${newPoint1.name}${newPoint2.name}", newPoint1, vector, newPoint2)
        } else {
            LineImpl(doc, newPoint1.name, newPoint1, line.parallelVector)
        }

    cr.setResult(newLine)
    return cr
}

fun createSymmetricVector(
    doc: GeoDoc,
    coord1: DoubleArray,
    coord2: DoubleArray,
    name1: String?,
    name2: String?
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val actualName2 = name2 ?: generatePointName(doc, actualName1)

    val newPoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])
    val newPoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])

    val newVector = VectorImpl(doc, "${newPoint1.name}${newPoint2.name}", newPoint1, newPoint2)

    cr.setResult(newVector)
    cr.addDependency(newPoint1, emptyList(), true)
    cr.addDependency(newPoint2, emptyList(), true)
    return cr
}

fun createSymmetricRay(
    doc: GeoDoc,
    line: Ray,
    coord1: DoubleArray,
    coord2: DoubleArray?,
    name1: String?, // Input parameter for the first point's name
    name2: String? // Input parameter for the second point's name (if applicable)
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val newPoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])

    if (coord2 != null) {
        val actualName2 = name2 ?: generatePointName(doc, actualName1)
        val newPoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])
        val vector = createVectorByEl(doc, newPoint1, newPoint2).let { Vector3D.of(it.x, it.y, 0.0) }
        val newRay = RayImpl(doc, "${newPoint1.name}${newPoint2.name}", newPoint1, vector, newPoint2)
        cr.setResult(newRay)
        cr.addDependency(newPoint2, emptyList(), true)
    } else {
        val newRay = RayImpl(doc, newPoint1.name, newPoint1, line.parallelVector) // Use actualName1 via newPoint1.name
        cr.setResult(newRay)
    }

    cr.addDependency(newPoint1, emptyList(), true)

    return cr
}

fun createSymmetricCircle(
    doc: GeoDoc,
    radius: Double,
    centerCoord: DoubleArray,
    centerName: String? // Input parameter for the center point's name
): ConstructionResultImpl<Circle> {
    val cr = ConstructionResultImpl<Circle>()

    val actualCenterName = centerName ?: generatePointName(doc)
    val newCenterPoint = PointImpl(doc, actualCenterName, centerCoord[0], centerCoord[1])

    val newCircle =
        CircleImpl(doc, actualCenterName, newCenterPoint, radius) // Name of circle often same as center point

    cr.setResult(newCircle)
    cr.addDependency(newCenterPoint, emptyList(), true)
    return cr
}

fun createSymmetricEllipse(
    doc: GeoDoc,
    ellipsea: Double,
    ellipseb: Double,
    focus1Coord: DoubleArray,
    focus2Coord: DoubleArray,
    focus1Name: String?,
    focus2Name: String?,
    ellipseName: String?
): ConstructionResultImpl<Ellipse> {
    val cr = ConstructionResultImpl<Ellipse>()

    val actualFocus1Name = focus1Name ?: generatePointName(doc)
    val newFocus1 = PointImpl(doc, actualFocus1Name, focus1Coord[0], focus1Coord[1])

    val actualFocus2Name = focus2Name ?: generatePointName(doc, actualFocus1Name)
    val newFocus2 = PointImpl(doc, actualFocus2Name, focus2Coord[0], focus2Coord[1])

    val actualEllipseName = ellipseName ?: generateLowercaseName(doc, arrayListOf())
    val newEllipse = EllipseImpl.fromFocusPoints(doc, actualEllipseName, newFocus1, newFocus2, ellipsea, ellipseb)

    cr.setResult(newEllipse)
    cr.addDependency(newFocus1, emptyList(), true)
    cr.addDependency(newFocus2, emptyList(), true)
    return cr
}

/**
 * Create symmetric ellipse from center-vector mode ellipse
 * Used when the original ellipse was created using center + vectorA + vectorB
 */
fun createSymmetricEllipseFromCenterVectors(
    doc: GeoDoc,
    symmetricCenterCoord: DoubleArray,
    symmetricVectorACoord: DoubleArray,
    symmetricVectorBCoord: DoubleArray,
    centerName: String?,
    vectorAName: String?,
    vectorBName: String?,
    ellipseName: String?
): ConstructionResultImpl<Ellipse> {
    val cr = ConstructionResultImpl<Ellipse>()

    // Create symmetric points for center-vector mode
    val actualCenterName = centerName ?: generatePointName(doc)
    val newCenter = PointImpl(doc, actualCenterName, symmetricCenterCoord[0], symmetricCenterCoord[1])

    val actualVectorAName = vectorAName ?: generatePointName(doc, actualCenterName)
    val newVectorA = PointImpl(doc, actualVectorAName, symmetricVectorACoord[0], symmetricVectorACoord[1])

    val actualVectorBName = vectorBName ?: generatePointName(doc, actualCenterName, actualVectorAName)
    val newVectorB = PointImpl(doc, actualVectorBName, symmetricVectorBCoord[0], symmetricVectorBCoord[1])

    // Create ellipse using center-vector factory method
    val actualEllipseName = ellipseName ?: generateLowercaseName(doc, arrayListOf())
    val newEllipse = EllipseImpl.fromCenterVectors(doc, actualEllipseName, newCenter, newVectorA, newVectorB)

    cr.setResult(newEllipse)
    cr.addDependency(newCenter, emptyList(), true)
    cr.addDependency(newVectorA, emptyList(), true)
    cr.addDependency(newVectorB, emptyList(), true)
    return cr
}

fun createSymmetricCircularSector(
    doc: GeoDoc,
    coord1: DoubleArray, // Center point
    coord2: DoubleArray, // Start point of arc
    coord3: DoubleArray, // End point of arc
    name1: String?, // Name for center point
    name2: String?, // Name for start arc point
    name3: String? // Name for end arc point
): ConstructionResultImpl<CircularSector> {
    val cr = ConstructionResultImpl<CircularSector>()

    val actualName1 = name1 ?: generatePointName(doc)
    val newpoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])

    val actualName2 = name2 ?: generatePointName(doc, actualName1)
    val newpoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])

    val actualName3 = name3 ?: generatePointName(doc, actualName1, actualName2)
    val newpoint3 = PointImpl(doc, actualName3, coord3[0], coord3[1])

    val newCircularSector =
        CircularSectorImpl(
            doc,
            "${newpoint1.name}${newpoint2.name}${newpoint3.name}", // Name of the sector itself
            newpoint1, // Center
            newpoint2, // Start
            newpoint3 // End
        )

    cr.setResult(newCircularSector)
    cr.addDependency(newpoint1, emptyList(), true)
    cr.addDependency(newpoint2, emptyList(), true)
    cr.addDependency(newpoint3, emptyList(), true)

    return cr
}

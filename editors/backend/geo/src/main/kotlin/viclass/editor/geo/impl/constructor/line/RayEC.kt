package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementListExtraction
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Ray
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.constructor.generateRayName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.RayImpl
import kotlin.reflect.KClass

@Singleton
class RayEC constructor(val extractorManager: ParamExtractorManager) : ElementConstructor<Ray> {

    override fun outputType(): KClass<Ray> {
        return Ray::class
    }

    private enum class CGS {
        ByPointsName
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.ByPointsName.name)
                    .constraintDefault()
                    .build(),
            )
            .elTypes(Ray::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Ray> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 1)
                constructFromRayName(doc, inputName, c)
            }
        }
    }

    private fun constructFromRayName(
        doc: GeoDoc, inputName: String?, c: Construction,
    ): ConstructionResult<Ray> {
        val points = extractFirstPossible<ElementListExtraction<Point>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val p1 = points.result[0].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val p2 = points.result[1].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val line = RayImpl(doc, inputName ?: generateRayName(doc, p1.name!!), p1, p1.coordinates().vectorTo(p2.coordinates()), p2)
        val cr = ConstructionResultImpl<Ray>()
        cr.setResult(line)
        cr.addDependency(p1, listOf(), true)
        cr.addDependency(p2, listOf(), true)
        return cr
    }
}

package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.math3.util.Precision.EPSILON
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.CircularSector
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.impl.elements.CircleImpl
import viclass.editor.geo.impl.elements.EllipseImpl
import viclass.editor.geo.solving.Coefficients
import viclass.editor.geo.solving.Complex
import viclass.editor.geo.solving.PolyBase
import kotlin.math.*

/** <AUTHOR> */
object Intersections {
    fun of(line: LineVi, other: LineVi): Vector3D? {
        val l1 = line.line()
        val l2 = other.line()
        val intersection = l1.intersection(l2) ?: return null
        return intersection
    }

    fun of(line: LineVi, circle: Circle): List<Vector3D>? {
        // Coordinates of the center and radius of the circle
        val x0 = circle.centerPoint.x
        val y0 = circle.centerPoint.y
        val r = circle.radius

        // Define the parametric line: p = p1 + t * (a, b)
        val x1 = line.p1.x
        val y1 = line.p1.y
        val a = line.orderedVector().x
        val b = line.orderedVector().y

        // Quick check: if the distance from the center to the line is greater than the radius, there is no intersection
        val d = Distances.of(circle.centerPoint, line)
        if (d - r > DEFAULT_TOLERANCE) return null

        // Substitute t into the circle equation:
        // (x1 + a*t - x0)^2 + (y1 + b*t - y0)^2 = r^2
        val dx = x1 - x0
        val dy = y1 - y0

        val A = a * a + b * b
        val B = 2 * (a * dx + b * dy)
        val C = dx * dx + dy * dy - r * r

        val discriminant = B * B - 4 * A * C

        if (discriminant < -DEFAULT_TOLERANCE) return null

        val tList = when {
            abs(discriminant) < DEFAULT_TOLERANCE -> { // Tangent
                listOf(-B / (2 * A))
            }

            discriminant > 0 -> { // Intersects at 2 points
                val sqrtD = sqrt(discriminant)
                listOf((-B + sqrtD) / (2 * A), (-B - sqrtD) / (2 * A))
            }

            else -> emptyList()
        }

        val points = tList.map { t ->
            Vector3D.of(x1 + a * t, y1 + b * t, 0.0)
        }.distinctBy { Pair(it.x, it.y) }

        if (points.isEmpty()) return null
        if (points.size == 1) return points

        return Orders.pointsOnParallelVector(line.orderedVector(), points)
    }

    fun of(circle: Circle, line: LineVi): List<Vector3D>? {
        return of(line, circle)
    }

    fun of(circle1: Circle, circle2: Circle): List<Vector3D>? {
        val pC1 = circle1.centerPoint.coordinates()
        val pC2 = circle2.centerPoint.coordinates()
        val x0 = circle1.centerPoint.x
        val y0 = circle1.centerPoint.y
        val x1 = circle2.centerPoint.x
        val y1 = circle2.centerPoint.y
        val r0 = circle1.radius
        val r1 = circle2.radius
        val a = x1 - x0
        val b = y1 - y0
        val c = x0 * x0 - x1 * x1 + y0 * y0 - y1.pow(2) + r1 * r1 - r0 * r0

        val r0r1 = Distances.of(circle1.centerPoint, circle2.centerPoint)

        if (r0r1 < DEFAULT_TOLERANCE) return null
        if (r0r1 > r0 + r1) return null
        if (r0r1 + r0 < r1 || r0r1 + r1 < r0) return null
        if (r0r1 == r0 + r1) return listOf(Vector3D.of((x0 + x1) / 2.0, (y0 + y1) / 2, .0))

        // { (x-x0)^2 + (y-y0)^2 = r0^2 , x = (-c-2by)/2a }

        val x_1 = (-(b * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) + 2 * a * b * y0 - 2 * b.pow(2) * x0 + a * c)) / (2 * b.pow(2) + 2 * a.pow(2))
        val y_1 = (a * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) + 2 * a.pow(2) * y0 - 2 * a * b * x0 - b * c) / (2 * b.pow(2) + 2 * a.pow(2))

        val x_2 = ((b * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) - 2 * a * b * y0 + 2 * b.pow(2) * x0 - a * c)) / (2 * b.pow(2) + 2 * a.pow(2))
        val y_2 = (-(a * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) - 2 * a.pow(2) * y0 + 2 * a * b * x0 + b * c)) / (2 * b.pow(2) + 2 * a.pow(2))

        val c1 = if (x_1.isNaN() || y_1.isNaN()) null else Vector3D.of(x_1, y_1, .0)
        val c2 = if (x_2.isNaN() || y_2.isNaN()) null else Vector3D.of(x_2, y_2, .0)

        if (c1 == null && c2 == null) return null
        if (c1 == c2) return listOf(c1!!)

        return Orders.pointsByRotation(pC1.vectorTo(pC2), pC1, c1!!, c2!!)
    }

    fun of(line: LineVi, sector: CircularSector): List<Vector3D>? {
        // Extract the center and radius of the sector (circular arc)
        val x0 = sector.centerPoint.x
        val y0 = sector.centerPoint.y
        val x1 = line.p1.x
        val y1 = line.p1.y
        val r = sector.radius
        val a = line.orderedVector().x
        val b = line.orderedVector().y

        // Compute the distance from the sector center to the line
        val d = Distances.of(sector.centerPoint, line)
        // If the distance is greater than the radius (plus tolerance), there is no intersection
        if (d - r > DEFAULT_TOLERANCE) return null

        // Solve the intersection of the line and the full circle (ignoring the sector's arc limits
        // for now)
        // Parametric line: x = x1 + a*t, y = y1 + b*t
        // Circle: (x - x0)^2 + (y - y0)^2 = r^2
        // Substitute line into circle and solve for t: At^2 + Bt + C = 0
        val A = a * a + b * b
        val B = 2 * (a * (x1 - x0) + b * (y1 - y0))
        val C = (x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0) - r * r

        val discriminant = B * B - 4 * A * C
        // If discriminant is negative (with tolerance), no real intersection
        if (discriminant < -DEFAULT_TOLERANCE) return null

        // Solve for t (intersection parameter(s) along the line)
        val tList = when {
            // One intersection (tangent): discriminant ~ 0
            abs(discriminant) < DEFAULT_TOLERANCE -> listOf(-B / (2 * A))
            // Two intersections
            discriminant > 0 -> {
                val sqrtD = sqrt(discriminant)
                listOf((-B + sqrtD) / (2 * A), (-B - sqrtD) / (2 * A))
            }
            // No intersection
            else -> emptyList()
        }

        // Get coordinates for sector center, start, and end points
        val pC = sector.centerPoint.coordinates()
        val pS = sector.startPoint.coordinates()
        val pE = sector.endPoint.coordinates()
        // Reference vector from center to the right (used as zero angle)
        val vec0 = pC.vectorTo(Vector3D.of(pC.x + 10, pC.y, 0.0))

        // Compute angles from reference to start and end points of the sector
        val angleS = vec0.angleTo(pC.vectorTo(pS))
        var angleE = vec0.angleTo(pC.vectorTo(pE))
        // Ensure angleE > angleS (if not, add 2*PI to angleE)
        if (angleS > angleE) angleE += 2 * PI

        // Filter intersection points to those that lie strictly on the arc of the sector,
        // excluding the start and end points
        val arcPoints = tList.map { t -> Vector3D.of(x1 + a * t, y1 + b * t, 0.0) }.filter { pt ->
            // Compute angle from reference to this intersection point
            val angle = vec0.angleTo(pC.vectorTo(pt)).let { ang -> if (angleE - 2 * PI > ang) ang + 2 * PI else ang }
            // Check if angle is strictly within the sector's angular span (excluding the boundaries)
            angle - angleS > DEFAULT_TOLERANCE && angle - angleE < -DEFAULT_TOLERANCE
        }

        // Combine all intersection points (arc only), removing duplicates (by x,y)
        val allPoints: List<Vector3D> = arcPoints.distinctBy { pt -> Pair(pt.x, pt.y) }

        // If no intersection points, return null
        if (allPoints.isEmpty()) return null
        // If only one intersection point, return it
        if (allPoints.size == 1) return allPoints

        // Otherwise, order the points along the direction of the line's parallel vector
        return Orders.pointsOnParallelVector(line.orderedVector(), allPoints)
    }

    fun of(sector: CircularSector, line: LineVi): List<Vector3D>? {
        return of(line, sector)
    }

    fun of(sector1: CircularSector, sector2: CircularSector): List<Vector3D>? {
        val x0 = sector1.centerPoint.x
        val y0 = sector1.centerPoint.y
        val x1 = sector2.centerPoint.x
        val y1 = sector2.centerPoint.y
        val r0 = sector1.radius
        val r1 = sector2.radius
        val a = x1 - x0
        val b = y1 - y0
        val c = x0 * x0 - x1 * x1 + y0 * y0 - y1.pow(2) + r1 * r1 - r0 * r0

        val r0r1 = sector1.centerPoint.coordinates().distance(sector2.centerPoint.coordinates())

        if (r0r1 < DEFAULT_TOLERANCE) return listOf()
        if (r0r1 > r0 + r1) return null
        if (r0r1 + r0 < r1 || r0r1 + r1 < r0) return null
        if (r0r1 == r0 + r1) return listOf(Vector3D.of((x0 + x1) / 2.0, (y0 + y1) / 2, .0))

        // { (x-x0)^2 + (y-y0)^2 = r0^2 , x = (-c-2by)/2a }

        val x_1 = (-(b * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) + 2 * a * b * y0 - 2 * b.pow(2) * x0 + a * c)) / (2 * b.pow(2) + 2 * a.pow(2))
        val y_1 = (a * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) + 2 * a.pow(2) * y0 - 2 * a * b * x0 - b * c) / (2 * b.pow(2) + 2 * a.pow(2))

        val x_2 = ((b * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) - 2 * a * b * y0 + 2 * b.pow(2) * x0 - a * c)) / (2 * b.pow(2) + 2 * a.pow(2))
        val y_2 = (-(a * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) - 2 * a.pow(2) * y0 + 2 * a * b * x0 + b * c)) / (2 * b.pow(2) + 2 * a.pow(2))

        val c1 = if (x_1.isNaN() || y_1.isNaN()) null else Vector3D.of(x_1, y_1, .0)
        val c2 = if (x_2.isNaN() || y_2.isNaN()) null else Vector3D.of(x_2, y_2, .0)

        if (c1 == null && c2 == null) return null

        val pSector1C = sector1.centerPoint.coordinates()
        val pSector1S = sector1.startPoint.coordinates()
        val pSector1E = sector1.endPoint.coordinates()
        val vecSector10 = pSector1C.vectorTo(Vector3D.of(pSector1C.x + 10, pSector1C.y, 0.0))

        val pSector2C = sector2.centerPoint.coordinates()
        val pSector2S = sector2.startPoint.coordinates()
        val pSector2E = sector2.endPoint.coordinates()
        val vecSector20 = pSector2C.vectorTo(Vector3D.of(pSector2C.x + 10, pSector2C.y, 0.0))

        val angleSector1S = vecSector10.angleTo(pSector1C.vectorTo(pSector1S))
        var angleSector1E = vecSector10.angleTo(pSector1C.vectorTo(pSector1E))

        val angleSector2S = vecSector20.angleTo(pSector2C.vectorTo(pSector2S))
        var angleSector2E = vecSector20.angleTo(pSector2C.vectorTo(pSector2E))

        // Calculate the angle starting from point (x,0). The case where the origin angle start is
        // smaller than the end angle.
        // This is the case where the start angle is larger than the end angle.
        if (angleSector1S > angleSector1E) angleSector1E += 2 * PI
        if (angleSector2S > angleSector2E) angleSector2E += 2 * PI

        val res = listOfNotNull(c1, c2).filter {
            val angle1 = vecSector10.angleTo(pSector1C.vectorTo(it))
            val angle2 = vecSector20.angleTo(pSector2C.vectorTo(it))
            if (angle1 - angleSector1S < -DEFAULT_TOLERANCE) return@filter false
            if (angle1 - angleSector1E > DEFAULT_TOLERANCE) return@filter false
            if (angle2 - angleSector2S < -DEFAULT_TOLERANCE) return@filter false
            if (angle2 - angleSector2E > DEFAULT_TOLERANCE) return@filter false
            true
        }

        return res
    }

    fun of(circle: Circle, sector: CircularSector): List<Vector3D>? {
        val x0 = circle.centerPoint.x
        val y0 = circle.centerPoint.y
        val x1 = sector.centerPoint.x
        val y1 = sector.centerPoint.y
        val r0 = circle.radius
        val r1 = sector.radius
        val a = x1 - x0
        val b = y1 - y0
        val c = x0 * x0 - x1 * x1 + y0 * y0 - y1.pow(2) + r1 * r1 - r0 * r0

        val r0r1 = circle.centerPoint.coordinates().distance(sector.centerPoint.coordinates())

        if (r0r1 < DEFAULT_TOLERANCE) return listOf()
        if (r0r1 > r0 + r1) return null
        if (r0r1 + r0 < r1 || r0r1 + r1 < r0) return null
        if (r0r1 == r0 + r1) return listOf(Vector3D.of((x0 + x1) / 2.0, (y0 + y1) / 2, .0))

        // { (x-x0)^2 + (y-y0)^2 = r0^2 , x = (-c-2by)/2a }

        val x_1 = (-(b * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) + 2 * a * b * y0 - 2 * b.pow(2) * x0 + a * c)) / (2 * b.pow(2) + 2 * a.pow(2))
        val y_1 = (a * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) + 2 * a.pow(2) * y0 - 2 * a * b * x0 - b * c) / (2 * b.pow(2) + 2 * a.pow(2))

        val x_2 = ((b * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) - 2 * a * b * y0 + 2 * b.pow(2) * x0 - a * c)) / (2 * b.pow(2) + 2 * a.pow(2))
        val y_2 = (-(a * sqrt(
            (-4 * b.pow(2) * y0 * y0) + ((-8 * a * b * x0) - 4 * b * c) * y0 - 4 * a.pow(2) * x0 * x0 - 4 * a * c * x0 + (4 * b.pow(
                2
            ) + 4 * a.pow(2)) * r0 * r0 - c * c
        ) - 2 * a.pow(2) * y0 + 2 * a * b * x0 + b * c)) / (2 * b.pow(2) + 2 * a.pow(2))

        val c1 = if (x_1.isNaN() || y_1.isNaN()) null else Vector3D.of(x_1, y_1, .0)
        val c2 = if (x_2.isNaN() || y_2.isNaN()) null else Vector3D.of(x_2, y_2, .0)

        if (c1 == null && c2 == null) return null

        val pSectorC = sector.centerPoint.coordinates()
        val pSectorS = sector.startPoint.coordinates()
        val pSectorE = sector.endPoint.coordinates()
        val vecSector0 = pSectorC.vectorTo(Vector3D.of(pSectorC.x + 10, pSectorC.y, 0.0))

        val angleSectorS = vecSector0.angleTo(pSectorC.vectorTo(pSectorS))
        var angleSectorE = vecSector0.angleTo(pSectorC.vectorTo(pSectorE))

        // Calculate the angle starting from point (x,0). The case where the origin angle start is
        // smaller than the end angle.
        // This is the case where the start angle is larger than the end angle.
        if (angleSectorS > angleSectorE) angleSectorE += 2 * PI

        val res = listOfNotNull(c1, c2).filter {
            val angle = vecSector0.angleTo(pSectorC.vectorTo(it))
            if (angle - angleSectorS < -DEFAULT_TOLERANCE) return@filter false
            if (angle - angleSectorE > DEFAULT_TOLERANCE) return@filter false
            true
        }

        return res
    }

    fun of(sector: CircularSector, ellipse: Ellipse): List<Vector3D>? {
        return of(ellipse, sector)
    }

    fun of(ellipse: Ellipse, sector: CircularSector): List<Vector3D>? {
        val ell = EllipseImpl.fromFocusPoints(
            sector.doc, null, sector.centerPoint, sector.centerPoint, sector.radius, sector.radius
        )
        ell.validate()
        val ints = findEllipseVsEllipseIntersections(ell.coefs, ellipse.coefs)

        val pSectorC = sector.centerPoint.coordinates()
        val pSectorS = sector.startPoint.coordinates()
        val pSectorE = sector.endPoint.coordinates()
        val vecSector0 = pSectorC.vectorTo(Vector3D.of(pSectorC.x + 10, pSectorC.y, 0.0))

        val angleSectorS = vecSector0.angleTo(pSectorC.vectorTo(pSectorS))
        var angleSectorE = vecSector0.angleTo(pSectorC.vectorTo(pSectorE))

        // Calculate the angle starting from point (x,0). The case where the origin angle start is
        // smaller than the end angle.
        // This is the case where the start angle is larger than the end angle.
        if (angleSectorS > angleSectorE) angleSectorE += 2 * PI

        val res = ints?.filter {
            var angle = vecSector0.angleTo(pSectorC.vectorTo(it))

            // This is the case where the start angle is larger than the end angle.
            if (angleSectorE - 2 * PI > angle) angle += 2 * PI

            if (angle - angleSectorS < -DEFAULT_TOLERANCE) return@filter false
            if (angle - angleSectorE > DEFAULT_TOLERANCE) return@filter false
            true
        }

        return res
    }

    fun of(line: LineVi, ellipse: Ellipse): List<Vector3D>? {
        return of(ellipse, line)
    }

    fun of(ell: Ellipse, line: LineVi): List<Vector3D>? {
        val (h, k, ang) = Triple(ell.center.x, ell.center.y, ell.rotate)
        val (cosAng, sinAng) = Pair(cos(ang), sin(ang))
        val a2 = ell.a.pow(2)
        val b2 = ell.b.pow(2)

        val cos2 = cosAng.pow(2)
        val sin2 = sinAng.pow(2)
        val sin2ang = sin(2 * ang)

        // Hệ số của phương trình ellipse bậc hai
        val a = cos2 / a2 + sin2 / b2
        val b = sin2 / a2 + cos2 / b2
        val c = sin2ang / a2 - sin2ang / b2
        val d = -2 * h * cos2 / a2 - k * sin2ang / a2 - 2 * h * sin2 / b2 + k * sin2ang / b2
        val e = -h * sin2ang / a2 - 2 * k * sin2 / a2 + h * sin2ang / b2 - 2 * k * cos2 / b2
        val f =
            h.pow(2) * cos2 / a2 + h * k * sin2ang / a2 + k.pow(2) * sin2 / a2 + h.pow(2) * sin2 / b2 - h * k * sin2ang / b2 + k.pow(
                2
            ) * cos2 / b2 - 1

        // Định nghĩa các hệ số từ vector pháp tuyến của đường thẳng
        val n = Vector3D.of(line.orderedVector().y, -line.orderedVector().x, line.orderedVector().z)
        val (u, v) = Pair(n.x, n.y)
        val m = u * line.p1.coordinates().x + v * line.p1.coordinates().y

        // Tính toán phần căn của nghiệm
        val discrimVal =
            e.pow(2) * u.pow(2) - 4 * b * u.pow(2) * f + 2 * e * c * m * u + 4 * c * u * v * f - 2 * e * d * u * v - 4 * b * m * d * u + c.pow(
                2
            ) * m.pow(2) + 2 * c * m * d * v + d.pow(2) * v.pow(2) - 4 * b * a * m.pow(2) - 4 * a * v.pow(2) * f - 4 * e * a * m * v

        if (discrimVal.isNaN() || discrimVal < 0) return null

        val discriminant = sqrt(discrimVal)

        // Tính các nghiệm của hệ phương trình
        val denom = 2 * (-b * u.pow(2) + c * u * v - a * v.pow(2))
        if (denom == 0.0 || denom.isNaN()) return null

        val x1 = (d * v.pow(2) + c * m * v - e * u * v - 2 * b * m * u + v * discriminant) / denom
        val y1 = (e * u.pow(2) + c * m * u - d * u * v - 2 * a * m * v - u * discriminant) / denom
        val x2 = (d * v.pow(2) + c * m * v - e * u * v - 2 * b * m * u - v * discriminant) / denom
        val y2 = (e * u.pow(2) + c * m * u - d * u * v - 2 * a * m * v + u * discriminant) / denom

        val intersections = mutableListOf<Vector3D>()

        if (!x1.isNaN() && !y1.isNaN()) {
            intersections.add(Vector3D.of(x1, y1, 0.0))
        }
        if ((x1 != x2 || y1 != y2) && !x2.isNaN() && !y2.isNaN()) {
            intersections.add(Vector3D.of(x2, y2, 0.0))
        }

        if (intersections.isEmpty()) return null

        // Order the points along the direction of the line's parallel vector
        return Orders.pointsOnParallelVector(line.orderedVector(), intersections)
    }

    fun of(circle: Circle, ellipse: Ellipse): List<Vector3D>? {
        return of(ellipse, circle)
    }

    fun of(ellipse: Ellipse, circle: Circle): List<Vector3D>? {
        val ell = EllipseImpl.fromFocusPoints(
            circle.doc, null, circle.centerPoint, circle.centerPoint, circle.radius, circle.radius
        )
        ell.validate()
        return findEllipseVsEllipseIntersections(ell.coefs, ellipse.coefs)
    }

    fun of(ellipse1: Ellipse, ellipse2: Ellipse): List<Vector3D>? {
        return findEllipseVsEllipseIntersections(ellipse1.coefs, ellipse2.coefs)
    }

    private fun findEllipseVsEllipseIntersections(es: Coefficients, et: Coefficients): List<Vector3D>? {
        // Get coeficients for implicit equation (IE) representing the
        // intersection of the two ellipses, elliminating the x^2 term
        val IE = Coefficients.elliminateTerm(es, et, Coefficients::a) ?: return null
        val b = IE.b
        val c = IE.c
        val d = IE.d
        val e = IE.e
        val f = IE.f
        val numerator: (y: Double) -> Double = { y -> c * y * y + (e) * y + (f) }
        val denominator: (y: Double) -> Double = { y -> b * y + d }
        // Get the implicit coefficients for one of the two ellipses
        val S = es
        val sa = S.a
        val sb = S.b
        val sc = S.c
        val sd = S.d
        val se = S.e
        val sf = S.f
        // Calculate the coefficents for the quartic function of y
        val p = c * d + b * e
        val q = d * e + b * f
        val z4 = sa * c * c + sc * b * b - sb * b * c
        val z3 = 2 * sa * c * e + +2 * sc * b * d + se * b * b - sd * b * c - sb * p
        val z2 = sa * (2 * c * f + e * e) + sc * d * d + 2 * se * b * d + sf * b * b - sd * p - sb * q
        val z1 = 2 * sa * e * f + se * d * d + 2 * sf * b * d - sd * q - sb * d * f
        val z0 = sa * f * f + sf * d * d - sd * d * f
        // Get the quartic function of y and solve it for all roots
        val quartic = PolyBase.getPoly(listOf(z4, z3, z2, z1, z0))
        var roots = quartic.roots()
        // Remove duplicates and complex roots using custom EPSILON
        roots = Complex.removeDuplicates(roots, EPSILON)
        roots = Complex.filterRealRoots(roots, EPSILON)
        // For all real roots calculate the x position
        val intersects = mutableListOf<Vector3D>()
        for (r in roots) {
            val de = denominator(r.real)
            if (abs(de) < EPSILON) {
                val a1 = sa
                val b1 = sb * r.real + sd
                val c1 = sc * r.real * r.real + se * r.real + sf
                var qroots = PolyBase.getPoly(listOf(a1, b1, c1)).roots()
                qroots = Complex.removeDuplicates(qroots, EPSILON)
                qroots = Complex.filterRealRoots(qroots, EPSILON)
                for (qr in qroots) {
                    intersects.add(Vector3D.of(qr.real, r.real, .0))
                }
            } else {
                val n = numerator(r.real)
                intersects.add(Vector3D.of(-n / de, r.real, .0))
            }
        }

        if (intersects.isEmpty()) return null

        return intersects
    }

    /**
     * Converts a CircularSector to a Circle for intersection calculation
     */
    fun createTempCircleFromSector(sector: CircularSector): Circle {
        return CircleImpl(sector.doc, "", sector.centerPoint, sector.radius)
    }
}

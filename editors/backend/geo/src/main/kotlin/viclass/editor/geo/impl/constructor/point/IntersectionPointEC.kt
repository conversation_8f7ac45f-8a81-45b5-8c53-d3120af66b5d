package viclass.editor.geo.impl.constructor.point

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircle
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircularSector
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLine
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aRay
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aVector
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.anEllipse
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.constructor.Intersections.createTempCircleFromSector
import viclass.editor.geo.impl.constructor.Orders.orderIntersections
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.reflect.KClass

data class ExtractedElements(
    val element1: Element?,
    val element2: Element?,
    val nthIntersection: Int?,
    val dependencies: List<ConstructionResult<out Element>>
)

/**
 *
 * <AUTHOR>
 */
@Singleton
class IntersectionPointEC : ElementConstructor<Point> {
    private val constraintParam = ConstraintParamDefManager.instance()

    override fun outputType(): KClass<Point> = Point::class

    enum class CGS {
        LineLine, LineCircle, LineSector, LineEllipse, CircleCircle, CircleSector, CircleEllipse, SectorSector, SectorEllipse, EllipseEllipse
    }

    override fun template(): ConstructorTemplate = ConstructorTemplateBuilder.create(this).apply {
        cgs(
            // LineLine - no optional value
            ConstraintGroupBuilder.create().name("LineLine").hints("IntersectionOfLineLine")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(1, constraintParam[aLine]!!, listOf(0), listOf("NameOfLine"), "tpl-IntersectionWithLine")
                .build(),
            
            // LineCircle
            ConstraintGroupBuilder.create().name("LineCircle").hints("IntersectionOfLineCircle")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(1, constraintParam[aCircle]!!, listOf(0), listOf("NameOfCircle"), "tpl-IntersectionWithCircle")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // LineSector
            ConstraintGroupBuilder.create().name("LineSector").hints("IntersectionOfLineSector")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(1, constraintParam[aCircularSector]!!, listOf(0), listOf("NameOfCircularSector"), "tpl-IntersectionWithSector")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // LineEllipse
            ConstraintGroupBuilder.create().name("LineEllipse").hints("IntersectionOfLineEllipse")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(1, constraintParam[anEllipse]!!, listOf(0), listOf("NameOfEllipse"), "tpl-IntersectionWithEllipse")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // CircleCircle
            ConstraintGroupBuilder.create().name("CircleCircle").hints("IntersectionOfCircleCircle")
                .constraint(0, constraintParam[aCircle]!!, listOf("NameOfCircle"), "tpl-IntersectionOfCircle")
                .constraintDepends(1, constraintParam[aCircle]!!, listOf(0), listOf("NameOfCircle"), "tpl-IntersectionWithCircle")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // CircleSector
            ConstraintGroupBuilder.create().name("CircleSector").hints("IntersectionOfCircleSector")
                .constraint(0, constraintParam[aCircle]!!, listOf("NameOfCircle"), "tpl-IntersectionOfCircle")
                .constraintDepends(1, constraintParam[aCircularSector]!!, listOf(0), listOf("NameOfCircularSector"), "tpl-IntersectionWithSector")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // CircleEllipse
            ConstraintGroupBuilder.create().name("CircleEllipse").hints("IntersectionOfCircleEllipse")
                .constraint(0, constraintParam[aCircle]!!, listOf("NameOfCircle"), "tpl-IntersectionOfCircle")
                .constraintDepends(1, constraintParam[anEllipse]!!, listOf(0), listOf("NameOfEllipse"), "tpl-IntersectionWithEllipse")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // SectorSector
            ConstraintGroupBuilder.create().name("SectorSector").hints("IntersectionOfSectorSector")
                .constraint(0, constraintParam[aCircularSector]!!, listOf("NameOfCircularSector"), "tpl-IntersectionOfSector")
                .constraintDepends(1, constraintParam[aCircularSector]!!, listOf(0), listOf("NameOfCircularSector"), "tpl-IntersectionWithSector")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // SectorEllipse
            ConstraintGroupBuilder.create().name("SectorEllipse").hints("IntersectionOfSectorEllipse")
                .constraint(0, constraintParam[aCircularSector]!!, listOf("NameOfCircularSector"), "tpl-IntersectionOfSector")
                .constraintDepends(1, constraintParam[anEllipse]!!, listOf(0), listOf("NameOfEllipse"), "tpl-IntersectionWithEllipse")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build(),
            
            // EllipseEllipse
            ConstraintGroupBuilder.create().name("EllipseEllipse").hints("IntersectionOfEllipseEllipse")
                .constraint(0, constraintParam[anEllipse]!!, listOf("NameOfEllipse"), "tpl-IntersectionOfEllipse")
                .constraintDepends(1, constraintParam[anEllipse]!!, listOf(0), listOf("NameOfEllipse"), "tpl-IntersectionWithEllipse")
                .constraintOptional(2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection")
                .build()
        )
        elTypes(Point::class)
    }.build()

    /**
     * Validates that an intersection point lies on both geometric elements
     * Following the TypeScript approach: convert complex elements to simple forms for intersection,
     * then validate against original elements
     */
    private fun validateIntersectionPoint(point: Vector3D, element1: Element, element2: Element) {
        val isOnElement1 = isPointOnElement(point, element1)
        val isOnElement2 = isPointOnElement(point, element2)

        if (!isOnElement1) throw ConstructionException("Điểm giao không nằm trên phần tử thứ nhất")
        if (!isOnElement2) throw ConstructionException("Điểm giao không nằm trên phần tử thứ hai")
    }

    /**
     * Checks if a point lies on a specific element using appropriate validation methods
     * Similar to TypeScript's isPointOnElement method
     */
    private fun isPointOnElement(point: Vector3D, element: Element): Boolean {
        return when (element) {
            is LineSegment -> Lines.isPointLiesOn(element, point) // VectorVi is also a LineSegment
            is Ray -> Lines.isPointLiesOn(element, point)
            is CircularSector -> CircularSectors.isOnCircularSector(element, point)
            else -> true // Skip validation for unknown types
        }
    }

    /**
     * Generic method to extract elements from construction parameters
     */
    private fun extractElements(doc: GeoDoc, c: Construction): ExtractedElements {
        var element1: Element? = null
        var element2: Element? = null
        var nthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach { param ->
            when (param.paramDef.id) {
                aLine, aLineSegment, aVector, aRay -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedElement = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy đường thẳng")
                    if (element1 == null) element1 = extractedElement else element2 = extractedElement
                    dependencies.add(extraction.result)
                }

                aCircle -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedElement = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy đường tròn")
                    if (element1 == null) element1 = extractedElement else element2 = extractedElement
                    dependencies.add(extraction.result)
                }

                aCircularSector -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedElement = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy hình quạt tròn")
                    if (element1 == null) element1 = extractedElement else element2 = extractedElement
                    dependencies.add(extraction.result)
                }

                anEllipse -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedElement = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy hình elip")
                    if (element1 == null) element1 = extractedElement else element2 = extractedElement
                    dependencies.add(extraction.result)
                }

                aValue -> if (param.specs.indexInCG == 2) {
                    val extraction =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, param, c.ctIdx)
                    nthIntersection = extraction.result.toInt() // Force to Int (because some case it return Double)
                }
            }
        }

        return ExtractedElements(element1, element2, nthIntersection, dependencies)
    }

    /**
     * Generic intersection calculation method with proper ordering logic
     * Orders intersections using the same logic as frontend to ensure nth parameter consistency
     */
    private fun calculateIntersection(
        doc: GeoDoc,
        inputName: String?,
        element1: Element,
        element2: Element,
        nthIntersection: Int?,
        dependencies: List<ConstructionResult<out Element>>,
        needsValidation: Boolean = true
    ): ConstructionResult<Point> {
        // Convert sectors to circles for intersection calculation if needed
        val actualElement1 = if (element1 is CircularSector) createTempCircleFromSector(element1) else element1
        val actualElement2 = if (element2 is CircularSector) createTempCircleFromSector(element2) else element2

        // Calculate intersections
        val allIntersections = when {
            actualElement1 is LineVi && actualElement2 is LineVi -> listOfNotNull(
                Intersections.of(
                    actualElement1, actualElement2
                )
            )

            actualElement1 is LineVi && actualElement2 is Circle -> Intersections.of(actualElement1, actualElement2)

            actualElement1 is Circle && actualElement2 is LineVi -> Intersections.of(actualElement2, actualElement1)

            actualElement1 is LineVi && actualElement2 is Ellipse -> Intersections.of(actualElement1, actualElement2)

            actualElement1 is Ellipse && actualElement2 is LineVi -> Intersections.of(actualElement2, actualElement1)

            actualElement1 is Circle && actualElement2 is Circle -> Intersections.of(actualElement1, actualElement2)

            actualElement1 is Circle && actualElement2 is Ellipse -> Intersections.of(actualElement1, actualElement2)

            actualElement1 is Ellipse && actualElement2 is Circle -> Intersections.of(actualElement2, actualElement1)

            actualElement1 is Ellipse && actualElement2 is Ellipse -> Intersections.of(actualElement1, actualElement2)

            else -> null
        } ?: throw ConstructionException("Không có điểm giao nhau")

        if (allIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        // Order intersections using the same logic as frontend
        val orderedIntersections = orderIntersections(doc, allIntersections, element1, element2)

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds
        if (actualNthIntersection < 0 || actualNthIntersection >= orderedIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${orderedIntersections.size} giao điểm.")
        }

        val selectedIntersection = orderedIntersections[actualNthIntersection]

        if (needsValidation) validateIntersectionPoint(selectedIntersection, element1, element2)

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, selectedIntersection.x, selectedIntersection.y, selectedIntersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }
        return result
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        Validations.validateNumConstraints(c, 2)
        val extracted = extractElements(doc, c)

        val element1 = extracted.element1 ?: throw ElementNotExistInDocumentException("Không tìm thấy phần tử thứ nhất")
        val element2 = extracted.element2 ?: throw ElementNotExistInDocumentException("Không tìm thấy phần tử thứ hai")

        // All other cases need validation when sectors are involved
        val needsValidation =
            element1 is CircularSector || element2 is CircularSector || element1 is LineSegment || element2 is LineSegment || element1 is Ray || element2 is Ray
        return calculateIntersection(
            doc, inputName, element1, element2, extracted.nthIntersection, extracted.dependencies, needsValidation
        )
    }
}

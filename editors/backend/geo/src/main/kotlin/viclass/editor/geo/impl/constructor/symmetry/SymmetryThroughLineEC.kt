package viclass.editor.geo.impl.constructor.symmetry

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class SymmetryThroughLineEC() : ElementConstructor<Element> {
    private enum class CGS {
        Point,
        Line,
        Circle,
        Ellipse,
        CircularSector,
        Polygon,
        Vector,
        Ray
    }

    @Suppress("UNCHECKED_CAST")
    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Element> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.Point -> {
                Validations.validateNumConstraints(c, 2)
                constructSymmetricPoint<LineVi>(doc, inputName, c, ::symmetricPointByLine) as ConstructionResult<Element>
            }

            CGS.Line, CGS.Vector, CGS.Ray -> {
                Validations.validateNumConstraints(c, 2)
                constructSymmetricLine<LineVi>(doc, inputName, c, ::symmetricPointByLine) as ConstructionResult<Element>
            }

            CGS.Circle -> {
                Validations.validateNumConstraints(c, 2)
                constructSymmetricCircle<LineVi>(doc, inputName, c, ::symmetricPointByLine) as ConstructionResult<Element>
            }

            CGS.Ellipse -> {
                Validations.validateNumConstraints(c, 2)
                constructSymmetricEllipse<LineVi>(doc, inputName, c, ::symmetricPointByLine) as ConstructionResult<Element>
            }

            CGS.CircularSector -> {
                Validations.validateNumConstraints(c, 2)
                constructSymmetricCircularSector<LineVi>(doc, inputName, c, ::symmetricPointByLine) as ConstructionResult<Element>
            }

            CGS.Polygon -> {
                Validations.validateNumConstraints(c, 2)
                constructSymmetricPolygon<LineVi>(doc, inputName, c, ::symmetricPointByLine) as ConstructionResult<Element>
            }
        }
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create().name(CGS.Point.name).constraintDefault().build(),
            )
            .elTypes(Element::class)
            .build()
    }

    override fun outputType(): KClass<Element> {
        return Element::class
    }


}

package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.transformdata.FreePointTransformData
import viclass.editor.geo.dbentity.transformdata.TransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.FreePointTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import viclass.editor.geo.transformer.Transformer
import kotlin.reflect.KClass

@Singleton
class PointFromCoordsEC : ElementConstructor<Point> {

    enum class CGS {
        G3d, G2d
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.G3d.name)
                    .hints("3DCoordinates")
                    .numDim(3)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.G2d.name)
                    .hints("2DCoordinates")
                    .numDim(2)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value"),
                        "tpl-2DPoint"
                    )
                    .build()
            )
            .elTypes(Point::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {

        val name = inputName ?: generatePointName(doc)

        when (CGS.valueOf(c.cgName)) {
            CGS.G3d, CGS.G2d -> {
                Validations.validateNumConstraints(c, 1)

                val constraint: ConstructionParams = c.params[0]

                val extractedResult = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, constraint, c.ctIdx)

                val coords = extractedResult.result
                val resultPoint = if (doc.numDim == 2) PointImpl(doc, name, coords[0], coords[1])
                else PointImpl(doc, name, coords[0], coords[1], coords[2])

                resultPoint.transformData = FreePointTransformData(0)
                resultPoint.transformer = TransformMapping.fromClazz(FreePointTransformer::class) as Transformer<TransformData>
                resultPoint.movementPath = MovementFreePath()

                val cr = ConstructionResultImpl<Point>()
                cr.setResult(resultPoint)
                return cr
            }
        }
    }

    override fun outputType(): KClass<Point> {
        return Point::class
    }
}

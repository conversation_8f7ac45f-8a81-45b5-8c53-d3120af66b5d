package viclass.editor.geo.impl.constructor.line

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementListExtraction
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.createVectorByEl
import kotlin.reflect.KClass
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException

@Singleton
class LineEC(
    val extractorManager: ParamExtractorManager
) : ElementConstructor<LineVi>, Logging {

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        ByPointsName
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.ByPointsName.name)
                    .constraintDefault()
                    .build(),
            )
            .elTypes(LineVi::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 1)
                constructFromLineSegmentName(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromLineSegmentName(
        doc: GeoDoc,
        inputName: String?,
        c: Construction,
    ): ConstructionResult<LineVi> {
        val points = extractFirstPossible<ElementListExtraction<Point>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val p1 = points.result[0].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val p2 = points.result[1].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val line = LineImpl(doc, inputName ?: generateLowercaseName(doc, arrayListOf()), p1, createVectorByEl(doc, p1, p2), p2)
        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(line)
        cr.addDependency(p1, listOf(), true)
        cr.addDependency(p2, listOf(), true)
        return cr
    }
}

package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Plane
import org.apache.commons.geometry.euclidean.threed.Planes
import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.numbers.core.Precision
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.impl.elements.PointImpl

/**
 * <AUTHOR>
 */
object Polygons {
    val nextIndex: (Int, List<Any>) -> Int = { idx, lst -> if (idx == lst.size - 1) 0 else idx + 1 }
    val previousIndex: (Int, List<Any>) -> Int = { idx, lst -> if (idx == 0) lst.size - 1 else idx - 1 }

    private fun arePointsCoplanar(points: List<Point>): Boolean {
        if (points.size < 4) return true
        val p1Coord = points[0].coordinates()
        val normal = points[1].coordinates().subtract(p1Coord).cross(points[2].coordinates().subtract(p1Coord))
        return points.drop(3).all {
            Precision.equals(normal.dot(it.coordinates().subtract(p1Coord)), 0.0, DEFAULT_TOLERANCE)
        }
    }

    private fun projectTo2D(points: List<Point>): List<Point> {
        val normal = findDominantPlaneNormal()
        val projX =
            if (normal.dot(Vector3D.of(1.0, 0.0, 0.0)) > 0.99) normal.orthogonal() else Vector3D.of(1.0, 0.0, 0.0)
        val projY =
            if (normal.dot(Vector3D.of(0.0, 1.0, 0.0)) > 0.99) normal.orthogonal() else Vector3D.of(0.0, 1.0, 0.0)
        return points.map { PointImpl(it.doc, it.name, it.coordinates().dot(projX), it.coordinates().dot(projY), 0.0) }
    }

    private fun findDominantPlaneNormal(): Vector3D = Vector3D.of(0.0, 0.0, 1.0)

    private fun calculateBisectorVector(p0: Point, p1: Point, p2: Point): Vector3D {
        // Get coordinates of the 3 points
        val A = p0.coordinates()
        val B = p1.coordinates()
        val C = p2.coordinates()

        // Calculate vectors BA = A - B, BC = C - B
        val BAx = A.x - B.x
        val BAy = A.y - B.y
        val BAz = A.z - B.z

        val BCx = C.x - B.x
        val BCy = C.y - B.y
        val BCz = C.z - B.z

        // Normalize BA and BC
        val lenBA = Math.sqrt(BAx * BAx + BAy * BAy + BAz * BAz)
        val lenBC = Math.sqrt(BCx * BCx + BCy * BCy + BCz * BCz)

        val baNormX = BAx / lenBA
        val baNormY = BAy / lenBA
        val baNormZ = BAz / lenBA

        val bcNormX = BCx / lenBC
        val bcNormY = BCy / lenBC
        val bcNormZ = BCz / lenBC

        // Sum the 2 normalized vectors
        val bisX = baNormX + bcNormX
        val bisY = baNormY + bcNormY
        val bisZ = baNormZ + bcNormZ

        // Normalize the result
        val bisLen = Math.sqrt(bisX * bisX + bisY * bisY + bisZ * bisZ)
        if (bisLen == 0.0) {
            // Special case: points do not form an angle (e.g., collinear)
            return Vector3D.ZERO
        }
        return Vector3D.of(bisX / bisLen, bisY / bisLen, bisZ / bisLen)
    }

    fun isPointInPolygon(point: Vector3D, polygon: List<Point>): Boolean {
        var intersections = 0
        for (i in polygon.indices) {
            val a = polygon[i]
            val b = polygon[(i + 1) % polygon.size]
            if ((a.y > point.y) != (b.y > point.y)) {
                val atX = (b.x - a.x) * (point.y - a.y) / (b.y - a.y + 1e-16) + a.x
                if (point.x < atX)
                    intersections++
            }
        }
        return intersections % 2 == 1
    }

    /**
     * Finds the center and radius of the inscribed circle (incircle) of a polygon.
     *
     * The incenter is the unique point that is equidistant to all sides of a convex polygon (the incircle center).
     * This method finds that point and ensures the radius is exactly the distance from the incenter to each side,
     * and also ensures the incenter is strictly inside the polygon, and that this distance is the same for all sides
     * within a given tolerance.
     *
     * @param doc The GeoDoc context for geometric construction.
     * @param vertices The vertices of the polygon for which to find the incircle center and radius.
     * @return A Pair containing the coordinates of the incircle center (Vector3D) and the radius (Double),
     *         or null if not found (e.g., polygon is not convex, degenerate, or less than 3 vertices).
     */
    fun findInscribedCircleCenter(doc: GeoDoc, vertices: List<Point>): Vector3D? {
        if (vertices.size < 3)
            return null

        // Utility to get the side as line segment (pair of points)
        fun side(i: Int): Pair<Point, Point> {
            return Pair(vertices[i], vertices[(i + 1) % vertices.size])
        }

        // Calculate angle bisector lines at each vertex
        val bisectorLines = vertices.indices.map { i ->
            val bisectorVec = calculateBisectorVector(
                vertices[(i - 1 + vertices.size) % vertices.size],
                vertices[i],
                vertices[(i + 1) % vertices.size]
            )
            bisectorVec.toLineVi(doc, null, vertices[i])
        }

        // Try all possible triples of angle bisectors to find the intersection that is indeed the incenter
        // (This is robust against floating-point or near-degenerate cases)
        var maybeIncenter: Vector3D?
        for (i in 0 until bisectorLines.size) {
            for (j in i + 1 until bisectorLines.size) {
                val intersection = Lines.findIntersection(bisectorLines[i], bisectorLines[j]) ?: continue

                // Ensure intersection is strictly inside the polygon
                val inPoly = isPointInPolygon(intersection, vertices)
                if (!inPoly) continue

                val centerPoint = PointImpl(doc, "", intersection)

                // Compute perpendicular distances to all sides
                val distances = (0 until vertices.size).map { k ->
                    val (p1, p2) = side(k)
                    Lines.distancePointToSegment(centerPoint, p1, p2)
                }
                val minDist = distances.minOrNull() ?: return null
                val maxDist = distances.maxOrNull() ?: return null

                // All distances must be equal within tolerance, and strictly positive
                if (minDist <= DEFAULT_TOLERANCE) continue
                if (maxDist - minDist > DEFAULT_TOLERANCE) continue

                // This intersection is a valid incenter
                maybeIncenter = intersection
                return maybeIncenter
            }
        }
        return null
    }

    fun findCircumscribedCircleCenter(doc: GeoDoc, centerName: String?, polygon: Polygon): Point? {
        val vertices = polygon.vertices()
        if (vertices.size < 3) return null

        // Use the first three vertices to define the circumscribed circle
        val plane = Planes.fromPoints(
            vertices[0].coordinates(),
            vertices[1].coordinates(),
            vertices[2].coordinates(),
            DEFAULT_PRECISION
        )
        val center = Plane.intersection(
            perpendicularBisectingPlane(vertices[0].coordinates(), vertices[1].coordinates()),
            perpendicularBisectingPlane(vertices[0].coordinates(), vertices[2].coordinates()),
            plane
        )?.toPoint(doc, centerName) ?: return null

        val radius = calculateDistance(center, vertices[0])
        for (v in vertices) {
            if (!Precision.equals(calculateDistance(center, v), radius, DEFAULT_TOLERANCE)) {
                return null
            }
        }
        return center
    }

    private fun perpendicularBisectingPlane(p1: Vector3D, p2: Vector3D): Plane {
        val midpoint = Vector3D.of((p1.x + p2.x) / 2, (p1.y + p2.y) / 2, (p1.z + p2.z) / 2)
        return Planes.fromPointAndNormal(midpoint, p2.subtract(p1).normalize(), DEFAULT_PRECISION)
    }

    fun calculateDistance(p1: Point, p2: Point): Double {
        val deltaX = p2.x - p1.x
        val deltaY = p2.y - p1.y
        return kotlin.math.sqrt(deltaX * deltaX + deltaY * deltaY)
    }
}

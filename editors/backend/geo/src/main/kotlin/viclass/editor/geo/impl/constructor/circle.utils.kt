package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.*


/**
 * <AUTHOR>
 */
object Circles {
    fun calculatePointOnCircleWithRadian(doc: GeoDoc, name: String?, circle: Circle, alpha: Double): Point {
        val r = circle.radius
        val x0 = circle.centerPoint.x
        val y0 = circle.centerPoint.y
        val x = x0 + r * cos(alpha)
        val y = y0 + r * sin(alpha)
        return PointImpl(doc, name, x, y)
    }

    fun angleOfPoint(center: Vector3D, radius: Double, v: Vector3D): Double {
        val vX = v.x - center.x
        val vY = v.y - center.y
//        val vZ = v.z-center.z
        val cosAlpha = vX / radius
        if (vY >= 0) return acos(cosAlpha)
        else return 2 * PI - acos(cosAlpha)
    }

    fun isOnCircle(circle: Circle, v: Vector3D): Boolean {
        return abs(Distances.of(circle.centerPoint.coordinates(), v) - circle.radius) < DEFAULT_TOLERANCE
    }

    fun tangentAt(circle: Circle, at: Point): LineVi? {
        if (!isOnCircle(circle, at.coordinates())) return null
        val center = circle.centerPoint.coordinates()
        val v = at.coordinates()
        val vtpt = center.vectorTo(v)
        val parallelVector = Vector3D.of(vtpt.y, -vtpt.x, vtpt.z)
        return LineImpl(circle.doc, null, at, parallelVector)
    }

    /**
     * Computes tangents from a point to a circle,
     * supporting both the point on the circle and outside the circle.
     */
    fun tangentThroughPoint(circle: Circle, through: Point): List<LineVi>? {
        val I = circle.centerPoint.coordinates()
        val R = circle.radius
        val M = through.coordinates()

        val dist = Distances.of(I, M)

        // If the point is inside the circle, no tangent exists
        if (dist < R - 1e-10) return null

        // If the point is exactly on the circle, only one tangent exists: the tangent at that point
        if (Math.abs(dist - R) < 1e-10) {
            // The radius vector IM: direction (M.x - I.x, M.y - I.y)
            // The tangent's direction is perpendicular to this vector
            val dx = M.x - I.x
            val dy = M.y - I.y
            // Perpendicular direction:
            val v1 = Vector3D.of(-dy, dx, 0.0)
            val line = LineImpl(circle.doc, null, through, v1)
            return listOf(line)
        }

        // Point outside the circle: two tangents exist
        val X = I.x - M.x
        val Y = I.y - M.y

        val Y_R = Y * Y - R * R
        val X_R = X * X - R * R

        val a: Double = 1.0 // chọn a = 1 rồi tìm b
        val delta = Y * Y + X * X - R * R
        if (delta < 0.0) return null

        val sqrt_delta = sqrt(delta)
        val b1: Double
        val b2: Double

        if (Y == R) {
            b1 = X_R / (2 * R * X)
            b2 = -X_R / (2 * R * X)
        } else {
            b1 = -(R * sqrt_delta + X * Y) / Y_R
            b2 = (R * sqrt_delta - X * Y) / Y_R
        }

        return listOf(b1, b2).map { Vector3D.of(it, -a, 0.0) }.map {
            LineImpl(circle.doc, null, through, it)
        }
    }
}

package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementSectorPath
import viclass.editor.geo.dbentity.transformdata.PointOnArcWithAngleTransformData
import viclass.editor.geo.dbentity.transformdata.PointOnSectorWithRatioTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircularSector
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.anExpression
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.CircularSector
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Expr
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.extractable.ValueExpression
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnArcWithAngleTransformer
import viclass.editor.geo.impl.transformer.PointOnSectorWithRatioTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class PointOnCircularSectorEC : ElementConstructor<Point> {
    private enum class CGS {
        OnCircularSectorWithDegree, OnCircularSectorWithRadian, OnCircularSectorWithRatio, OnCircularSectorWithRatioExpr
    }

    override fun outputType(): KClass<Point> {
        return Point::class
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircularSectorWithRatio.name)
                    .hints("PointOnCircularSectorWithRatio")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircularSector]!!,
                        listOf("NameOfCircularSector"),
                        "tpl-OnCircularSector"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[anExpression]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-RatioValue",
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircularSectorWithRatioExpr.name)
                    .hints("PointOnCircularSectorWithRatioExpr")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircularSector]!!,
                        listOf("NameOfCircularSector"),
                        "tpl-OnCircularSector"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[anExpression]!!,
                        listOf(0),
                        listOf("Expression"),
                        "tpl-Ratio",
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircularSectorWithDegree.name)
                    .hints("PointOnCircularSectorWithDegree")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircularSector]!!,
                        listOf("NameOfCircularSector"),
                        "tpl-OnCircularSector"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Degree"),
                        "tpl-AngleDegree"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircularSectorWithRadian.name)
                    .hints("PointOnCircularSectorWithRadian")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircularSector]!!,
                        listOf("NameOfCircularSector"),
                        "tpl-OnCircularSector"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Radian"),
                        "tpl-AngleRadian"
                    )
                    .build(),
            )
            .elTypes(Point::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        return when(CGS.valueOf(c.cgName)) {
            CGS.OnCircularSectorWithDegree, CGS.OnCircularSectorWithRadian -> {
                Validations.validateNumConstraints(c, 2)
                constructOnCircularSectorWithAngle(doc, inputName, c)
            }

            CGS.OnCircularSectorWithRatio -> {
                Validations.validateNumConstraints(c, 2)
                constructPointOnSectorWithRatioValue(doc, inputName, c)
            }

            CGS.OnCircularSectorWithRatioExpr -> {
                Validations.validateNumConstraints(c, 2)
                constructPointOnSectorWithRatioExpression(doc, inputName, c)
            }
        }
    }

    private fun constructPointOnSectorWithRatioValue(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResultImpl<Point> {
        var sectorExResult: ConstructionResult<out CircularSector>? = null
        var ratio: Double = -1.0

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aCircularSector -> {
                    sectorExResult = extractFirstPossible<ElementExtraction<CircularSector>>(doc, PK_Name, p, c.ctIdx).result
                }
                aValue -> {
                    ratio = extractFirstPossible<NumberExtraction<Double>>(doc, PK_Value, p, c.ctIdx).result
                }
            }
        }

        val sectorResult = sectorExResult ?: throw ConstructionException("Thiếu tham số cung tròn cho việc tạo điểm bằng giá trị tỉ lệ.")

        val sector = sectorResult.result() ?: run {
            logger.error("Không tìm thấy cung tròn cho việc tạo điểm bằng giá trị tỉ lệ. (sectorResult.result() trả về null)")
            throw ConstructionException("Không tìm thấy cung tròn")
        }

        if (ratio < 0 || ratio > 1) {
            throw ConstructionException("Giá trị tỷ lệ phải nằm giữa 0 và 1 (bao gồm cả hai đầu).")
        }

        // Tính toán tọa độ điểm trên cung tròn dựa trên giá trị tỉ lệ
        val vS = sector.centerPoint.coordinates().vectorTo(sector.startPoint.coordinates())
        val vE = sector.centerPoint.coordinates().vectorTo(sector.endPoint.coordinates())
        val angle = vS.angleTo(vE)
        val v = CircularSectors.calculatePointOnCircularSectorWithRadian(sector, angle * ratio) ?: run {
            logger.error("Cannot construct point on sector")
            throw ConstructionException("Không tạo được điểm trên cugn tròn")
        }

        val name = inputName ?: generatePointName(doc)
        val p = PointImpl(doc, name, v)

        p.transformData = PointOnSectorWithRatioTransformData(
            targetParamIdx = 1, // Chỉ số của tham số tỉ lệ
            paramKind = PK_Value, // Loại tham số (giá trị)
            center = sector.centerPoint.coordinates().toArray(), // Tọa độ tâm của cung tròn
            pS = sector.startPoint.coordinates().toArray(), // Tọa độ điểm bắt đầu của cung tròn
            pE = sector.endPoint.coordinates().toArray(), // Tọa độ điểm kết thúc của cung tròn
        )
        p.transformer = TransformMapping.fromClazz(PointOnSectorWithRatioTransformer::class)
        p.movementPath = MovementSectorPath(
            sector.centerPoint.coordinates().toArray(),
            sector.startPoint.coordinates().toArray(),
            sector.endPoint.coordinates().toArray()
        )

        val result = ConstructionResultImpl<Point>()
        result.setResult(p)
        result.mergeAsDependency(sectorResult) // Gộp phụ thuộc từ kết quả xây dựng cung tròn

        return result
    }

    private fun constructPointOnSectorWithRatioExpression(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResultImpl<Point> {
        var sectorExResult: ConstructionResult<out CircularSector>? = null
        var ratioExpression: ValueExpression? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aCircularSector -> {
                    sectorExResult = extractFirstPossible<ElementExtraction<CircularSector>>(doc, PK_Name, p, c.ctIdx).result
                }
                anExpression -> {
                    ratioExpression = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(doc, PK_Expr, p, c.ctIdx).result
                }
            }
        }

        val sectorResult = sectorExResult ?: throw ConstructionException("Thiếu tham số cung tròn cho việc tạo điểm bằng biểu thức tỉ lệ.")
        val ratioExpr = ratioExpression ?: throw ConstructionException("Thiếu tham số biểu thức tỉ lệ.")

        val sector = sectorResult.result() ?: run {
            logger.error("Not found sector {}", c.params)
            throw ConstructionException("Không tìm thấy cung tròn")
        }

        val vS = sector.centerPoint.coordinates().vectorTo(sector.startPoint.coordinates())
        val vE = sector.centerPoint.coordinates().vectorTo(sector.endPoint.coordinates())
        val angle = vS.angleTo(vE)

        val v = CircularSectors.calculatePointOnCircularSectorWithRadian(sector, angle * ratioExpr.value) ?: run {
            logger.error("Cannot construct point on sector {}", c.params)
            throw ConstructionException("Không tìm thấy điểm trên cung tròn")
        }

        val name = inputName ?: generatePointName(doc)
        val p = PointImpl(doc, name, v)

        val result = ConstructionResultImpl<Point>()
        result.setResult(p)
        result.mergeAsDependency(sectorResult) // Gộp phụ thuộc từ kết quả xây dựng cung tròn
        ratioExpr.constructions().forEach { result.mergeAsDependency(it) } // Gộp phụ thuộc từ các construction của biểu thức

        return result
    }

    private fun constructOnCircularSectorWithAngle(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val result = ConstructionResultImpl<Point>()

        var sector: CircularSector? = null
        var angle: Double? = null

        c.params.forEach {
            when (it.paramDef.id) {
                aCircularSector -> {
                    val extractedCircleResult = extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    sector = extractedCircleResult.result.result()!!
                    result.mergeAsDependency(extractedCircleResult.result)
                }

                aValue -> {
                    val extractedRangeResult = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    angle = extractedRangeResult.result
                }
            }
        }

        if (sector == null || angle == null) {
            throw ConstructionException("missing circle or angle")
        }

        if(c.cgName == CGS.OnCircularSectorWithDegree.name) angle = radian(angle)

        val name = inputName ?: generatePointName(doc)
        val v = CircularSectors.calculatePointOnCircularSectorWithRadian(sector, angle)
            ?: throw ConstructionException("angle out of bound")

        val point = PointImpl(doc, name, v)

        point.transformData = PointOnArcWithAngleTransformData(
            1,
            ParamKind.PK_Value,
            sector.centerPoint.coordinates().toArray(),
            sector.startPoint.coordinates().toArray(),
            degree = c.cgName == CGS.OnCircularSectorWithDegree.name
        )

        point.transformer = TransformMapping.fromClazz(PointOnArcWithAngleTransformer::class)
        point.movementPath = MovementSectorPath(
            sector.centerPoint.coordinates().toArray(),
            sector.startPoint.coordinates().toArray(),
            sector.endPoint.coordinates().toArray(),
        )

        result.setResult(point)
        result.addDependency(sector, listOf(), true)

        return result
    }
}

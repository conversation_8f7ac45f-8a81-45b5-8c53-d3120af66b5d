package viclass.editor.geo.impl.render

import io.ktor.util.reflect.*
import kotlinx.collections.immutable.toImmutableList
import viclass.editor.geo.dbentity.*
import viclass.editor.geo.dbentity.renderdata.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.ViException
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.impl.elements.createVectorByEl
import viclass.editor.geo.render.RenderDoc
import viclass.editor.geo.render.RenderDocState
import kotlin.reflect.KClass

class RenderDocImpl constructor(val doc: GeoDoc, private val _state: RenderDocState) : RenderDoc {
    private val _relements: MutableList<RenderElement> = mutableListOf()

    override val elements: List<RenderElement>
        get() = _relements.toImmutableList()

    override val state: RenderDocState
        get() = _state.clone()

    override fun clone(): RenderDocImpl {
        val o = RenderDocImpl(doc, state)
        o._relements.addAll(_relements.map { it.clone() })
        return o
    }

    /**
     * Elements must be rendered in the same order they are inside the geodoc
     */
    @Throws(RuntimeException::class)
    override fun render(element: Element, renderProp: RenderProp?, rerender: Boolean?): List<RenderElement> {
        if (doc != element.doc) throw ViException("Phần tử thuộc về một tài liệu không sở hữu tài liệu hiển thị này")

        val elIndex = doc.getIndex(element)
            ?: throw ElementNotExistInDocumentException("Phần tử không được lập chỉ mục trong tài liệu")
        val ctIndex = doc.getConstructionIndex(element)
            ?: throw ElementNotExistInDocumentException("Cấu trúc của phần tử không được lập chỉ mục trong tài liệu")

        val curRenderEl: RenderElement? = findMatchRenderElement(element)

        val depsRenderEl = mutableSetOf<RenderElement>()

        val renderEl: RenderElement = when (element) {
            is Point -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderVertex::class, elIndex)?.let {
                    if (rerender == true) it as RenderVertex
                    else return listOf()
                } ?: RenderVertex(renderProp ?: buildPointRenderProp(doc.docDefaultElRenderProp))

                element.movementPath?.let {
                    r.movementPath = element.movementPath
                }

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.coords = element.coordinates().toArray()

                r
            }

            is Polygon -> {
                // Find and add the index of the current render element if it exists, or create a new RenderPolygon if not
                val r = findAndAddElIdx(curRenderEl, element.name, RenderPolygon::class, elIndex)?.let {
                    // If rerender = true then cast to RenderPolygon, otherwise return an empty list
                    if (rerender == true) it as RenderPolygon
                    else return listOf()
                } ?: RenderPolygon(
                    // Use the provided renderProp if available, otherwise build the default from doc
                    renderProp ?: buildPolygonRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                )

                // Get the list of vertices of the polygon
                val vertexes = element.vertices()

                // Iterate through each pair of consecutive vertices to create edges of the polygon
                listOf(vertexes, listOf(vertexes.first())).flatten().reduce { currVertex, nextVertex ->
                    // Create RenderLine for each edge of the polygon
                    val rdLine = buildRenderLineFromVertex(
                        ctIndex,
                        depsRenderEl,
                        currVertex,
                        nextVertex,
                        element.usable,
                        element.deleted,
                        element.valid,
                        buildPolygonEdgeRenderProp(doc.docDefaultElRenderProp),
                        rerender
                    )

                    // Assign elIndex to this edge
                    // Add the element index to the current line's elIndexes list
                    try {
                        rdLine.elIndexes.add(elIndex)
                        // Find the render element associated with the current vertex by name; throw an exception if not found
                        val currVertexEl = findRenderElByName(currVertex.name, RenderVertex::class)
                            ?: throw NoSuchElementException("Không tìm thấy phần tử hiển thị cho đỉnh hiện tại: ${currVertex.name}")
                        // Add the first elIndex from the found render element for the current vertex
                        rdLine.elIndexes.add(currVertexEl.elIndexes.first())
                        // Find the render element associated with the next vertex by name; throw an exception if not found
                        val nextVertexEl = findRenderElByName(nextVertex.name, RenderVertex::class)
                            ?: throw NoSuchElementException("Không tìm thấy phần tử hiển thị cho đỉnh tiếp theo: ${nextVertex.name}")
                        // Add the first elIndex from the found render element for the next vertex
                        rdLine.elIndexes.add(nextVertexEl.elIndexes.first())
                    } catch (e: Exception) {
                        // If any exception occurs during the process, wrap it in a RuntimeException and throw with a detailed message
                        throw RuntimeException(
                            "Đã xảy ra lỗi nội bộ khi xử lý dữ liệu. Vui lòng thử lại hoặc liên hệ hỗ trợ.", e
                        )
                    }

                    // Add the edge index to the polygon's lineRelIdxes list
                    r.lineRelIdxes.add(rdLine.relIndex)
                    nextVertex
                }

                // Add the polygon's elIndex to its elIndexes list
                r.elIndexes.add(elIndex)
                // Set the name for the render polygon
                r.name = element.name ?: ""
                // Determine the vertices (faces) of the polygon by finding corresponding RenderVertex elements
                r.faces = findRenderVertices(element).map { it.relIndex }
                // Calculate the area of the polygon
                r.area = element.area()
                // Calculate the perimeter of the polygon
                r.perimeter = element.perimeter()

                // Add all vertex indexes to vertexRelIdxes
                r.vertexRelIdxes.addAll(r.faces)

                r
            }

            is Circle -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderCircleShape::class, elIndex)?.let {
                    if (rerender == true) it as RenderCircleShape
                    else return listOf()
                } ?: RenderCircleShape(
                    renderProp ?: buildCircleShapeRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                )

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.centerPointIdx = findMatchRenderElement(element.centerPoint)!!.relIndex
                r.radius = element.radius
                r.area = element.area()
                r.perimeter = element.perimeter()

                val arc = findRenderElByName(element.name, RenderCircle::class) ?: run {
                    val arc = RenderCircle(
                        buildCircleRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                    )
                    arc.relIndex = _relements.size
                    _relements.add(arc)
                    arc
                }
                arc.elIndexes.add(elIndex)
                arc.name = element.name ?: ""
                arc.centerPointIdx = r.centerPointIdx
                arc.vertexRelIdxes.add(r.centerPointIdx)
                arc.radius = element.radius
                arc.length = r.perimeter
                arc.deleted = element.deleted
                arc.usable = element.usable
                arc.valid = element.valid

                depsRenderEl.add(arc)

                r.arcRelIdx = arc.relIndex
                r.vertexRelIdxes.add(r.centerPointIdx)

                r
            }

            is Ellipse -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderEllipseShape::class, elIndex)?.let {
                    if (rerender == true) it as RenderEllipseShape
                    else return listOf()
                } ?: RenderEllipseShape(renderProp ?: buildEllipseShapeRenderProp(doc.docDefaultElRenderProp))
                
                val f1 = element.f1?.let { findMatchRenderElement(it) }
                val f2 = element.f2?.let { findMatchRenderElement(it) }
                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.f1Idx = f1?.relIndex ?: -1
                r.f2Idx = f2?.relIndex ?: -1

                r.a = element.a
                r.b = element.b
                r.rotate = element.rotate
                r.area = element.area()
                r.perimeter = element.perimeter()

                // Handle center-vector data for EllipseImpl
                if (element.hasCenterVectorData()) {
                    // Find or create render vertices for center and vector points
                    val centerPoint = element.centerPoint?.let { findMatchRenderElement(it) }
                    val vectorAPoint = element.vectorA?.let { findMatchRenderElement(it) }
                    val vectorBPoint = element.vectorB?.let { findMatchRenderElement(it) }

                    r.centerIdx = centerPoint?.relIndex ?: -1
                    r.vaIdx = vectorAPoint?.relIndex ?: -1
                    r.vbIdx = vectorBPoint?.relIndex ?: -1

                    // Add center and vector points to vertex relations
                    centerPoint?.let { r.vertexRelIdxes.add(it.relIndex) }
                    vectorAPoint?.let { r.vertexRelIdxes.add(it.relIndex) }
                    vectorBPoint?.let { r.vertexRelIdxes.add(it.relIndex) }
                }

                val arc = findRenderElByName(element.name, RenderEllipse::class) ?: run {
                    val arc = RenderEllipse(
                        buildSectorRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                    )
                    arc.relIndex = _relements.size
                    _relements.add(arc)
                    arc
                }
                arc.elIndexes.add(elIndex)
                arc.name = element.name ?: ""
                arc.f1Idx = f1?.relIndex ?: -1
                arc.f2Idx = f2?.relIndex ?: -1
                arc.a = r.a
                arc.b = r.b
                arc.rotate = r.rotate
                arc.length = r.perimeter

                // Copy center-vector properties from RenderEllipseShape
                arc.centerIdx = r.centerIdx
                arc.vaIdx = r.vaIdx
                arc.vbIdx = r.vbIdx

                f1?.let { arc.vertexRelIdxes.add(it.relIndex) }
                f2?.let { arc.vertexRelIdxes.add(it.relIndex) }
                // Add center and vector points to vertex relations if they exist
                if (r.centerIdx != -1 && r.vaIdx != -1 && r.vbIdx != -1) {
                    arc.vertexRelIdxes.add(r.centerIdx)
                    arc.vertexRelIdxes.add(r.vaIdx)
                    arc.vertexRelIdxes.add(r.vbIdx)
                }

                arc.deleted = element.deleted
                arc.usable = element.usable
                arc.valid = element.valid

                depsRenderEl.add(arc)

                r.arcRelIdx = arc.relIndex
                f1?.let { r.vertexRelIdxes.add(it.relIndex) }
                f2?.let { r.vertexRelIdxes.add(it.relIndex) }

                arc.f1Idx = r.f1Idx
                arc.f2Idx = r.f2Idx
                r
            }

            is Semicircle -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderSectorShape::class, elIndex)?.let {
                    if (rerender == true) it as RenderSectorShape
                    else return listOf()
                } ?: RenderSectorShape(
                    renderProp ?: buildSectorShapeRenderProp(doc.docDefaultElRenderProp),
                    elType = Semicircle::class.simpleName!!
                )

                val rdVertexC = findMatchRenderElement(element.centerPoint) as RenderVertex?
                    ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
                val rdVertexS = findMatchRenderElement(element.startPoint) as RenderVertex?
                    ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
                val rdVertexE = findMatchRenderElement(element.endPoint) as RenderVertex?
                    ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
                val rdLineS = buildRenderLineFromVertex(
                    ctIndex,
                    depsRenderEl,
                    element.centerPoint,
                    element.startPoint,
                    element.usable,
                    element.deleted,
                    element.valid,
                    buildSectorLineRenderProp(doc.docDefaultElRenderProp),
                    rerender
                )
                val rdLineE = buildRenderLineFromVertex(
                    ctIndex,
                    depsRenderEl,
                    element.centerPoint,
                    element.endPoint,
                    element.usable,
                    element.deleted,
                    element.valid,
                    buildSectorLineRenderProp(doc.docDefaultElRenderProp),
                    rerender
                )

                rdLineS.elIndexes.add(elIndex)
                rdLineE.elIndexes.add(elIndex)

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.centerPointIdx = rdVertexC.relIndex
                r.startPointIdx = rdVertexS.relIndex
                r.endPointIdx = rdVertexE.relIndex
                r.radius = element.radius
                r.area = element.area()
                r.length = element.length()

                val arc = findRenderElByName(element.name, RenderSector::class) ?: run {
                    val arc =
                        RenderSector(buildSectorRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!)
                    arc.relIndex = _relements.size
                    _relements.add(arc)
                    arc
                }
                arc.elIndexes.add(elIndex)
                arc.name = element.name ?: ""
                arc.centerPointIdx = rdVertexC.relIndex
                arc.startPointIdx = rdVertexS.relIndex
                arc.endPointIdx = rdVertexE.relIndex
                arc.radius = r.radius
                arc.length = r.length

                arc.vertexRelIdxes.add(rdVertexS.relIndex)
                arc.vertexRelIdxes.add(rdVertexE.relIndex)
                arc.deleted = element.deleted
                arc.usable = element.usable
                arc.valid = element.valid

                depsRenderEl.add(arc)

                r.lineRelIdxes.add(rdLineS.relIndex)
                r.lineRelIdxes.add(rdLineE.relIndex)

                r.arcRelIdx = arc.relIndex

                r.vertexRelIdxes.add(r.startPointIdx)
                r.vertexRelIdxes.add(r.endPointIdx)

                r
            }

            is CircularSector -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderSectorShape::class, elIndex)?.let {
                    if (rerender == true) it as RenderSectorShape
                    else return listOf()
                } ?: RenderSectorShape(renderProp ?: buildSectorShapeRenderProp(doc.docDefaultElRenderProp))

                val rdVertexCenter = findMatchRenderElement(element.centerPoint) as RenderVertex?
                    ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
                val rdVertexS = findMatchRenderElement(element.startPoint) as RenderVertex?
                    ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
                val rdVertexE = findMatchRenderElement(element.endPoint) as RenderVertex?
                    ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
                val rdLineS = buildRenderLineFromVertex(
                    ctIndex,
                    depsRenderEl,
                    element.centerPoint,
                    element.startPoint,
                    element.usable,
                    element.deleted,
                    element.valid,
                    buildSectorLineRenderProp(doc.docDefaultElRenderProp),
                    rerender
                )
                val rdLineE = buildRenderLineFromVertex(
                    ctIndex,
                    depsRenderEl,
                    element.centerPoint,
                    element.endPoint,
                    element.usable,
                    element.deleted,
                    element.valid,
                    buildSectorLineRenderProp(doc.docDefaultElRenderProp),
                    rerender
                )

                rdLineS.elIndexes.add(elIndex)
                rdLineE.elIndexes.add(elIndex)

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.centerPointIdx = rdVertexCenter.relIndex
                r.startPointIdx = rdVertexS.relIndex
                r.endPointIdx = rdVertexE.relIndex
                r.radius = Distances.of(element.centerPoint, element.startPoint)
                r.area = element.area()
                r.length = element.length()

                val arc = findRenderElByName(element.name, RenderSector::class) ?: run {
                    val arc =
                        RenderSector(buildSectorRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!)
                    arc.relIndex = _relements.size
                    _relements.add(arc)
                    arc
                }
                arc.elIndexes.add(elIndex)
                arc.name = element.name ?: ""
                arc.centerPointIdx = rdVertexCenter.relIndex
                arc.startPointIdx = rdVertexS.relIndex
                arc.endPointIdx = rdVertexE.relIndex
                arc.radius = r.radius
                arc.length = r.length
                arc.deleted = element.deleted
                arc.usable = element.usable
                arc.valid = element.valid

                arc.vertexRelIdxes.add(rdVertexCenter.relIndex)
                arc.vertexRelIdxes.add(rdVertexS.relIndex)
                arc.vertexRelIdxes.add(rdVertexE.relIndex)

                depsRenderEl.add(arc)

                r.arcRelIdx = arc.relIndex

                r.lineRelIdxes.add(rdLineS.relIndex)
                r.lineRelIdxes.add(rdLineE.relIndex)

                r.vertexRelIdxes.add(rdVertexCenter.relIndex)
                r.vertexRelIdxes.add(rdVertexS.relIndex)
                r.vertexRelIdxes.add(rdVertexE.relIndex)

                r
            }

            is VectorVi -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderVector::class, elIndex)?.let {
                    if (rerender == true) it as RenderVector
                    else return listOf()
                } ?: RenderVector(
                    renderProp ?: buildLineRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                )

                val vertices = findRenderVertices(element)

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.startPointIdx = vertices[0].relIndex
                r.vertexRelIdxes.add(r.startPointIdx)
                if (vertices.size == 2) {
                    r.endPointIdx = vertices[1].relIndex
                    r.vertexRelIdxes.add(r.endPointIdx)
                }
                r.vector = element.parallelVector.toArray()
                r.length = element.length()

                r
            }

            is LineSegment -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderLineSegment::class, elIndex)?.let {
                    if (rerender == true) it as RenderLineSegment
                    else return listOf()
                } ?: RenderLineSegment(
                    renderProp ?: buildLineRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                )

                val vertices = findRenderVertices(element)

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.startPointIdx = vertices[0].relIndex
                r.vertexRelIdxes.add(r.startPointIdx)
                if (vertices.size == 2) {
                    r.endPointIdx = vertices[1].relIndex
                    r.vertexRelIdxes.add(r.endPointIdx)
                }
                r.vector = element.parallelVector.toArray()
                r.length = element.length()

                r
            }

            is Ray -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderRay::class, elIndex)?.let {
                    if (rerender == true) it as RenderRay
                    else return listOf()
                } ?: RenderRay(
                    renderProp ?: buildLineRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                )

                val vertices = findRenderVertices(element)

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.startPointIdx = vertices[0].relIndex
                r.vertexRelIdxes.add(r.startPointIdx)
                if (vertices.size == 2) {
                    r.endPointIdx = vertices[1].relIndex
                    r.vertexRelIdxes.add(r.endPointIdx!!)
                }
                r.vector = element.parallelVector.toArray()
                r.length = element.length()

                r
            }

            is LineVi -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderLine::class, elIndex)?.let {
                    if (rerender == true) it as RenderLine
                    else return listOf()
                } ?: RenderLine(
                    renderProp ?: buildLineRenderProp(doc.docDefaultElRenderProp), element.clazz.simpleName!!
                )

                val vertices = findRenderVertices(element)

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.startPointIdx = vertices[0].relIndex
                r.vertexRelIdxes.add(r.startPointIdx)
                if (vertices.size == 2) {
                    r.endPointIdx = vertices[1].relIndex
                    r.vertexRelIdxes.add(r.endPointIdx!!)
                }
                r.vector = element.parallelVector.toArray()
                r.length = element.length()

                r
            }

            is Angle -> {
                val r = findAndAddElIdx(curRenderEl, element.name, RenderAngle::class, elIndex)?.let {
                    if (rerender == true) it as RenderAngle
                    else return listOf()
                } ?: RenderAngle(renderProp ?: buildAngleRenderProp(doc.docDefaultElRenderProp, element))

                val lS = findMatchRenderElement(element.lineStart)!!
                val lE = findMatchRenderElement(element.lineEnd)!!

                r.elIndexes.add(elIndex)
                r.name = element.name ?: ""
                r.anglePointIdx = findMatchRenderElement(element.anglePoint)!!.relIndex
                r.startVIdx = lS.relIndex
                r.endVIdx = lE.relIndex
                r.startVDir = element.directionStart
                r.endVDir = element.directionEnd
                r.lineRelIdxes.add(lS.relIndex)
                r.lineRelIdxes.add(lE.relIndex)
                r.vertexRelIdxes.addAll(setOf(r.anglePointIdx))

                val lineStart = element.lineStart.orderedVector().multiply(r.startVDir.toDouble())
                val lineEnd = element.lineEnd.orderedVector().multiply(r.endVDir.toDouble())

                val angleDeg = Math.toDegrees(lineStart.angleTo(lineEnd))
                r.degree = if (angleDeg < 0) angleDeg + 360 else angleDeg

                r
            }

            else -> throw ViException("Loại phần tử không thể hiển thị")
        }

        if (curRenderEl == null) {
            renderEl.relIndex = _relements.size
            _relements.add(renderEl)
        }

        // Only the first element that creates the render element is allowed to affect the render element
        if (renderEl.elIndexes.first() == elIndex) {
            renderEl.usable = element.usable
            renderEl.deleted = element.deleted
            renderEl.valid = element.valid
        }

        depsRenderEl.add(renderEl)

        return depsRenderEl.toList()
    }

    private fun buildRenderLineFromVertex(
        ctIndex: Int,
        depsRenderEl: MutableSet<RenderElement>,
        vertex1: Point,
        vertex2: Point,
        usable: Boolean = true,
        deleted: Boolean? = null,
        valid: Boolean = true,
        lineProp: RenderProp = buildLineRenderProp(doc.docDefaultElRenderProp),
        rerender: Boolean? = false,
    ): RenderLineSegment {
        val lineName = if (vertex1.name.isNullOrBlank() || vertex2.name.isNullOrBlank()) ""
        else "${vertex1.name}${vertex2.name}"

        var rdLine = doc.findElementByName(lineName, LineSegment::class, ctIndex)
            ?.let { findMatchRenderElement(it) as RenderLineSegment } ?: findRenderElByName(
            lineName, RenderLineSegment::class
        )
        rdLine = rdLine?.let {
            if (rerender == true) it
            else return it
        }

        if (rdLine == null) {
            rdLine = RenderLineSegment(lineProp, LineSegment::class.simpleName!!)
            rdLine.relIndex = _relements.size
            rdLine.startPointIdx = findMatchRenderElement(vertex1)?.relIndex
                ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
            rdLine.endPointIdx = findMatchRenderElement(vertex2)?.relIndex
                ?: throw ElementNotExistInDocumentException("không tìm thấy phần tử hiển thị")
            rdLine.name = lineName
            rdLine.vertexRelIdxes.addAll(listOf(rdLine.startPointIdx, rdLine.endPointIdx))

            _relements.add(rdLine)
        }

        val vec = createVectorByEl(doc, vertex1, vertex2)
        rdLine.vector = vec.toArray()
        rdLine.length = Distances.of(vertex1, vertex2)

        depsRenderEl.add(rdLine)

        val els = rdLine.elIndexes.map { doc.elements[it] }
        if (els.any { it.usable } || usable) rdLine.usable = true else rdLine.usable = false
        if (els.all { it.deleted == true } && deleted == true) rdLine.deleted = true else rdLine.deleted = false
        if (els.any { it.valid } || valid) rdLine.valid = true else rdLine.valid = false

        return rdLine
    }

    override fun addRenderEls(renderEls: List<RenderElement>) {
        _relements.addAll(renderEls)
    }

    override fun updateRelsValid(idxes: Set<Int>, valid: Boolean) {
        idxes.map { this._relements[it] }.forEach { it.valid = valid }
    }

    override fun updateRelsDeleted(idxes: Set<Int>, deleted: Boolean?) {
        idxes.map { this._relements[it] }.forEach { it.deleted = deleted }
    }

    override fun updateRelsUsable(idxes: Set<Int>, usable: Boolean) {
        idxes.map { this._relements[it] }.forEach { it.usable = usable }
    }

    override fun updateDocSize(width: Double, height: Double) {
        this._state.canvasWidth = width
        this._state.canvasHeight = height
    }

    override fun updateDocState(docRenderProp: DocRenderProp) {
        this._state.scale = docRenderProp.scale

        this._state.screenUnit = docRenderProp.screenUnit
        this._state.canvasWidth = docRenderProp.canvasWidth
        this._state.canvasHeight = docRenderProp.canvasHeight

        this._state.translation = docRenderProp.translation
        this._state.rotation = docRenderProp.rotation

        this._state.axis = docRenderProp.axis
        this._state.grid = docRenderProp.grid
        this._state.detailGrid = docRenderProp.detailGrid

        this._state.snapMode = docRenderProp.snapMode
        this._state.snapToExistingPoints = docRenderProp.snapToExistingPoints
        this._state.snapToGrid = docRenderProp.snapToGrid
        this._state.namingMode = docRenderProp.namingMode
    }

    override fun updateDocDefaultElRenderProps(docDefaultElRenderProp: DocDefaultElRenderProp) {
        this.doc.docDefaultElRenderProp.color = docDefaultElRenderProp.color
        this.doc.docDefaultElRenderProp.lineColor = docDefaultElRenderProp.lineColor
        this.doc.docDefaultElRenderProp.pointColor = docDefaultElRenderProp.pointColor
        this.doc.docDefaultElRenderProp.opacity = docDefaultElRenderProp.opacity
        this.doc.docDefaultElRenderProp.lineWeight = docDefaultElRenderProp.lineWeight
        this.doc.docDefaultElRenderProp.strokeStyle = docDefaultElRenderProp.strokeStyle
        this.doc.docDefaultElRenderProp.spaceFromArcToCorner = docDefaultElRenderProp.spaceFromArcToCorner
    }

    override fun clearUnusableElements() {
        val unusableEls = this._relements.filter { !it.usable }
        this._relements.removeAll(unusableEls)
    }

    override fun deleteRenderElements(relIdx: Int, elDepIdxes: List<Int>): List<Int> {
        val relRoot = _relements.getOrNull(relIdx) ?: return emptyList()
        relRoot.deleted = true

        val res: MutableList<Int> = mutableListOf(relIdx)

        for ((idx, rel) in _relements.withIndex()) {
            if (relIdx == idx || rel.deleted == true) continue
            if (rel.elIndexes.size > 0 && elDepIdxes.containsAll(rel.elIndexes)) {
                if (rel is RenderLineSegment) {
                    if (relRoot !is RenderPolygon && !rel.vertexRelIdxes.contains(relIdx)) continue
                }
                rel.deleted = true
                res.add(idx)
                continue
            }

            val tobeDelete = when (rel) {
                is RenderLine -> {
                    rel.vertexRelIdxes.map { _relements[it] }.any { it.deleted == true }
                }

                is RenderRay -> {
                    rel.vertexRelIdxes.map { _relements[it] }.any { it.deleted == true }
                }

                is RenderLineSegment -> {
                    rel.vertexRelIdxes.map { _relements[it] }.any { it.deleted == true }
                }

                is RenderPolygon -> {
                    listOf(rel.vertexRelIdxes, rel.lineRelIdxes).flatten().map { _relements[it] }
                        .any { it.deleted == true }
                }

                is RenderAngle -> {
                    listOf(rel.vertexRelIdxes, rel.lineRelIdxes).flatten().map { _relements[it] }
                        .any { it.deleted == true }
                }

                is RenderSectorShape -> {
                    listOf(rel.vertexRelIdxes, rel.lineRelIdxes, listOf(rel.arcRelIdx)).flatten().map { _relements[it] }
                        .any { it.deleted == true }
                }

                is RenderCircleShape -> {
                    listOf(rel.centerPointIdx, rel.arcRelIdx).map { _relements[it] }.any { it.deleted == true }
                }

                is RenderEllipseShape -> {
                    listOf(rel.vertexRelIdxes, listOf(rel.arcRelIdx)).flatten().map { _relements[it] }
                        .any { it.deleted == true }
                }

                is RenderSector -> {
                    listOf(rel.vertexRelIdxes).flatten().map { _relements[it] }.any { it.deleted == true }
                }

                is RenderEllipse -> {
                    listOf(rel.vertexRelIdxes).flatten().map { _relements[it] }.any { it.deleted == true }
                }

                is RenderCircle -> {
                    listOf(rel.vertexRelIdxes).flatten().map { _relements[it] }.any { it.deleted == true }
                }

                else -> false
            }

            if (tobeDelete) {
                rel.deleted = true
                res.add(idx)
            }
        }

        return res
    }

    /**
     * Retrieve the list of render vertices of an element, given that
     * this element has been rendered
     */
    private fun findRenderVertices(element: Element): List<RenderVertex> {
        return element.vertices().mapNotNull { findMatchRenderElement(it) as? RenderVertex }
    }

    private fun <T : RenderElement> findAndAddElIdx(
        curRenderEl: T?, name: String?, clazz: KClass<out T>, elIdx: Int
    ): T? {
        if (curRenderEl != null) return curRenderEl
        return findRenderElByName(name, clazz)?.let {
            if (!it.elIndexes.contains(elIdx)) it.elIndexes.add(elIdx)
            it
        }
    }

    private fun findMatchRenderElement(element: Element): RenderElement? {
        return _relements.firstOrNull { it.isMatchElement(element) }
    }

    @Suppress("UNCHECKED_CAST")
    override fun <T : RenderElement> findRenderElByName(name: String?, relType: KClass<out T>): T? {
        if (name.isNullOrBlank()) return null
        for (el in _relements) {
            if (el.usable && el.deleted != true && el.instanceOf(relType) && el.isNameMatch(name)) {
                return el as T
            }
        }

        return null
    }
}
package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnSectorWithRatioTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.transformer.Transformer

/**
 * A transformer that updates a parameter based on the angular position of a point within a circular sector.
 *
 * This transformer calculates the ratio of the angle of the point (relative to the sector's start)
 * to the total angle of the sector. The resulting ratio is clamped to the range [0.0, 1.0] and then
 * stored as a string in a specified target parameter.
 */
class PointOnSectorWithRatioTransformer : Transformer<PointOnSectorWithRatioTransformData> {
    /**
     * Applies the transformation by calculating an angular ratio.
     *
     * @param doc The geographic document, which is not used in this transformer.
     * @param c The construction context containing the parameters to be updated.
     * @param transformData The data defining the sector (center, start, end points) and the target parameter.
     * @param pos The current 3D position of the point being transformed.
     */
    override fun apply(doc: GeoDoc, c: Construction, transformData: PointOnSectorWithRatioTransformData, pos: Vector3D) {
        // Define the sector's geometry from the transform data.
        val center = Vector3D.of(transformData.center)
        val sectorStartPoint = Vector3D.of(transformData.pS)
        val sectorEndPoint = Vector3D.of(transformData.pE)

        // Create vectors from the center to the key points. These vectors define the angles.
        val vectorToStart = center.vectorTo(sectorStartPoint)
        val vectorToEnd = center.vectorTo(sectorEndPoint)
        val vectorToPosition = center.vectorTo(pos)

        // Calculate the total angle of the sector.
        val sectorAngle = vectorToStart.angleTo(vectorToEnd)

        // Avoid division by zero if the sector has no angular width (i.e., start and end vectors are collinear).
        if (sectorAngle < DEFAULT_TOLERANCE) {
            // If the sector angle is negligible, no meaningful ratio can be calculated.
            // We return early to avoid changing the parameter state with NaN or Infinity.
            return
        }

        // Calculate the angle of the current position relative to the start of the sector.
        val positionAngle = vectorToStart.angleTo(vectorToPosition)

        // Calculate the ratio of the position's angle to the total sector angle.
        val ratio = positionAngle / sectorAngle

        // Clamp the ratio to the [0.0, 1.0] range to ensure it's a valid percentage.
        // This handles cases where the point might be dragged outside the sector's angular bounds.
        val clampedRatio = ratio.coerceIn(0.0, 1.0)

        // Retrieve the target parameter from the construction.
        val targetParameter = c.params[transformData.targetParamIdx].specs
            .getParam(transformData.paramKind) as ParamStoreValue

        // Update the parameter's value with the calculated and clamped ratio.
        targetParameter.value = clampedRatio.toString()
    }
}
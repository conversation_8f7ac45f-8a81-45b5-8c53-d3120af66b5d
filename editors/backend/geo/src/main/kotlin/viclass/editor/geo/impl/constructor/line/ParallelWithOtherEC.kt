package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.constructor.Intersections.createTempCircleFromSector
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnLineWithCoefficientTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

data class ExtractedParallelElements(
    val throughPoint: Point?,
    val baseLine: LineVi?,
    val intersectionElement: Element?,
    val nthIntersection: Int?,
    val dependencies: List<ConstructionResult<out Element>>
)

@Singleton
class ParallelWithOtherEC : ElementConstructor<LineVi> {
    private val constraintParam = ConstraintParamDefManager.instance()

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        ThroughPointParallelWithLine, ThroughPointSegmentParallelWithLine, ThroughPointSegmentParallelWithLineAndIntersectionLine, ThroughPointSegmentParallelWithPoint, ThroughPointParallelWithCircle, ThroughPointParallelWithEllipse, ThroughPointParallelWithSector
    }

    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create().name(CGS.ThroughPointParallelWithLine.name)
            .hints("LineThroughAPointParallelWithLine").constraint(
                0, constraintParam[ConstraintParamDefManager.aPoint]!!, listOf("NameOfPoint"), "tpl-ThroughPoint"
            )
            .constraint(1, constraintParam[ConstraintParamDefManager.aLine]!!, listOf("NameOfLine"), "tpl-ParallelWith")
            .build()

        return ConstructorTemplateBuilder.create(this).apply {
            cgs(
                cg1,
                ConstraintGroupBuilder.create().name(CGS.ThroughPointSegmentParallelWithPoint.name)
                    .hints("SegmentThroughPointParallelWithPoint").constraint(
                        0,
                        constraintParam[ConstraintParamDefManager.aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-ThroughPoint"
                    ).constraint(
                        1, constraintParam[ConstraintParamDefManager.aLine]!!, listOf("NameOfLine"), "tpl-ParallelWith"
                    ).constraint(
                        2, constraintParam[ConstraintParamDefManager.aPoint]!!, listOf("NameOfPoint"), "tpl-EndPoint"
                    ).build(),
                ConstraintGroupBuilder.create().name(CGS.ThroughPointParallelWithCircle.name)
                    .hints("LineThroughAPointParallelWithCircle").constraint(
                        0,
                        constraintParam[ConstraintParamDefManager.aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-ThroughPoint"
                    ).constraint(
                        1, constraintParam[ConstraintParamDefManager.aLine]!!, listOf("NameOfLine"), "tpl-ParallelWith"
                    ).constraintDepends(
                        2,
                        constraintParam[ConstraintParamDefManager.aCircle]!!,
                        listOf(0, 1),
                        listOf("NameOfCircle"),
                        "tpl-IntersectionWithCircle"
                    ).constraintOptional(
                        3,
                        constraintParam[ConstraintParamDefManager.aValue]!!,
                        listOf(0, 1, 2),
                        listOf("NthIntersection"),
                        "tpl-thIntersection"
                    ).build(),
                ConstraintGroupBuilder.create().name(CGS.ThroughPointParallelWithEllipse.name)
                    .hints("LineThroughAPointParallelWithEllipse").constraint(
                        0,
                        constraintParam[ConstraintParamDefManager.aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-ThroughPoint"
                    ).constraint(
                        1, constraintParam[ConstraintParamDefManager.aLine]!!, listOf("NameOfLine"), "tpl-ParallelWith"
                    ).constraintDepends(
                        2,
                        constraintParam[ConstraintParamDefManager.anEllipse]!!,
                        listOf(0, 1),
                        listOf("NameOfEllipse"),
                        "tpl-IntersectionWithEllipse"
                    ).constraintOptional(
                        3,
                        constraintParam[ConstraintParamDefManager.aValue]!!,
                        listOf(0, 1, 2),
                        listOf("NthIntersection"),
                        "tpl-thIntersection"
                    ).build(),
                ConstraintGroupBuilder.create().name(CGS.ThroughPointParallelWithSector.name)
                    .hints("LineThroughAPointParallelWithSector").constraint(
                        0,
                        constraintParam[ConstraintParamDefManager.aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-ThroughPoint"
                    ).constraint(
                        1, constraintParam[ConstraintParamDefManager.aLine]!!, listOf("NameOfLine"), "tpl-ParallelWith"
                    ).constraintDepends(
                        2,
                        constraintParam[ConstraintParamDefManager.aCircularSector]!!,
                        listOf(0, 1),
                        listOf("NameOfSector"),
                        "tpl-IntersectionWithSector"
                    ).constraintOptional(
                        3,
                        constraintParam[ConstraintParamDefManager.aValue]!!,
                        listOf(0, 1, 2),
                        listOf("NthIntersection"),
                        "tpl-thIntersection"
                    ).build()
            )
            elTypes(LineVi::class)
        }.build()
    }

    /**
     * Generic method to extract elements from construction parameters
     */
    private fun extractElements(doc: GeoDoc, c: Construction): ExtractedParallelElements {
        var throughPoint: Point? = null
        var baseLine: LineVi? = null
        var intersectionElement: Element? = null
        var nthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach { param ->
            when (param.paramDef.id) {
                ConstraintParamDefManager.aPoint -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    throughPoint = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm đi qua")
                    dependencies.add(extraction.result)
                }

                ConstraintParamDefManager.aLine -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    baseLine = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("Không tìm thấy đường thẳng gốc")
                    dependencies.add(extraction.result)
                }

                ConstraintParamDefManager.aCircle -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    intersectionElement = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("Không tìm thấy đường tròn")
                    dependencies.add(extraction.result)
                }

                ConstraintParamDefManager.anEllipse -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    intersectionElement = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("Không tìm thấy hình elip")
                    dependencies.add(extraction.result)
                }

                ConstraintParamDefManager.aCircularSector -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    intersectionElement = extraction.result.result()
                        ?: throw ElementNotExistInDocumentException("Không tìm thấy hình quạt tròn")
                    dependencies.add(extraction.result)
                }

                ConstraintParamDefManager.aValue -> if (param.specs.indexInCG == 3) {
                    val extraction =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, param, c.ctIdx)
                    nthIntersection = extraction.result.toInt() // Force to Int (because some case it return Double)
                }
            }
        }

        return ExtractedParallelElements(throughPoint, baseLine, intersectionElement, nthIntersection, dependencies)
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ThroughPointParallelWithLine -> {
                Validations.validateNumConstraints(c, 2)
                constructParallelWithLine(doc, inputName, c)
            }

            CGS.ThroughPointSegmentParallelWithLine -> {
                Validations.validateNumConstraints(c, 3)
                constructSegmentParallelWithLine(doc, inputName, c)
            }

            CGS.ThroughPointSegmentParallelWithLineAndIntersectionLine -> {
                Validations.validateNumConstraints(c, 3)
                constructSegmentParallelWithLineAndIntersectionLine(doc, inputName, c)
            }

            CGS.ThroughPointSegmentParallelWithPoint -> {
                Validations.validateNumConstraints(c, 3)
                constructSegmentParallelWithPoint(doc, inputName, c)
            }

            CGS.ThroughPointParallelWithCircle, CGS.ThroughPointParallelWithEllipse, CGS.ThroughPointParallelWithSector -> {
                Validations.validateNumConstraints(c, 3)
                constructThroughPointParallelWithCurvedElement(doc, inputName, c)
            }
        }
    }

    private fun findEndingPoint(
        sPointX: Double, sPointY: Double, uVectorX: Double, uVectorY: Double, k: Double
    ): List<Double> {
        val x = sPointX + uVectorX * k;
        val y = sPointY + uVectorY * k;
        return listOf(x, y);
    }

    private fun constructSegmentParallelWithLine(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3 = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, c.params[2], c.ctIdx)

        val sourceLine = exr1.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy đường")
        val pointThrough = exr2.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm")
        val k = exr3.result

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(pointThrough.name)
        }?.first() ?: generatePointName(doc)

        val vu = sourceLine.parallelVector.normalize()
        val coords = this.findEndingPoint(
            pointThrough.coordinates().x, pointThrough.coordinates().y, vu.x, vu.y, k
        )
        val newPoint = PointImpl(doc, pointName, coords[0], coords[1])
        newPoint.transformer = TransformMapping.fromClazz(PointOnLineWithCoefficientTransformer::class)
        newPoint.transformData = PointOnLineWithCoefficientTransformData(
            targetParamIdx = 2,
            paramKind = ParamKind.PK_Value,
            rootPoint = pointThrough.coordinates().toArray(),
            unitVector = vu.toArray()
        )
        newPoint.movementPath = MovementLinePath(pointThrough.coordinates().toArray(), vu.toArray())

        val newLine = LineSegmentImpl(
            doc, "${pointThrough.name}${newPoint.name}", pointThrough, newPoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.addDependency(newPoint, listOf(pointThrough, sourceLine), true)

        return cr
    }

    private fun constructSegmentParallelWithLineAndIntersectionLine(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)

        val sourceLine = exr1.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy đường")
        val pointThrough = exr2.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm")
        val intersectionLine =
            exr3.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy đường giao")

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(pointThrough.name)
        }?.first() ?: generatePointName(doc)
        val tempLine = LineImpl(doc, null, pointThrough, sourceLine.parallelVector)
        val intersectionPoint = Intersections.of(intersectionLine, tempLine) ?: throw ConstructionException("Đường sai")

        val newpoint = PointImpl(doc, pointName, intersectionPoint.x, intersectionPoint.y)

        val newLine = LineSegmentImpl(
            doc, "${pointThrough.name}${newpoint.name}", pointThrough, newpoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)
        cr.addDependency(newpoint, listOf(pointThrough, sourceLine, intersectionLine), true)

        return cr
    }

    private fun constructSegmentParallelWithPoint(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)

        val startPoint = exr1.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm")
        val sourceLine = exr2.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy đường")
        val endPoint = exr3.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm kết thúc")

        val segmentVector = startPoint.coordinates().vectorTo(endPoint.coordinates())
        val sourceLineVector = sourceLine.orderedVector()

        val crossProduct = segmentVector.normalize().cross(sourceLineVector.normalize()).norm()
        if (crossProduct > DEFAULT_TOLERANCE) {
            throw ConstructionException("Đoạn thẳng giữa hai điểm không song song với đường thẳng gốc")
        }

        val lineName: String = if (inputName.isNullOrBlank()) {
            "${startPoint.name}${endPoint.name}"
        } else inputName

        val newLine = LineSegmentImpl(
            doc, lineName, startPoint, endPoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)

        return cr
    }

    private fun constructParallelWithLine(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        var exr1: ElementExtraction<Point>? = null
        var exr2: ElementExtraction<LineVi>? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                ConstraintParamDefManager.aPoint -> {
                    exr1 = extractFirstPossible(doc, ParamKind.PK_Name, p, c.ctIdx)
                }

                ConstraintParamDefManager.aLine -> {
                    exr2 = extractFirstPossible(doc, ParamKind.PK_Name, p, c.ctIdx)
                }
            }
        }
        val pointThrough =
            exr1!!.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm đi qua")
        val sourceLine =
            exr2!!.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy đường thẳng")

        val cr = ConstructionResultImpl<LineVi>()

        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName
        var newLine: LineVi = LineImpl(doc, lineName, pointThrough, sourceLine.parallelVector)

        NamePattern.get(LineSegment::class)!![0].find(lineName)?.let {
            val pName1 = it.groupValues[1]
            val pName2 = it.groupValues[2]

            val p1 = doc.findElementByName(pName1, Point::class, c.ctIdx)
                ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm đi qua")
            val p2 = doc.findElementByName(pName2, Point::class, c.ctIdx)
                ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm đi qua")

            if (pName1 != pointThrough.name && !newLine.line().contains(p1.coordinates())) {
                throw ConstructionException("Điểm $pName1 không nằm trên đường thẳng mục tiêu $lineName")
            }
            if (pName2 != pointThrough.name && !newLine.line().contains(p2.coordinates())) {
                throw ConstructionException("Điểm $pName2 không nằm trên đường thẳng mục tiêu $lineName")
            }

            newLine = LineSegmentImpl(doc, lineName, p1, p2)

            cr.addDependency(p1, emptyList(), true)
            cr.addDependency(p2, emptyList(), true)
        }

        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)

        return cr
    }

    /**
     * Generic construction method for parallel lines with curved elements
     */
    private fun constructThroughPointParallelWithCurvedElement(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        val extracted = extractElements(doc, c)

        val throughPoint =
            extracted.throughPoint ?: throw ElementNotExistInDocumentException("Không tìm thấy điểm đi qua")
        val baseLine = extracted.baseLine ?: throw ElementNotExistInDocumentException("Không tìm thấy đường thẳng gốc")
        val intersectionElement =
            extracted.intersectionElement ?: throw ElementNotExistInDocumentException("Không tìm thấy phần tử giao cắt")

        // Create parallel line through the point
        val parallelLine = LineImpl(doc, null, throughPoint, baseLine.orderedVector())

        // Convert sector to circle if needed for intersection calculation
        val actualIntersectionElement = if (intersectionElement is CircularSector) {
            createTempCircleFromSector(intersectionElement)
        } else {
            intersectionElement
        }

        // Calculate intersections
        val allIntersections = when (actualIntersectionElement) {
            is Circle -> Intersections.of(parallelLine, actualIntersectionElement)
            is Ellipse -> Intersections.of(parallelLine, actualIntersectionElement)
            else -> emptyList()
        } ?: throw ConstructionException("Không có giao điểm giữa đường thẳng song song và phần tử")

        if (allIntersections.isEmpty()) throw ConstructionException("Không có giao điểm")

        val actualNthIntersection = extracted.nthIntersection ?: 0

        // Validate nth parameter is within bounds
        if (actualNthIntersection < 0 || actualNthIntersection >= allIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${allIntersections.size} giao điểm.")
        }

        val orderedIntersections =
            Orders.orderIntersections(doc, allIntersections, parallelLine, actualIntersectionElement)

        val selectedIntersection = orderedIntersections[actualNthIntersection]

        if (intersectionElement is CircularSector && !CircularSectors.isOnCircularSector(
                intersectionElement, selectedIntersection
            )
        ) {
            throw ConstructionException("Điểm giao không nằm trên cung tròn")
        }

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(throughPoint.name)
        }?.first() ?: generatePointName(doc)

        val newPoint = PointImpl(doc, pointName, selectedIntersection.x, selectedIntersection.y)
        val newLine = LineSegmentImpl(doc, "${throughPoint.name}${newPoint.name}", throughPoint, newPoint)

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)

        // Add dependencies from extracted elements
        extracted.dependencies.forEach { dependency ->
            cr.mergeAsDependency(dependency)
        }
        cr.addDependency(newPoint, extracted.dependencies.mapNotNull { it.result() }, true)

        return cr
    }
}

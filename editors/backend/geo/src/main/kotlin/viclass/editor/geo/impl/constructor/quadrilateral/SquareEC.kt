package viclass.editor.geo.impl.constructor.quadrilateral

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.transformdata.FreePointTransformData
import viclass.editor.geo.dbentity.transformdata.MoveOrderTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.elements.Square
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.SquareImpl
import viclass.editor.geo.impl.transformer.FreePointTransformer
import viclass.editor.geo.impl.transformer.MoveOrderTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.math.PI
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class SquareEC : ElementConstructor<Square> {

    override fun outputType(): KClass<Square> {
        return Square::class
    }

    private enum class CGS {
        FromLineSegment, FromTwoPosition, FromPoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.FromLineSegment.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("LineSegment"),
                        "tpl-LineSegment"
                    )
                    .constraintOptional(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.FromTwoPosition.name)
                    .numDim(3)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(Square::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Square> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c)
            }

            CGS.FromLineSegment -> {
                Validations.validateNumConstraints(c, 1)
                constructFromLineSegment(doc, inputName, c)
            }

            CGS.FromTwoPosition -> {
                Validations.validateNumConstraints(c, 2)
                constructFromTwoPosition(doc, inputName, c)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Square> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Square::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }
                    .sortedBy { it } != pointNames) throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val square = SquareImpl(
            doc,
            inputName ?: points.map { it.name }.joinToString(""),
            points[0],
            points[1],
            points[2],
            points[3]
        )
        val cr = ConstructionResultImpl<Square>()
        cr.addDependencies(points, true)
        cr.setResult(square)
        return cr;
    }

    private fun constructFromTwoPosition(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Square> {
        val ext1 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[0], c.ctIdx)
        val ext2 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[1], c.ctIdx)
        val ext3 = extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[2], c.ctIdx)

        val thResult = ext3.result - 1

        val pointsName: List<String> = if (!inputName.isNullOrBlank()) {
            NamePattern.extractPointName(Polygon::class, inputName)
        } else {
            val excluded = ArrayList<String>()
            (0..4).map { generatePointName(doc, excluded) }
        }

        val p1 = PointImpl(doc, pointsName[0], ext1.result[0], ext1.result[1], .0)
        val p2 = PointImpl(doc, pointsName[1], ext2.result[0], ext2.result[1], .0)

        val v1 = p1.coordinates()
        val v2 = p2.coordinates()

        val sig = if (thResult == 1) 1 else -1

        val v3 = v1.rotate(PI / 2 * sig, v2)
        val v4 = v2.rotate(-PI / 2 * sig, v1)


        val p3 = v3.toPoint(doc, pointsName[2])
        val p4 = v4.toPoint(doc, pointsName[3])

        val vC = Points.calculateCenterPoint(p1, p3)

        p1.transformData = FreePointTransformData(0)
        p1.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
        p1.movementPath = MovementFreePath()

        p2.transformData = FreePointTransformData(1)
        p2.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
        p2.movementPath = MovementFreePath()

        val buildTransformer: (p: Point) -> Unit = { p ->
            p.transformData = MoveOrderTransformData(vC.toArray(), p.coordinates().toArray(), listOf(0, 1))
            p.transformer = TransformMapping.fromClazz(MoveOrderTransformer::class)
            p.movementPath = MovementFreePath()
        }

        buildTransformer(p3)
        buildTransformer(p4)

        val square = SquareImpl(doc, pointsName.joinToString(""), p1, p2, p3, p4)

        val cr = ConstructionResultImpl<Square>()
        cr.setResult(square)
        cr.addDependencies(listOf(p1, p2), true)
        cr.addDependency(p3, listOf(p1, p2), true)
        cr.addDependency(p4, listOf(p1, p2), true)

        return cr
    }

    private fun constructFromLineSegment(
        doc: GeoDoc,
        inputName: String?,
        c: Construction,
    ): ConstructionResult<Square> {
        val ext1 = extractFirstPossible<ElementExtraction<LineSegment>>(doc, PK_Name, c.params[0], c.ctIdx)
        val ext2 = extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[1], c.ctIdx)

        val thResult = ext2.result

        val line = ext1.result.result() ?: throw ElementNotExistInDocumentException("not found line segment")

        val pointsName: List<String> = if (!inputName.isNullOrBlank()) {
            NamePattern.extractPointName(Polygon::class, inputName)
        } else {
            val excluded = ArrayList<String>()
            (0..4).map { generatePointName(doc, excluded) }
        }

        val p1 = line.p1
        val p2 = line.p2

        val v1 = p1.coordinates()
        val v2 = p2.coordinates()

        val sig = if (thResult == 1) -1 else 1

        val v3 = v1.rotate(PI / 2 * sig, v2)
        val v4 = v2.rotate(-PI / 2 * sig, v1)

        val p3 = v3.toPoint(doc, pointsName[2])
        val p4 = v4.toPoint(doc, pointsName[3])

        val square = SquareImpl(doc, pointsName.joinToString(""), p1, p2, p3, p4)

        val cr = ConstructionResultImpl<Square>()
        cr.setResult(square)
        cr.addDependencies(listOf(p1, p2), true)
        cr.addDependency(p3, listOf(p1, p2), true)
        cr.addDependency(p4, listOf(p1, p2), true)

        return cr
    }
}

package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.impl.elements.CircleImpl
import kotlin.math.abs

/** <AUTHOR> */
object Lines {
    /**
     * Find all points on the given line (or line segment) `l` that are at a distance `d` from the point `root`.
     *
     * This function computes the intersection points between the line (or segment) and a circle centered at `root` with
     * radius `d`. If the line type is a segment, only intersection points that actually lie on the segment (not just
     * the infinite line) are returned.
     *
     * @param lineType The type of the line (e.g., segment or infinite line), as defined in ConstraintParamDefManager
     * @param l The LineVi object representing the line or segment
     * @param root The reference point from which the distance is measured
     * @param d The distance from `root` to the sought points on `l`
     * @return A list of Vector3D points satisfying the condition, or null if there are no intersections
     */
    fun findPointOnlineWithLength(lineType: String, l: LineVi, root: Point, d: Double): List<Vector3D>? {
        // Find intersection points between the line/segment and the circle centered at root with
        // radius d
        val intersections = Intersections.of(l, CircleImpl(l.doc, null, root, d)) ?: return null

        return if (lineType == ConstraintParamDefManager.aLineSegment) {
            // If the line is a segment, filter out points that do not lie within the segment
            // endpoints
            val (p1, p2) = l.run { p1.coordinates() to p2!!.coordinates() }
            intersections
                .filter {
                    // Check if the sum of distances from p1 to the point and from the point to p2
                    // is approximately equal to the length of the segment (allowing for a small
                    // tolerance)
                    p1.distance(it) + p2.distance(it) <= p1.distance(p2) + DEFAULT_TOLERANCE
                }
                .takeIf { it.isNotEmpty() }
        } else intersections // For infinite lines, return all intersection points
    }

    /**
     * Find the intersection point of two lines.
     *
     * This function represents both lines in the general form a*x + b*y = c, computes the determinant, and solves for
     * the intersection point. If the lines are parallel (determinant near zero), returns null.
     *
     * @param doc The GeoDoc document context
     * @param centerName The name for the intersection point (can be null)
     * @param l1 The first line
     * @param l2 The second line
     * @return A Point object representing the intersection, or null if the lines are parallel
     */
    fun findIntersection(l1: LineVi, l2: LineVi): Vector3D? {
        // Represent both lines in the general form: a*x + b*y = c
        val (a1, b1, c1) = with(l1.line().direction) { Triple(y, -x, y * l1.p1.x - x * l1.p1.y) }
        val (a2, b2, c2) = with(l2.line().direction) { Triple(y, -x, y * l2.p1.x - x * l2.p1.y) }

        // Compute the determinant to check if the lines are parallel
        val determinant = a1 * b2 - a2 * b1
        if (abs(determinant) < DEFAULT_TOLERANCE) return null // Lines are parallel

        // Solve for the intersection point coordinates
        val centerX = (b2 * c1 - b1 * c2) / determinant
        val centerY = (a1 * c2 - a2 * c1) / determinant

        return Vector3D.of(centerX, centerY, 0.0)
    }

    /**
     * Compute the shortest distance from a point to a line segment ab using pure math.
     *
     * This function projects the point onto the line defined by ab, then checks if the projection falls within the
     * segment. If not, the distance to the nearest endpoint is returned.
     *
     * @param point The point from which the distance is measured
     * @param a The first endpoint of the segment
     * @param b The second endpoint of the segment
     * @return The shortest distance from `point` to the segment ab
     */
    fun distancePointToSegment(point: Point, a: Point, b: Point): Double {
        val px = point.x
        val py = point.y
        val ax = a.x
        val ay = a.y
        val bx = b.x
        val by = b.y

        val abx = bx - ax
        val aby = by - ay
        val apx = px - ax
        val apy = py - ay

        val ab2 = abx * abx + aby * aby
        if (ab2 == 0.0) {
            // a and b are the same point
            val dx = px - ax
            val dy = py - ay
            return Math.sqrt(dx * dx + dy * dy)
        }
        // projection parameter t
        val t = (apx * abx + apy * aby) / ab2
        return when {
            t < 0.0 -> {
                // Closest to a
                val dx = px - ax
                val dy = py - ay
                Math.sqrt(dx * dx + dy * dy)
            }
            t > 1.0 -> {
                // Closest to b
                val dx = px - bx
                val dy = py - by
                Math.sqrt(dx * dx + dy * dy)
            }
            else -> {
                // Closest to point on the segment
                val projx = ax + t * abx
                val projy = ay + t * aby
                val dx = px - projx
                val dy = py - projy
                Math.sqrt(dx * dx + dy * dy)
            }
        }
    }

    /**
     * Create a LineVi object from two points.
     *
     * This function constructs a LineVi (line or segment) connecting points p1 and p2.
     *
     * @param doc The GeoDoc document context
     * @param name The name of the line (can be null)
     * @param p1 The first endpoint
     * @param p2 The second endpoint
     * @return A LineVi object representing the line or segment between p1 and p2
     */
    fun getLineViFrom2Points(doc: GeoDoc, name: String?, p1: Point, p2: Point): LineVi =
        p1.coordinates().subtract(p2.coordinates()).toLineVi(doc, name, p1, p2)

    fun isPointLiesOn(lineObj: LineVi, pt: Vector3D): Boolean {
        when (lineObj) {
            is LineSegment -> {
                // Must be between endpoints (within tolerance)
                val p1 = lineObj.p1.coordinates()
                val p2 = lineObj.p2.coordinates()
                val v1 = p2.subtract(p1)
                val v2 = pt.subtract(p1)
                val dot = v1.dot(v2)
                val lenSq = v1.normSq()
                return dot >= -DEFAULT_TOLERANCE &&
                        dot <= lenSq + DEFAULT_TOLERANCE &&
                        v1.cross(v2).norm() <= DEFAULT_TOLERANCE
            }

            is VectorVi -> {
                // Ray: starts at p1, extends in direction of parallelVector
                val p1 = lineObj.p1.coordinates()
                val dir = lineObj.parallelVector
                val v = pt.subtract(p1)
                val dot = dir.dot(v)
                return dot >= -DEFAULT_TOLERANCE && dir.cross(v).norm() <= DEFAULT_TOLERANCE
            }

            is Ray -> {
                // Ray: identical to VectorImpl for this purpose
                val p1 = lineObj.p1.coordinates()
                val dir = lineObj.parallelVector
                val v = pt.subtract(p1)
                val dot = dir.dot(v)
                return dot >= -DEFAULT_TOLERANCE && dir.cross(v).norm() <= DEFAULT_TOLERANCE
            }

            is LineVi -> {
                // Infinite line: always true
                return true
            }

            else -> {
                // If unknown type, check if the point lies on the infinite line
                // with a proximity check
                val distance = lineObj.line().distance(pt)
                return distance <= DEFAULT_TOLERANCE
            }
        }
    }
}

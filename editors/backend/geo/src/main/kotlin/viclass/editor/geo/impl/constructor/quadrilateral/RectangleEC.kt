package viclass.editor.geo.impl.constructor.quadrilateral

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Quadrilateral
import viclass.editor.geo.elements.Rectangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RectangleImpl
import viclass.editor.geo.impl.elements.createVectorByEl
import viclass.editor.geo.impl.transformer.PointOnLineWithLengthTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class RectangleEC : ElementConstructor<Rectangle> {

    override fun outputType(): KClass<Rectangle> {
        return Rectangle::class
    }

    private enum class CGS {
        LineSegmentAndLength, FromPoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegmentAndLength.name)
                    .invisible()
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value"),
                        "tpl-LengthValue"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(Rectangle::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Rectangle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c)
            }
            CGS.LineSegmentAndLength -> {
                Validations.validateNumConstraints(c, 2)
                constructFromLineSegmentAndLength(doc, inputName!!, c, c.params)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Rectangle> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Rectangle::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }.sortedBy { it } != pointNames) throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val rectangle = RectangleImpl(doc, inputName ?: points.map { it.name }.joinToString(""), points[0], points[1], points[2], points[3])
        val cr = ConstructionResultImpl<Rectangle>()
        cr.addDependencies(points, true)
        cr.setResult(rectangle)
        return cr;
    }

    private fun constructFromLineSegmentAndLength(
        doc: GeoDoc, inputName: String, c: Construction, params: List<ConstructionParams>
    ): ConstructionResult<Rectangle> {
        var thResult: Int? = null
        var p1: Point? = null
        var p2: Point? = null
        var length: Double? = null

        params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val store: ParamStoreValue = ((p.specs.getParam(ParamKind.PK_Name)
                        ?: throw InvalidElementNameException("Parameter with name ${ParamKind.PK_Name} doesn't have any value"))
                            as ParamStoreValue)
                    val lineNameSubmit = store.value
                    val pointsName = NamePattern.extractPointName(LineSegment::class, lineNameSubmit)
                    val points = pointsName.map { n -> doc.findElementByName(n, Point::class, c.ctIdx) }
                    p1 = points[0]!!
                    p2 = points[1]!!
                }

                aValue -> if (p.specs.indexInCG == 1) {
                    val extractedResult = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    length = extractedResult.result
                } else if (p.specs.indexInCG == 2) {
                    val extractedResult = extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                }
            }
        }

        val names = NamePattern.extractPointName(Quadrilateral::class, inputName)
        val vertexGroup = ArrayList<List<Point>>()

        val v3s =
            RightTriangles.computeRightTriangleAdjacentVertices(p2!!, p1!!, length!!)
        if (thResult == null) {
            v3s
                .map { PointImpl(doc, names[2], it) }
                .map { findVertexes(doc, names, p1!!, p2!!, it) }.filter { it.isNotEmpty() }
                .forEach { vertexGroup.add(it) }
        } else {
            val parallelVector = createVectorByEl(doc, p1!!, p2!!)
            val v = Orders.pointByParallelVector(parallelVector, p1!!.coordinates(), v3s[0], v3s[1])[thResult!!]
            val ps = findVertexes(doc, names, p1!!, p2!!, PointImpl(doc, names[2], v))
            vertexGroup.add(ps)
        }

        val points = vertexGroup.firstOrNull()
            ?: throw ConstructionException("Cannot construct rectangle")

        val setupTransform: (p: Point, root: Vector3D) -> Unit = { p, vRoot ->
            val root = vRoot.toArray()
            val unitVector = p.coordinates().vectorTo(vRoot).toArray()
            p.transformer = TransformMapping.fromClazz(PointOnLineWithLengthTransformer::class)
            p.transformData = PointOnLineWithLengthTransformData(
                lengthParamIdx = 1,
                lengthParamKind = ParamKind.PK_Value,
                nthParamIdx = 2,
                root = root,
                unitVector = unitVector
            )
            p.movementPath = MovementLinePath(root, unitVector)
        }

        setupTransform(points[2], p2!!.coordinates())
        setupTransform(points[3], p1!!.coordinates())

        val cr = ConstructionResultImpl<Rectangle>()
        cr.setResult(RectangleImpl(doc, inputName, points[0], points[1], points[2], points[3]))
        cr.addDependency(points[0], listOf(), true)
        cr.addDependency(points[1], listOf(), true)
        cr.addDependency(points[2], listOf(), true)
        cr.addDependency(points[3], listOf(), true)

        return cr
    }

    private fun findVertexes(doc: GeoDoc, names: List<String>, p1: Point, p2: Point, p3: Point): List<Point> {
        val vM = Points.calculateCenterPoint(p1, p3)
        val pM = PointImpl(doc, null, vM)
        val line_p2M = LineSegmentImpl(doc, null, p2, pM)

        val v4s = Lines.findPointOnlineWithLength(ConstraintParamDefManager.aLine, line_p2M, pM, line_p2M.length())
            ?.filter { it.distance(p2.coordinates()) > DEFAULT_TOLERANCE }

        v4s ?: return emptyList()

        val v4 = v4s.first()

        val p4 = PointImpl(doc, (names - setOf(p1.name!!, p2.name!!, p3.name!!)).first(), v4)

        return listOf(p1, p2, p3, p4)
    }
}

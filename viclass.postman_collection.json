{"info": {"_postman_id": "ae0366c0-f47e-467b-ac2c-1fd672c10fb3", "name": "viclass", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "1890130"}, "item": [{"name": "frd", "item": [{"name": "createDoc", "protocolProfileBehavior": {"disabledSystemHeaders": {"user-agent": true}}, "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON><PERSON>", "value": "https://devlocal.viclass.vn/classrooms/63aeaee1395de0143d5a4d94/online", "type": "text"}, {"key": "Origin", "value": "https://devlocal.viclass.vn", "type": "text"}, {"key": "sec-ch-ua", "value": "\"Not?A_Brand\";v=\"8\", \"Chromium\";v=\"108\", \"Google Chrome\";v=\"108\"", "type": "text"}, {"key": "sec-ch-ua-mobile", "value": "?0", "type": "text"}, {"key": "sec-ch-ua-platform", "value": "\"Windows\"", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "viclass_sess=eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7Il9hdCI6Ijg4N2RkYWRkYzQ0NWZkMDRjOGNjNjExMDQ4YTRjNGE2IiwiX3RvIjoiMTgwMDAwMCIsIl9zaWQiOiI4ZjdjMjc1MC1lNjQwLTQwYWYtOTJjOS1lOGJkOGZiNjBkMzUiLCJfdSI6InJ5YW4ifSwibmJmIjoxNjcyMzkyMzM3LCJpYXQiOjE2NzIzOTIzMzd9.AIaGBeGBWROREwAeEsFUaCMBePP6t6NgKZoRnArYOzA", "type": "text"}, {"key": "Sec-Fetch-Dest", "value": "empty", "type": "text"}, {"key": "Sec-Fetch-Mode", "value": "cors", "type": "text"}, {"key": "Sec-Fetch-Site", "value": "same-origin", "type": "text"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "type": "text"}, {"key": "Host", "value": "devlocal.viclass.vn", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{viclass.host}}/frd/createDoc", "host": ["{{viclass.host}}"], "path": ["frd", "createDoc"]}}, "response": []}, {"name": "createDoc Copy", "protocolProfileBehavior": {"disabledSystemHeaders": {"user-agent": true}}, "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON><PERSON>", "value": "https://devlocal.viclass.vn/classrooms/63aeaee1395de0143d5a4d94/online", "type": "text"}, {"key": "Origin", "value": "https://devlocal.viclass.vn", "type": "text"}, {"key": "sec-ch-ua", "value": "\"Not?A_Brand\";v=\"8\", \"Chromium\";v=\"108\", \"Google Chrome\";v=\"108\"", "type": "text"}, {"key": "sec-ch-ua-mobile", "value": "?0", "type": "text"}, {"key": "sec-ch-ua-platform", "value": "\"Windows\"", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "viclass_sess=eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7Il9hdCI6Ijg4N2RkYWRkYzQ0NWZkMDRjOGNjNjExMDQ4YTRjNGE2IiwiX3RvIjoiMTgwMDAwMCIsIl9zaWQiOiI4ZjdjMjc1MC1lNjQwLTQwYWYtOTJjOS1lOGJkOGZiNjBkMzUiLCJfdSI6InJ5YW4ifSwibmJmIjoxNjcyMzkyMzM3LCJpYXQiOjE2NzIzOTIzMzd9.AIaGBeGBWROREwAeEsFUaCMBePP6t6NgKZoRnArYOzA", "type": "text"}, {"key": "Sec-Fetch-Dest", "value": "empty", "type": "text"}, {"key": "Sec-Fetch-Mode", "value": "cors", "type": "text"}, {"key": "Sec-Fetch-Site", "value": "same-origin", "type": "text"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "type": "text"}, {"key": "Host", "value": "devlocal.viclass.vn", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8010/createDoc", "protocol": "http", "host": ["localhost"], "port": "8010", "path": ["createDoc"]}}, "response": []}]}, {"name": "word", "item": [{"name": "createDoc", "request": {"method": "POST", "header": [], "url": {"raw": "{{viclass.host}}/word/createDoc", "host": ["{{viclass.host}}"], "path": ["word", "createDoc"]}}, "response": []}, {"name": "processCmd", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/octet-stream", "type": "text"}, {"key": "Content-Transfer-Encoding", "value": "application/octet-stream", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "abc"}, "url": {"raw": "{{viclass.host}}/word/cmd", "host": ["{{viclass.host}}"], "path": ["word", "cmd"]}}, "response": []}, {"name": "getDocument", "request": {"method": "GET", "header": [], "url": {"raw": "{{viclass.host}}/word/document/6402d70a3ced0f32164cbe05", "host": ["{{viclass.host}}"], "path": ["word", "document", "6402d70a3ced0f32164cbe05"]}}, "response": []}]}, {"name": "conf-server", "item": [{"name": "editors/:env", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"specs\": [\r\n        {\r\n            \"item\" : \"free-drawing-editor\",\r\n            \"impl\" : true,\r\n            \"ui\" : true\r\n        },\r\n        {\r\n            \"item\" : \"geometry-editor\",\r\n            \"impl\" : true,\r\n            \"ui\" : true\r\n        },\r\n        {\r\n            \"item\" : \"word-editor\",\r\n            \"impl\" : {\r\n                \"settings\": {\r\n                    \"embedded\": [\"free-drawing-editor\", \"geometry-editor\"]\r\n                }\r\n            },\r\n            \"ui\" : {\r\n                \"settings\": {\r\n                    \"emebedded\": [\"free-drawing-editor\", \"geometry-editor\"]\r\n                }\r\n            }\r\n        },\r\n        {\r\n            \"item\": \"editor-ui-base-style\",\r\n            \"ui\" : true\r\n        },\r\n        {\r\n            \"item\": \"common-tools-editor\",\r\n            \"ui\" : true\r\n        },\r\n        {\r\n            \"item\": \"zoom-tools-editor\",\r\n            \"ui\" : true\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{viclass.host}}/conf/editors/dev", "host": ["{{viclass.host}}"], "path": ["conf", "editors", "dev"]}}, "response": []}, {"name": "editors/:env-embed", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"specs\": [\r\n        {\r\n            \"item\" : \"free-drawing-editor\",\r\n            \"impl\" : {\r\n                \"useCase\": \"embed\"\r\n            }\r\n        },\r\n        {\r\n            \"item\" : \"geometry-editor\",\r\n            \"impl\" : {\r\n                \"useCase\": \"embed\"\r\n            }\r\n        },\r\n        {\r\n            \"item\" : \"word-editor\",\r\n            \"impl\" : {\r\n                \"settings\": {\r\n                    \"embedded\": [\"free-drawing-editor\", \"geometry-editor\"]\r\n                },\r\n                \"useCase\": \"embed\"\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{viclass.host}}/conf/editors/dev", "host": ["{{viclass.host}}"], "path": ["conf", "editors", "dev"]}}, "response": []}]}]}
# Portal Beta Package Documentation

## Tổng quan

Package `portal.beta` là một microservice trong hệ thống <PERSON>, chuy<PERSON>n quản lý việc đăng ký và mời tham gia chương trình beta testing.

## Kiến trúc tổng thể

### Công nghệ sử dụng
- **Framework**: Ktor (Kotlin web framework)
- **Dependency Injection**: Koin
- **Database**: MongoDB
- **Cache**: Redis
- **Communication**: gRPC
- **Serialization**: Jackson, Protobuf
- **Session Management**: Dagger

### Cấu trúc thư mục
```
portal/beta/src/main/kotlin/portal/beta/
├── configuration/     # Các class cấu hình
├── dbgateway/        # Database access layer
├── gateway/          # External service gateways
├── koin/            # Dependency injection modules
├── models/          # Data transfer objects
├── plugins/         # Ktor plugins (CORS, Routing)
├── pojo/            # Plain old Java objects
├── services/        # Business logic layer
└── utility/         # Utility functions
```

## C<PERSON><PERSON> thành phần chính

### 1. BetaService
Service chính xử lý logic nghiệp vụ cho beta invitations:

**Chức năng chính:**
- `getOrCreateBetaRegistration`: Tạo registration mới với code mới cho mỗi lần đăng ký
- `inviteJoinBeta`: Gửi email mời tham gia beta cho danh sách accounts
- `applyInvitationCode`: Xác thực invitation code
- `generateBetaVerificationLink`: Tạo link xác thực beta với shortURL
- `generateVerificationCode`: Tạo mã xác thực ngẫu nhiên 9 ký tự

**Dependencies:**
- NotificationServiceGateway: Gửi email
- BetaInvitationGateway: Truy cập database
- IShorturlServiceGateway: Rút gọn URL
- ServerConfig: Cấu hình server

### 2. BetaInvitationGateway
Gateway truy cập MongoDB collection `beta_invitations`:

**Methods:**
- `create(email)`: Tạo invitation mới không có code
- `createWithCode(email, code)`: Tạo invitation mới với code ngay lập tức
- `get(email)`: Lấy invitation theo email
- `getByCode(code)`: Lấy invitation theo code
- `updateCode(email, code)`: Cập nhật code cho invitation

### 3. Routing & Controllers
Xử lý HTTP requests thông qua Ktor routing:

**API Endpoints:**
- `POST /api/beta/register`: Đăng ký beta waitlist
- `GET /api/beta/beta-invitation`: Gửi invitation cho danh sách emails
- `GET /api/beta/beta-invitation-with-create-account`: Gửi invitation và tạo account
- `POST /api/beta/slack`: Xử lý Slack webhook interactions
- `GET /api/beta/verify-invitation-code`: Xác thực invitation code
- `GET /api/beta/apply-invitation-code`: Apply invitation code và redirect

### 4. External Service Gateways

**NotificationServiceGateway:**
- `sendInviteJoinBeta`: Gửi email mời beta thông thường
- `sendInviteJoinBetaWithPassword`: Gửi email mời beta kèm password

**UserServiceGateway:**
- `registerByEmail`: Đăng ký user mới
- `getUserByEmail`: Lấy thông tin user theo email
- `createOneTimeLogin`: Tạo one-time login token
- `verifyEmail`: Xác thực email
- `createOrMergeUserProfile`: Tạo hoặc merge user profile

**ShorturlServiceGateway:**
- `generateShortUrl`: Tạo short URL

## Data Models

### 1. BetaInvitationPojo
MongoDB document structure:
- `id`: ObjectId
- `email`: Email address
- `code`: Invitation code (nullable)
- `createdAt`: Timestamp (nullable)

### 2. gRPC Messages
**BetaMessage.GetOrCreateBetaRegistrationRequest:**
- `email`: Email để đăng ký

**BetaMessage.BetaRegistration:**
- `email`: Email
- `code`: Invitation code (optional)
- `success`: Trạng thái thành công
- `status`: gRPC status

**BetaMessage.InviteJoinBetaRequest:**
- `accounts`: Danh sách InviteJoinBetaAccountRequest

**BetaMessage.InviteJoinBetaAccountRequest:**
- `email`: Email
- `password`: Password (optional)
- `redirect_url`: URL redirect sau khi apply code (optional)

### 3. HTTP DTOs
- `BetaRegisterRequest/Response`: Đăng ký beta
- `BetaInvitationRequest/Response`: Mời beta
- `AdminBetaRegisterRequest/Response`: Admin đăng ký beta

## Flows chính

### 1. User Beta Registration Flow
1. User gửi POST request đến `/api/beta/register` với email
2. System kiểm tra user đã tồn tại chưa
3. Tạo beta registration mới với code mới (luôn tạo mới)
4. Nếu user đã tồn tại, tạo one-time login token và gửi invitation email
5. Gửi notification lên Slack về registration mới
6. Trả về response cho user

### 2. Admin Beta Invitation Flow
1. Admin gửi request với danh sách emails
2. Với mỗi email:
   - Tạo beta registration mới với code mới
   - Kiểm tra user đã tồn tại chưa
   - Tạo one-time login token nếu user tồn tại
   - Gửi invitation email với link redirect
3. Trả về kết quả cho từng email

### 3. Beta Invitation with Account Creation Flow
1. Admin gửi request với emails và usernames
2. Với mỗi email:
   - Validate email và username format
   - Tạo random password và hash MD5
   - Đăng ký user mới (hoặc sử dụng user hiện tại)
   - Verify email nếu chưa verify
   - Tạo user profile nếu chưa có
   - Tạo beta registration với code mới
   - Tạo one-time login token
   - Gửi invitation email kèm password (nếu user mới)

### 4. Invitation Code Application Flow
1. User click vào link trong email
2. System decode base64 JSON từ query parameter
3. Extract invitation code và redirect URL
4. Validate invitation code thông qua BetaService
5. Nếu valid, set cookie và redirect đến URL đích
6. Nếu invalid, redirect đến coming-soon page

### 5. Slack Integration Flow
1. User đăng ký beta → System gửi notification lên Slack
2. Slack hiển thị interactive buttons: "Invite to beta" và "Invite to beta and create account"
3. Admin click button → Slack gửi webhook đến `/api/beta/slack`
4. System xử lý action tương ứng và gửi response về Slack

## Configuration

### Database Configuration
- MongoDB connection string
- Database name: "viclass"
- Collection: "beta_invitations"

### Service Explorer Configuration
- NotificationService: Port 11155
- UserService: Port 11122
- ShorturlService: Port 1299

### Email Configuration
- `sendInviteJoinBeta`: Template "inviteJoinBeta"
- `sendInviteJoinBetaWithPassword`: Template "inviteJoinBetaWithPassword"

### Security Configuration
- Beta whitelist IPs and hosts
- JWT configuration
- Slack webhook configuration

## Đặc điểm quan trọng

### 1. Multiple Codes per User
- Mỗi lần đăng ký beta tạo ra code mới
- Một user có thể có nhiều invitation codes
- Database không require unique constraint trên (user, code)

### 2. URL Shortening
- Tất cả invitation links được rút gọn qua ShorturlService
- Fallback về original URL nếu shortening fails

### 3. One-time Login Integration
- Tự động tạo one-time login token cho existing users
- Redirect users đến dashboard sau khi apply invitation code

### 4. Slack Integration
- Real-time notification khi có registration mới
- Interactive buttons cho admin actions
- Delayed response handling

### 5. Security Features
- IP/Host whitelist cho verification endpoint
- Cookie-based invitation code storage
- Base64 encoded invitation parameters

## Logging và Monitoring
- Comprehensive logging với different log levels
- Separate log files: application.log, service.log, database.log
- Error handling với proper status codes
- Request/response logging với CallLogging plugin

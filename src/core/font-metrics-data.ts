const M1 = [0, 0.68889, 0, 0, 0.72222];
const M2 = [0, 0.68889, 0, 0, 0.66667];
const M3 = [0, 0.68889, 0, 0, 0.77778];
const M4 = [0, 0.68889, 0, 0, 0.61111];
const M5 = [0.16667, 0.68889, 0, 0, 0.77778];
const M6 = [0, 0.68889, 0, 0, 0.55556];
const M7 = [0, 0, 0, 0, 0.25];
const M8 = [0, 0.825, 0, 0, 2.33334];
const M9 = [0, 0.9, 0, 0, 2.33334];
const M10 = [0, 0.68889, 0, 0, 0.54028];
const M11 = [-0.03598, 0.46402, 0, 0, 0.5];
const M12 = [-0.13313, 0.36687, 0, 0, 1];
const M13 = [0.01354, 0.52239, 0, 0, 1];
const M14 = [0.01354, 0.52239, 0, 0, 1.11111];
const M15 = [0, 0.54986, 0, 0, 1];
const M16 = [0, 0.69224, 0, 0, 0.5];
const M17 = [0, 0.43056, 0, 0, 1];
const M18 = [0.08198, 0.58198, 0, 0, 0.77778];
const M19 = [0.19444, 0.69224, 0, 0, 0.41667];
const M20 = [0.1808, 0.675, 0, 0, 1];
const M21 = [0.19444, 0.69224, 0, 0, 0.83334];
const M22 = [0.13667, 0.63667, 0, 0, 1];
const M23 = [-0.064, 0.437, 0, 0, 1.334];
const M24 = [0.08167, 0.58167, 0, 0, 0.77778];
const M25 = [0, 0.69224, 0, 0, 0.72222];
const M26 = [0, 0.69224, 0, 0, 0.66667];
const M27 = [-0.13313, 0.36687, 0, 0, 0.77778];
const M28 = [0.06062, 0.54986, 0, 0, 0.77778];
const M29 = [0, 0.69224, 0, 0, 0.77778];
const M30 = [0.25583, 0.75583, 0, 0, 0.77778];
const M31 = [0.25142, 0.75726, 0, 0, 0.77778];
const M32 = [0.20576, 0.70576, 0, 0, 0.77778];
const M33 = [0.30274, 0.79383, 0, 0, 0.77778];
const M34 = [0.22958, 0.72958, 0, 0, 0.77778];
const M35 = [0.1808, 0.675, 0, 0, 0.77778];
const M36 = [0.13667, 0.63667, 0, 0, 0.77778];
const M37 = [0.13597, 0.63597, 0, 0, 0.77778];
const M38 = [0.03517, 0.54986, 0, 0, 0.77778];
const M39 = [0, 0.675, 0, 0, 0.77778];
const M40 = [0.19444, 0.69224, 0, 0, 0.61111];
const M41 = [0, 0.54986, 0, 0, 0.76042];
const M42 = [0, 0.54986, 0, 0, 0.66667];
const M43 = [0.0391, 0.5391, 0, 0, 0.77778];
const M44 = [0.03517, 0.54986, 0, 0, 1.33334];
const M45 = [0.38569, 0.88569, 0, 0, 0.77778];
const M46 = [0.23222, 0.74111, 0, 0, 0.77778];
const M47 = [0.19444, 0.69224, 0, 0, 0.77778];
const M48 = [0, 0.37788, 0, 0, 0.5];
const M49 = [0, 0.54986, 0, 0, 0.72222];
const M50 = [0, 0.69224, 0, 0, 0.83334];
const M51 = [0.11111, 0.69224, 0, 0, 0.66667];
const M52 = [0.26167, 0.75726, 0, 0, 0.77778];
const M53 = [0.48256, 0.98256, 0, 0, 0.77778];
const M54 = [0.28481, 0.79383, 0, 0, 0.77778];
const M55 = [0.08167, 0.58167, 0, 0, 0.22222];
const M56 = [0.08167, 0.58167, 0, 0, 0.38889];
const M57 = [0, 0.43056, 0.04028, 0, 0.66667];
const M58 = [0.41951, 0.91951, 0, 0, 0.77778];
const M59 = [0.24982, 0.74947, 0, 0, 0.38865];
const M60 = [0.08319, 0.58283, 0, 0, 0.75623];
const M61 = [0, 0.10803, 0, 0, 0.27764];
const M62 = [0, 0.47534, 0, 0, 0.50181];
const M63 = [0.18906, 0.47534, 0, 0, 0.50181];
const M64 = [0, 0.69141, 0, 0, 0.50181];
const M65 = [0.24982, 0.74947, 0, 0, 0.27764];
const M66 = [0, 0.69141, 0, 0, 0.21471];
const M67 = [0.25, 0.75, 0, 0, 0.44722];
const M68 = [0, 0.64444, 0, 0, 0.575];
const M69 = [0.08556, 0.58556, 0, 0, 0.89444];
const M70 = [0, 0.69444, 0, 0, 0.89444];
const M71 = [0, 0.68611, 0, 0, 0.9];
const M72 = [0, 0.68611, 0, 0, 0.86944];
const M73 = [0.25, 0.75, 0, 0, 0.575];
const M74 = [0.25, 0.75, 0, 0, 0.31944];
const M75 = [0, 0.69444, 0, 0, 0.63889];
const M76 = [0, 0.69444, 0, 0, 0.31944];
const M77 = [0, 0.44444, 0, 0, 0.63889];
const M78 = [0, 0.44444, 0, 0, 0.51111];
const M79 = [0, 0.69444, 0, 0, 0.575];
const M80 = [0.13333, 0.63333, 0, 0, 0.89444];
const M81 = [0, 0.44444, 0, 0, 0.31944];
const M82 = [0, 0.69444, 0, 0, 0.86944];
const M83 = [0, 0.68611, 0, 0, 0.69166];
const M84 = [0, 0.68611, 0, 0, 0.83055];
const M85 = [0, 0.68611, 0, 0, 0.89444];
const M86 = [0, 0.69444, 0, 0, 0.60278];
const M87 = [0.19444, 0.69444, 0, 0, 0.51111];
const M88 = [0, 0.69444, 0, 0, 0.83055];
const M89 = [-0.10889, 0.39111, 0, 0, 1.14999];
const M90 = [0.19444, 0.69444, 0, 0, 0.575];
const M91 = [0.19444, 0.69444, 0, 0, 1.14999];
const M92 = [0.19444, 0.69444, 0, 0, 0.70277];
const M93 = [0.05556, 0.75, 0, 0, 0.575];
const M94 = [0, 0.68611, 0, 0, 0.95833];
const M95 = [0.08556, 0.58556, 0, 0, 0.76666];
const M96 = [-0.02639, 0.47361, 0, 0, 0.575];
const M97 = [0, 0.44444, 0, 0, 0.89444];
const M98 = [0, 0.55556, 0, 0, 0.76666];
const M99 = [-0.10889, 0.39111, 0, 0, 0.89444];
const M100 = [0.00222, 0.50222, 0, 0, 0.89444];
const M101 = [0.19667, 0.69667, 0, 0, 0.89444];
const M102 = [0.08556, 0.58556, 0, 0, 1.14999];
const M103 = [0, 0.69444, 0, 0, 0.70277];
const M104 = [-0.02778, 0.47222, 0, 0, 0.575];
const M105 = [0.25, 0.75, 0, 0, 0.51111];
const M106 = [-0.13889, 0.36111, 0, 0, 1.14999];
const M107 = [0.19444, 0.69444, 0, 0, 1.02222];
const M108 = [0.12963, 0.69444, 0, 0, 0.89444];
const M109 = [0.19444, 0.69444, 0, 0, 0.44722];
const M110 = [0, 0.64444, 0.13167, 0, 0.59111];
const M111 = [0.19444, 0.64444, 0.13167, 0, 0.59111];
const M112 = [0, 0.68611, 0.17208, 0, 0.8961];
const M113 = [0.19444, 0.44444, 0.105, 0, 0.53222];
const M114 = [0, 0.44444, 0.085, 0, 0.82666];
const M115 = [0, 0.69444, 0.06709, 0, 0.59111];
const M116 = [0, 0.69444, 0.12945, 0, 0.35555];
const M117 = [0, 0.69444, 0, 0, 0.94888];
const M118 = [0, 0.69444, 0.11472, 0, 0.59111];
const M119 = [0, 0.68611, 0.10778, 0, 0.88555];
const M120 = [0, 0.69444, 0.07939, 0, 0.62055];
const M121 = [0, 0.69444, 0.12417, 0, 0.30667];
const M122 = [0, 0.64444, 0.13556, 0, 0.51111];
const M123 = [0.19444, 0.64444, 0.13556, 0, 0.51111];
const M124 = [0, 0.68333, 0.16389, 0, 0.74333];
const M125 = [0.19444, 0.43056, 0.08847, 0, 0.46];
const M126 = [0, 0.43056, 0.07514, 0, 0.71555];
const M127 = [0, 0.69444, 0.06646, 0, 0.51111];
const M128 = [0, 0.69444, 0, 0, 0.83129];
const M129 = [0, 0.69444, 0.1225, 0, 0.51111];
const M130 = [0, 0.68333, 0.09403, 0, 0.76666];
const M131 = [0, 0.68333, 0.11111, 0, 0.76666];
const M132 = [0, 0.69444, 0.06961, 0, 0.51444];
const M133 = [0, 0.69444, 0, 0, 0.27778];
const M134 = [0.25, 0.75, 0, 0, 0.38889];
const M135 = [0, 0.64444, 0, 0, 0.5];
const M136 = [0, 0.69444, 0, 0, 0.77778];
const M137 = [0, 0.68333, 0, 0, 0.75];
const M138 = [0, 0.68333, 0, 0, 0.77778];
const M139 = [0, 0.68333, 0, 0, 0.68056];
const M140 = [0, 0.68333, 0, 0, 0.72222];
const M141 = [0.25, 0.75, 0, 0, 0.5];
const M142 = [0.25, 0.75, 0, 0, 0.27778];
const M143 = [0, 0.69444, 0, 0, 0.5];
const M144 = [0, 0.69444, 0, 0, 0.55556];
const M145 = [0, 0.43056, 0, 0, 0.44445];
const M146 = [0, 0.43056, 0, 0, 0.5];
const M147 = [0.19444, 0.43056, 0, 0, 0.55556];
const M148 = [0, 0.43056, 0, 0, 0.55556];
const M149 = [0.08333, 0.58333, 0, 0, 0.77778];
const M150 = [0, 0.43056, 0, 0, 0.27778];
const M151 = [0, 0.66786, 0, 0, 0.27778];
const M152 = [0, 0.69444, 0, 0, 0.75];
const M153 = [0, 0.66786, 0, 0, 0.5];
const M154 = [0, 0.68333, 0, 0, 0.625];
const M155 = [0.19444, 0.69444, 0, 0, 0.44445];
const M156 = [0, 0.69444, 0, 0, 0.72222];
const M157 = [0.19444, 0.69444, 0, 0, 0.5];
const M158 = [0.19444, 0.69444, 0, 0, 1];
const M159 = [0.011, 0.511, 0, 0, 1.126];
const M160 = [0.19444, 0.69444, 0, 0, 0.61111];
const M161 = [0.05556, 0.75, 0, 0, 0.5];
const M162 = [0, 0.68333, 0, 0, 0.83334];
const M163 = [0.0391, 0.5391, 0, 0, 0.66667];
const M164 = [-0.05555, 0.44445, 0, 0, 0.5];
const M165 = [0, 0.43056, 0, 0, 0.77778];
const M166 = [0, 0.55556, 0, 0, 0.66667];
const M167 = [-0.03625, 0.46375, 0, 0, 0.77778];
const M168 = [-0.01688, 0.48312, 0, 0, 0.77778];
const M169 = [0.0391, 0.5391, 0, 0, 1];
const M170 = [0, 0.69444, 0, 0, 0.61111];
const M171 = [-0.03472, 0.46528, 0, 0, 0.5];
const M172 = [0.25, 0.75, 0, 0, 0.44445];
const M173 = [-0.14236, 0.35764, 0, 0, 1];
const M174 = [0.244, 0.744, 0, 0, 0.412];
const M175 = [0.19444, 0.69444, 0, 0, 0.88889];
const M176 = [0.12963, 0.69444, 0, 0, 0.77778];
const M177 = [0.19444, 0.69444, 0, 0, 0.38889];
const M178 = [0.011, 0.511, 0, 0, 1.638];
const M179 = [0.19444, 0.69444, 0, 0, 0];
const M180 = [0, 0.44444, 0, 0, 0.575];
const M181 = [0.19444, 0.44444, 0, 0, 0.575];
const M182 = [0, 0.68611, 0, 0, 0.75555];
const M183 = [0, 0.69444, 0, 0, 0.66759];
const M184 = [0, 0.68611, 0, 0, 0.80555];
const M185 = [0, 0.68611, 0.08229, 0, 0.98229];
const M186 = [0, 0.68611, 0, 0, 0.76666];
const M187 = [0, 0.44444, 0, 0, 0.58472];
const M188 = [0.19444, 0.44444, 0, 0, 0.6118];
const M189 = [0.19444, 0.43056, 0, 0, 0.5];
const M190 = [0, 0.68333, 0.02778, 0.08334, 0.76278];
const M191 = [0, 0.68333, 0.08125, 0.05556, 0.83125];
const M192 = [0, 0.43056, 0, 0.05556, 0.48472];
const M193 = [0.19444, 0.43056, 0, 0.08334, 0.51702];
const M194 = [0.25, 0.75, 0, 0, 0.42778];
const M195 = [0, 0.69444, 0, 0, 0.55];
const M196 = [0, 0.69444, 0, 0, 0.73334];
const M197 = [0, 0.69444, 0, 0, 0.79445];
const M198 = [0, 0.69444, 0, 0, 0.51945];
const M199 = [0, 0.69444, 0, 0, 0.70278];
const M200 = [0, 0.69444, 0, 0, 0.76389];
const M201 = [0.25, 0.75, 0, 0, 0.34306];
const M202 = [0, 0.69444, 0, 0, 0.56111];
const M203 = [0, 0.69444, 0, 0, 0.25556];
const M204 = [0.19444, 0.45833, 0, 0, 0.56111];
const M205 = [0, 0.45833, 0, 0, 0.56111];
const M206 = [0, 0.69444, 0, 0, 0.30556];
const M207 = [0, 0.69444, 0, 0, 0.58056];
const M208 = [0, 0.69444, 0, 0, 0.67223];
const M209 = [0, 0.69444, 0, 0, 0.85556];
const M210 = [0, 0.69444, 0, 0, 0.55834];
const M211 = [0, 0.65556, 0.11156, 0, 0.5];
const M212 = [0, 0.69444, 0.08094, 0, 0.70834];
const M213 = [0.17014, 0, 0, 0, 0.44445];
const M214 = [0, 0.69444, 0.0799, 0, 0.5];
const M215 = [0, 0.69444, 0, 0, 0.73752];
const M216 = [0, 0.69444, 0.09205, 0, 0.5];
const M217 = [0, 0.69444, 0.09031, 0, 0.77778];
const M218 = [0, 0.69444, 0.07816, 0, 0.27778];
const M219 = [0, 0.69444, 0.00316, 0, 0.5];
const M220 = [0.19444, 0.69444, 0, 0, 0.83334];
const M221 = [0.05556, 0.75, 0, 0, 0.83334];
const M222 = [0, 0.75, 0, 0, 0.5];
const M223 = [0.125, 0.08333, 0, 0, 0.27778];
const M224 = [0, 0.08333, 0, 0, 0.27778];
const M225 = [0, 0.65556, 0, 0, 0.5];
const M226 = [0, 0.69444, 0, 0, 0.47222];
const M227 = [0, 0.69444, 0, 0, 0.66667];
const M228 = [0, 0.69444, 0, 0, 0.59722];
const M229 = [0, 0.69444, 0, 0, 0.54167];
const M230 = [0, 0.69444, 0, 0, 0.70834];
const M231 = [0.25, 0.75, 0, 0, 0.28889];
const M232 = [0, 0.69444, 0, 0, 0.51667];
const M233 = [0, 0.44444, 0, 0, 0.44445];
const M234 = [0.19444, 0.44444, 0, 0, 0.51667];
const M235 = [0, 0.44444, 0, 0, 0.38333];
const M236 = [0, 0.44444, 0, 0, 0.51667];
const M237 = [0, 0.69444, 0, 0, 0.83334];
const M238 = [0.35001, 0.85, 0, 0, 0.45834];
const M239 = [0.35001, 0.85, 0, 0, 0.57778];
const M240 = [0.35001, 0.85, 0, 0, 0.41667];
const M241 = [0.35001, 0.85, 0, 0, 0.58334];
const M242 = [0, 0.72222, 0, 0, 0.55556];
const M243 = [0.00001, 0.6, 0, 0, 0.66667];
const M244 = [0.00001, 0.6, 0, 0, 0.77778];
const M245 = [0.25001, 0.75, 0, 0, 0.94445];
const M246 = [0.306, 0.805, 0.19445, 0, 0.47222];
const M247 = [0.30612, 0.805, 0.19445, 0, 0.47222];
const M248 = [0.25001, 0.75, 0, 0, 0.83334];
const M249 = [0.35001, 0.85, 0, 0, 0.47222];
const M250 = [0.25001, 0.75, 0, 0, 1.11111];
const M251 = [0.65002, 1.15, 0, 0, 0.59722];
const M252 = [0.65002, 1.15, 0, 0, 0.81111];
const M253 = [0.65002, 1.15, 0, 0, 0.47222];
const M254 = [0.65002, 1.15, 0, 0, 0.66667];
const M255 = [0, 0.75, 0, 0, 1];
const M256 = [0.55001, 1.05, 0, 0, 1.27778];
const M257 = [0.862, 1.36, 0.44445, 0, 0.55556];
const M258 = [0.86225, 1.36, 0.44445, 0, 0.55556];
const M259 = [0.55001, 1.05, 0, 0, 1.11111];
const M260 = [0.65002, 1.15, 0, 0, 0.52778];
const M261 = [0.65002, 1.15, 0, 0, 0.61111];
const M262 = [0.55001, 1.05, 0, 0, 1.51112];
const M263 = [0.95003, 1.45, 0, 0, 0.73611];
const M264 = [0.95003, 1.45, 0, 0, 1.04445];
const M265 = [0.95003, 1.45, 0, 0, 0.52778];
const M266 = [0.95003, 1.45, 0, 0, 0.75];
const M267 = [0, 0.75, 0, 0, 1.44445];
const M268 = [0.95003, 1.45, 0, 0, 0.58334];
const M269 = [1.25003, 1.75, 0, 0, 0.79167];
const M270 = [1.25003, 1.75, 0, 0, 1.27778];
const M271 = [1.25003, 1.75, 0, 0, 0.58334];
const M272 = [1.25003, 1.75, 0, 0, 0.80556];
const M273 = [0, 0.825, 0, 0, 1.8889];
const M274 = [1.25003, 1.75, 0, 0, 0.63889];
const M275 = [0.64502, 1.155, 0, 0, 0.875];
const M276 = [0.00001, 0.6, 0, 0, 0.875];
const M277 = [-0.00099, 0.601, 0, 0, 0.66667];
const M278 = [0.64502, 1.155, 0, 0, 0.66667];
const M279 = [0.00001, 0.9, 0, 0, 0.88889];
const M280 = [0.65002, 1.15, 0, 0, 0.88889];
const M281 = [0.90001, 0, 0, 0, 0.88889];
const M282 = [-0.00499, 0.605, 0, 0, 1.05556];
const M283 = [0, 0.12, 0, 0, 0.45];
const M284 = [0, 0.61111, 0, 0, 0.525];
const M285 = [0.08333, 0.69444, 0, 0, 0.525];
const M286 = [-0.08056, 0.53055, 0, 0, 0.525];
const M287 = [-0.05556, 0.55556, 0, 0, 0.525];
const M288 = [0, 0.43056, 0, 0, 0.525];
const M289 = [0.22222, 0.43056, 0, 0, 0.525];
const M290 = [0, 0, 0, 0, 0.525];

export default {
  'AMS-Regular': {
    32: M7, // U+0020
    65: M1, // U+0041 A
    66: M2, // U+0042 B
    67: M1, // U+0043 C
    68: M1, // U+0044 D
    69: M2, // U+0045 E
    70: M4, // U+0046 F
    71: M3, // U+0047 G
    72: M3, // U+0048 H
    73: [0, 0.68889, 0, 0, 0.38889], // U+0049 I
    74: [0.16667, 0.68889, 0, 0, 0.5], // U+004a J
    75: M3, // U+004b K
    76: M2, // U+004c L
    77: [0, 0.68889, 0, 0, 0.94445], // U+004d M
    78: M1, // U+004e N
    79: M5, // U+004f O
    80: M4, // U+0050 P
    81: M5, // U+0051 Q
    82: M1, // U+0052 R
    83: M6, // U+0053 S
    84: M2, // U+0054 T
    85: M1, // U+0055 U
    86: M1, // U+0056 V
    87: [0, 0.68889, 0, 0, 1], // U+0057 W
    88: M1, // U+0058 X
    89: M1, // U+0059 Y
    90: M2, // U+005a Z
    107: M6, // U+006b k
    160: M7, // U+00a0
    165: [0, 0.675, 0.025, 0, 0.75], // U+00a5 ¥
    174: [0.15559, 0.69224, 0, 0, 0.94666], // U+00ae ®
    240: M6, // U+00f0 ð
    295: M10, // U+0127 ħ
    710: M8, // U+02c6 ˆ
    732: M9, // U+02dc ˜
    770: M8, // U+0302 ̂
    771: M9, // U+0303 ̃
    989: M24, // U+03dd ϝ
    1008: M57, // U+03f0 ϰ
    8245: [0, 0.54986, 0, 0, 0.275], // U+2035 ‵
    8463: M10, // U+210f ℏ
    8487: M1, // U+2127 ℧
    8498: M6, // U+2132 Ⅎ
    8502: M2, // U+2136 ℶ
    8503: [0, 0.68889, 0, 0, 0.44445], // U+2137 ℷ
    8504: M2, // U+2138 ℸ
    8513: [0, 0.68889, 0, 0, 0.63889], // U+2141 ⅁
    8592: M11, // U+2190 ←
    8594: M11, // U+2192 →
    8602: M12, // U+219a ↚
    8603: M12, // U+219b ↛
    8606: M13, // U+219e ↞
    8608: M13, // U+21a0 ↠
    8610: M14, // U+21a2 ↢
    8611: M14, // U+21a3 ↣
    8619: M15, // U+21ab ↫
    8620: M15, // U+21ac ↬
    8621: [-0.13313, 0.37788, 0, 0, 1.38889], // U+21ad ↭
    8622: M12, // U+21ae ↮
    8624: M16, // U+21b0 ↰
    8625: M16, // U+21b1 ↱
    8630: M17, // U+21b6 ↶
    8631: M17, // U+21b7 ↷
    8634: M18, // U+21ba ↺
    8635: M18, // U+21bb ↻
    8638: M19, // U+21be ↾
    8639: M19, // U+21bf ↿
    8642: M19, // U+21c2 ⇂
    8643: M19, // U+21c3 ⇃
    8644: M20, // U+21c4 ⇄
    8646: M20, // U+21c6 ⇆
    8647: M20, // U+21c7 ⇇
    8648: M21, // U+21c8 ⇈
    8649: M20, // U+21c9 ⇉
    8650: M21, // U+21ca ⇊
    8651: M13, // U+21cb ⇋
    8652: M13, // U+21cc ⇌
    8653: M12, // U+21cd ⇍
    8654: M12, // U+21ce ⇎
    8655: M12, // U+21cf ⇏
    8666: M22, // U+21da ⇚
    8667: M22, // U+21db ⇛
    8669: [-0.13313, 0.37788, 0, 0, 1], // U+21dd ⇝
    8672: M23, // U+21e0 ⇠
    8674: M23, // U+21e2 ⇢
    8705: [0, 0.825, 0, 0, 0.5], // U+2201 ∁
    8708: M6, // U+2204 ∄
    8709: M24, // U+2205 ∅
    8717: [0, 0.43056, 0, 0, 0.42917], // U+220d ∍
    8722: M11, // U+2212 −
    8724: [0.08198, 0.69224, 0, 0, 0.77778], // U+2214 ∔
    8726: M24, // U+2216 ∖
    8733: M29, // U+221d ∝
    8736: M25, // U+2220 ∠
    8737: M25, // U+2221 ∡
    8738: [0.03517, 0.52239, 0, 0, 0.72222], // U+2222 ∢
    8739: M55, // U+2223 ∣
    8740: [0.25142, 0.74111, 0, 0, 0.27778], // U+2224 ∤
    8741: M56, // U+2225 ∥
    8742: [0.25142, 0.74111, 0, 0, 0.5], // U+2226 ∦
    8756: M26, // U+2234 ∴
    8757: M26, // U+2235 ∵
    8764: M27, // U+223c ∼
    8765: [-0.13313, 0.37788, 0, 0, 0.77778], // U+223d ∽
    8769: M27, // U+2241 ≁
    8770: M167, // U+2242 ≂
    8774: M33, // U+2246 ≆
    8776: M168, // U+2248 ≈
    8778: M24, // U+224a ≊
    8782: M28, // U+224e ≎
    8783: M28, // U+224f ≏
    8785: M18, // U+2251 ≑
    8786: M18, // U+2252 ≒
    8787: M18, // U+2253 ≓
    8790: M29, // U+2256 ≖
    8791: M34, // U+2257 ≗
    8796: [0.08198, 0.91667, 0, 0, 0.77778], // U+225c ≜
    8806: M30, // U+2266 ≦
    8807: M30, // U+2267 ≧
    8808: M31, // U+2268 ≨
    8809: M31, // U+2269 ≩
    8812: [0.25583, 0.75583, 0, 0, 0.5], // U+226c ≬
    8814: M32, // U+226e ≮
    8815: M32, // U+226f ≯
    8816: M33, // U+2270 ≰
    8817: M33, // U+2271 ≱
    8818: M34, // U+2272 ≲
    8819: M34, // U+2273 ≳
    8822: M35, // U+2276 ≶
    8823: M35, // U+2277 ≷
    8828: M36, // U+227c ≼
    8829: M36, // U+227d ≽
    8830: M34, // U+227e ≾
    8831: M34, // U+227f ≿
    8832: M32, // U+2280 ⊀
    8833: M32, // U+2281 ⊁
    8840: M33, // U+2288 ⊈
    8841: M33, // U+2289 ⊉
    8842: M37, // U+228a ⊊
    8843: M37, // U+228b ⊋
    8847: M38, // U+228f ⊏
    8848: M38, // U+2290 ⊐
    8858: M18, // U+229a ⊚
    8859: M18, // U+229b ⊛
    8861: M18, // U+229d ⊝
    8862: M39, // U+229e ⊞
    8863: M39, // U+229f ⊟
    8864: M39, // U+22a0 ⊠
    8865: M39, // U+22a1 ⊡
    8872: [0, 0.69224, 0, 0, 0.61111], // U+22a8 ⊨
    8873: M25, // U+22a9 ⊩
    8874: [0, 0.69224, 0, 0, 0.88889], // U+22aa ⊪
    8876: M4, // U+22ac ⊬
    8877: M4, // U+22ad ⊭
    8878: M1, // U+22ae ⊮
    8879: M1, // U+22af ⊯
    8882: M38, // U+22b2 ⊲
    8883: M38, // U+22b3 ⊳
    8884: M36, // U+22b4 ⊴
    8885: M36, // U+22b5 ⊵
    8888: [0, 0.54986, 0, 0, 1.11111], // U+22b8 ⊸
    8890: M147, // U+22ba ⊺
    8891: M40, // U+22bb ⊻
    8892: M40, // U+22bc ⊼
    8901: [0, 0.54986, 0, 0, 0.27778], // U+22c5 ⋅
    8903: M24, // U+22c7 ⋇
    8905: M24, // U+22c9 ⋉
    8906: M24, // U+22ca ⋊
    8907: M29, // U+22cb ⋋
    8908: M29, // U+22cc ⋌
    8909: [-0.03598, 0.46402, 0, 0, 0.77778], // U+22cd ⋍
    8910: M41, // U+22ce ⋎
    8911: M41, // U+22cf ⋏
    8912: M38, // U+22d0 ⋐
    8913: M38, // U+22d1 ⋑
    8914: M42, // U+22d2 ⋒
    8915: M42, // U+22d3 ⋓
    8916: M26, // U+22d4 ⋔
    8918: M43, // U+22d6 ⋖
    8919: M43, // U+22d7 ⋗
    8920: M44, // U+22d8 ⋘
    8921: M44, // U+22d9 ⋙
    8922: M45, // U+22da ⋚
    8923: M45, // U+22db ⋛
    8926: M36, // U+22de ⋞
    8927: M36, // U+22df ⋟
    8928: M33, // U+22e0 ⋠
    8929: M33, // U+22e1 ⋡
    8934: M46, // U+22e6 ⋦
    8935: M46, // U+22e7 ⋧
    8936: M46, // U+22e8 ⋨
    8937: M46, // U+22e9 ⋩
    8938: M32, // U+22ea ⋪
    8939: M32, // U+22eb ⋫
    8940: M33, // U+22ec ⋬
    8941: M33, // U+22ed ⋭
    8994: M47, // U+2322 ⌢
    8995: M47, // U+2323 ⌣
    9416: [0.15559, 0.69224, 0, 0, 0.90222], // U+24c8 Ⓢ
    9484: M16, // U+250c ┌
    9488: M16, // U+2510 ┐
    9492: M48, // U+2514 └
    9496: M48, // U+2518 ┘
    9585: [0.19444, 0.68889, 0, 0, 0.88889], // U+2571 ╱
    9586: [0.19444, 0.74111, 0, 0, 0.88889], // U+2572 ╲
    9632: M39, // U+25a0 ■
    9633: M39, // U+25a1 □
    9650: M49, // U+25b2 ▲
    9651: M49, // U+25b3 △
    9654: M38, // U+25b6 ▶
    9660: M49, // U+25bc ▼
    9661: M49, // U+25bd ▽
    9664: M38, // U+25c0 ◀
    9674: M51, // U+25ca ◊
    9733: [0.19444, 0.69224, 0, 0, 0.94445], // U+2605 ★
    10003: M50, // U+2713 ✓
    10016: M50, // U+2720 ✠
    10731: M51, // U+29eb ⧫
    10846: [0.19444, 0.75583, 0, 0, 0.61111], // U+2a5e ⩞
    10877: M36, // U+2a7d ⩽
    10878: M36, // U+2a7e ⩾
    10885: M30, // U+2a85 ⪅
    10886: M30, // U+2a86 ⪆
    10887: M37, // U+2a87 ⪇
    10888: M37, // U+2a88 ⪈
    10889: M52, // U+2a89 ⪉
    10890: M52, // U+2a8a ⪊
    10891: M53, // U+2a8b ⪋
    10892: M53, // U+2a8c ⪌
    10901: M36, // U+2a95 ⪕
    10902: M36, // U+2a96 ⪖
    10933: M31, // U+2ab5 ⪵
    10934: M31, // U+2ab6 ⪶
    10935: M52, // U+2ab7 ⪷
    10936: M52, // U+2ab8 ⪸
    10937: M52, // U+2ab9 ⪹
    10938: M52, // U+2aba ⪺
    10949: M30, // U+2ac5 ⫅
    10950: M30, // U+2ac6 ⫆
    10955: M54, // U+2acb ⫋
    10956: M54, // U+2acc ⫌
    57350: M55, // U+e006 
    57351: M56, // U+e007 
    57352: M24, // U+e008 
    57353: M57, // U+e009 
    57356: M31, // U+e00c 
    57357: M31, // U+e00d 
    57358: M58, // U+e00e 
    57359: M33, // U+e00f 
    57360: M33, // U+e010 
    57361: M58, // U+e011 
    57366: M31, // U+e016 
    57367: M31, // U+e017 
    57368: M31, // U+e018 
    57369: M31, // U+e019 
    57370: M37, // U+e01a 
    57371: M37, // U+e01b 
  },
  'Caligraphic-Regular': {
    32: M7, // U+0020
    65: [0, 0.68333, 0, 0.19445, 0.79847], // U+0041 A
    66: [0, 0.68333, 0.03041, 0.13889, 0.65681], // U+0042 B
    67: [0, 0.68333, 0.05834, 0.13889, 0.52653], // U+0043 C
    68: [0, 0.68333, 0.02778, 0.08334, 0.77139], // U+0044 D
    69: [0, 0.68333, 0.08944, 0.11111, 0.52778], // U+0045 E
    70: [0, 0.68333, 0.09931, 0.11111, 0.71875], // U+0046 F
    71: [0.09722, 0.68333, 0.0593, 0.11111, 0.59487], // U+0047 G
    72: [0, 0.68333, 0.00965, 0.11111, 0.84452], // U+0048 H
    73: [0, 0.68333, 0.07382, 0, 0.54452], // U+0049 I
    74: [0.09722, 0.68333, 0.18472, 0.16667, 0.67778], // U+004a J
    75: [0, 0.68333, 0.01445, 0.05556, 0.76195], // U+004b K
    76: [0, 0.68333, 0, 0.13889, 0.68972], // U+004c L
    77: [0, 0.68333, 0, 0.13889, 1.2009], // U+004d M
    78: [0, 0.68333, 0.14736, 0.08334, 0.82049], // U+004e N
    79: [0, 0.68333, 0.02778, 0.11111, 0.79611], // U+004f O
    80: [0, 0.68333, 0.08222, 0.08334, 0.69556], // U+0050 P
    81: [0.09722, 0.68333, 0, 0.11111, 0.81667], // U+0051 Q
    82: [0, 0.68333, 0, 0.08334, 0.8475], // U+0052 R
    83: [0, 0.68333, 0.075, 0.13889, 0.60556], // U+0053 S
    84: [0, 0.68333, 0.25417, 0, 0.54464], // U+0054 T
    85: [0, 0.68333, 0.09931, 0.08334, 0.62583], // U+0055 U
    86: [0, 0.68333, 0.08222, 0, 0.61278], // U+0056 V
    87: [0, 0.68333, 0.08222, 0.08334, 0.98778], // U+0057 W
    88: [0, 0.68333, 0.14643, 0.13889, 0.7133], // U+0058 X
    89: [0.09722, 0.68333, 0.08222, 0.08334, 0.66834], // U+0059 Y
    90: [0, 0.68333, 0.07944, 0.13889, 0.72473], // U+005a Z
    160: M7, // U+00a0
  },
  'Fraktur-Regular': {
    32: M7, // U+0020
    33: [0, 0.69141, 0, 0, 0.29574], // U+0021 !
    34: M66, // U+0022 "
    38: [0, 0.69141, 0, 0, 0.73786], // U+0026 &
    39: [0, 0.69141, 0, 0, 0.21201], // U+0027 '
    40: M59, // U+0028 (
    41: M59, // U+0029 )
    42: [0, 0.62119, 0, 0, 0.27764], // U+002a *
    43: M60, // U+002b +
    44: M61, // U+002c ,
    45: M60, // U+002d -
    46: M61, // U+002e .
    47: [0.24982, 0.74947, 0, 0, 0.50181], // U+002f /
    48: M62, // U+0030 0
    49: M62, // U+0031 1
    50: M62, // U+0032 2
    51: M63, // U+0033 3
    52: M63, // U+0034 4
    53: M63, // U+0035 5
    54: M64, // U+0036 6
    55: M63, // U+0037 7
    56: M64, // U+0038 8
    57: M63, // U+0039 9
    58: [0, 0.47534, 0, 0, 0.21606], // U+003a :
    59: [0.12604, 0.47534, 0, 0, 0.21606], // U+003b ;
    61: [-0.13099, 0.36866, 0, 0, 0.75623], // U+003d =
    63: [0, 0.69141, 0, 0, 0.36245], // U+003f ?
    65: [0, 0.69141, 0, 0, 0.7176], // U+0041 A
    66: [0, 0.69141, 0, 0, 0.88397], // U+0042 B
    67: [0, 0.69141, 0, 0, 0.61254], // U+0043 C
    68: [0, 0.69141, 0, 0, 0.83158], // U+0044 D
    69: [0, 0.69141, 0, 0, 0.66278], // U+0045 E
    70: [0.12604, 0.69141, 0, 0, 0.61119], // U+0046 F
    71: [0, 0.69141, 0, 0, 0.78539], // U+0047 G
    72: [0.06302, 0.69141, 0, 0, 0.7203], // U+0048 H
    73: [0, 0.69141, 0, 0, 0.55448], // U+0049 I
    74: [0.12604, 0.69141, 0, 0, 0.55231], // U+004a J
    75: [0, 0.69141, 0, 0, 0.66845], // U+004b K
    76: [0, 0.69141, 0, 0, 0.66602], // U+004c L
    77: [0, 0.69141, 0, 0, 1.04953], // U+004d M
    78: [0, 0.69141, 0, 0, 0.83212], // U+004e N
    79: [0, 0.69141, 0, 0, 0.82699], // U+004f O
    80: [0.18906, 0.69141, 0, 0, 0.82753], // U+0050 P
    81: [0.03781, 0.69141, 0, 0, 0.82699], // U+0051 Q
    82: [0, 0.69141, 0, 0, 0.82807], // U+0052 R
    83: [0, 0.69141, 0, 0, 0.82861], // U+0053 S
    84: [0, 0.69141, 0, 0, 0.66899], // U+0054 T
    85: [0, 0.69141, 0, 0, 0.64576], // U+0055 U
    86: [0, 0.69141, 0, 0, 0.83131], // U+0056 V
    87: [0, 0.69141, 0, 0, 1.04602], // U+0057 W
    88: [0, 0.69141, 0, 0, 0.71922], // U+0058 X
    89: [0.18906, 0.69141, 0, 0, 0.83293], // U+0059 Y
    90: [0.12604, 0.69141, 0, 0, 0.60201], // U+005a Z
    91: M65, // U+005b [
    93: M65, // U+005d ]
    94: [0, 0.69141, 0, 0, 0.49965], // U+005e ^
    97: [0, 0.47534, 0, 0, 0.50046], // U+0061 a
    98: [0, 0.69141, 0, 0, 0.51315], // U+0062 b
    99: [0, 0.47534, 0, 0, 0.38946], // U+0063 c
    100: [0, 0.62119, 0, 0, 0.49857], // U+0064 d
    101: [0, 0.47534, 0, 0, 0.40053], // U+0065 e
    102: [0.18906, 0.69141, 0, 0, 0.32626], // U+0066 f
    103: [0.18906, 0.47534, 0, 0, 0.5037], // U+0067 g
    104: [0.18906, 0.69141, 0, 0, 0.52126], // U+0068 h
    105: [0, 0.69141, 0, 0, 0.27899], // U+0069 i
    106: [0, 0.69141, 0, 0, 0.28088], // U+006a j
    107: [0, 0.69141, 0, 0, 0.38946], // U+006b k
    108: [0, 0.69141, 0, 0, 0.27953], // U+006c l
    109: [0, 0.47534, 0, 0, 0.76676], // U+006d m
    110: [0, 0.47534, 0, 0, 0.52666], // U+006e n
    111: [0, 0.47534, 0, 0, 0.48885], // U+006f o
    112: [0.18906, 0.52396, 0, 0, 0.50046], // U+0070 p
    113: [0.18906, 0.47534, 0, 0, 0.48912], // U+0071 q
    114: [0, 0.47534, 0, 0, 0.38919], // U+0072 r
    115: [0, 0.47534, 0, 0, 0.44266], // U+0073 s
    116: [0, 0.62119, 0, 0, 0.33301], // U+0074 t
    117: [0, 0.47534, 0, 0, 0.5172], // U+0075 u
    118: [0, 0.52396, 0, 0, 0.5118], // U+0076 v
    119: [0, 0.52396, 0, 0, 0.77351], // U+0077 w
    120: [0.18906, 0.47534, 0, 0, 0.38865], // U+0078 x
    121: [0.18906, 0.47534, 0, 0, 0.49884], // U+0079 y
    122: [0.18906, 0.47534, 0, 0, 0.39054], // U+007a z
    160: M7, // U+00a0
    8216: M66, // U+2018 ‘
    8217: M66, // U+2019 ’
    58112: [0, 0.62119, 0, 0, 0.49749], // U+e300 
    58113: [0, 0.62119, 0, 0, 0.4983], // U+e301 
    58114: [0.18906, 0.69141, 0, 0, 0.33328], // U+e302 
    58115: [0.18906, 0.69141, 0, 0, 0.32923], // U+e303 
    58116: [0.18906, 0.47534, 0, 0, 0.50343], // U+e304 
    58117: [0, 0.69141, 0, 0, 0.33301], // U+e305 
    58118: [0, 0.62119, 0, 0, 0.33409], // U+e306 
    58119: [0, 0.47534, 0, 0, 0.50073], // U+e307 
  },
  'Main-Bold': {
    32: M7, // U+0020
    33: [0, 0.69444, 0, 0, 0.35], // U+0021 !
    34: M86, // U+0022 "
    35: [0.19444, 0.69444, 0, 0, 0.95833], // U+0023 #
    36: M93, // U+0024 $
    37: [0.05556, 0.75, 0, 0, 0.95833], // U+0025 %
    38: M70, // U+0026 &
    39: M76, // U+0027 '
    40: M67, // U+0028 (
    41: M67, // U+0029 )
    42: [0, 0.75, 0, 0, 0.575], // U+002a *
    43: M80, // U+002b +
    44: [0.19444, 0.15556, 0, 0, 0.31944], // U+002c ,
    45: M235, // U+002d -
    46: [0, 0.15556, 0, 0, 0.31944], // U+002e .
    47: M73, // U+002f /
    48: M68, // U+0030 0
    49: M68, // U+0031 1
    50: M68, // U+0032 2
    51: M68, // U+0033 3
    52: M68, // U+0034 4
    53: M68, // U+0035 5
    54: M68, // U+0036 6
    55: M68, // U+0037 7
    56: M68, // U+0038 8
    57: M68, // U+0039 9
    58: M81, // U+003a :
    59: [0.19444, 0.44444, 0, 0, 0.31944], // U+003b ;
    60: M69, // U+003c <
    61: M99, // U+003d =
    62: M69, // U+003e >
    63: [0, 0.69444, 0, 0, 0.54305], // U+003f ?
    64: M70, // U+0040 @
    65: M72, // U+0041 A
    66: [0, 0.68611, 0, 0, 0.81805], // U+0042 B
    67: M84, // U+0043 C
    68: [0, 0.68611, 0, 0, 0.88194], // U+0044 D
    69: M182, // U+0045 E
    70: [0, 0.68611, 0, 0, 0.72361], // U+0046 F
    71: [0, 0.68611, 0, 0, 0.90416], // U+0047 G
    72: M71, // U+0048 H
    73: [0, 0.68611, 0, 0, 0.43611], // U+0049 I
    74: [0, 0.68611, 0, 0, 0.59444], // U+004a J
    75: [0, 0.68611, 0, 0, 0.90138], // U+004b K
    76: M83, // U+004c L
    77: [0, 0.68611, 0, 0, 1.09166], // U+004d M
    78: M71, // U+004e N
    79: [0, 0.68611, 0, 0, 0.86388], // U+004f O
    80: [0, 0.68611, 0, 0, 0.78611], // U+0050 P
    81: [0.19444, 0.68611, 0, 0, 0.86388], // U+0051 Q
    82: [0, 0.68611, 0, 0, 0.8625], // U+0052 R
    83: [0, 0.68611, 0, 0, 0.63889], // U+0053 S
    84: [0, 0.68611, 0, 0, 0.8], // U+0054 T
    85: [0, 0.68611, 0, 0, 0.88472], // U+0055 U
    86: [0, 0.68611, 0.01597, 0, 0.86944], // U+0056 V
    87: [0, 0.68611, 0.01597, 0, 1.18888], // U+0057 W
    88: M72, // U+0058 X
    89: [0, 0.68611, 0.02875, 0, 0.86944], // U+0059 Y
    90: [0, 0.68611, 0, 0, 0.70277], // U+005a Z
    91: M74, // U+005b [
    92: M73, // U+005c \
    93: M74, // U+005d ]
    94: M79, // U+005e ^
    95: [0.31, 0.13444, 0.03194, 0, 0.575], // U+005f _
    97: [0, 0.44444, 0, 0, 0.55902], // U+0061 a
    98: M75, // U+0062 b
    99: M78, // U+0063 c
    100: M75, // U+0064 d
    101: [0, 0.44444, 0, 0, 0.52708], // U+0065 e
    102: [0, 0.69444, 0.10903, 0, 0.35139], // U+0066 f
    103: [0.19444, 0.44444, 0.01597, 0, 0.575], // U+0067 g
    104: M75, // U+0068 h
    105: M76, // U+0069 i
    106: [0.19444, 0.69444, 0, 0, 0.35139], // U+006a j
    107: [0, 0.69444, 0, 0, 0.60694], // U+006b k
    108: M76, // U+006c l
    109: [0, 0.44444, 0, 0, 0.95833], // U+006d m
    110: M77, // U+006e n
    111: M180, // U+006f o
    112: [0.19444, 0.44444, 0, 0, 0.63889], // U+0070 p
    113: [0.19444, 0.44444, 0, 0, 0.60694], // U+0071 q
    114: [0, 0.44444, 0, 0, 0.47361], // U+0072 r
    115: [0, 0.44444, 0, 0, 0.45361], // U+0073 s
    116: [0, 0.63492, 0, 0, 0.44722], // U+0074 t
    117: M77, // U+0075 u
    118: [0, 0.44444, 0.01597, 0, 0.60694], // U+0076 v
    119: [0, 0.44444, 0.01597, 0, 0.83055], // U+0077 w
    120: [0, 0.44444, 0, 0, 0.60694], // U+0078 x
    121: [0.19444, 0.44444, 0.01597, 0, 0.60694], // U+0079 y
    122: M78, // U+007a z
    123: M73, // U+007b {
    124: M74, // U+007c |
    125: M73, // U+007d }
    126: [0.35, 0.34444, 0, 0, 0.575], // U+007e ~
    160: M7, // U+00a0
    163: [0, 0.69444, 0, 0, 0.86853], // U+00a3 £
    168: M79, // U+00a8 ¨
    172: [0, 0.44444, 0, 0, 0.76666], // U+00ac ¬
    176: M82, // U+00b0 °
    177: M80, // U+00b1 ±
    184: [0.17014, 0, 0, 0, 0.51111], // U+00b8 ¸
    198: [0, 0.68611, 0, 0, 1.04166], // U+00c6 Æ
    215: M80, // U+00d7 ×
    216: [0.04861, 0.73472, 0, 0, 0.89444], // U+00d8 Ø
    223: M228, // U+00df ß
    230: [0, 0.44444, 0, 0, 0.83055], // U+00e6 æ
    247: M80, // U+00f7 ÷
    248: [0.09722, 0.54167, 0, 0, 0.575], // U+00f8 ø
    305: M81, // U+0131 ı
    338: [0, 0.68611, 0, 0, 1.16944], // U+0152 Œ
    339: M97, // U+0153 œ
    567: [0.19444, 0.44444, 0, 0, 0.35139], // U+0237 ȷ
    710: M79, // U+02c6 ˆ
    711: [0, 0.63194, 0, 0, 0.575], // U+02c7 ˇ
    713: [0, 0.59611, 0, 0, 0.575], // U+02c9 ˉ
    714: M79, // U+02ca ˊ
    715: M79, // U+02cb ˋ
    728: M79, // U+02d8 ˘
    729: M76, // U+02d9 ˙
    730: M82, // U+02da ˚
    732: M79, // U+02dc ˜
    733: M79, // U+02dd ˝
    915: M83, // U+0393 Γ
    916: M94, // U+0394 Δ
    920: M85, // U+0398 Θ
    923: M184, // U+039b Λ
    926: M186, // U+039e Ξ
    928: M71, // U+03a0 Π
    931: M84, // U+03a3 Σ
    933: M85, // U+03a5 Υ
    934: M84, // U+03a6 Φ
    936: M85, // U+03a8 Ψ
    937: M84, // U+03a9 Ω
    8211: [0, 0.44444, 0.03194, 0, 0.575], // U+2013 –
    8212: [0, 0.44444, 0.03194, 0, 1.14999], // U+2014 —
    8216: M76, // U+2018 ‘
    8217: M76, // U+2019 ’
    8220: M86, // U+201c “
    8221: M86, // U+201d ”
    8224: M87, // U+2020 †
    8225: M87, // U+2021 ‡
    8242: [0, 0.55556, 0, 0, 0.34444], // U+2032 ′
    8407: [0, 0.72444, 0.15486, 0, 0.575], // U+20d7 ⃗
    8463: M183, // U+210f ℏ
    8465: M88, // U+2111 ℑ
    8467: [0, 0.69444, 0, 0, 0.47361], // U+2113 ℓ
    8472: [0.19444, 0.44444, 0, 0, 0.74027], // U+2118 ℘
    8476: M88, // U+211c ℜ
    8501: M103, // U+2135 ℵ
    8592: M89, // U+2190 ←
    8593: M90, // U+2191 ↑
    8594: M89, // U+2192 →
    8595: M90, // U+2193 ↓
    8596: M89, // U+2194 ↔
    8597: M73, // U+2195 ↕
    8598: M91, // U+2196 ↖
    8599: M91, // U+2197 ↗
    8600: M91, // U+2198 ↘
    8601: M91, // U+2199 ↙
    8636: M89, // U+21bc ↼
    8637: M89, // U+21bd ↽
    8640: M89, // U+21c0 ⇀
    8641: M89, // U+21c1 ⇁
    8656: M89, // U+21d0 ⇐
    8657: M92, // U+21d1 ⇑
    8658: M89, // U+21d2 ⇒
    8659: M92, // U+21d3 ⇓
    8660: M89, // U+21d4 ⇔
    8661: [0.25, 0.75, 0, 0, 0.70277], // U+21d5 ⇕
    8704: M75, // U+2200 ∀
    8706: [0, 0.69444, 0.06389, 0, 0.62847], // U+2202 ∂
    8707: M75, // U+2203 ∃
    8709: M93, // U+2205 ∅
    8711: M94, // U+2207 ∇
    8712: M95, // U+2208 ∈
    8715: M95, // U+220b ∋
    8722: M80, // U+2212 −
    8723: M80, // U+2213 ∓
    8725: M73, // U+2215 ∕
    8726: M73, // U+2216 ∖
    8727: M104, // U+2217 ∗
    8728: M96, // U+2218 ∘
    8729: M96, // U+2219 ∙
    8730: [0.18, 0.82, 0, 0, 0.95833], // U+221a √
    8733: M97, // U+221d ∝
    8734: [0, 0.44444, 0, 0, 1.14999], // U+221e ∞
    8736: M25, // U+2220 ∠
    8739: M74, // U+2223 ∣
    8741: M73, // U+2225 ∥
    8743: M98, // U+2227 ∧
    8744: M98, // U+2228 ∨
    8745: M98, // U+2229 ∩
    8746: M98, // U+222a ∪
    8747: [0.19444, 0.69444, 0.12778, 0, 0.56875], // U+222b ∫
    8764: M99, // U+223c ∼
    8768: [0.19444, 0.69444, 0, 0, 0.31944], // U+2240 ≀
    8771: M100, // U+2243 ≃
    8776: [0.02444, 0.52444, 0, 0, 0.89444], // U+2248 ≈
    8781: M100, // U+224d ≍
    8801: M100, // U+2261 ≡
    8804: M101, // U+2264 ≤
    8805: M101, // U+2265 ≥
    8810: M102, // U+226a ≪
    8811: M102, // U+226b ≫
    8826: M69, // U+227a ≺
    8827: M69, // U+227b ≻
    8834: M69, // U+2282 ⊂
    8835: M69, // U+2283 ⊃
    8838: M101, // U+2286 ⊆
    8839: M101, // U+2287 ⊇
    8846: M98, // U+228e ⊎
    8849: M101, // U+2291 ⊑
    8850: M101, // U+2292 ⊒
    8851: M98, // U+2293 ⊓
    8852: M98, // U+2294 ⊔
    8853: M80, // U+2295 ⊕
    8854: M80, // U+2296 ⊖
    8855: M80, // U+2297 ⊗
    8856: M80, // U+2298 ⊘
    8857: M80, // U+2299 ⊙
    8866: M103, // U+22a2 ⊢
    8867: M103, // U+22a3 ⊣
    8868: M70, // U+22a4 ⊤
    8869: M70, // U+22a5 ⊥
    8900: M96, // U+22c4 ⋄
    8901: [-0.02639, 0.47361, 0, 0, 0.31944], // U+22c5 ⋅
    8902: M104, // U+22c6 ⋆
    8968: M105, // U+2308 ⌈
    8969: M105, // U+2309 ⌉
    8970: M105, // U+230a ⌊
    8971: M105, // U+230b ⌋
    8994: M106, // U+2322 ⌢
    8995: M106, // U+2323 ⌣
    9651: M107, // U+25b3 △
    9657: M104, // U+25b9 ▹
    9661: M107, // U+25bd ▽
    9667: M104, // U+25c3 ◃
    9711: M91, // U+25ef ◯
    9824: M108, // U+2660 ♠
    9825: M108, // U+2661 ♡
    9826: M108, // U+2662 ♢
    9827: M108, // U+2663 ♣
    9837: [0, 0.75, 0, 0, 0.44722], // U+266d ♭
    9838: M109, // U+266e ♮
    9839: M109, // U+266f ♯
    10216: M67, // U+27e8 ⟨
    10217: M67, // U+27e9 ⟩
    10815: M71, // U+2a3f ⨿
    10927: M101, // U+2aaf ⪯
    10928: M101, // U+2ab0 ⪰
    57376: M179, // U+e020 
  },
  'Main-BoldItalic': {
    32: M7, // U+0020
    33: [0, 0.69444, 0.11417, 0, 0.38611], // U+0021 !
    34: M120, // U+0022 "
    35: [0.19444, 0.69444, 0.06833, 0, 0.94444], // U+0023 #
    37: [0.05556, 0.75, 0.12861, 0, 0.94444], // U+0025 %
    38: [0, 0.69444, 0.08528, 0, 0.88555], // U+0026 &
    39: M116, // U+0027 '
    40: [0.25, 0.75, 0.15806, 0, 0.47333], // U+0028 (
    41: [0.25, 0.75, 0.03306, 0, 0.47333], // U+0029 )
    42: [0, 0.75, 0.14333, 0, 0.59111], // U+002a *
    43: [0.10333, 0.60333, 0.03306, 0, 0.88555], // U+002b +
    44: [0.19444, 0.14722, 0, 0, 0.35555], // U+002c ,
    45: [0, 0.44444, 0.02611, 0, 0.41444], // U+002d -
    46: [0, 0.14722, 0, 0, 0.35555], // U+002e .
    47: [0.25, 0.75, 0.15806, 0, 0.59111], // U+002f /
    48: M110, // U+0030 0
    49: M110, // U+0031 1
    50: M110, // U+0032 2
    51: M110, // U+0033 3
    52: M111, // U+0034 4
    53: M110, // U+0035 5
    54: M110, // U+0036 6
    55: M111, // U+0037 7
    56: M110, // U+0038 8
    57: M110, // U+0039 9
    58: [0, 0.44444, 0.06695, 0, 0.35555], // U+003a :
    59: [0.19444, 0.44444, 0.06695, 0, 0.35555], // U+003b ;
    61: [-0.10889, 0.39111, 0.06833, 0, 0.88555], // U+003d =
    63: M118, // U+003f ?
    64: [0, 0.69444, 0.09208, 0, 0.88555], // U+0040 @
    65: [0, 0.68611, 0, 0, 0.86555], // U+0041 A
    66: [0, 0.68611, 0.0992, 0, 0.81666], // U+0042 B
    67: [0, 0.68611, 0.14208, 0, 0.82666], // U+0043 C
    68: [0, 0.68611, 0.09062, 0, 0.87555], // U+0044 D
    69: [0, 0.68611, 0.11431, 0, 0.75666], // U+0045 E
    70: [0, 0.68611, 0.12903, 0, 0.72722], // U+0046 F
    71: [0, 0.68611, 0.07347, 0, 0.89527], // U+0047 G
    72: M112, // U+0048 H
    73: [0, 0.68611, 0.15681, 0, 0.47166], // U+0049 I
    74: [0, 0.68611, 0.145, 0, 0.61055], // U+004a J
    75: [0, 0.68611, 0.14208, 0, 0.89499], // U+004b K
    76: [0, 0.68611, 0, 0, 0.69777], // U+004c L
    77: [0, 0.68611, 0.17208, 0, 1.07277], // U+004d M
    78: M112, // U+004e N
    79: [0, 0.68611, 0.09062, 0, 0.85499], // U+004f O
    80: [0, 0.68611, 0.0992, 0, 0.78721], // U+0050 P
    81: [0.19444, 0.68611, 0.09062, 0, 0.85499], // U+0051 Q
    82: [0, 0.68611, 0.02559, 0, 0.85944], // U+0052 R
    83: [0, 0.68611, 0.11264, 0, 0.64999], // U+0053 S
    84: [0, 0.68611, 0.12903, 0, 0.7961], // U+0054 T
    85: [0, 0.68611, 0.17208, 0, 0.88083], // U+0055 U
    86: [0, 0.68611, 0.18625, 0, 0.86555], // U+0056 V
    87: [0, 0.68611, 0.18625, 0, 1.15999], // U+0057 W
    88: [0, 0.68611, 0.15681, 0, 0.86555], // U+0058 X
    89: [0, 0.68611, 0.19803, 0, 0.86555], // U+0059 Y
    90: [0, 0.68611, 0.14208, 0, 0.70888], // U+005a Z
    91: [0.25, 0.75, 0.1875, 0, 0.35611], // U+005b [
    93: [0.25, 0.75, 0.09972, 0, 0.35611], // U+005d ]
    94: M115, // U+005e ^
    95: [0.31, 0.13444, 0.09811, 0, 0.59111], // U+005f _
    97: [0, 0.44444, 0.09426, 0, 0.59111], // U+0061 a
    98: [0, 0.69444, 0.07861, 0, 0.53222], // U+0062 b
    99: [0, 0.44444, 0.05222, 0, 0.53222], // U+0063 c
    100: [0, 0.69444, 0.10861, 0, 0.59111], // U+0064 d
    101: [0, 0.44444, 0.085, 0, 0.53222], // U+0065 e
    102: [0.19444, 0.69444, 0.21778, 0, 0.4], // U+0066 f
    103: M113, // U+0067 g
    104: [0, 0.69444, 0.09426, 0, 0.59111], // U+0068 h
    105: [0, 0.69326, 0.11387, 0, 0.35555], // U+0069 i
    106: [0.19444, 0.69326, 0.1672, 0, 0.35555], // U+006a j
    107: [0, 0.69444, 0.11111, 0, 0.53222], // U+006b k
    108: [0, 0.69444, 0.10861, 0, 0.29666], // U+006c l
    109: [0, 0.44444, 0.09426, 0, 0.94444], // U+006d m
    110: [0, 0.44444, 0.09426, 0, 0.64999], // U+006e n
    111: [0, 0.44444, 0.07861, 0, 0.59111], // U+006f o
    112: [0.19444, 0.44444, 0.07861, 0, 0.59111], // U+0070 p
    113: M113, // U+0071 q
    114: [0, 0.44444, 0.11111, 0, 0.50167], // U+0072 r
    115: [0, 0.44444, 0.08167, 0, 0.48694], // U+0073 s
    116: [0, 0.63492, 0.09639, 0, 0.385], // U+0074 t
    117: [0, 0.44444, 0.09426, 0, 0.62055], // U+0075 u
    118: [0, 0.44444, 0.11111, 0, 0.53222], // U+0076 v
    119: [0, 0.44444, 0.11111, 0, 0.76777], // U+0077 w
    120: [0, 0.44444, 0.12583, 0, 0.56055], // U+0078 x
    121: [0.19444, 0.44444, 0.105, 0, 0.56166], // U+0079 y
    122: [0, 0.44444, 0.13889, 0, 0.49055], // U+007a z
    126: [0.35, 0.34444, 0.11472, 0, 0.59111], // U+007e ~
    160: M7, // U+00a0
    168: [0, 0.69444, 0.11473, 0, 0.59111], // U+00a8 ¨
    176: M117, // U+00b0 °
    184: [0.17014, 0, 0, 0, 0.53222], // U+00b8 ¸
    198: [0, 0.68611, 0.11431, 0, 1.02277], // U+00c6 Æ
    216: [0.04861, 0.73472, 0.09062, 0, 0.88555], // U+00d8 Ø
    223: [0.19444, 0.69444, 0.09736, 0, 0.665], // U+00df ß
    230: M114, // U+00e6 æ
    248: [0.09722, 0.54167, 0.09458, 0, 0.59111], // U+00f8 ø
    305: [0, 0.44444, 0.09426, 0, 0.35555], // U+0131 ı
    338: [0, 0.68611, 0.11431, 0, 1.14054], // U+0152 Œ
    339: M114, // U+0153 œ
    567: [0.19444, 0.44444, 0.04611, 0, 0.385], // U+0237 ȷ
    710: M115, // U+02c6 ˆ
    711: [0, 0.63194, 0.08271, 0, 0.59111], // U+02c7 ˇ
    713: [0, 0.59444, 0.10444, 0, 0.59111], // U+02c9 ˉ
    714: [0, 0.69444, 0.08528, 0, 0.59111], // U+02ca ˊ
    715: [0, 0.69444, 0, 0, 0.59111], // U+02cb ˋ
    728: [0, 0.69444, 0.10333, 0, 0.59111], // U+02d8 ˘
    729: M116, // U+02d9 ˙
    730: M117, // U+02da ˚
    732: M118, // U+02dc ˜
    733: M118, // U+02dd ˝
    915: [0, 0.68611, 0.12903, 0, 0.69777], // U+0393 Γ
    916: [0, 0.68611, 0, 0, 0.94444], // U+0394 Δ
    920: [0, 0.68611, 0.09062, 0, 0.88555], // U+0398 Θ
    923: [0, 0.68611, 0, 0, 0.80666], // U+039b Λ
    926: [0, 0.68611, 0.15092, 0, 0.76777], // U+039e Ξ
    928: M112, // U+03a0 Π
    931: [0, 0.68611, 0.11431, 0, 0.82666], // U+03a3 Σ
    933: M119, // U+03a5 Υ
    934: [0, 0.68611, 0.05632, 0, 0.82666], // U+03a6 Φ
    936: M119, // U+03a8 Ψ
    937: [0, 0.68611, 0.0992, 0, 0.82666], // U+03a9 Ω
    8211: [0, 0.44444, 0.09811, 0, 0.59111], // U+2013 –
    8212: [0, 0.44444, 0.09811, 0, 1.18221], // U+2014 —
    8216: M116, // U+2018 ‘
    8217: M116, // U+2019 ’
    8220: [0, 0.69444, 0.16772, 0, 0.62055], // U+201c “
    8221: M120, // U+201d ”
  },
  'Main-Italic': {
    32: M7, // U+0020
    33: M121, // U+0021 !
    34: M132, // U+0022 "
    35: [0.19444, 0.69444, 0.06616, 0, 0.81777], // U+0023 #
    37: [0.05556, 0.75, 0.13639, 0, 0.81777], // U+0025 %
    38: [0, 0.69444, 0.09694, 0, 0.76666], // U+0026 &
    39: M121, // U+0027 '
    40: [0.25, 0.75, 0.16194, 0, 0.40889], // U+0028 (
    41: [0.25, 0.75, 0.03694, 0, 0.40889], // U+0029 )
    42: [0, 0.75, 0.14917, 0, 0.51111], // U+002a *
    43: [0.05667, 0.56167, 0.03694, 0, 0.76666], // U+002b +
    44: [0.19444, 0.10556, 0, 0, 0.30667], // U+002c ,
    45: [0, 0.43056, 0.02826, 0, 0.35778], // U+002d -
    46: [0, 0.10556, 0, 0, 0.30667], // U+002e .
    47: [0.25, 0.75, 0.16194, 0, 0.51111], // U+002f /
    48: M122, // U+0030 0
    49: M122, // U+0031 1
    50: M122, // U+0032 2
    51: M122, // U+0033 3
    52: M123, // U+0034 4
    53: M122, // U+0035 5
    54: M122, // U+0036 6
    55: M123, // U+0037 7
    56: M122, // U+0038 8
    57: M122, // U+0039 9
    58: [0, 0.43056, 0.0582, 0, 0.30667], // U+003a :
    59: [0.19444, 0.43056, 0.0582, 0, 0.30667], // U+003b ;
    61: [-0.13313, 0.36687, 0.06616, 0, 0.76666], // U+003d =
    63: M129, // U+003f ?
    64: [0, 0.69444, 0.09597, 0, 0.76666], // U+0040 @
    65: [0, 0.68333, 0, 0, 0.74333], // U+0041 A
    66: [0, 0.68333, 0.10257, 0, 0.70389], // U+0042 B
    67: [0, 0.68333, 0.14528, 0, 0.71555], // U+0043 C
    68: [0, 0.68333, 0.09403, 0, 0.755], // U+0044 D
    69: [0, 0.68333, 0.12028, 0, 0.67833], // U+0045 E
    70: [0, 0.68333, 0.13305, 0, 0.65277], // U+0046 F
    71: [0, 0.68333, 0.08722, 0, 0.77361], // U+0047 G
    72: M124, // U+0048 H
    73: [0, 0.68333, 0.15806, 0, 0.38555], // U+0049 I
    74: [0, 0.68333, 0.14028, 0, 0.525], // U+004a J
    75: [0, 0.68333, 0.14528, 0, 0.76888], // U+004b K
    76: [0, 0.68333, 0, 0, 0.62722], // U+004c L
    77: [0, 0.68333, 0.16389, 0, 0.89666], // U+004d M
    78: M124, // U+004e N
    79: M130, // U+004f O
    80: [0, 0.68333, 0.10257, 0, 0.67833], // U+0050 P
    81: [0.19444, 0.68333, 0.09403, 0, 0.76666], // U+0051 Q
    82: [0, 0.68333, 0.03868, 0, 0.72944], // U+0052 R
    83: [0, 0.68333, 0.11972, 0, 0.56222], // U+0053 S
    84: [0, 0.68333, 0.13305, 0, 0.71555], // U+0054 T
    85: M124, // U+0055 U
    86: [0, 0.68333, 0.18361, 0, 0.74333], // U+0056 V
    87: [0, 0.68333, 0.18361, 0, 0.99888], // U+0057 W
    88: [0, 0.68333, 0.15806, 0, 0.74333], // U+0058 X
    89: [0, 0.68333, 0.19383, 0, 0.74333], // U+0059 Y
    90: [0, 0.68333, 0.14528, 0, 0.61333], // U+005a Z
    91: [0.25, 0.75, 0.1875, 0, 0.30667], // U+005b [
    93: [0.25, 0.75, 0.10528, 0, 0.30667], // U+005d ]
    94: M127, // U+005e ^
    95: [0.31, 0.12056, 0.09208, 0, 0.51111], // U+005f _
    97: [0, 0.43056, 0.07671, 0, 0.51111], // U+0061 a
    98: [0, 0.69444, 0.06312, 0, 0.46], // U+0062 b
    99: [0, 0.43056, 0.05653, 0, 0.46], // U+0063 c
    100: [0, 0.69444, 0.10333, 0, 0.51111], // U+0064 d
    101: [0, 0.43056, 0.07514, 0, 0.46], // U+0065 e
    102: [0.19444, 0.69444, 0.21194, 0, 0.30667], // U+0066 f
    103: M125, // U+0067 g
    104: [0, 0.69444, 0.07671, 0, 0.51111], // U+0068 h
    105: [0, 0.65536, 0.1019, 0, 0.30667], // U+0069 i
    106: [0.19444, 0.65536, 0.14467, 0, 0.30667], // U+006a j
    107: [0, 0.69444, 0.10764, 0, 0.46], // U+006b k
    108: [0, 0.69444, 0.10333, 0, 0.25555], // U+006c l
    109: [0, 0.43056, 0.07671, 0, 0.81777], // U+006d m
    110: [0, 0.43056, 0.07671, 0, 0.56222], // U+006e n
    111: [0, 0.43056, 0.06312, 0, 0.51111], // U+006f o
    112: [0.19444, 0.43056, 0.06312, 0, 0.51111], // U+0070 p
    113: M125, // U+0071 q
    114: [0, 0.43056, 0.10764, 0, 0.42166], // U+0072 r
    115: [0, 0.43056, 0.08208, 0, 0.40889], // U+0073 s
    116: [0, 0.61508, 0.09486, 0, 0.33222], // U+0074 t
    117: [0, 0.43056, 0.07671, 0, 0.53666], // U+0075 u
    118: [0, 0.43056, 0.10764, 0, 0.46], // U+0076 v
    119: [0, 0.43056, 0.10764, 0, 0.66444], // U+0077 w
    120: [0, 0.43056, 0.12042, 0, 0.46389], // U+0078 x
    121: [0.19444, 0.43056, 0.08847, 0, 0.48555], // U+0079 y
    122: [0, 0.43056, 0.12292, 0, 0.40889], // U+007a z
    126: [0.35, 0.31786, 0.11585, 0, 0.51111], // U+007e ~
    160: M7, // U+00a0
    168: [0, 0.66786, 0.10474, 0, 0.51111], // U+00a8 ¨
    176: M128, // U+00b0 °
    184: [0.17014, 0, 0, 0, 0.46], // U+00b8 ¸
    198: [0, 0.68333, 0.12028, 0, 0.88277], // U+00c6 Æ
    216: [0.04861, 0.73194, 0.09403, 0, 0.76666], // U+00d8 Ø
    223: [0.19444, 0.69444, 0.10514, 0, 0.53666], // U+00df ß
    230: M126, // U+00e6 æ
    248: [0.09722, 0.52778, 0.09194, 0, 0.51111], // U+00f8 ø
    338: [0, 0.68333, 0.12028, 0, 0.98499], // U+0152 Œ
    339: M126, // U+0153 œ
    710: M127, // U+02c6 ˆ
    711: [0, 0.62847, 0.08295, 0, 0.51111], // U+02c7 ˇ
    713: [0, 0.56167, 0.10333, 0, 0.51111], // U+02c9 ˉ
    714: [0, 0.69444, 0.09694, 0, 0.51111], // U+02ca ˊ
    715: [0, 0.69444, 0, 0, 0.51111], // U+02cb ˋ
    728: [0, 0.69444, 0.10806, 0, 0.51111], // U+02d8 ˘
    729: [0, 0.66786, 0.11752, 0, 0.30667], // U+02d9 ˙
    730: M128, // U+02da ˚
    732: [0, 0.66786, 0.11585, 0, 0.51111], // U+02dc ˜
    733: M129, // U+02dd ˝
    915: [0, 0.68333, 0.13305, 0, 0.62722], // U+0393 Γ
    916: [0, 0.68333, 0, 0, 0.81777], // U+0394 Δ
    920: M130, // U+0398 Θ
    923: [0, 0.68333, 0, 0, 0.69222], // U+039b Λ
    926: [0, 0.68333, 0.15294, 0, 0.66444], // U+039e Ξ
    928: M124, // U+03a0 Π
    931: [0, 0.68333, 0.12028, 0, 0.71555], // U+03a3 Σ
    933: M131, // U+03a5 Υ
    934: [0, 0.68333, 0.05986, 0, 0.71555], // U+03a6 Φ
    936: M131, // U+03a8 Ψ
    937: [0, 0.68333, 0.10257, 0, 0.71555], // U+03a9 Ω
    8211: [0, 0.43056, 0.09208, 0, 0.51111], // U+2013 –
    8212: [0, 0.43056, 0.09208, 0, 1.02222], // U+2014 —
    8216: M121, // U+2018 ‘
    8217: M121, // U+2019 ’
    8220: [0, 0.69444, 0.1685, 0, 0.51444], // U+201c “
    8221: M132, // U+201d ”
    8463: M10, // U+210f ℏ
  },
  'Main-Regular': {
    32: M7, // U+0020
    33: M133, // U+0021 !
    34: M143, // U+0022 "
    35: M220, // U+0023 #
    36: M161, // U+0024 $
    37: M221, // U+0025 %
    38: M136, // U+0026 &
    39: M133, // U+0027 '
    40: M134, // U+0028 (
    41: M134, // U+0029 )
    42: M222, // U+002a *
    43: M149, // U+002b +
    44: [0.19444, 0.10556, 0, 0, 0.27778], // U+002c ,
    45: [0, 0.43056, 0, 0, 0.33333], // U+002d -
    46: [0, 0.10556, 0, 0, 0.27778], // U+002e .
    47: M141, // U+002f /
    48: M135, // U+0030 0
    49: M135, // U+0031 1
    50: M135, // U+0032 2
    51: M135, // U+0033 3
    52: M135, // U+0034 4
    53: M135, // U+0035 5
    54: M135, // U+0036 6
    55: M135, // U+0037 7
    56: M135, // U+0038 8
    57: M135, // U+0039 9
    58: M150, // U+003a :
    59: [0.19444, 0.43056, 0, 0, 0.27778], // U+003b ;
    60: M43, // U+003c <
    61: M27, // U+003d =
    62: M43, // U+003e >
    63: M226, // U+003f ?
    64: M136, // U+0040 @
    65: M137, // U+0041 A
    66: [0, 0.68333, 0, 0, 0.70834], // U+0042 B
    67: M140, // U+0043 C
    68: [0, 0.68333, 0, 0, 0.76389], // U+0044 D
    69: M139, // U+0045 E
    70: [0, 0.68333, 0, 0, 0.65278], // U+0046 F
    71: [0, 0.68333, 0, 0, 0.78472], // U+0047 G
    72: M137, // U+0048 H
    73: [0, 0.68333, 0, 0, 0.36111], // U+0049 I
    74: [0, 0.68333, 0, 0, 0.51389], // U+004a J
    75: M138, // U+004b K
    76: M154, // U+004c L
    77: [0, 0.68333, 0, 0, 0.91667], // U+004d M
    78: M137, // U+004e N
    79: M138, // U+004f O
    80: M139, // U+0050 P
    81: [0.19444, 0.68333, 0, 0, 0.77778], // U+0051 Q
    82: [0, 0.68333, 0, 0, 0.73611], // U+0052 R
    83: [0, 0.68333, 0, 0, 0.55556], // U+0053 S
    84: M140, // U+0054 T
    85: M137, // U+0055 U
    86: [0, 0.68333, 0.01389, 0, 0.75], // U+0056 V
    87: [0, 0.68333, 0.01389, 0, 1.02778], // U+0057 W
    88: M137, // U+0058 X
    89: [0, 0.68333, 0.025, 0, 0.75], // U+0059 Y
    90: [0, 0.68333, 0, 0, 0.61111], // U+005a Z
    91: M142, // U+005b [
    92: M141, // U+005c \
    93: M142, // U+005d ]
    94: M143, // U+005e ^
    95: [0.31, 0.12056, 0.02778, 0, 0.5], // U+005f _
    97: M146, // U+0061 a
    98: M144, // U+0062 b
    99: M145, // U+0063 c
    100: M144, // U+0064 d
    101: M145, // U+0065 e
    102: [0, 0.69444, 0.07778, 0, 0.30556], // U+0066 f
    103: [0.19444, 0.43056, 0.01389, 0, 0.5], // U+0067 g
    104: M144, // U+0068 h
    105: M151, // U+0069 i
    106: [0.19444, 0.66786, 0, 0, 0.30556], // U+006a j
    107: [0, 0.69444, 0, 0, 0.52778], // U+006b k
    108: M133, // U+006c l
    109: [0, 0.43056, 0, 0, 0.83334], // U+006d m
    110: M148, // U+006e n
    111: M146, // U+006f o
    112: M147, // U+0070 p
    113: [0.19444, 0.43056, 0, 0, 0.52778], // U+0071 q
    114: [0, 0.43056, 0, 0, 0.39167], // U+0072 r
    115: [0, 0.43056, 0, 0, 0.39445], // U+0073 s
    116: [0, 0.61508, 0, 0, 0.38889], // U+0074 t
    117: M148, // U+0075 u
    118: [0, 0.43056, 0.01389, 0, 0.52778], // U+0076 v
    119: [0, 0.43056, 0.01389, 0, 0.72222], // U+0077 w
    120: [0, 0.43056, 0, 0, 0.52778], // U+0078 x
    121: [0.19444, 0.43056, 0.01389, 0, 0.52778], // U+0079 y
    122: M145, // U+007a z
    123: M141, // U+007b {
    124: M142, // U+007c |
    125: M141, // U+007d }
    126: [0.35, 0.31786, 0, 0, 0.5], // U+007e ~
    160: M7, // U+00a0
    163: [0, 0.69444, 0, 0, 0.76909], // U+00a3 £
    167: M155, // U+00a7 §
    168: M153, // U+00a8 ¨
    172: [0, 0.43056, 0, 0, 0.66667], // U+00ac ¬
    176: M152, // U+00b0 °
    177: M149, // U+00b1 ±
    182: M160, // U+00b6 ¶
    184: M213, // U+00b8 ¸
    198: [0, 0.68333, 0, 0, 0.90278], // U+00c6 Æ
    215: M149, // U+00d7 ×
    216: [0.04861, 0.73194, 0, 0, 0.77778], // U+00d8 Ø
    223: M143, // U+00df ß
    230: [0, 0.43056, 0, 0, 0.72222], // U+00e6 æ
    247: M149, // U+00f7 ÷
    248: [0.09722, 0.52778, 0, 0, 0.5], // U+00f8 ø
    305: M150, // U+0131 ı
    338: [0, 0.68333, 0, 0, 1.01389], // U+0152 Œ
    339: M165, // U+0153 œ
    567: [0.19444, 0.43056, 0, 0, 0.30556], // U+0237 ȷ
    710: M143, // U+02c6 ˆ
    711: [0, 0.62847, 0, 0, 0.5], // U+02c7 ˇ
    713: [0, 0.56778, 0, 0, 0.5], // U+02c9 ˉ
    714: M143, // U+02ca ˊ
    715: M143, // U+02cb ˋ
    728: M143, // U+02d8 ˘
    729: M151, // U+02d9 ˙
    730: M152, // U+02da ˚
    732: M153, // U+02dc ˜
    733: M143, // U+02dd ˝
    915: M154, // U+0393 Γ
    916: M162, // U+0394 Δ
    920: M138, // U+0398 Θ
    923: [0, 0.68333, 0, 0, 0.69445], // U+039b Λ
    926: [0, 0.68333, 0, 0, 0.66667], // U+039e Ξ
    928: M137, // U+03a0 Π
    931: M140, // U+03a3 Σ
    933: M138, // U+03a5 Υ
    934: M140, // U+03a6 Φ
    936: M138, // U+03a8 Ψ
    937: M140, // U+03a9 Ω
    8211: [0, 0.43056, 0.02778, 0, 0.5], // U+2013 –
    8212: [0, 0.43056, 0.02778, 0, 1], // U+2014 —
    8216: M133, // U+2018 ‘
    8217: M133, // U+2019 ’
    8220: M143, // U+201c “
    8221: M143, // U+201d ”
    8224: M155, // U+2020 †
    8225: M155, // U+2021 ‡
    8230: [0, 0.12, 0, 0, 1.172], // U+2026 …
    8242: [0, 0.55556, 0, 0, 0.275], // U+2032 ′
    8407: [0, 0.71444, 0.15382, 0, 0.5], // U+20d7 ⃗
    8463: M10, // U+210f ℏ
    8465: M156, // U+2111 ℑ
    8467: [0, 0.69444, 0, 0.11111, 0.41667], // U+2113 ℓ
    8472: [0.19444, 0.43056, 0, 0.11111, 0.63646], // U+2118 ℘
    8476: M156, // U+211c ℜ
    8501: M170, // U+2135 ℵ
    8592: M12, // U+2190 ←
    8593: M157, // U+2191 ↑
    8594: M12, // U+2192 →
    8595: M157, // U+2193 ↓
    8596: M12, // U+2194 ↔
    8597: M141, // U+2195 ↕
    8598: M158, // U+2196 ↖
    8599: M158, // U+2197 ↗
    8600: M158, // U+2198 ↘
    8601: M158, // U+2199 ↙
    8614: [0.011, 0.511, 0, 0, 1], // U+21a6 ↦
    8617: M159, // U+21a9 ↩
    8618: M159, // U+21aa ↪
    8636: M12, // U+21bc ↼
    8637: M12, // U+21bd ↽
    8640: M12, // U+21c0 ⇀
    8641: M12, // U+21c1 ⇁
    8652: [0.011, 0.671, 0, 0, 1], // U+21cc ⇌
    8656: M12, // U+21d0 ⇐
    8657: M160, // U+21d1 ⇑
    8658: M12, // U+21d2 ⇒
    8659: M160, // U+21d3 ⇓
    8660: M12, // U+21d4 ⇔
    8661: [0.25, 0.75, 0, 0, 0.61111], // U+21d5 ⇕
    8704: M144, // U+2200 ∀
    8706: [0, 0.69444, 0.05556, 0.08334, 0.5309], // U+2202 ∂
    8707: M144, // U+2203 ∃
    8709: M161, // U+2205 ∅
    8711: M162, // U+2207 ∇
    8712: M163, // U+2208 ∈
    8715: M163, // U+220b ∋
    8722: M149, // U+2212 −
    8723: M149, // U+2213 ∓
    8725: M141, // U+2215 ∕
    8726: M141, // U+2216 ∖
    8727: M171, // U+2217 ∗
    8728: M164, // U+2218 ∘
    8729: M164, // U+2219 ∙
    8730: [0.2, 0.8, 0, 0, 0.83334], // U+221a √
    8733: M165, // U+221d ∝
    8734: M17, // U+221e ∞
    8736: M25, // U+2220 ∠
    8739: M142, // U+2223 ∣
    8741: M141, // U+2225 ∥
    8743: M166, // U+2227 ∧
    8744: M166, // U+2228 ∨
    8745: M166, // U+2229 ∩
    8746: M166, // U+222a ∪
    8747: [0.19444, 0.69444, 0.11111, 0, 0.41667], // U+222b ∫
    8764: M27, // U+223c ∼
    8768: [0.19444, 0.69444, 0, 0, 0.27778], // U+2240 ≀
    8771: M167, // U+2243 ≃
    8773: [-0.022, 0.589, 0, 0, 1], // U+2245 ≅
    8776: M168, // U+2248 ≈
    8781: M167, // U+224d ≍
    8784: [-0.133, 0.67, 0, 0, 0.778], // U+2250 ≐
    8801: M167, // U+2261 ≡
    8804: M37, // U+2264 ≤
    8805: M37, // U+2265 ≥
    8810: M169, // U+226a ≪
    8811: M169, // U+226b ≫
    8826: M43, // U+227a ≺
    8827: M43, // U+227b ≻
    8834: M43, // U+2282 ⊂
    8835: M43, // U+2283 ⊃
    8838: M37, // U+2286 ⊆
    8839: M37, // U+2287 ⊇
    8846: M166, // U+228e ⊎
    8849: M37, // U+2291 ⊑
    8850: M37, // U+2292 ⊒
    8851: M166, // U+2293 ⊓
    8852: M166, // U+2294 ⊔
    8853: M149, // U+2295 ⊕
    8854: M149, // U+2296 ⊖
    8855: M149, // U+2297 ⊗
    8856: M149, // U+2298 ⊘
    8857: M149, // U+2299 ⊙
    8866: M170, // U+22a2 ⊢
    8867: M170, // U+22a3 ⊣
    8868: M136, // U+22a4 ⊤
    8869: M136, // U+22a5 ⊥
    8872: [0.249, 0.75, 0, 0, 0.867], // U+22a8 ⊨
    8900: M164, // U+22c4 ⋄
    8901: [-0.05555, 0.44445, 0, 0, 0.27778], // U+22c5 ⋅
    8902: M171, // U+22c6 ⋆
    8904: [0.005, 0.505, 0, 0, 0.9], // U+22c8 ⋈
    8942: [0.03, 0.9, 0, 0, 0.278], // U+22ee ⋮
    8943: [-0.19, 0.31, 0, 0, 1.172], // U+22ef ⋯
    8945: [-0.1, 0.82, 0, 0, 1.282], // U+22f1 ⋱
    8968: M172, // U+2308 ⌈
    8969: M172, // U+2309 ⌉
    8970: M172, // U+230a ⌊
    8971: M172, // U+230b ⌋
    8994: M173, // U+2322 ⌢
    8995: M173, // U+2323 ⌣
    9136: M174, // U+23b0 ⎰
    9137: M174, // U+23b1 ⎱
    9651: M175, // U+25b3 △
    9657: M171, // U+25b9 ▹
    9661: M175, // U+25bd ▽
    9667: M171, // U+25c3 ◃
    9711: M158, // U+25ef ◯
    9824: M176, // U+2660 ♠
    9825: M176, // U+2661 ♡
    9826: M176, // U+2662 ♢
    9827: M176, // U+2663 ♣
    9837: [0, 0.75, 0, 0, 0.38889], // U+266d ♭
    9838: M177, // U+266e ♮
    9839: M177, // U+266f ♯
    10216: M134, // U+27e8 ⟨
    10217: M134, // U+27e9 ⟩
    10222: M174, // U+27ee ⟮
    10223: M174, // U+27ef ⟯
    10229: [0.011, 0.511, 0, 0, 1.609], // U+27f5 ⟵
    10230: M178, // U+27f6 ⟶
    10231: [0.011, 0.511, 0, 0, 1.859], // U+27f7 ⟷
    10232: [0.024, 0.525, 0, 0, 1.609], // U+27f8 ⟸
    10233: [0.024, 0.525, 0, 0, 1.638], // U+27f9 ⟹
    10234: [0.024, 0.525, 0, 0, 1.858], // U+27fa ⟺
    10236: M178, // U+27fc ⟼
    10815: M137, // U+2a3f ⨿
    10927: M37, // U+2aaf ⪯
    10928: M37, // U+2ab0 ⪰
    57376: M179, // U+e020 
  },
  'Math-BoldItalic': {
    32: M7, // U+0020
    48: M180, // U+0030 0
    49: M180, // U+0031 1
    50: M180, // U+0032 2
    51: M181, // U+0033 3
    52: M181, // U+0034 4
    53: M181, // U+0035 5
    54: M68, // U+0036 6
    55: M181, // U+0037 7
    56: M68, // U+0038 8
    57: M181, // U+0039 9
    65: M72, // U+0041 A
    66: [0, 0.68611, 0.04835, 0, 0.8664], // U+0042 B
    67: [0, 0.68611, 0.06979, 0, 0.81694], // U+0043 C
    68: [0, 0.68611, 0.03194, 0, 0.93812], // U+0044 D
    69: [0, 0.68611, 0.05451, 0, 0.81007], // U+0045 E
    70: [0, 0.68611, 0.15972, 0, 0.68889], // U+0046 F
    71: [0, 0.68611, 0, 0, 0.88673], // U+0047 G
    72: M185, // U+0048 H
    73: [0, 0.68611, 0.07778, 0, 0.51111], // U+0049 I
    74: [0, 0.68611, 0.10069, 0, 0.63125], // U+004a J
    75: [0, 0.68611, 0.06979, 0, 0.97118], // U+004b K
    76: M182, // U+004c L
    77: [0, 0.68611, 0.11424, 0, 1.14201], // U+004d M
    78: [0, 0.68611, 0.11424, 0, 0.95034], // U+004e N
    79: [0, 0.68611, 0.03194, 0, 0.83666], // U+004f O
    80: [0, 0.68611, 0.15972, 0, 0.72309], // U+0050 P
    81: [0.19444, 0.68611, 0, 0, 0.86861], // U+0051 Q
    82: [0, 0.68611, 0.00421, 0, 0.87235], // U+0052 R
    83: [0, 0.68611, 0.05382, 0, 0.69271], // U+0053 S
    84: [0, 0.68611, 0.15972, 0, 0.63663], // U+0054 T
    85: [0, 0.68611, 0.11424, 0, 0.80027], // U+0055 U
    86: [0, 0.68611, 0.25555, 0, 0.67778], // U+0056 V
    87: [0, 0.68611, 0.15972, 0, 1.09305], // U+0057 W
    88: [0, 0.68611, 0.07778, 0, 0.94722], // U+0058 X
    89: [0, 0.68611, 0.25555, 0, 0.67458], // U+0059 Y
    90: [0, 0.68611, 0.06979, 0, 0.77257], // U+005a Z
    97: [0, 0.44444, 0, 0, 0.63287], // U+0061 a
    98: [0, 0.69444, 0, 0, 0.52083], // U+0062 b
    99: [0, 0.44444, 0, 0, 0.51342], // U+0063 c
    100: [0, 0.69444, 0, 0, 0.60972], // U+0064 d
    101: [0, 0.44444, 0, 0, 0.55361], // U+0065 e
    102: [0.19444, 0.69444, 0.11042, 0, 0.56806], // U+0066 f
    103: [0.19444, 0.44444, 0.03704, 0, 0.5449], // U+0067 g
    104: M183, // U+0068 h
    105: [0, 0.69326, 0, 0, 0.4048], // U+0069 i
    106: [0.19444, 0.69326, 0.0622, 0, 0.47083], // U+006a j
    107: [0, 0.69444, 0.01852, 0, 0.6037], // U+006b k
    108: [0, 0.69444, 0.0088, 0, 0.34815], // U+006c l
    109: [0, 0.44444, 0, 0, 1.0324], // U+006d m
    110: [0, 0.44444, 0, 0, 0.71296], // U+006e n
    111: M187, // U+006f o
    112: [0.19444, 0.44444, 0, 0, 0.60092], // U+0070 p
    113: [0.19444, 0.44444, 0.03704, 0, 0.54213], // U+0071 q
    114: [0, 0.44444, 0.03194, 0, 0.5287], // U+0072 r
    115: [0, 0.44444, 0, 0, 0.53125], // U+0073 s
    116: [0, 0.63492, 0, 0, 0.41528], // U+0074 t
    117: [0, 0.44444, 0, 0, 0.68102], // U+0075 u
    118: [0, 0.44444, 0.03704, 0, 0.56666], // U+0076 v
    119: [0, 0.44444, 0.02778, 0, 0.83148], // U+0077 w
    120: [0, 0.44444, 0, 0, 0.65903], // U+0078 x
    121: [0.19444, 0.44444, 0.03704, 0, 0.59028], // U+0079 y
    122: [0, 0.44444, 0.04213, 0, 0.55509], // U+007a z
    160: M7, // U+00a0
    915: [0, 0.68611, 0.15972, 0, 0.65694], // U+0393 Γ
    916: M94, // U+0394 Δ
    920: [0, 0.68611, 0.03194, 0, 0.86722], // U+0398 Θ
    923: M184, // U+039b Λ
    926: [0, 0.68611, 0.07458, 0, 0.84125], // U+039e Ξ
    928: M185, // U+03a0 Π
    931: [0, 0.68611, 0.05451, 0, 0.88507], // U+03a3 Σ
    933: [0, 0.68611, 0.15972, 0, 0.67083], // U+03a5 Υ
    934: M186, // U+03a6 Φ
    936: [0, 0.68611, 0.11653, 0, 0.71402], // U+03a8 Ψ
    937: [0, 0.68611, 0.04835, 0, 0.8789], // U+03a9 Ω
    945: [0, 0.44444, 0, 0, 0.76064], // U+03b1 α
    946: [0.19444, 0.69444, 0.03403, 0, 0.65972], // U+03b2 β
    947: [0.19444, 0.44444, 0.06389, 0, 0.59003], // U+03b3 γ
    948: [0, 0.69444, 0.03819, 0, 0.52222], // U+03b4 δ
    949: [0, 0.44444, 0, 0, 0.52882], // U+03b5 ε
    950: [0.19444, 0.69444, 0.06215, 0, 0.50833], // U+03b6 ζ
    951: [0.19444, 0.44444, 0.03704, 0, 0.6], // U+03b7 η
    952: [0, 0.69444, 0.03194, 0, 0.5618], // U+03b8 θ
    953: [0, 0.44444, 0, 0, 0.41204], // U+03b9 ι
    954: [0, 0.44444, 0, 0, 0.66759], // U+03ba κ
    955: [0, 0.69444, 0, 0, 0.67083], // U+03bb λ
    956: [0.19444, 0.44444, 0, 0, 0.70787], // U+03bc μ
    957: [0, 0.44444, 0.06898, 0, 0.57685], // U+03bd ν
    958: [0.19444, 0.69444, 0.03021, 0, 0.50833], // U+03be ξ
    959: M187, // U+03bf ο
    960: [0, 0.44444, 0.03704, 0, 0.68241], // U+03c0 π
    961: M188, // U+03c1 ρ
    962: [0.09722, 0.44444, 0.07917, 0, 0.42361], // U+03c2 ς
    963: [0, 0.44444, 0.03704, 0, 0.68588], // U+03c3 σ
    964: [0, 0.44444, 0.13472, 0, 0.52083], // U+03c4 τ
    965: [0, 0.44444, 0.03704, 0, 0.63055], // U+03c5 υ
    966: [0.19444, 0.44444, 0, 0, 0.74722], // U+03c6 φ
    967: [0.19444, 0.44444, 0, 0, 0.71805], // U+03c7 χ
    968: [0.19444, 0.69444, 0.03704, 0, 0.75833], // U+03c8 ψ
    969: [0, 0.44444, 0.03704, 0, 0.71782], // U+03c9 ω
    977: [0, 0.69444, 0, 0, 0.69155], // U+03d1 ϑ
    981: [0.19444, 0.69444, 0, 0, 0.7125], // U+03d5 ϕ
    982: [0, 0.44444, 0.03194, 0, 0.975], // U+03d6 ϖ
    1009: M188, // U+03f1 ϱ
    1013: [0, 0.44444, 0, 0, 0.48333], // U+03f5 ϵ
    57649: [0, 0.44444, 0, 0, 0.39352], // U+e131 
    57911: [0.19444, 0.44444, 0, 0, 0.43889], // U+e237 
  },
  'Math-Italic': {
    32: M7, // U+0020
    48: M146, // U+0030 0
    49: M146, // U+0031 1
    50: M146, // U+0032 2
    51: M189, // U+0033 3
    52: M189, // U+0034 4
    53: M189, // U+0035 5
    54: M135, // U+0036 6
    55: M189, // U+0037 7
    56: M135, // U+0038 8
    57: M189, // U+0039 9
    65: [0, 0.68333, 0, 0.13889, 0.75], // U+0041 A
    66: [0, 0.68333, 0.05017, 0.08334, 0.75851], // U+0042 B
    67: [0, 0.68333, 0.07153, 0.08334, 0.71472], // U+0043 C
    68: [0, 0.68333, 0.02778, 0.05556, 0.82792], // U+0044 D
    69: [0, 0.68333, 0.05764, 0.08334, 0.7382], // U+0045 E
    70: [0, 0.68333, 0.13889, 0.08334, 0.64306], // U+0046 F
    71: [0, 0.68333, 0, 0.08334, 0.78625], // U+0047 G
    72: M191, // U+0048 H
    73: [0, 0.68333, 0.07847, 0.11111, 0.43958], // U+0049 I
    74: [0, 0.68333, 0.09618, 0.16667, 0.55451], // U+004a J
    75: [0, 0.68333, 0.07153, 0.05556, 0.84931], // U+004b K
    76: [0, 0.68333, 0, 0.02778, 0.68056], // U+004c L
    77: [0, 0.68333, 0.10903, 0.08334, 0.97014], // U+004d M
    78: [0, 0.68333, 0.10903, 0.08334, 0.80347], // U+004e N
    79: M190, // U+004f O
    80: [0, 0.68333, 0.13889, 0.08334, 0.64201], // U+0050 P
    81: [0.19444, 0.68333, 0, 0.08334, 0.79056], // U+0051 Q
    82: [0, 0.68333, 0.00773, 0.08334, 0.75929], // U+0052 R
    83: [0, 0.68333, 0.05764, 0.08334, 0.6132], // U+0053 S
    84: [0, 0.68333, 0.13889, 0.08334, 0.58438], // U+0054 T
    85: [0, 0.68333, 0.10903, 0.02778, 0.68278], // U+0055 U
    86: [0, 0.68333, 0.22222, 0, 0.58333], // U+0056 V
    87: [0, 0.68333, 0.13889, 0, 0.94445], // U+0057 W
    88: [0, 0.68333, 0.07847, 0.08334, 0.82847], // U+0058 X
    89: [0, 0.68333, 0.22222, 0, 0.58056], // U+0059 Y
    90: [0, 0.68333, 0.07153, 0.08334, 0.68264], // U+005a Z
    97: [0, 0.43056, 0, 0, 0.52859], // U+0061 a
    98: [0, 0.69444, 0, 0, 0.42917], // U+0062 b
    99: [0, 0.43056, 0, 0.05556, 0.43276], // U+0063 c
    100: [0, 0.69444, 0, 0.16667, 0.52049], // U+0064 d
    101: [0, 0.43056, 0, 0.05556, 0.46563], // U+0065 e
    102: [0.19444, 0.69444, 0.10764, 0.16667, 0.48959], // U+0066 f
    103: [0.19444, 0.43056, 0.03588, 0.02778, 0.47697], // U+0067 g
    104: [0, 0.69444, 0, 0, 0.57616], // U+0068 h
    105: [0, 0.65952, 0, 0, 0.34451], // U+0069 i
    106: [0.19444, 0.65952, 0.05724, 0, 0.41181], // U+006a j
    107: [0, 0.69444, 0.03148, 0, 0.5206], // U+006b k
    108: [0, 0.69444, 0.01968, 0.08334, 0.29838], // U+006c l
    109: [0, 0.43056, 0, 0, 0.87801], // U+006d m
    110: [0, 0.43056, 0, 0, 0.60023], // U+006e n
    111: M192, // U+006f o
    112: [0.19444, 0.43056, 0, 0.08334, 0.50313], // U+0070 p
    113: [0.19444, 0.43056, 0.03588, 0.08334, 0.44641], // U+0071 q
    114: [0, 0.43056, 0.02778, 0.05556, 0.45116], // U+0072 r
    115: [0, 0.43056, 0, 0.05556, 0.46875], // U+0073 s
    116: [0, 0.61508, 0, 0.08334, 0.36111], // U+0074 t
    117: [0, 0.43056, 0, 0.02778, 0.57246], // U+0075 u
    118: [0, 0.43056, 0.03588, 0.02778, 0.48472], // U+0076 v
    119: [0, 0.43056, 0.02691, 0.08334, 0.71592], // U+0077 w
    120: [0, 0.43056, 0, 0.02778, 0.57153], // U+0078 x
    121: [0.19444, 0.43056, 0.03588, 0.05556, 0.49028], // U+0079 y
    122: [0, 0.43056, 0.04398, 0.05556, 0.46505], // U+007a z
    160: M7, // U+00a0
    915: [0, 0.68333, 0.13889, 0.08334, 0.61528], // U+0393 Γ
    916: [0, 0.68333, 0, 0.16667, 0.83334], // U+0394 Δ
    920: M190, // U+0398 Θ
    923: [0, 0.68333, 0, 0.16667, 0.69445], // U+039b Λ
    926: [0, 0.68333, 0.07569, 0.08334, 0.74236], // U+039e Ξ
    928: M191, // U+03a0 Π
    931: [0, 0.68333, 0.05764, 0.08334, 0.77986], // U+03a3 Σ
    933: [0, 0.68333, 0.13889, 0.05556, 0.58333], // U+03a5 Υ
    934: [0, 0.68333, 0, 0.08334, 0.66667], // U+03a6 Φ
    936: [0, 0.68333, 0.11, 0.05556, 0.61222], // U+03a8 Ψ
    937: [0, 0.68333, 0.05017, 0.08334, 0.7724], // U+03a9 Ω
    945: [0, 0.43056, 0.0037, 0.02778, 0.6397], // U+03b1 α
    946: [0.19444, 0.69444, 0.05278, 0.08334, 0.56563], // U+03b2 β
    947: [0.19444, 0.43056, 0.05556, 0, 0.51773], // U+03b3 γ
    948: [0, 0.69444, 0.03785, 0.05556, 0.44444], // U+03b4 δ
    949: [0, 0.43056, 0, 0.08334, 0.46632], // U+03b5 ε
    950: [0.19444, 0.69444, 0.07378, 0.08334, 0.4375], // U+03b6 ζ
    951: [0.19444, 0.43056, 0.03588, 0.05556, 0.49653], // U+03b7 η
    952: [0, 0.69444, 0.02778, 0.08334, 0.46944], // U+03b8 θ
    953: [0, 0.43056, 0, 0.05556, 0.35394], // U+03b9 ι
    954: [0, 0.43056, 0, 0, 0.57616], // U+03ba κ
    955: [0, 0.69444, 0, 0, 0.58334], // U+03bb λ
    956: [0.19444, 0.43056, 0, 0.02778, 0.60255], // U+03bc μ
    957: [0, 0.43056, 0.06366, 0.02778, 0.49398], // U+03bd ν
    958: [0.19444, 0.69444, 0.04601, 0.11111, 0.4375], // U+03be ξ
    959: M192, // U+03bf ο
    960: [0, 0.43056, 0.03588, 0, 0.57003], // U+03c0 π
    961: M193, // U+03c1 ρ
    962: [0.09722, 0.43056, 0.07986, 0.08334, 0.36285], // U+03c2 ς
    963: [0, 0.43056, 0.03588, 0, 0.57141], // U+03c3 σ
    964: [0, 0.43056, 0.1132, 0.02778, 0.43715], // U+03c4 τ
    965: [0, 0.43056, 0.03588, 0.02778, 0.54028], // U+03c5 υ
    966: [0.19444, 0.43056, 0, 0.08334, 0.65417], // U+03c6 φ
    967: [0.19444, 0.43056, 0, 0.05556, 0.62569], // U+03c7 χ
    968: [0.19444, 0.69444, 0.03588, 0.11111, 0.65139], // U+03c8 ψ
    969: [0, 0.43056, 0.03588, 0, 0.62245], // U+03c9 ω
    977: [0, 0.69444, 0, 0.08334, 0.59144], // U+03d1 ϑ
    981: [0.19444, 0.69444, 0, 0.08334, 0.59583], // U+03d5 ϕ
    982: [0, 0.43056, 0.02778, 0, 0.82813], // U+03d6 ϖ
    1009: M193, // U+03f1 ϱ
    1013: [0, 0.43056, 0, 0.05556, 0.4059], // U+03f5 ϵ
    57649: [0, 0.43056, 0, 0.02778, 0.32246], // U+e131 
    57911: [0.19444, 0.43056, 0, 0.08334, 0.38403], // U+e237 
  },
  'SansSerif-Bold': {
    32: M7, // U+0020
    33: [0, 0.69444, 0, 0, 0.36667], // U+0021 !
    34: M210, // U+0022 "
    35: [0.19444, 0.69444, 0, 0, 0.91667], // U+0023 #
    36: [0.05556, 0.75, 0, 0, 0.55], // U+0024 $
    37: [0.05556, 0.75, 0, 0, 1.02912], // U+0025 %
    38: [0, 0.69444, 0, 0, 0.83056], // U+0026 &
    39: M206, // U+0027 '
    40: M194, // U+0028 (
    41: M194, // U+0029 )
    42: [0, 0.75, 0, 0, 0.55], // U+002a *
    43: [0.11667, 0.61667, 0, 0, 0.85556], // U+002b +
    44: [0.10556, 0.13056, 0, 0, 0.30556], // U+002c ,
    45: [0, 0.45833, 0, 0, 0.36667], // U+002d -
    46: [0, 0.13056, 0, 0, 0.30556], // U+002e .
    47: [0.25, 0.75, 0, 0, 0.55], // U+002f /
    48: M195, // U+0030 0
    49: M195, // U+0031 1
    50: M195, // U+0032 2
    51: M195, // U+0033 3
    52: M195, // U+0034 4
    53: M195, // U+0035 5
    54: M195, // U+0036 6
    55: M195, // U+0037 7
    56: M195, // U+0038 8
    57: M195, // U+0039 9
    58: [0, 0.45833, 0, 0, 0.30556], // U+003a :
    59: [0.10556, 0.45833, 0, 0, 0.30556], // U+003b ;
    61: [-0.09375, 0.40625, 0, 0, 0.85556], // U+003d =
    63: M198, // U+003f ?
    64: M196, // U+0040 @
    65: M196, // U+0041 A
    66: M196, // U+0042 B
    67: M199, // U+0043 C
    68: M197, // U+0044 D
    69: [0, 0.69444, 0, 0, 0.64167], // U+0045 E
    70: M170, // U+0046 F
    71: M196, // U+0047 G
    72: M197, // U+0048 H
    73: [0, 0.69444, 0, 0, 0.33056], // U+0049 I
    74: M198, // U+004a J
    75: M200, // U+004b K
    76: M207, // U+004c L
    77: [0, 0.69444, 0, 0, 0.97778], // U+004d M
    78: M197, // U+004e N
    79: M197, // U+004f O
    80: M199, // U+0050 P
    81: [0.10556, 0.69444, 0, 0, 0.79445], // U+0051 Q
    82: M199, // U+0052 R
    83: M170, // U+0053 S
    84: M196, // U+0054 T
    85: M200, // U+0055 U
    86: [0, 0.69444, 0.01528, 0, 0.73334], // U+0056 V
    87: [0, 0.69444, 0.01528, 0, 1.03889], // U+0057 W
    88: M196, // U+0058 X
    89: [0, 0.69444, 0.0275, 0, 0.73334], // U+0059 Y
    90: M208, // U+005a Z
    91: M201, // U+005b [
    93: M201, // U+005d ]
    94: M195, // U+005e ^
    95: [0.35, 0.10833, 0.03056, 0, 0.55], // U+005f _
    97: [0, 0.45833, 0, 0, 0.525], // U+0061 a
    98: M202, // U+0062 b
    99: [0, 0.45833, 0, 0, 0.48889], // U+0063 c
    100: M202, // U+0064 d
    101: [0, 0.45833, 0, 0, 0.51111], // U+0065 e
    102: [0, 0.69444, 0.07639, 0, 0.33611], // U+0066 f
    103: [0.19444, 0.45833, 0.01528, 0, 0.55], // U+0067 g
    104: M202, // U+0068 h
    105: M203, // U+0069 i
    106: [0.19444, 0.69444, 0, 0, 0.28611], // U+006a j
    107: [0, 0.69444, 0, 0, 0.53056], // U+006b k
    108: M203, // U+006c l
    109: [0, 0.45833, 0, 0, 0.86667], // U+006d m
    110: M205, // U+006e n
    111: [0, 0.45833, 0, 0, 0.55], // U+006f o
    112: M204, // U+0070 p
    113: M204, // U+0071 q
    114: [0, 0.45833, 0.01528, 0, 0.37222], // U+0072 r
    115: [0, 0.45833, 0, 0, 0.42167], // U+0073 s
    116: [0, 0.58929, 0, 0, 0.40417], // U+0074 t
    117: M205, // U+0075 u
    118: [0, 0.45833, 0.01528, 0, 0.5], // U+0076 v
    119: [0, 0.45833, 0.01528, 0, 0.74445], // U+0077 w
    120: [0, 0.45833, 0, 0, 0.5], // U+0078 x
    121: [0.19444, 0.45833, 0.01528, 0, 0.5], // U+0079 y
    122: [0, 0.45833, 0, 0, 0.47639], // U+007a z
    126: [0.35, 0.34444, 0, 0, 0.55], // U+007e ~
    160: M7, // U+00a0
    168: M195, // U+00a8 ¨
    176: M196, // U+00b0 °
    180: M195, // U+00b4 ´
    184: [0.17014, 0, 0, 0, 0.48889], // U+00b8 ¸
    305: [0, 0.45833, 0, 0, 0.25556], // U+0131 ı
    567: [0.19444, 0.45833, 0, 0, 0.28611], // U+0237 ȷ
    710: M195, // U+02c6 ˆ
    711: [0, 0.63542, 0, 0, 0.55], // U+02c7 ˇ
    713: [0, 0.63778, 0, 0, 0.55], // U+02c9 ˉ
    728: M195, // U+02d8 ˘
    729: M206, // U+02d9 ˙
    730: M196, // U+02da ˚
    732: M195, // U+02dc ˜
    733: M195, // U+02dd ˝
    915: M207, // U+0393 Γ
    916: [0, 0.69444, 0, 0, 0.91667], // U+0394 Δ
    920: M209, // U+0398 Θ
    923: M208, // U+039b Λ
    926: M196, // U+039e Ξ
    928: M197, // U+03a0 Π
    931: M197, // U+03a3 Σ
    933: M209, // U+03a5 Υ
    934: M197, // U+03a6 Φ
    936: M209, // U+03a8 Ψ
    937: M197, // U+03a9 Ω
    8211: [0, 0.45833, 0.03056, 0, 0.55], // U+2013 –
    8212: [0, 0.45833, 0.03056, 0, 1.10001], // U+2014 —
    8216: M206, // U+2018 ‘
    8217: M206, // U+2019 ’
    8220: M210, // U+201c “
    8221: M210, // U+201d ”
  },
  'SansSerif-Italic': {
    32: M7, // U+0020
    33: [0, 0.69444, 0.05733, 0, 0.31945], // U+0021 !
    34: M219, // U+0022 "
    35: [0.19444, 0.69444, 0.05087, 0, 0.83334], // U+0023 #
    36: [0.05556, 0.75, 0.11156, 0, 0.5], // U+0024 $
    37: [0.05556, 0.75, 0.03126, 0, 0.83334], // U+0025 %
    38: [0, 0.69444, 0.03058, 0, 0.75834], // U+0026 &
    39: M218, // U+0027 '
    40: [0.25, 0.75, 0.13164, 0, 0.38889], // U+0028 (
    41: [0.25, 0.75, 0.02536, 0, 0.38889], // U+0029 )
    42: [0, 0.75, 0.11775, 0, 0.5], // U+002a *
    43: [0.08333, 0.58333, 0.02536, 0, 0.77778], // U+002b +
    44: M223, // U+002c ,
    45: [0, 0.44444, 0.01946, 0, 0.33333], // U+002d -
    46: M224, // U+002e .
    47: [0.25, 0.75, 0.13164, 0, 0.5], // U+002f /
    48: M211, // U+0030 0
    49: M211, // U+0031 1
    50: M211, // U+0032 2
    51: M211, // U+0033 3
    52: M211, // U+0034 4
    53: M211, // U+0035 5
    54: M211, // U+0036 6
    55: M211, // U+0037 7
    56: M211, // U+0038 8
    57: M211, // U+0039 9
    58: [0, 0.44444, 0.02502, 0, 0.27778], // U+003a :
    59: [0.125, 0.44444, 0.02502, 0, 0.27778], // U+003b ;
    61: [-0.13, 0.37, 0.05087, 0, 0.77778], // U+003d =
    63: [0, 0.69444, 0.11809, 0, 0.47222], // U+003f ?
    64: [0, 0.69444, 0.07555, 0, 0.66667], // U+0040 @
    65: M227, // U+0041 A
    66: [0, 0.69444, 0.08293, 0, 0.66667], // U+0042 B
    67: [0, 0.69444, 0.11983, 0, 0.63889], // U+0043 C
    68: [0, 0.69444, 0.07555, 0, 0.72223], // U+0044 D
    69: [0, 0.69444, 0.11983, 0, 0.59722], // U+0045 E
    70: [0, 0.69444, 0.13372, 0, 0.56945], // U+0046 F
    71: [0, 0.69444, 0.11983, 0, 0.66667], // U+0047 G
    72: M212, // U+0048 H
    73: [0, 0.69444, 0.13372, 0, 0.27778], // U+0049 I
    74: [0, 0.69444, 0.08094, 0, 0.47222], // U+004a J
    75: [0, 0.69444, 0.11983, 0, 0.69445], // U+004b K
    76: M229, // U+004c L
    77: [0, 0.69444, 0.08094, 0, 0.875], // U+004d M
    78: M212, // U+004e N
    79: [0, 0.69444, 0.07555, 0, 0.73611], // U+004f O
    80: [0, 0.69444, 0.08293, 0, 0.63889], // U+0050 P
    81: [0.125, 0.69444, 0.07555, 0, 0.73611], // U+0051 Q
    82: [0, 0.69444, 0.08293, 0, 0.64584], // U+0052 R
    83: [0, 0.69444, 0.09205, 0, 0.55556], // U+0053 S
    84: [0, 0.69444, 0.13372, 0, 0.68056], // U+0054 T
    85: [0, 0.69444, 0.08094, 0, 0.6875], // U+0055 U
    86: [0, 0.69444, 0.1615, 0, 0.66667], // U+0056 V
    87: [0, 0.69444, 0.1615, 0, 0.94445], // U+0057 W
    88: [0, 0.69444, 0.13372, 0, 0.66667], // U+0058 X
    89: [0, 0.69444, 0.17261, 0, 0.66667], // U+0059 Y
    90: [0, 0.69444, 0.11983, 0, 0.61111], // U+005a Z
    91: [0.25, 0.75, 0.15942, 0, 0.28889], // U+005b [
    93: [0.25, 0.75, 0.08719, 0, 0.28889], // U+005d ]
    94: M214, // U+005e ^
    95: [0.35, 0.09444, 0.08616, 0, 0.5], // U+005f _
    97: [0, 0.44444, 0.00981, 0, 0.48056], // U+0061 a
    98: [0, 0.69444, 0.03057, 0, 0.51667], // U+0062 b
    99: [0, 0.44444, 0.08336, 0, 0.44445], // U+0063 c
    100: [0, 0.69444, 0.09483, 0, 0.51667], // U+0064 d
    101: [0, 0.44444, 0.06778, 0, 0.44445], // U+0065 e
    102: [0, 0.69444, 0.21705, 0, 0.30556], // U+0066 f
    103: [0.19444, 0.44444, 0.10836, 0, 0.5], // U+0067 g
    104: [0, 0.69444, 0.01778, 0, 0.51667], // U+0068 h
    105: [0, 0.67937, 0.09718, 0, 0.23889], // U+0069 i
    106: [0.19444, 0.67937, 0.09162, 0, 0.26667], // U+006a j
    107: [0, 0.69444, 0.08336, 0, 0.48889], // U+006b k
    108: [0, 0.69444, 0.09483, 0, 0.23889], // U+006c l
    109: [0, 0.44444, 0.01778, 0, 0.79445], // U+006d m
    110: [0, 0.44444, 0.01778, 0, 0.51667], // U+006e n
    111: [0, 0.44444, 0.06613, 0, 0.5], // U+006f o
    112: [0.19444, 0.44444, 0.0389, 0, 0.51667], // U+0070 p
    113: [0.19444, 0.44444, 0.04169, 0, 0.51667], // U+0071 q
    114: [0, 0.44444, 0.10836, 0, 0.34167], // U+0072 r
    115: [0, 0.44444, 0.0778, 0, 0.38333], // U+0073 s
    116: [0, 0.57143, 0.07225, 0, 0.36111], // U+0074 t
    117: [0, 0.44444, 0.04169, 0, 0.51667], // U+0075 u
    118: [0, 0.44444, 0.10836, 0, 0.46111], // U+0076 v
    119: [0, 0.44444, 0.10836, 0, 0.68334], // U+0077 w
    120: [0, 0.44444, 0.09169, 0, 0.46111], // U+0078 x
    121: [0.19444, 0.44444, 0.10836, 0, 0.46111], // U+0079 y
    122: [0, 0.44444, 0.08752, 0, 0.43472], // U+007a z
    126: [0.35, 0.32659, 0.08826, 0, 0.5], // U+007e ~
    160: M7, // U+00a0
    168: [0, 0.67937, 0.06385, 0, 0.5], // U+00a8 ¨
    176: M215, // U+00b0 °
    184: M213, // U+00b8 ¸
    305: [0, 0.44444, 0.04169, 0, 0.23889], // U+0131 ı
    567: [0.19444, 0.44444, 0.04169, 0, 0.26667], // U+0237 ȷ
    710: M214, // U+02c6 ˆ
    711: [0, 0.63194, 0.08432, 0, 0.5], // U+02c7 ˇ
    713: [0, 0.60889, 0.08776, 0, 0.5], // U+02c9 ˉ
    714: M216, // U+02ca ˊ
    715: M143, // U+02cb ˋ
    728: [0, 0.69444, 0.09483, 0, 0.5], // U+02d8 ˘
    729: [0, 0.67937, 0.07774, 0, 0.27778], // U+02d9 ˙
    730: M215, // U+02da ˚
    732: [0, 0.67659, 0.08826, 0, 0.5], // U+02dc ˜
    733: M216, // U+02dd ˝
    915: [0, 0.69444, 0.13372, 0, 0.54167], // U+0393 Γ
    916: M237, // U+0394 Δ
    920: [0, 0.69444, 0.07555, 0, 0.77778], // U+0398 Θ
    923: M170, // U+039b Λ
    926: [0, 0.69444, 0.12816, 0, 0.66667], // U+039e Ξ
    928: M212, // U+03a0 Π
    931: [0, 0.69444, 0.11983, 0, 0.72222], // U+03a3 Σ
    933: M217, // U+03a5 Υ
    934: [0, 0.69444, 0.04603, 0, 0.72222], // U+03a6 Φ
    936: M217, // U+03a8 Ψ
    937: [0, 0.69444, 0.08293, 0, 0.72222], // U+03a9 Ω
    8211: [0, 0.44444, 0.08616, 0, 0.5], // U+2013 –
    8212: [0, 0.44444, 0.08616, 0, 1], // U+2014 —
    8216: M218, // U+2018 ‘
    8217: M218, // U+2019 ’
    8220: [0, 0.69444, 0.14205, 0, 0.5], // U+201c “
    8221: M219, // U+201d ”
  },
  'SansSerif-Regular': {
    32: M7, // U+0020
    33: [0, 0.69444, 0, 0, 0.31945], // U+0021 !
    34: M143, // U+0022 "
    35: M220, // U+0023 #
    36: M161, // U+0024 $
    37: M221, // U+0025 %
    38: [0, 0.69444, 0, 0, 0.75834], // U+0026 &
    39: M133, // U+0027 '
    40: M134, // U+0028 (
    41: M134, // U+0029 )
    42: M222, // U+002a *
    43: M149, // U+002b +
    44: M223, // U+002c ,
    45: [0, 0.44444, 0, 0, 0.33333], // U+002d -
    46: M224, // U+002e .
    47: M141, // U+002f /
    48: M225, // U+0030 0
    49: M225, // U+0031 1
    50: M225, // U+0032 2
    51: M225, // U+0033 3
    52: M225, // U+0034 4
    53: M225, // U+0035 5
    54: M225, // U+0036 6
    55: M225, // U+0037 7
    56: M225, // U+0038 8
    57: M225, // U+0039 9
    58: [0, 0.44444, 0, 0, 0.27778], // U+003a :
    59: [0.125, 0.44444, 0, 0, 0.27778], // U+003b ;
    61: [-0.13, 0.37, 0, 0, 0.77778], // U+003d =
    63: M226, // U+003f ?
    64: M227, // U+0040 @
    65: M227, // U+0041 A
    66: M227, // U+0042 B
    67: M75, // U+0043 C
    68: [0, 0.69444, 0, 0, 0.72223], // U+0044 D
    69: M228, // U+0045 E
    70: [0, 0.69444, 0, 0, 0.56945], // U+0046 F
    71: M227, // U+0047 G
    72: M230, // U+0048 H
    73: M133, // U+0049 I
    74: M226, // U+004a J
    75: [0, 0.69444, 0, 0, 0.69445], // U+004b K
    76: M229, // U+004c L
    77: [0, 0.69444, 0, 0, 0.875], // U+004d M
    78: M230, // U+004e N
    79: [0, 0.69444, 0, 0, 0.73611], // U+004f O
    80: M75, // U+0050 P
    81: [0.125, 0.69444, 0, 0, 0.73611], // U+0051 Q
    82: [0, 0.69444, 0, 0, 0.64584], // U+0052 R
    83: M144, // U+0053 S
    84: [0, 0.69444, 0, 0, 0.68056], // U+0054 T
    85: [0, 0.69444, 0, 0, 0.6875], // U+0055 U
    86: [0, 0.69444, 0.01389, 0, 0.66667], // U+0056 V
    87: [0, 0.69444, 0.01389, 0, 0.94445], // U+0057 W
    88: M227, // U+0058 X
    89: [0, 0.69444, 0.025, 0, 0.66667], // U+0059 Y
    90: M170, // U+005a Z
    91: M231, // U+005b [
    93: M231, // U+005d ]
    94: M143, // U+005e ^
    95: [0.35, 0.09444, 0.02778, 0, 0.5], // U+005f _
    97: [0, 0.44444, 0, 0, 0.48056], // U+0061 a
    98: M232, // U+0062 b
    99: M233, // U+0063 c
    100: M232, // U+0064 d
    101: M233, // U+0065 e
    102: [0, 0.69444, 0.06944, 0, 0.30556], // U+0066 f
    103: [0.19444, 0.44444, 0.01389, 0, 0.5], // U+0067 g
    104: M232, // U+0068 h
    105: [0, 0.67937, 0, 0, 0.23889], // U+0069 i
    106: [0.19444, 0.67937, 0, 0, 0.26667], // U+006a j
    107: [0, 0.69444, 0, 0, 0.48889], // U+006b k
    108: [0, 0.69444, 0, 0, 0.23889], // U+006c l
    109: [0, 0.44444, 0, 0, 0.79445], // U+006d m
    110: M236, // U+006e n
    111: [0, 0.44444, 0, 0, 0.5], // U+006f o
    112: M234, // U+0070 p
    113: M234, // U+0071 q
    114: [0, 0.44444, 0.01389, 0, 0.34167], // U+0072 r
    115: M235, // U+0073 s
    116: [0, 0.57143, 0, 0, 0.36111], // U+0074 t
    117: M236, // U+0075 u
    118: [0, 0.44444, 0.01389, 0, 0.46111], // U+0076 v
    119: [0, 0.44444, 0.01389, 0, 0.68334], // U+0077 w
    120: [0, 0.44444, 0, 0, 0.46111], // U+0078 x
    121: [0.19444, 0.44444, 0.01389, 0, 0.46111], // U+0079 y
    122: [0, 0.44444, 0, 0, 0.43472], // U+007a z
    126: [0.35, 0.32659, 0, 0, 0.5], // U+007e ~
    160: M7, // U+00a0
    168: [0, 0.67937, 0, 0, 0.5], // U+00a8 ¨
    176: M227, // U+00b0 °
    184: M213, // U+00b8 ¸
    305: [0, 0.44444, 0, 0, 0.23889], // U+0131 ı
    567: [0.19444, 0.44444, 0, 0, 0.26667], // U+0237 ȷ
    710: M143, // U+02c6 ˆ
    711: [0, 0.63194, 0, 0, 0.5], // U+02c7 ˇ
    713: [0, 0.60889, 0, 0, 0.5], // U+02c9 ˉ
    714: M143, // U+02ca ˊ
    715: M143, // U+02cb ˋ
    728: M143, // U+02d8 ˘
    729: [0, 0.67937, 0, 0, 0.27778], // U+02d9 ˙
    730: M227, // U+02da ˚
    732: [0, 0.67659, 0, 0, 0.5], // U+02dc ˜
    733: M143, // U+02dd ˝
    915: M229, // U+0393 Γ
    916: M237, // U+0394 Δ
    920: M136, // U+0398 Θ
    923: M170, // U+039b Λ
    926: M227, // U+039e Ξ
    928: M230, // U+03a0 Π
    931: M156, // U+03a3 Σ
    933: M136, // U+03a5 Υ
    934: M156, // U+03a6 Φ
    936: M136, // U+03a8 Ψ
    937: M156, // U+03a9 Ω
    8211: [0, 0.44444, 0.02778, 0, 0.5], // U+2013 –
    8212: [0, 0.44444, 0.02778, 0, 1], // U+2014 —
    8216: M133, // U+2018 ‘
    8217: M133, // U+2019 ’
    8220: M143, // U+201c “
    8221: M143, // U+201d ”
  },
  'Script-Regular': {
    32: M7, // U+0020
    65: [0, 0.7, 0.22925, 0, 0.80253], // U+0041 A
    66: [0, 0.7, 0.04087, 0, 0.90757], // U+0042 B
    67: [0, 0.7, 0.1689, 0, 0.66619], // U+0043 C
    68: [0, 0.7, 0.09371, 0, 0.77443], // U+0044 D
    69: [0, 0.7, 0.18583, 0, 0.56162], // U+0045 E
    70: [0, 0.7, 0.13634, 0, 0.89544], // U+0046 F
    71: [0, 0.7, 0.17322, 0, 0.60961], // U+0047 G
    72: [0, 0.7, 0.29694, 0, 0.96919], // U+0048 H
    73: [0, 0.7, 0.19189, 0, 0.80907], // U+0049 I
    74: [0.27778, 0.7, 0.19189, 0, 1.05159], // U+004a J
    75: [0, 0.7, 0.31259, 0, 0.91364], // U+004b K
    76: [0, 0.7, 0.19189, 0, 0.87373], // U+004c L
    77: [0, 0.7, 0.15981, 0, 1.08031], // U+004d M
    78: [0, 0.7, 0.3525, 0, 0.9015], // U+004e N
    79: [0, 0.7, 0.08078, 0, 0.73787], // U+004f O
    80: [0, 0.7, 0.08078, 0, 1.01262], // U+0050 P
    81: [0, 0.7, 0.03305, 0, 0.88282], // U+0051 Q
    82: [0, 0.7, 0.06259, 0, 0.85], // U+0052 R
    83: [0, 0.7, 0.19189, 0, 0.86767], // U+0053 S
    84: [0, 0.7, 0.29087, 0, 0.74697], // U+0054 T
    85: [0, 0.7, 0.25815, 0, 0.79996], // U+0055 U
    86: [0, 0.7, 0.27523, 0, 0.62204], // U+0056 V
    87: [0, 0.7, 0.27523, 0, 0.80532], // U+0057 W
    88: [0, 0.7, 0.26006, 0, 0.94445], // U+0058 X
    89: [0, 0.7, 0.2939, 0, 0.70961], // U+0059 Y
    90: [0, 0.7, 0.24037, 0, 0.8212], // U+005a Z
    160: M7, // U+00a0
  },
  'Size1-Regular': {
    32: M7, // U+0020
    40: M238, // U+0028 (
    41: M238, // U+0029 )
    47: M239, // U+002f /
    91: M240, // U+005b [
    92: M239, // U+005c \
    93: M240, // U+005d ]
    123: M241, // U+007b {
    125: M241, // U+007d }
    160: M7, // U+00a0
    710: M242, // U+02c6 ˆ
    732: M242, // U+02dc ˜
    770: M242, // U+0302 ̂
    771: M242, // U+0303 ̃
    8214: [-0.00099, 0.601, 0, 0, 0.77778], // U+2016 ‖
    8593: M243, // U+2191 ↑
    8595: M243, // U+2193 ↓
    8657: M244, // U+21d1 ⇑
    8659: M244, // U+21d3 ⇓
    8719: M245, // U+220f ∏
    8720: M245, // U+2210 ∐
    8721: [0.25001, 0.75, 0, 0, 1.05556], // U+2211 ∑
    8730: [0.35001, 0.85, 0, 0, 1], // U+221a √
    8739: [-0.00599, 0.606, 0, 0, 0.33333], // U+2223 ∣
    8741: [-0.00599, 0.606, 0, 0, 0.55556], // U+2225 ∥
    8747: M247, // U+222b ∫
    8748: M246, // U+222c ∬
    8749: M246, // U+222d ∭
    8750: M247, // U+222e ∮
    8896: M248, // U+22c0 ⋀
    8897: M248, // U+22c1 ⋁
    8898: M248, // U+22c2 ⋂
    8899: M248, // U+22c3 ⋃
    8968: M249, // U+2308 ⌈
    8969: M249, // U+2309 ⌉
    8970: M249, // U+230a ⌊
    8971: M249, // U+230b ⌋
    9168: M277, // U+23d0 ⏐
    10216: M249, // U+27e8 ⟨
    10217: M249, // U+27e9 ⟩
    10752: M250, // U+2a00 ⨀
    10753: M250, // U+2a01 ⨁
    10754: M250, // U+2a02 ⨂
    10756: M248, // U+2a04 ⨄
    10758: M248, // U+2a06 ⨆
  },
  'Size2-Regular': {
    32: M7, // U+0020
    40: M251, // U+0028 (
    41: M251, // U+0029 )
    47: M252, // U+002f /
    91: M253, // U+005b [
    92: M252, // U+005c \
    93: M253, // U+005d ]
    123: M254, // U+007b {
    125: M254, // U+007d }
    160: M7, // U+00a0
    710: M255, // U+02c6 ˆ
    732: M255, // U+02dc ˜
    770: M255, // U+0302 ̂
    771: M255, // U+0303 ̃
    8719: M256, // U+220f ∏
    8720: M256, // U+2210 ∐
    8721: [0.55001, 1.05, 0, 0, 1.44445], // U+2211 ∑
    8730: [0.65002, 1.15, 0, 0, 1], // U+221a √
    8747: M258, // U+222b ∫
    8748: M257, // U+222c ∬
    8749: M257, // U+222d ∭
    8750: M258, // U+222e ∮
    8896: M259, // U+22c0 ⋀
    8897: M259, // U+22c1 ⋁
    8898: M259, // U+22c2 ⋂
    8899: M259, // U+22c3 ⋃
    8968: M260, // U+2308 ⌈
    8969: M260, // U+2309 ⌉
    8970: M260, // U+230a ⌊
    8971: M260, // U+230b ⌋
    10216: M261, // U+27e8 ⟨
    10217: M261, // U+27e9 ⟩
    10752: M262, // U+2a00 ⨀
    10753: M262, // U+2a01 ⨁
    10754: M262, // U+2a02 ⨂
    10756: M259, // U+2a04 ⨄
    10758: M259, // U+2a06 ⨆
  },
  'Size3-Regular': {
    32: M7, // U+0020
    40: M263, // U+0028 (
    41: M263, // U+0029 )
    47: M264, // U+002f /
    91: M265, // U+005b [
    92: M264, // U+005c \
    93: M265, // U+005d ]
    123: M266, // U+007b {
    125: M266, // U+007d }
    160: M7, // U+00a0
    710: M267, // U+02c6 ˆ
    732: M267, // U+02dc ˜
    770: M267, // U+0302 ̂
    771: M267, // U+0303 ̃
    8730: [0.95003, 1.45, 0, 0, 1], // U+221a √
    8968: M268, // U+2308 ⌈
    8969: M268, // U+2309 ⌉
    8970: M268, // U+230a ⌊
    8971: M268, // U+230b ⌋
    10216: M266, // U+27e8 ⟨
    10217: M266, // U+27e9 ⟩
  },
  'Size4-Regular': {
    32: M7, // U+0020
    40: M269, // U+0028 (
    41: M269, // U+0029 )
    47: M270, // U+002f /
    91: M271, // U+005b [
    92: M270, // U+005c \
    93: M271, // U+005d ]
    123: M272, // U+007b {
    125: M272, // U+007d }
    160: M7, // U+00a0
    710: M273, // U+02c6 ˆ
    732: M273, // U+02dc ˜
    770: M273, // U+0302 ̂
    771: M273, // U+0303 ̃
    8730: [1.25003, 1.75, 0, 0, 1], // U+221a √
    8968: M274, // U+2308 ⌈
    8969: M274, // U+2309 ⌉
    8970: M274, // U+230a ⌊
    8971: M274, // U+230b ⌋
    9115: M275, // U+239b ⎛
    9116: M276, // U+239c ⎜
    9117: M275, // U+239d ⎝
    9118: M275, // U+239e ⎞
    9119: M276, // U+239f ⎟
    9120: M275, // U+23a0 ⎠
    9121: M278, // U+23a1 ⎡
    9122: M277, // U+23a2 ⎢
    9123: M278, // U+23a3 ⎣
    9124: M278, // U+23a4 ⎤
    9125: M277, // U+23a5 ⎥
    9126: M278, // U+23a6 ⎦
    9127: M279, // U+23a7 ⎧
    9128: M280, // U+23a8 ⎨
    9129: M281, // U+23a9 ⎩
    9130: [0, 0.3, 0, 0, 0.88889], // U+23aa ⎪
    9131: M279, // U+23ab ⎫
    9132: M280, // U+23ac ⎬
    9133: M281, // U+23ad ⎭
    9143: [0.88502, 0.915, 0, 0, 1.05556], // U+23b7 ⎷
    10216: M272, // U+27e8 ⟨
    10217: M272, // U+27e9 ⟩
    57344: M282, // U+e000 
    57345: M282, // U+e001 
    57680: M283, // U+e150 
    57681: M283, // U+e151 
    57682: M283, // U+e152 
    57683: M283, // U+e153 
  },
  'Typewriter-Regular': {
    32: M290, // U+0020
    33: M284, // U+0021 !
    34: M284, // U+0022 "
    35: M284, // U+0023 #
    36: M285, // U+0024 $
    37: M285, // U+0025 %
    38: M284, // U+0026 &
    39: M284, // U+0027 '
    40: M285, // U+0028 (
    41: M285, // U+0029 )
    42: [0, 0.52083, 0, 0, 0.525], // U+002a *
    43: M286, // U+002b +
    44: [0.13889, 0.125, 0, 0, 0.525], // U+002c ,
    45: M286, // U+002d -
    46: [0, 0.125, 0, 0, 0.525], // U+002e .
    47: M285, // U+002f /
    48: M284, // U+0030 0
    49: M284, // U+0031 1
    50: M284, // U+0032 2
    51: M284, // U+0033 3
    52: M284, // U+0034 4
    53: M284, // U+0035 5
    54: M284, // U+0036 6
    55: M284, // U+0037 7
    56: M284, // U+0038 8
    57: M284, // U+0039 9
    58: M288, // U+003a :
    59: [0.13889, 0.43056, 0, 0, 0.525], // U+003b ;
    60: M287, // U+003c <
    61: [-0.19549, 0.41562, 0, 0, 0.525], // U+003d =
    62: M287, // U+003e >
    63: M284, // U+003f ?
    64: M284, // U+0040 @
    65: M284, // U+0041 A
    66: M284, // U+0042 B
    67: M284, // U+0043 C
    68: M284, // U+0044 D
    69: M284, // U+0045 E
    70: M284, // U+0046 F
    71: M284, // U+0047 G
    72: M284, // U+0048 H
    73: M284, // U+0049 I
    74: M284, // U+004a J
    75: M284, // U+004b K
    76: M284, // U+004c L
    77: M284, // U+004d M
    78: M284, // U+004e N
    79: M284, // U+004f O
    80: M284, // U+0050 P
    81: [0.13889, 0.61111, 0, 0, 0.525], // U+0051 Q
    82: M284, // U+0052 R
    83: M284, // U+0053 S
    84: M284, // U+0054 T
    85: M284, // U+0055 U
    86: M284, // U+0056 V
    87: M284, // U+0057 W
    88: M284, // U+0058 X
    89: M284, // U+0059 Y
    90: M284, // U+005a Z
    91: M285, // U+005b [
    92: M285, // U+005c \
    93: M285, // U+005d ]
    94: M284, // U+005e ^
    95: [0.09514, 0, 0, 0, 0.525], // U+005f _
    96: M284, // U+0060 `
    97: M288, // U+0061 a
    98: M284, // U+0062 b
    99: M288, // U+0063 c
    100: M284, // U+0064 d
    101: M288, // U+0065 e
    102: M284, // U+0066 f
    103: M289, // U+0067 g
    104: M284, // U+0068 h
    105: M284, // U+0069 i
    106: [0.22222, 0.61111, 0, 0, 0.525], // U+006a j
    107: M284, // U+006b k
    108: M284, // U+006c l
    109: M288, // U+006d m
    110: M288, // U+006e n
    111: M288, // U+006f o
    112: M289, // U+0070 p
    113: M289, // U+0071 q
    114: M288, // U+0072 r
    115: M288, // U+0073 s
    116: [0, 0.55358, 0, 0, 0.525], // U+0074 t
    117: M288, // U+0075 u
    118: M288, // U+0076 v
    119: M288, // U+0077 w
    120: M288, // U+0078 x
    121: M289, // U+0079 y
    122: M288, // U+007a z
    123: M285, // U+007b {
    124: M285, // U+007c |
    125: M285, // U+007d }
    126: M284, // U+007e ~
    127: M284, // U+007f 
    160: M290, // U+00a0
    176: M284, // U+00b0 °
    184: [0.19445, 0, 0, 0, 0.525], // U+00b8 ¸
    305: M288, // U+0131 ı
    567: M289, // U+0237 ȷ
    711: [0, 0.56597, 0, 0, 0.525], // U+02c7 ˇ
    713: [0, 0.56555, 0, 0, 0.525], // U+02c9 ˉ
    714: M284, // U+02ca ˊ
    715: M284, // U+02cb ˋ
    728: M284, // U+02d8 ˘
    730: M284, // U+02da ˚
    770: M284, // U+0302 ̂
    771: M284, // U+0303 ̃
    776: M284, // U+0308 ̈
    915: M284, // U+0393 Γ
    916: M284, // U+0394 Δ
    920: M284, // U+0398 Θ
    923: M284, // U+039b Λ
    926: M284, // U+039e Ξ
    928: M284, // U+03a0 Π
    931: M284, // U+03a3 Σ
    933: M284, // U+03a5 Υ
    934: M284, // U+03a6 Φ
    936: M284, // U+03a8 Ψ
    937: M284, // U+03a9 Ω
    8216: M284, // U+2018 ‘
    8217: M284, // U+2019 ’
    8242: M284, // U+2032 ′
    9251: [0.11111, 0.21944, 0, 0, 0.525], // U+2423 ␣
  },
};

export enum JitsiTrackErrors {
  CONSTRAINT_FAILED = 'gum.constraint_failed',
  ELECTRON_DESKTOP_PICKER_ERROR = 'gum.electron_desktop_picker_error',
  ELECTRON_DESKTOP_PICKER_NOT_FOUND = 'gum.electron_desktop_picker_not_found',
  GENERAL = 'gum.general',
  NOT_FOUND = 'gum.not_found',
  PERMISSION_DENIED = 'gum.permission_denied',
  SCREENSHARING_GENERIC_ERROR = 'gum.screensharing_generic_error',
  SCREENSHARING_USER_CANCELED = 'gum.screensharing_user_canceled',
  TIMEOUT = 'gum.timeout',
  TRACK_IS_DISPOSED = 'track.track_is_disposed',
  TRACK_NO_STREAM_FOUND = 'track.no_stream_found',
  UNSUPPORTED_RESOLUTION = 'gum.unsupported_resolution'
}

<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8" />
    <title>MathLive Custom Virtual Keyboard</title>
    <link rel="stylesheet" href="../style.css" />
    <style>
      .mathfield {
        font-size: 32px;
      }

      math-field:not(:defined) {
        visibility: hidden;
      }

      /* math-field:focus-within {
      outline: none;
      background: red;
    } */

      code {
        white-space: pre-wrap;
      }

      h2 {
        font-size: 1em;
        padding: 0;
        margin: 0;
        color: #666;
      }

      #mathfield-container {
        padding: 8px;
        min-width: 50px;
        border: 1px solid #ddd;
      }

      #ta {
        font-size: 1.5em;
        font-weight: 700;
      }

      #find-input,
      #replace-input {
        width: 320px;
      }

      .hidden {
        display: none;
      }

      .label {
        font-weight: 700;
      }

      .math {
        display: none;
      }

      #keyboard-container {
        display: block;
        position: relative;

        height: 600px;
        width: 1024px;
        background: palegreen;
        overflow: hidden;
        border: 5px solid red;
      }
    </style>
  </head>

  <body>
    <header><h1>MathLive Custom Virtual Keyboard</h1></header>
    <main class="centered" tabindex="0">
      <!-- <div id='keyboard-container'> -->
      <math-field
        id="mf"
        class="mathfield"
        style="font-size: 32px; max-width: 640px; width: 100%"
        virtual-keyboard-mode="manual"
        >a+b</math-field
      >

      <h2>LaTeX</h2>
      <div class="output" id="latex"></div>
    </main>

    <script type="module">
      import '/dist/mathlive.mjs';

      const mf = document.getElementById('mf');

      mathVirtualKeyboard.layouts = [
        {
          label: 'Basic',
          rows: [
            [
              '[7]',
              '[8]',
              '[9]',
              '[+]',
              '[separator-5]',
              { class: 'small', latex: '\\frac{#@}{#0}' },
              '\\varnothing',
              '\\infty',
              '\\in',
              '\\notin',
              '[separator]',
            ],
            [
              '[4]',
              '[5]',
              '[6]',
              '[-]',
              '[separator-5]',
              '[(]',
              '[)]',
              '\\lt',
              '\\le',
              '\\hat{=}',
              '[separator]',
            ],
            [
              '[1]',
              '[2]',
              '[3]',
              '\\cdot',
              '[separator-5]',
              '[',
              ']',
              '\\gt',
              '\\ge',

              { label: '[backspace]', width: 2 },
            ],
            [
              { label: '[0]', width: 2 },
              '[.]',
              '\\colon',
              '[separator-5]',
              '\\lbrace',
              '\\rbrace',
              '=',
              '\\ne',
              '[left]',
              '[right]',
            ],
          ],
        },
        {
          label: 'Geometry',
          rows: [
            [
              '\\vert',
              '\\not{\\vert}',
              '\\parallel',
              '\\bot',
              '\\overrightarrow{#@}',
              '\\begin{pmatrix}#0\\\\#0\\end{pmatrix}',
            ],
            [
              '\\measuredangle',
              ';',
              '\\longmapsto',
              '\\left\\lvert #0 \\right\\rvert',
              '\\left\\lvert \\overline{#0} \\right\\rvert',
              '\\overline{#@}',
            ],
            [],
            [
              { class: 'separator w20' },
              { class: 'separator w20' },
              { class: 'separator w20' },
              { class: 'separator w15' },
              '[left]',
              '[right]',
            ],
          ],
        },
        {
          label: 'Secondary',
          rows: [
            [
              {
                latex: 'x',
                variants: [{ latex: '\\cos\\left( #0 \\right)' }],
                width: 1.5,
              },
              // { class: "w15", latex: "\\sin\\left( #0 \\right)" },
              {
                latex: '\\cos',
                insert: '\\cos(#0)',
                shift: { latex: '\\sin', insert: '\\sin (#0)' },
                width: 1.5,
              },
              { latex: '\\tan\\left( #0 \\right)', width: 1.5 },
              '[separator-5]',

              '\\land',
              '\\lor',
              '\\lnot',
              '[separator-5]',

              '\\approx',
            ],
            [
              { latex: '\\sin^{-1}\\left( #0 \\right)', width: 1.5 },
              { latex: '\\cos^{-1}\\left( #0 \\right)', width: 1.5 },
              { latex: '\\tan^{-1}\\left( #0 \\right)', width: 1.5 },
              '[separator-5]',
              { latex: '#@^{#?}', width: 1.5 },
              { latex: '#@^2', width: 1.5 },
              '[separator-5]',
              '\\cong',
            ],
            [
              {
                latex: '\\begin{vmatrix}#?&#?\\\\#?&#?\\end{vmatrix}',
                width: 1.5,
              },
              '\\sqrt{#@}',
              '\\pm',
              '\\overarc{#@}',
              '\\sim',
              { latex: '#@_{#?}', width: 1.5 },
            ],
            [
              '[separator-20]',
              '[separator-20]',
              '[separator-20]',
              '[separator-15]',
              '[left]',
              '[right]',
            ],
          ],
        },
      ];

      mf?.addEventListener('input', (ev) => updateContent(mf));
      mf?.addEventListener('math-error', (ev) =>
        console.log('Error :' + ev.detail.code)
      );

      if (mf) updateContent(mf);

      function updateContent(mf) {
        const latex = mf.getValue('latex-expanded');
        try {
          document.getElementById('latex').innerHTML = escapeHtml(latex);
        } catch (e) {
          console.error(
            'Error converting %c ' + latex + '%c ' + e.toString(),
            'color: red;  background: hsla(0, 60%, 90%)',
            'background: transparent'
          );
        }
      }

      function escapeHtml(string) {
        return String(string).replace(/[&<>"'`= /\u200b]/g, function (s) {
          return (
            {
              '&': '&amp;',
              '<': '&lt;',
              '>': '&gt;',
              '"': '&quot;',
              "'": '&#39;',
              '/': '&#x2F;',
              '`': '&#x60;',
              '=': '&#x3D;',
              '\u200b': '&amp;#zws;',
            }[s] || s
          );
        });
      }
    </script>
  </body>
</html>

#!/bin/bash

set -e  # exit immediately on error

# export DOT="\033[32m ● \033[0m"
# export CHECK="\033[32m ✔ \033[0m"
# export LINECLEAR="\033[1G\033[2K" # position to column 1; erase whole line
# export ERROR="\033[31m ❌ ERROR \033[0m"
# export RESET="\033[0m"

# The hook is usually executed in environment that doesn't support ANSI codes
export DOT="●"
export CHECK="✔"
export LINECLEAR="" # position to column 1; erase whole line
export ERROR="❌ ERROR"
export RESET=""


git config advice.addEmptyPathspec false

echo "${LINECLEAR}${CHECK} Linting Typescript files"
exit_code=1
export all_ts_files=$(git diff --cached --diff-filter=d --name-only | grep .ts$)

if [ ${#all_ts_files} -gt 0 ]; then
  npx eslint ${all_ts_files} --quiet --fix
  exit_code=$?

  # Add to the commit list any filed updated by the linter
  git add -f ${all_ts_files}

  if [ $exit_code -ne 0 ]
  then
    echo "${LINECLEAR}${ERROR} eslint failed${RESET}"
    exit 1
  else
    echo "${LINECLEAR}${CHECK} eslint passed${RESET}"
  fi
fi


echo "${LINECLEAR}${CHECK} Linting other files"
exit_code=1
export all_other_files=$(git diff --cached --diff-filter=d --name-only | grep -E '(.css|.json|.less|.md)$')
if [ ${#all_other_files} -gt 0 ]; then
  npx prettier ${all_other_files} --write
  exit_code=$?

  # Add to the commit list any filed updated by prettier
  git add -f ${all_other_files}

  if [ $exit_code -ne 0 ]
  then
    echo "${LINECLEAR}${ERROR} prettier failed${RESET}"
    exit 1
  else
    echo "${LINECLEAR}${CHECK} prettier passed${RESET}"
  fi
fi

exit_code=1
npm run test
exit_code=$?

if [ $exit_code -ne 0 ]
then
  echo "${LINECLEAR}${ERROR} Unit testing failed${RESET}"
  exit 1
else
  echo "${LINECLEAR}${CHECK} Unit testing passed${RESET}"
  exit 0
fi

